[31m2025-08-15 01:00:00.009[0;39m [32m[scheduling-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.timer.FileCleaningScheduler[0;39m - ----------Fri Aug 15 01:00:00 CST 2025文件清理启动--------
[31m2025-08-15 01:00:00.120[0;39m [32m[scheduling-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.timer.FileCleaningScheduler[0;39m - ----------Fri Aug 15 01:00:00 CST 2025文件清理结束--------
[31m2025-08-15 10:20:53.889[0;39m [32m[SpringApplicationShutdownHook][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} closing ...
[31m2025-08-15 10:20:53.894[0;39m [32m[SpringApplicationShutdownHook][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} closed
[31m2025-08-15 10:20:56.996[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-08-15 10:20:57.002[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 29864 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-08-15 10:20:57.002[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-08-15 10:20:58.387[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-08-15 10:20:58.390[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-08-15 10:20:58.439[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
[31m2025-08-15 10:20:58.652[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.653[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.654[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.655[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.655[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.655[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.655[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.655[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.655[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.655[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:20:58.818[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=c0c3359a-a69b-3a9d-a9b3-0395b257e885
[31m2025-08-15 10:21:00.187[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-15 10:21:00.221[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-15 10:21:01.655[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-08-15 10:21:01.669[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-08-15 10:21:01.669[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-08-15 10:21:01.670[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-08-15 10:21:01.745[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-08-15 10:21:01.745[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 4661 ms
[31m2025-08-15 10:21:02.174[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-08-15 10:21:02.919[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-08-15 10:21:04.583[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-08-15 10:21:07.536[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-08-15 10:21:08.095[0;39m [32m[redisson-netty-4-9][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-15 10:21:08.244[0;39m [32m[redisson-netty-4-19][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-15 10:21:10.325[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.elasticsearch.client.RestClient[0;39m - request [GET http://192.168.1.18:8199/_mapping?master_timeout=30s] returned 1 warnings: [299 Elasticsearch-7.16.0-6fc81662312141fe7691d7c1c91b8658ac17aa0d "this request accesses system indices: [.security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]
[31m2025-08-15 10:21:16.560[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentIpcEntity".
[31m2025-08-15 10:21:16.560[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentIpcEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 10:21:16.631[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis".
[31m2025-08-15 10:21:16.631[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 10:21:18.248[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.model.RDRatio".
[31m2025-08-15 10:21:18.249[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.model.RDRatio ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 10:21:24.811[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.cloud.sentinel.SentinelWebAutoConfiguration[0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[31m2025-08-15 10:21:31.475[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension".
[31m2025-08-15 10:21:31.475[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 10:21:31.526[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Starting ProtocolHandler ["http-nio-8804"]
[31m2025-08-15 10:21:31.556[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat started on port(s): 8804 (http) with context path '/api'
[31m2025-08-15 10:21:32.519[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.534[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.541[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.545[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.545[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.546[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.547[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.591[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.750[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.753[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.759[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: MultipartFile, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.764[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.767[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:32.768[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.014[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.026[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.036[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.036[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.058[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.068[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.070[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.071[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.071[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.074[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.075[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.075[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.077[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.080[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.081[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.083[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.085[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.086[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:21:33.373[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Started NanPingInnovationApplication in 37.218 seconds (JVM running for 38.221)
[31m2025-08-15 10:25:03.504[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[31m2025-08-15 10:25:03.504[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Initializing Servlet 'dispatcherServlet'
[31m2025-08-15 10:25:03.506[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Completed initialization in 2 ms
[31m2025-08-15 10:25:03.929[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-15 10:25:04.849[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:CockpitController.fusionDetail(..),success,cost:654ms,uri:/api/statistics/fusion
[31m2025-08-15 10:25:04.850[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:StatisticsController.fusionDetail(..),success,cost:693ms,uri:/api/statistics/fusion
[31m2025-08-15 10:25:12.072[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-15 10:25:12.116[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:CockpitController.fusionDetail(..),success,cost:42ms,uri:/api/statistics/fusion
[31m2025-08-15 10:25:12.116[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:StatisticsController.fusionDetail(..),success,cost:43ms,uri:/api/statistics/fusion
[31m2025-08-15 10:37:52.695[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-08-15 10:37:52.700[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 28456 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-08-15 10:37:52.700[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-08-15 10:37:53.936[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-08-15 10:37:53.939[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-08-15 10:37:53.987[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
[31m2025-08-15 10:37:54.199[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.199[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.199[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.199[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.199[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.200[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.201[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 10:37:54.366[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=c0c3359a-a69b-3a9d-a9b3-0395b257e885
[31m2025-08-15 10:37:55.694[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-15 10:37:55.748[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-15 10:37:57.137[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-08-15 10:37:57.150[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-08-15 10:37:57.151[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-08-15 10:37:57.151[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-08-15 10:37:57.222[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-08-15 10:37:57.222[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 4450 ms
[31m2025-08-15 10:37:57.609[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-08-15 10:37:58.213[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-08-15 10:37:59.861[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-08-15 10:38:02.610[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-08-15 10:38:03.154[0;39m [32m[redisson-netty-4-11][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-15 10:38:03.373[0;39m [32m[redisson-netty-4-19][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-15 10:38:05.487[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.elasticsearch.client.RestClient[0;39m - request [GET http://192.168.1.18:8199/_mapping?master_timeout=30s] returned 1 warnings: [299 Elasticsearch-7.16.0-6fc81662312141fe7691d7c1c91b8658ac17aa0d "this request accesses system indices: [.security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]
[31m2025-08-15 10:38:11.494[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentIpcEntity".
[31m2025-08-15 10:38:11.494[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentIpcEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 10:38:11.557[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis".
[31m2025-08-15 10:38:11.557[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 10:38:13.234[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.model.RDRatio".
[31m2025-08-15 10:38:13.234[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.model.RDRatio ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 10:38:19.790[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.cloud.sentinel.SentinelWebAutoConfiguration[0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[31m2025-08-15 10:38:26.185[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension".
[31m2025-08-15 10:38:26.185[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 10:38:26.236[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Starting ProtocolHandler ["http-nio-8804"]
[31m2025-08-15 10:38:26.262[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat started on port(s): 8804 (http) with context path '/api'
[31m2025-08-15 10:38:27.203[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.219[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.227[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.230[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.231[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.231[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.232[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.279[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.440[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.446[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.456[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: MultipartFile, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.461[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.464[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.465[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.737[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.749[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.759[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.759[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.769[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.779[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.792[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.796[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.797[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.797[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.802[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.803[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.803[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.805[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.806[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.806[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.809[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.809[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.813[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.817[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:27.818[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 10:38:28.130[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Started NanPingInnovationApplication in 36.084 seconds (JVM running for 36.907)
[31m2025-08-15 15:04:44.420[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 27704 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-08-15 15:04:44.418[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-08-15 15:04:44.422[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-08-15 15:04:45.772[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-08-15 15:04:45.776[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-08-15 15:04:45.824[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
[31m2025-08-15 15:04:46.043[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.043[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.043[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.043[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.043[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.043[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.045[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-15 15:04:46.210[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=c0c3359a-a69b-3a9d-a9b3-0395b257e885
[31m2025-08-15 15:04:47.648[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-15 15:04:47.682[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-15 15:04:49.147[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-08-15 15:04:49.161[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-08-15 15:04:49.161[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-08-15 15:04:49.161[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-08-15 15:04:49.238[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-08-15 15:04:49.239[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 4739 ms
[31m2025-08-15 15:04:49.722[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-08-15 15:04:50.398[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-08-15 15:04:52.072[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-08-15 15:04:54.968[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-08-15 15:04:55.507[0;39m [32m[redisson-netty-4-11][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-15 15:04:55.652[0;39m [32m[redisson-netty-4-19][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-15 15:04:57.930[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.elasticsearch.client.RestClient[0;39m - request [GET http://192.168.1.18:8199/_mapping?master_timeout=30s] returned 1 warnings: [299 Elasticsearch-7.16.0-6fc81662312141fe7691d7c1c91b8658ac17aa0d "this request accesses system indices: [.security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]
[31m2025-08-15 15:05:04.313[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentIpcEntity".
[31m2025-08-15 15:05:04.314[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentIpcEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 15:05:04.377[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis".
[31m2025-08-15 15:05:04.377[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 15:05:06.009[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.model.RDRatio".
[31m2025-08-15 15:05:06.009[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.model.RDRatio ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 15:05:12.551[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.cloud.sentinel.SentinelWebAutoConfiguration[0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[31m2025-08-15 15:05:19.233[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension".
[31m2025-08-15 15:05:19.233[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-15 15:05:19.287[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Starting ProtocolHandler ["http-nio-8804"]
[31m2025-08-15 15:05:19.324[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat started on port(s): 8804 (http) with context path '/api'
[31m2025-08-15 15:05:20.321[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.337[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.346[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.351[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.352[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.352[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.355[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.399[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.571[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.579[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.586[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: MultipartFile, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.589[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.591[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.591[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.858[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.868[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.879[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.880[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.890[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.907[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.921[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.926[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.927[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.927[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.934[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.935[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.935[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.938[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.938[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.939[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.941[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.941[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.944[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.947[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:20.948[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-15 15:05:21.251[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Started NanPingInnovationApplication in 37.496 seconds (JVM running for 38.345)
[31m2025-08-15 15:07:22.690[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[31m2025-08-15 15:07:22.690[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Initializing Servlet 'dispatcherServlet'
[31m2025-08-15 15:07:22.692[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Completed initialization in 2 ms
[31m2025-08-15 15:07:23.116[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
