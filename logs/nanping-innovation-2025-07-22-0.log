[31m2025-07-22 17:21:35.775[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-07-22 17:21:35.816[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 32288 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-07-22 17:21:35.817[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-07-22 17:21:37.961[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-07-22 17:21:37.964[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-07-22 17:21:38.032[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 50 ms. Found 0 Redis repository interfaces.
[31m2025-07-22 17:21:38.352[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.352[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.352[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.352[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.353[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.353[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.353[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.353[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.353[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.353[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.353[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.354[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.354[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.354[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.354[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.354[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.354[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.354[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.354[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.354[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.355[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.355[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.355[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.355[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.355[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.355[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.357[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.357[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.357[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.359[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.362[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.362[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-22 17:21:38.628[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=67881af0-b01b-3d7c-b70f-9dadf6306c5c
[31m2025-07-22 17:21:40.593[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-22 17:21:40.658[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-22 17:21:42.461[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-07-22 17:21:42.478[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-07-22 17:21:42.478[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-07-22 17:21:42.478[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-07-22 17:21:42.586[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-07-22 17:21:42.586[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 6593 ms
[31m2025-07-22 17:21:43.168[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-07-22 17:21:45.126[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-07-22 17:21:47.927[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-07-22 17:21:52.921[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-07-22 17:21:53.827[0;39m [32m[redisson-netty-4-10][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-22 17:21:54.179[0;39m [32m[redisson-netty-4-19][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-22 17:21:57.460[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.elasticsearch.client.RestClient[0;39m - request [GET http://192.168.1.18:8199/_mapping?master_timeout=30s] returned 1 warnings: [299 Elasticsearch-7.16.0-6fc81662312141fe7691d7c1c91b8658ac17aa0d "this request accesses system indices: [.security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]
[31m2025-07-22 17:22:10.400[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentIpcEntity".
[31m2025-07-22 17:22:10.401[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentIpcEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-22 17:22:10.601[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis".
[31m2025-07-22 17:22:10.601[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-22 17:22:13.624[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.model.RDRatio".
[31m2025-07-22 17:22:13.624[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.model.RDRatio ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-22 17:22:24.427[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.cloud.sentinel.SentinelWebAutoConfiguration[0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[31m2025-07-22 17:22:36.216[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension".
[31m2025-07-22 17:22:36.216[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-22 17:22:36.326[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Starting ProtocolHandler ["http-nio-8804"]
[31m2025-07-22 17:22:36.377[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat started on port(s): 8804 (http) with context path '/api'
[31m2025-07-22 17:22:38.234[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.263[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.276[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.283[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.284[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.284[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.286[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.361[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.713[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.721[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.729[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: MultipartFile, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.736[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.743[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:38.743[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.202[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.225[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.261[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.261[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.277[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.293[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.315[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.320[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.321[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.322[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.328[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.328[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.329[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.331[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.332[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.332[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.336[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.337[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.340[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.345[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.346[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-22 17:22:39.776[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Started NanPingInnovationApplication in 65.26 seconds (JVM running for 67.107)
[31m2025-07-22 17:23:08.102[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[31m2025-07-22 17:23:08.102[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Initializing Servlet 'dispatcherServlet'
[31m2025-07-22 17:23:08.104[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Completed initialization in 2 ms
[31m2025-07-22 17:23:40.945[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.apache.catalina.connector.Request[0;39m - Creating the temporary upload location [C:\Users\<USER>\AppData\Local\Temp\tomcat.8804.3127679443315928839\work\Tomcat\localhost\api\home\liangzhi\nanping-innovation\temp] as it is required by the servlet [dispatcherServlet]
