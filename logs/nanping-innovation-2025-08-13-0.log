[31m2025-08-13 10:54:03.071[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-08-13 10:54:03.083[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 34788 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-08-13 10:54:03.084[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-08-13 10:54:04.747[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-08-13 10:54:04.752[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-08-13 10:54:04.809[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
[31m2025-08-13 10:54:05.077[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.077[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.077[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.077[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.078[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.079[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.079[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.079[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.079[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.079[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:05.257[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=c0c3359a-a69b-3a9d-a9b3-0395b257e885
[31m2025-08-13 10:54:06.885[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-13 10:54:06.946[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-13 10:54:08.471[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-08-13 10:54:08.486[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-08-13 10:54:08.487[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-08-13 10:54:08.487[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-08-13 10:54:08.574[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-08-13 10:54:08.574[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 5390 ms
[31m2025-08-13 10:54:09.076[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-08-13 10:54:10.004[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-08-13 10:54:11.682[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-08-13 10:54:23.087[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 32260 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-08-13 10:54:23.084[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-08-13 10:54:23.089[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-08-13 10:54:24.545[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-08-13 10:54:24.549[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-08-13 10:54:24.599[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
[31m2025-08-13 10:54:24.847[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.849[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.849[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.849[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.849[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.849[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.849[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.849[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:24.849[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-13 10:54:25.026[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=c0c3359a-a69b-3a9d-a9b3-0395b257e885
[31m2025-08-13 10:54:26.472[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-13 10:54:26.513[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-13 10:54:28.005[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-08-13 10:54:28.019[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-08-13 10:54:28.019[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-08-13 10:54:28.020[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-08-13 10:54:28.093[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-08-13 10:54:28.093[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 4925 ms
[31m2025-08-13 10:54:28.611[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-08-13 10:54:29.449[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-08-13 10:54:31.196[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-08-13 10:54:34.223[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-08-13 10:54:34.823[0;39m [32m[redisson-netty-4-9][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-13 10:54:35.279[0;39m [32m[redisson-netty-4-19][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-13 10:54:37.599[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.elasticsearch.client.RestClient[0;39m - request [GET http://192.168.1.18:8199/_mapping?master_timeout=30s] returned 1 warnings: [299 Elasticsearch-7.16.0-6fc81662312141fe7691d7c1c91b8658ac17aa0d "this request accesses system indices: [.security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]
[31m2025-08-13 10:54:44.309[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentIpcEntity".
[31m2025-08-13 10:54:44.310[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentIpcEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-13 10:54:44.385[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis".
[31m2025-08-13 10:54:44.385[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-13 10:54:46.085[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.model.RDRatio".
[31m2025-08-13 10:54:46.085[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.model.RDRatio ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-13 10:54:52.966[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.cloud.sentinel.SentinelWebAutoConfiguration[0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[31m2025-08-13 10:54:59.939[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension".
[31m2025-08-13 10:54:59.939[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-13 10:54:59.992[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Starting ProtocolHandler ["http-nio-8804"]
[31m2025-08-13 10:55:00.023[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat started on port(s): 8804 (http) with context path '/api'
[31m2025-08-13 10:55:01.101[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.118[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.125[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.129[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.129[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.129[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.130[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.175[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.342[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.345[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.352[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: MultipartFile, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.356[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.359[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.359[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.655[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.671[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.685[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.686[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.700[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.717[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.730[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.735[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.736[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.737[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.742[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.743[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.744[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.748[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.749[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.752[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.753[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.758[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.761[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:01.762[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-13 10:55:02.051[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Started NanPingInnovationApplication in 39.705 seconds (JVM running for 40.528)
[31m2025-08-13 11:05:38.207[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[31m2025-08-13 11:05:38.207[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Initializing Servlet 'dispatcherServlet'
[31m2025-08-13 11:05:38.209[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Completed initialization in 2 ms
[31m2025-08-13 11:05:38.937[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-13 11:05:57.159[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-13 11:05:57.514[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.RateLimiterAspect[0;39m - 限制令牌 => 60, 剩余令牌 => 59, 缓存key => 'rate_limit:expert_query:0:0:0:0:0:0:0:1-com.quantchi.nanping.innovation.external.controller.StatisticsController-queryExperts'
[31m2025-08-13 11:05:59.324[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.RateLimiterAspect[0;39m - 限制令牌 => 60, 剩余令牌 => 59, 缓存key => 'rate_limit:knowledge_filter:0:0:0:0:0:0:0:1-com.quantchi.nanping.innovation.knowledge.center.controller.KnowledgeCenterController-queryByTermsAndKey'
[31m2025-08-13 11:06:00.473[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.knowledge.center.utils.ElasticsearchBuilder[0;39m - 分页查询参数:nanping_innovation_expert {"from":0,"size":10,"query":{"function_score":{"query":{"match_all":{"boost":1.0}},"functions":[{"filter":{"match_all":{"boost":1.0}},"field_value_factor":{"field":"field_rank","factor":1.0,"modifier":"none"}}],"score_mode":"multiply","max_boost":3.4028235E38,"boost":1.0}},"post_filter":{"bool":{"adjust_pure_negative":true,"boost":1.0}},"explain":false,"_source":{"includes":["id","logo","name","desc","final_edu_degree","orgs","organization","chain","research_fields","prof_title","product_node","match_score","score_model","labels","honors","tag","source","research_fields"],"excludes":[]},"sort":[{"_score":{"order":"desc"}},{"field_rank":{"order":"desc","missing":"_last"}}],"track_total_hits":**********}
[31m2025-08-13 11:06:00.523[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.knowledge.center.utils.ElasticsearchBuilder[0;39m - searchRequest:SearchRequest{searchType=QUERY_THEN_FETCH, indices=[nanping_innovation_expert], indicesOptions=IndicesOptions[ignore_unavailable=false, allow_no_indices=true, expand_wildcards_open=true, expand_wildcards_closed=false, expand_wildcards_hidden=false, allow_aliases_to_multiple_indices=true, forbid_closed_indices=true, ignore_aliases=false, ignore_throttled=true], types=[], routing='null', preference='null', requestCache=null, scroll=null, maxConcurrentShardRequests=0, batchedReduceSize=512, preFilterShardSize=null, allowPartialSearchResults=null, localClusterAlias=null, getOrCreateAbsoluteStartMillis=-1, ccsMinimizeRoundtrips=true, enableFieldsEmulation=false, source={"from":0,"size":10,"query":{"function_score":{"query":{"match_all":{"boost":1.0}},"functions":[{"filter":{"match_all":{"boost":1.0}},"field_value_factor":{"field":"field_rank","factor":1.0,"modifier":"none"}}],"score_mode":"multiply","max_boost":3.4028235E38,"boost":1.0}},"post_filter":{"bool":{"adjust_pure_negative":true,"boost":1.0}},"explain":false,"_source":{"includes":["id","logo","name","desc","final_edu_degree","orgs","organization","chain","research_fields","prof_title","product_node","match_score","score_model","labels","honors","tag","source","research_fields"],"excludes":[]},"sort":[{"_score":{"order":"desc"}},{"field_rank":{"order":"desc","missing":"_last"}}],"track_total_hits":**********}}
[31m2025-08-13 11:06:00.948[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:KnowledgeCenterController.queryByTermsAndKey(..),success,cost:1654ms,uri:/api/statistics/filtering
[31m2025-08-13 11:06:00.948[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:StatisticsController.queryExperts(..),success,cost:3739ms,uri:/api/statistics/filtering
[31m2025-08-13 11:07:17.297[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-13 11:07:18.060[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - countRequest:nanping_innovation_company param:{"query":{"bool":{"filter":[{"term":{"chain.id":{"value":"1002","boost":1.0}}},{"term":{"city.id":{"value":"division/*********","boost":1.0}}},{"exists":{"field":"chain.id","boost":1.0}}],"must_not":[{"terms":{"chain.id":[""],"boost":1.0}}],"adjust_pure_negative":true,"boost":1.0}}}, cost48
[31m2025-08-13 11:07:18.337[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - 聚合查询参数{"size":0,"query":{"bool":{"filter":[{"term":{"chain.id":{"value":"1002","boost":1.0}}},{"term":{"city.id":{"value":"division/*********","boost":1.0}}},{"exists":{"field":"chain_node.id","boost":1.0}}],"must_not":[{"term":{"chain_node.id":{"value":"","boost":1.0}}}],"adjust_pure_negative":true,"boost":1.0}},"track_total_hits":**********,"aggregations":{"bucket.node":{"terms":{"field":"chain_node.id","size":1000,"min_doc_count":1,"shard_min_doc_count":0,"show_term_doc_count_error":false,"order":[{"_count":"desc"},{"_key":"asc"}]}}}}
[31m2025-08-13 11:07:18.493[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_nanping_water_technology
[31m2025-08-13 11:07:18.493[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_nanping_water_technology-03
[31m2025-08-13 11:07:18.493[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_nanping_water_technology-0303
[31m2025-08-13 11:07:18.494[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_nanping_water_technology-030302
[31m2025-08-13 11:07:18.494[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - 聚合查询参数{"size":0,"query":{"bool":{"filter":[{"term":{"chain.id":{"value":"1002","boost":1.0}}},{"term":{"city.id":{"value":"division/*********","boost":1.0}}}],"adjust_pure_negative":true,"boost":1.0}},"track_total_hits":**********,"aggregations":{"bucket.tag.name":{"terms":{"field":"tag.name","size":10,"min_doc_count":1,"shard_min_doc_count":0,"show_term_doc_count_error":false,"order":[{"_count":"desc"},{"_key":"asc"}]}}}}
[31m2025-08-13 11:07:18.556[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:CockpitController.industryDetail(..),success,cost:1082ms,uri:/api/statistics/industry/company?appKey=78965432101&requestTime=1755054318776&sign=ca6de022993c4b8fa9a93110620c95d7&chainId=1002&regionId
[31m2025-08-13 11:07:18.556[0;39m [32m[http-nio-8804-exec-6][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:StatisticsController.industryDetail(..),success,cost:1083ms,uri:/api/statistics/industry/company?appKey=78965432101&requestTime=1755054318776&sign=ca6de022993c4b8fa9a93110620c95d7&chainId=1002&regionId
[31m2025-08-13 11:07:30.630[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-13 11:07:31.222[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - countRequest:nanping_innovation_company param:{"query":{"bool":{"filter":[{"term":{"chain.id":{"value":"1001","boost":1.0}}},{"term":{"city.id":{"value":"division/*********","boost":1.0}}},{"exists":{"field":"chain.id","boost":1.0}}],"must_not":[{"terms":{"chain.id":[""],"boost":1.0}}],"adjust_pure_negative":true,"boost":1.0}}}, cost22
[31m2025-08-13 11:07:31.224[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - 聚合查询参数{"size":0,"query":{"bool":{"filter":[{"term":{"chain.id":{"value":"1001","boost":1.0}}},{"term":{"city.id":{"value":"division/*********","boost":1.0}}},{"exists":{"field":"chain_node.id","boost":1.0}}],"must_not":[{"term":{"chain_node.id":{"value":"","boost":1.0}}}],"adjust_pure_negative":true,"boost":1.0}},"track_total_hits":**********,"aggregations":{"bucket.node":{"terms":{"field":"chain_node.id","size":1000,"min_doc_count":1,"shard_min_doc_count":0,"show_term_doc_count_error":false,"order":[{"_count":"desc"},{"_key":"asc"}]}}}}
[31m2025-08-13 11:07:31.286[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_solid_battery_industry
[31m2025-08-13 11:07:31.286[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_solid_battery_industry-01
[31m2025-08-13 11:07:31.287[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_solid_battery_industry-0102
[31m2025-08-13 11:07:31.287[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_solid_battery_industry-010203
[31m2025-08-13 11:07:31.287[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_solid_battery_industry-01020301
[31m2025-08-13 11:07:31.287[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_solid_battery_industry-010205
[31m2025-08-13 11:07:31.287[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_solid_battery_industry-01020502
[31m2025-08-13 11:07:31.287[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.service.library.impl.CompanyServiceImpl[0;39m - 非本产业链节点：instance_concept_node_solid_battery_industry-01020501
[31m2025-08-13 11:07:31.288[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - 聚合查询参数{"size":0,"query":{"bool":{"filter":[{"term":{"chain.id":{"value":"1001","boost":1.0}}},{"term":{"city.id":{"value":"division/*********","boost":1.0}}}],"adjust_pure_negative":true,"boost":1.0}},"track_total_hits":**********,"aggregations":{"bucket.tag.name":{"terms":{"field":"tag.name","size":10,"min_doc_count":1,"shard_min_doc_count":0,"show_term_doc_count_error":false,"order":[{"_count":"desc"},{"_key":"asc"}]}}}}
[31m2025-08-13 11:07:31.354[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:CockpitController.industryDetail(..),success,cost:575ms,uri:/api/statistics/industry/company?appKey=78965432101&requestTime=1755054318776&sign=ca6de022993c4b8fa9a93110620c95d7&chainId=1001&regionId
[31m2025-08-13 11:07:31.354[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:StatisticsController.industryDetail(..),success,cost:575ms,uri:/api/statistics/industry/company?appKey=78965432101&requestTime=1755054318776&sign=ca6de022993c4b8fa9a93110620c95d7&chainId=1001&regionId
[31m2025-08-13 11:10:09.116[0;39m [32m[http-nio-8804-exec-10][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-13 11:10:09.337[0;39m [32m[http-nio-8804-exec-10][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:StatisticsController.industryDetail(..),fail,cost:5ms,uri:/api/statistics/industry/company?appKey=**********&requestTime=1755054318776&sign=ca6de022993c4b8fa9a93110620c95d7&chainId=1001&regionId
[31m2025-08-13 11:10:09.354[0;39m [32m[http-nio-8804-exec-10][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - StatisticsController.industryDetail(..)
com.quantchi.nanping.innovation.common.exception.MessageException: null
	at com.quantchi.nanping.innovation.external.controller.StatisticsController.calibratePermission(StatisticsController.java:84)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController.industryDetail(StatisticsController.java:60)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController$$FastClassBySpringCGLIB$$14ec7a96.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect.metrics(MetricsAspect.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController$$EnhancerBySpringCGLIB$$f8e231e6.industryDetail(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.quantchi.nanping.innovation.config.filter.CorsFilter.doFilter(CorsFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-13 11:10:26.076[0;39m [32m[http-nio-8804-exec-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-13 11:10:26.227[0;39m [32m[http-nio-8804-exec-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:StatisticsController.industryDetail(..),fail,cost:1ms,uri:/api/statistics/industry/company?appKey=**********&requestTime=175554318776&sign=ca6de022993c4b8fa9a93110620c95d7&chainId=1001&regionId
[31m2025-08-13 11:10:26.227[0;39m [32m[http-nio-8804-exec-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - StatisticsController.industryDetail(..)
com.quantchi.nanping.innovation.common.exception.MessageException: null
	at com.quantchi.nanping.innovation.external.controller.StatisticsController.calibratePermission(StatisticsController.java:84)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController.industryDetail(StatisticsController.java:60)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController$$FastClassBySpringCGLIB$$14ec7a96.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect.metrics(MetricsAspect.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController$$EnhancerBySpringCGLIB$$f8e231e6.industryDetail(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.quantchi.nanping.innovation.config.filter.CorsFilter.doFilter(CorsFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-13 11:10:32.155[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-13 11:10:32.380[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:StatisticsController.industryDetail(..),fail,cost:0ms,uri:/api/statistics/industry/company?appKey=78965432101&requestTime=175554318776&sign=ca6de022993c4b8fa9a93110620c95d7&chainId=1001&regionId
[31m2025-08-13 11:10:32.381[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - StatisticsController.industryDetail(..)
com.quantchi.nanping.innovation.common.exception.MessageException: null
	at com.quantchi.nanping.innovation.external.controller.StatisticsController.calibratePermission(StatisticsController.java:89)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController.industryDetail(StatisticsController.java:60)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController$$FastClassBySpringCGLIB$$14ec7a96.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect.metrics(MetricsAspect.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController$$EnhancerBySpringCGLIB$$f8e231e6.industryDetail(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.quantchi.nanping.innovation.config.filter.CorsFilter.doFilter(CorsFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-13 11:10:52.815[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-13 11:10:53.044[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:StatisticsController.industryDetail(..),fail,cost:2ms,uri:/api/statistics/industry/company?appKey=78965432101&requestTime=1755054318776&sign=ca6de02293c4b8fa9a93110620c95d7&chainId=1001&regionId
[31m2025-08-13 11:10:53.044[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - StatisticsController.industryDetail(..)
com.quantchi.nanping.innovation.common.exception.MessageException: null
	at com.quantchi.nanping.innovation.external.controller.StatisticsController.calibratePermission(StatisticsController.java:99)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController.industryDetail(StatisticsController.java:60)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController$$FastClassBySpringCGLIB$$14ec7a96.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect.metrics(MetricsAspect.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.quantchi.nanping.innovation.external.controller.StatisticsController$$EnhancerBySpringCGLIB$$f8e231e6.industryDetail(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.quantchi.nanping.innovation.config.filter.CorsFilter.doFilter(CorsFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
