[31m2025-07-14 15:23:41.218[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 27356 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-07-14 15:23:41.216[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-07-14 15:23:41.222[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-07-14 15:23:42.858[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-07-14 15:23:42.863[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-07-14 15:23:42.917[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
[31m2025-07-14 15:23:43.239[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.239[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.239[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.239[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.239[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.239[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.239[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.242[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.242[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:43.447[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=67881af0-b01b-3d7c-b70f-9dadf6306c5c
[31m2025-07-14 15:23:45.058[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-14 15:23:45.102[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-14 15:23:46.524[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-07-14 15:23:46.538[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-07-14 15:23:46.539[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-07-14 15:23:46.539[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-07-14 15:23:46.627[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-07-14 15:23:46.628[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 5292 ms
[31m2025-07-14 15:23:47.042[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-07-14 15:23:47.671[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-07-14 15:23:49.361[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-07-14 15:23:52.045[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-07-14 15:23:52.540[0;39m [32m[redisson-netty-4-9][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-14 15:23:52.679[0;39m [32m[redisson-netty-4-19][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-14 15:23:57.938[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 32752 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-07-14 15:23:57.936[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-07-14 15:23:57.939[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-07-14 15:23:59.231[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-07-14 15:23:59.235[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-07-14 15:23:59.282[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
[31m2025-07-14 15:23:59.479[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.480[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.481[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:23:59.646[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=67881af0-b01b-3d7c-b70f-9dadf6306c5c
[31m2025-07-14 15:24:01.107[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-14 15:24:01.158[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-14 15:24:02.468[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-07-14 15:24:02.482[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-07-14 15:24:02.482[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-07-14 15:24:02.483[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-07-14 15:24:02.560[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-07-14 15:24:02.560[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 4551 ms
[31m2025-07-14 15:24:03.065[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-07-14 15:24:03.815[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-07-14 15:24:05.497[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-07-14 15:24:08.032[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-07-14 15:24:08.529[0;39m [32m[redisson-netty-4-9][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-14 15:24:08.742[0;39m [32m[redisson-netty-4-19][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-14 15:24:10.998[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.elasticsearch.client.RestClient[0;39m - request [GET http://192.168.1.18:8199/_mapping?master_timeout=30s] returned 1 warnings: [299 Elasticsearch-7.16.0-6fc81662312141fe7691d7c1c91b8658ac17aa0d "this request accesses system indices: [.security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]
[31m2025-07-14 15:24:17.181[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentIpcEntity".
[31m2025-07-14 15:24:17.182[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentIpcEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:24:17.249[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis".
[31m2025-07-14 15:24:17.249[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:24:18.897[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.model.RDRatio".
[31m2025-07-14 15:24:18.897[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.model.RDRatio ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:24:25.305[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.cloud.sentinel.SentinelWebAutoConfiguration[0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[31m2025-07-14 15:24:31.881[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension".
[31m2025-07-14 15:24:31.882[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:24:31.939[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Starting ProtocolHandler ["http-nio-8804"]
[31m2025-07-14 15:24:31.969[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat started on port(s): 8804 (http) with context path '/api'
[31m2025-07-14 15:24:32.821[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:32.836[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:32.843[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:32.847[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:32.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:32.848[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:32.849[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:32.892[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.052[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.057[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.065[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: MultipartFile, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.070[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.074[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.074[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.346[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.359[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.369[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.370[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.378[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.388[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.397[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.400[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.401[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.401[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.405[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.406[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.406[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.408[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.409[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.409[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.411[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.412[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.414[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.419[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.420[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:24:33.646[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Started NanPingInnovationApplication in 36.29 seconds (JVM running for 37.032)
[31m2025-07-14 15:38:31.575[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[31m2025-07-14 15:38:31.575[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Initializing Servlet 'dispatcherServlet'
[31m2025-07-14 15:38:31.577[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Completed initialization in 2 ms
[31m2025-07-14 15:38:32.072[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-07-14 15:38:33.732[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-07-14 15:38:43.977[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-07-14 15:38:44.546[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.RateLimiterAspect[0;39m - 限制令牌 => 20, 剩余令牌 => 19, 缓存key => 'rate_limit:chat:0:0:0:0:0:0:0:1-com.quantchi.nanping.innovation.company.controller.ChatController-match'
[31m2025-07-14 15:38:44.951[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - http://*************/v1/chat-messages 请求出错!响应码：401
[31m2025-07-14 15:38:45.267[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:ChatController.match(..),fail,cost:926ms,uri:/api/chat/expert/start_match
[31m2025-07-14 15:38:45.278[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - ChatController.match(..)
java.lang.NullPointerException: null
	at com.quantchi.nanping.innovation.component.LocalModelComponent.requestChat(LocalModelComponent.java:74)
	at com.quantchi.nanping.innovation.company.controller.ChatController.match(ChatController.java:271)
	at com.quantchi.nanping.innovation.company.controller.ChatController$$FastClassBySpringCGLIB$$8d0e782d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect.metrics(MetricsAspect.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.quantchi.nanping.innovation.company.controller.ChatController$$EnhancerBySpringCGLIB$$d4498995.match(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.quantchi.nanping.innovation.config.filter.CorsFilter.doFilter(CorsFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-07-14 15:39:41.694[0;39m [32m[SpringApplicationShutdownHook][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} closing ...
[31m2025-07-14 15:39:41.700[0;39m [32m[SpringApplicationShutdownHook][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} closed
[31m2025-07-14 15:39:45.768[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-07-14 15:39:45.775[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 4208 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-07-14 15:39:45.775[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-07-14 15:39:47.258[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-07-14 15:39:47.261[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-07-14 15:39:47.311[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.
[31m2025-07-14 15:39:47.565[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.565[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.565[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.565[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.565[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.565[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.565[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.566[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.567[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:39:47.736[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=67881af0-b01b-3d7c-b70f-9dadf6306c5c
[31m2025-07-14 15:39:49.287[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-14 15:39:49.360[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-14 15:39:50.790[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-07-14 15:39:50.806[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-07-14 15:39:50.806[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-07-14 15:39:50.806[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-07-14 15:39:50.893[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-07-14 15:39:50.893[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 5035 ms
[31m2025-07-14 15:39:51.470[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-07-14 15:39:52.093[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-07-14 15:39:53.840[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-07-14 15:39:56.576[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-07-14 15:39:57.092[0;39m [32m[redisson-netty-4-9][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-14 15:39:57.234[0;39m [32m[redisson-netty-4-19][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-14 15:39:59.393[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.elasticsearch.client.RestClient[0;39m - request [GET http://192.168.1.18:8199/_mapping?master_timeout=30s] returned 1 warnings: [299 Elasticsearch-7.16.0-6fc81662312141fe7691d7c1c91b8658ac17aa0d "this request accesses system indices: [.security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]
[31m2025-07-14 15:40:05.596[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentIpcEntity".
[31m2025-07-14 15:40:05.596[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentIpcEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:40:05.661[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis".
[31m2025-07-14 15:40:05.661[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:40:07.310[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.model.RDRatio".
[31m2025-07-14 15:40:07.310[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.model.RDRatio ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:40:13.887[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.cloud.sentinel.SentinelWebAutoConfiguration[0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[31m2025-07-14 15:40:20.359[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension".
[31m2025-07-14 15:40:20.359[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:40:20.406[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Starting ProtocolHandler ["http-nio-8804"]
[31m2025-07-14 15:40:20.432[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat started on port(s): 8804 (http) with context path '/api'
[31m2025-07-14 15:40:21.297[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.311[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.318[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.323[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.323[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.324[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.325[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.368[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.527[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.532[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.537[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: MultipartFile, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.541[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.545[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.545[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.841[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.853[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.864[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.864[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.874[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.884[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.892[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.894[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.895[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.895[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.898[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.899[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.899[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.901[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.901[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.901[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.904[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.904[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.906[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.907[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:21.908[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:40:22.116[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Started NanPingInnovationApplication in 37.267 seconds (JVM running for 38.712)
[31m2025-07-14 15:40:37.208[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[31m2025-07-14 15:40:37.209[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Initializing Servlet 'dispatcherServlet'
[31m2025-07-14 15:40:37.210[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Completed initialization in 1 ms
[31m2025-07-14 15:40:38.789[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-07-14 15:40:39.664[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.RateLimiterAspect[0;39m - 限制令牌 => 20, 剩余令牌 => 19, 缓存key => 'rate_limit:chat:0:0:0:0:0:0:0:1-com.quantchi.nanping.innovation.company.controller.ChatController-match'
[31m2025-07-14 15:40:40.025[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - http://*************/v1/chat-messages 请求出错!响应码：401
[31m2025-07-14 15:40:40.366[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:ChatController.match(..),fail,cost:909ms,uri:/api/chat/expert/start_match
[31m2025-07-14 15:40:40.375[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - ChatController.match(..)
java.lang.NullPointerException: null
	at com.quantchi.nanping.innovation.component.LocalModelComponent.requestChat(LocalModelComponent.java:74)
	at com.quantchi.nanping.innovation.company.controller.ChatController.match(ChatController.java:271)
	at com.quantchi.nanping.innovation.company.controller.ChatController$$FastClassBySpringCGLIB$$8d0e782d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect.metrics(MetricsAspect.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.quantchi.nanping.innovation.company.controller.ChatController$$EnhancerBySpringCGLIB$$3e12cb1.match(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.quantchi.nanping.innovation.config.filter.CorsFilter.doFilter(CorsFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-07-14 15:41:24.548[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-07-14 15:41:24.640[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.RateLimiterAspect[0;39m - 限制令牌 => 20, 剩余令牌 => 18, 缓存key => 'rate_limit:chat:0:0:0:0:0:0:0:1-com.quantchi.nanping.innovation.company.controller.ChatController-match'
[31m2025-07-14 15:41:25.075[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - http://*************/v1/chat-messages 请求出错!响应码：401
[31m2025-07-14 15:43:11.425[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:ChatController.match(..),fail,cost:106814ms,uri:/api/chat/expert/start_match
[31m2025-07-14 15:43:11.426[0;39m [32m[http-nio-8804-exec-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - ChatController.match(..)
java.lang.NullPointerException: null
	at com.quantchi.nanping.innovation.component.LocalModelComponent.requestChat(LocalModelComponent.java:74)
	at com.quantchi.nanping.innovation.company.controller.ChatController.match(ChatController.java:271)
	at com.quantchi.nanping.innovation.company.controller.ChatController$$FastClassBySpringCGLIB$$8d0e782d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect.metrics(MetricsAspect.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.quantchi.nanping.innovation.company.controller.ChatController$$EnhancerBySpringCGLIB$$3e12cb1.match(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.quantchi.nanping.innovation.config.filter.CorsFilter.doFilter(CorsFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-07-14 15:43:12.099[0;39m [32m[SpringApplicationShutdownHook][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} closing ...
[31m2025-07-14 15:43:12.110[0;39m [32m[SpringApplicationShutdownHook][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} closed
[31m2025-07-14 15:43:15.271[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-07-14 15:43:15.277[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 30560 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-07-14 15:43:15.278[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-07-14 15:43:16.499[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-07-14 15:43:16.502[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-07-14 15:43:16.545[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
[31m2025-07-14 15:43:16.746[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.746[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.746[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.747[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.748[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.748[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.748[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.748[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.748[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.748[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.748[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.748[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-07-14 15:43:16.904[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=67881af0-b01b-3d7c-b70f-9dadf6306c5c
[31m2025-07-14 15:43:18.161[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-14 15:43:18.214[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-07-14 15:43:19.465[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-07-14 15:43:19.482[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-07-14 15:43:19.483[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-07-14 15:43:19.483[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-07-14 15:43:19.584[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-07-14 15:43:19.584[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 4224 ms
[31m2025-07-14 15:43:20.045[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-07-14 15:43:20.728[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-07-14 15:43:22.376[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-07-14 15:43:24.689[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-07-14 15:43:25.127[0;39m [32m[redisson-netty-4-9][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-14 15:43:25.267[0;39m [32m[redisson-netty-4-19][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-07-14 15:43:27.234[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.elasticsearch.client.RestClient[0;39m - request [GET http://192.168.1.18:8199/_mapping?master_timeout=30s] returned 1 warnings: [299 Elasticsearch-7.16.0-6fc81662312141fe7691d7c1c91b8658ac17aa0d "this request accesses system indices: [.security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]
[31m2025-07-14 15:43:32.280[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentIpcEntity".
[31m2025-07-14 15:43:32.280[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentIpcEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:43:32.330[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis".
[31m2025-07-14 15:43:32.330[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:43:33.590[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.model.RDRatio".
[31m2025-07-14 15:43:33.591[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.model.RDRatio ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:43:38.928[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.cloud.sentinel.SentinelWebAutoConfiguration[0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[31m2025-07-14 15:43:44.855[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension".
[31m2025-07-14 15:43:44.855[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-07-14 15:43:44.904[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Starting ProtocolHandler ["http-nio-8804"]
[31m2025-07-14 15:43:44.930[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat started on port(s): 8804 (http) with context path '/api'
[31m2025-07-14 15:43:45.804[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:45.818[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:45.825[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:45.828[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:45.828[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:45.829[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:45.831[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:45.870[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.028[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.032[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.040[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: MultipartFile, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.044[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.047[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.048[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.330[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.341[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.351[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.351[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.364[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.380[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.391[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.394[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.395[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.395[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.400[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.401[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.402[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.404[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.405[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.405[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.407[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.407[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.409[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.411[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.412[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-07-14 15:43:46.646[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Started NanPingInnovationApplication in 32.026 seconds (JVM running for 32.916)
[31m2025-07-14 15:44:56.389[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[31m2025-07-14 15:44:56.389[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Initializing Servlet 'dispatcherServlet'
[31m2025-07-14 15:44:56.390[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Completed initialization in 1 ms
[31m2025-07-14 15:44:58.002[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-07-14 15:44:58.940[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.RateLimiterAspect[0;39m - 限制令牌 => 20, 剩余令牌 => 19, 缓存key => 'rate_limit:chat:0:0:0:0:0:0:0:1-com.quantchi.nanping.innovation.company.controller.ChatController-match'
[31m2025-07-14 15:45:39.816[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 技术分析：### 技术点1：竹环保包装固化定型  
**技术分析：** -竹环保包装固化定型技术涉及竹材预处理、胶黏剂选择及固化工艺优化，旨在提升包装材料的强度、耐久性和环保性能。  

### 技术点2：竹材预处理技术  
**技术分析：** -通过物理或化学方法（如蒸汽爆破、碱处理）改善竹纤维的分散性和结合力，为后续固化定型提供均匀稳定的基材。  

### 技术点3：环保胶黏剂开发  
**技术分析：** -研发基于生物质（如淀粉、木质素）或低甲醛的胶黏剂，确保竹包装材料在固化过程中无毒、可降解，符合环保标准。  

### 技术点4：固化工艺优化  
**技术分析：** -调控温度、压力及时间等参数，实现竹纤维与胶黏剂的高效结合，平衡定型效率与能耗，同时保证材料力学性能与尺寸稳定性。
[31m2025-07-14 15:45:40.123[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:ChatController.match(..),success,cost:41395ms,uri:/api/chat/expert/start_match
[31m2025-07-14 15:45:55.385[0;39m [32m[task-4][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - 请求地址：http://192.168.1.33:8404/model/team_ai_pool，请求参数：{"point":"固化工艺优化","pointAnalysis":"调控温度、压力及时间等参数，实现竹纤维与胶黏剂的高效结合，平衡定型效率与能耗，同时保证材料力学性能与尺寸稳定性。","pointId":"point_77fc7b51-3d7b-4cc6-813a-40047990ba29"}，返回结果：{
  "result": [
    {
      "id": "instance_entity_expert-ec3d5cafcab982b412d6e8c8e791b767", 
      "match_score": {
        "achievement": 51.59, 
        "cooperation": 60.25, 
        "final_score": 65.88, 
        "influence": 56.31, 
        "matched": 85.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-c404921c127998b45f5887aa6e64155b", 
      "match_score": {
        "achievement": 50.81, 
        "cooperation": 59.67, 
        "final_score": 64.69, 
        "influence": 55.64, 
        "matched": 80.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-eae3f2e09ccf12f10e32548220e8b4e5", 
      "match_score": {
        "achievement": 52.18, 
        "cooperation": 58.64, 
        "final_score": 63.21, 
        "influence": 56.48, 
        "matched": 75.0, 
        "performance": 73.75
      }
    }
  ]
}

[31m2025-07-14 15:45:55.386[0;39m [32m[task-4][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 发起人才匹配：{
  "result": [
    {
      "id": "instance_entity_expert-ec3d5cafcab982b412d6e8c8e791b767", 
      "match_score": {
        "achievement": 51.59, 
        "cooperation": 60.25, 
        "final_score": 65.88, 
        "influence": 56.31, 
        "matched": 85.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-c404921c127998b45f5887aa6e64155b", 
      "match_score": {
        "achievement": 50.81, 
        "cooperation": 59.67, 
        "final_score": 64.69, 
        "influence": 55.64, 
        "matched": 80.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-eae3f2e09ccf12f10e32548220e8b4e5", 
      "match_score": {
        "achievement": 52.18, 
        "cooperation": 58.64, 
        "final_score": 63.21, 
        "influence": 56.48, 
        "matched": 75.0, 
        "performance": 73.75
      }
    }
  ]
}

[31m2025-07-14 15:46:00.632[0;39m [32m[task-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - 请求地址：http://192.168.1.33:8404/model/team_ai_pool，请求参数：{"point":"竹材预处理技术","pointAnalysis":"通过物理或化学方法（如蒸汽爆破、碱处理）改善竹纤维的分散性和结合力，为后续固化定型提供均匀稳定的基材。","pointId":"point_24be461e-772d-4f9c-b461-236c1893e0c1"}，返回结果：{
  "result": [
    {
      "id": "instance_entity_expert-9f9c5c9fbbbbc2b82aac7e5f68b69ef7", 
      "match_score": {
        "achievement": 51.39, 
        "cooperation": 59.65, 
        "final_score": 65.4, 
        "influence": 57.22, 
        "matched": 85.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-fd94fda826935e00281787e2d6420596", 
      "match_score": {
        "achievement": 51.76, 
        "cooperation": 58.64, 
        "final_score": 64.9, 
        "influence": 56.74, 
        "matched": 80.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-1c34cb48b13a286f8f0bcbfb0f5fe210", 
      "match_score": {
        "achievement": 50.67, 
        "cooperation": 58.94, 
        "final_score": 63.51, 
        "influence": 56.67, 
        "matched": 75.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-bf8c4d21ad33fe46e616f96b473b7ab5", 
      "match_score": {
        "achievement": 51.11, 
        "cooperation": 59.18, 
        "final_score": 62.07, 
        "influence": 56.29, 
        "matched": 70.0, 
        "performance": 73.75
      }
    }
  ]
}

[31m2025-07-14 15:46:00.633[0;39m [32m[task-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 发起人才匹配：{
  "result": [
    {
      "id": "instance_entity_expert-9f9c5c9fbbbbc2b82aac7e5f68b69ef7", 
      "match_score": {
        "achievement": 51.39, 
        "cooperation": 59.65, 
        "final_score": 65.4, 
        "influence": 57.22, 
        "matched": 85.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-fd94fda826935e00281787e2d6420596", 
      "match_score": {
        "achievement": 51.76, 
        "cooperation": 58.64, 
        "final_score": 64.9, 
        "influence": 56.74, 
        "matched": 80.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-1c34cb48b13a286f8f0bcbfb0f5fe210", 
      "match_score": {
        "achievement": 50.67, 
        "cooperation": 58.94, 
        "final_score": 63.51, 
        "influence": 56.67, 
        "matched": 75.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-bf8c4d21ad33fe46e616f96b473b7ab5", 
      "match_score": {
        "achievement": 51.11, 
        "cooperation": 59.18, 
        "final_score": 62.07, 
        "influence": 56.29, 
        "matched": 70.0, 
        "performance": 73.75
      }
    }
  ]
}

[31m2025-07-14 15:46:02.351[0;39m [32m[task-3][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - 请求地址：http://192.168.1.33:8404/model/team_ai_pool，请求参数：{"point":"环保胶黏剂开发","pointAnalysis":"研发基于生物质（如淀粉、木质素）或低甲醛的胶黏剂，确保竹包装材料在固化过程中无毒、可降解，符合环保标准。","pointId":"point_88cc5f33-b907-4720-945b-d62e4c515ef6"}，返回结果：{
  "result": [
    {
      "id": "instance_entity_expert-65d923f416a6f8275a7a481995de920f", 
      "match_score": {
        "achievement": 50.87, 
        "cooperation": 67.79, 
        "final_score": 67.49, 
        "influence": 55.03, 
        "matched": 90.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-96c7f2196a65c2b7061eeed9000a5314", 
      "match_score": {
        "achievement": 52.13, 
        "cooperation": 60.28, 
        "final_score": 64.63, 
        "influence": 57.0, 
        "matched": 80.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-0e8c8c2ab5c2b883a7e19febac094699", 
      "match_score": {
        "achievement": 50.27, 
        "cooperation": 58.4, 
        "final_score": 63.53, 
        "influence": 56.35, 
        "matched": 75.0, 
        "performance": 77.61
      }
    }, 
    {
      "id": "instance_entity_expert-b16fda14c8de561a3245eb1ca623cb52", 
      "match_score": {
        "achievement": 51.03, 
        "cooperation": 59.24, 
        "final_score": 61.59, 
        "influence": 56.44, 
        "matched": 70.0, 
        "performance": 71.25
      }
    }
  ]
}

[31m2025-07-14 15:46:02.351[0;39m [32m[task-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 发起人才匹配：{
  "result": [
    {
      "id": "instance_entity_expert-65d923f416a6f8275a7a481995de920f", 
      "match_score": {
        "achievement": 50.87, 
        "cooperation": 67.79, 
        "final_score": 67.49, 
        "influence": 55.03, 
        "matched": 90.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-96c7f2196a65c2b7061eeed9000a5314", 
      "match_score": {
        "achievement": 52.13, 
        "cooperation": 60.28, 
        "final_score": 64.63, 
        "influence": 57.0, 
        "matched": 80.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-0e8c8c2ab5c2b883a7e19febac094699", 
      "match_score": {
        "achievement": 50.27, 
        "cooperation": 58.4, 
        "final_score": 63.53, 
        "influence": 56.35, 
        "matched": 75.0, 
        "performance": 77.61
      }
    }, 
    {
      "id": "instance_entity_expert-b16fda14c8de561a3245eb1ca623cb52", 
      "match_score": {
        "achievement": 51.03, 
        "cooperation": 59.24, 
        "final_score": 61.59, 
        "influence": 56.44, 
        "matched": 70.0, 
        "performance": 71.25
      }
    }
  ]
}

[31m2025-07-14 15:46:10.756[0;39m [32m[task-1][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - 请求地址：http://192.168.1.33:8404/model/team_ai_pool，请求参数：{"point":"我要找竹环保新型包装材料固化定型技术领域的人才","pointId":"point_d7b4f94b-3050-41a4-a44a-5dcceda615ba"}，返回结果：{
  "result": [
    {
      "id": "instance_entity_expert-06c97edb6376ac162a14e128f3204d2b", 
      "match_score": {
        "achievement": 52.94, 
        "cooperation": 60.97, 
        "final_score": 67.83, 
        "influence": 58.97, 
        "matched": 90.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-8ff2d15e1993744a51fc439f98b447c8", 
      "match_score": {
        "achievement": 51.39, 
        "cooperation": 58.95, 
        "final_score": 65.58, 
        "influence": 56.32, 
        "matched": 85.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-b49045fa011d97c51f73697b071c0a4c", 
      "match_score": {
        "achievement": 55.09, 
        "cooperation": 61.92, 
        "final_score": 67.35, 
        "influence": 62.42, 
        "matched": 80.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-2190e49629db37550bdd948d404eb5a0", 
      "match_score": {
        "achievement": 50.69, 
        "cooperation": 59.04, 
        "final_score": 63.45, 
        "influence": 56.28, 
        "matched": 75.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-3021b3870a765e996292ee9b5fe5c495", 
      "match_score": {
        "achievement": 51.76, 
        "cooperation": 59.93, 
        "final_score": 62.43, 
        "influence": 56.73, 
        "matched": 70.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-6327a95317a8578511c2755ef2f998f2", 
      "match_score": {
        "achievement": 50.53, 
        "cooperation": 57.46, 
        "final_score": 61.15, 
        "influence": 56.52, 
        "matched": 65.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-f1df4472717348c9001da83f5f00cd12", 
      "match_score": {
        "achievement": 50.75, 
        "cooperation": 60.94, 
        "final_score": 60.63, 
        "influence": 55.19, 
        "matched": 60.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-4fdb28ac580e1a5849781b46cdd6b04a", 
      "match_score": {
        "achievement": 50.41, 
        "cooperation": 59.19, 
        "final_score": 58.59, 
        "influence": 55.99, 
        "matched": 55.0, 
        "performance": 72.34
      }
    }, 
    {
      "id": "instance_entity_expert-bd01e46529371c8d8ec507b20b1636a4", 
      "match_score": {
        "achievement": 50.83, 
        "cooperation": 59.24, 
        "final_score": 58.63, 
        "influence": 56.83, 
        "matched": 50.0, 
        "performance": 76.25
      }
    }
  ]
}

[31m2025-07-14 15:46:10.756[0;39m [32m[task-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 发起人才匹配：{
  "result": [
    {
      "id": "instance_entity_expert-06c97edb6376ac162a14e128f3204d2b", 
      "match_score": {
        "achievement": 52.94, 
        "cooperation": 60.97, 
        "final_score": 67.83, 
        "influence": 58.97, 
        "matched": 90.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-8ff2d15e1993744a51fc439f98b447c8", 
      "match_score": {
        "achievement": 51.39, 
        "cooperation": 58.95, 
        "final_score": 65.58, 
        "influence": 56.32, 
        "matched": 85.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-b49045fa011d97c51f73697b071c0a4c", 
      "match_score": {
        "achievement": 55.09, 
        "cooperation": 61.92, 
        "final_score": 67.35, 
        "influence": 62.42, 
        "matched": 80.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-2190e49629db37550bdd948d404eb5a0", 
      "match_score": {
        "achievement": 50.69, 
        "cooperation": 59.04, 
        "final_score": 63.45, 
        "influence": 56.28, 
        "matched": 75.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-3021b3870a765e996292ee9b5fe5c495", 
      "match_score": {
        "achievement": 51.76, 
        "cooperation": 59.93, 
        "final_score": 62.43, 
        "influence": 56.73, 
        "matched": 70.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-6327a95317a8578511c2755ef2f998f2", 
      "match_score": {
        "achievement": 50.53, 
        "cooperation": 57.46, 
        "final_score": 61.15, 
        "influence": 56.52, 
        "matched": 65.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-f1df4472717348c9001da83f5f00cd12", 
      "match_score": {
        "achievement": 50.75, 
        "cooperation": 60.94, 
        "final_score": 60.63, 
        "influence": 55.19, 
        "matched": 60.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-4fdb28ac580e1a5849781b46cdd6b04a", 
      "match_score": {
        "achievement": 50.41, 
        "cooperation": 59.19, 
        "final_score": 58.59, 
        "influence": 55.99, 
        "matched": 55.0, 
        "performance": 72.34
      }
    }, 
    {
      "id": "instance_entity_expert-bd01e46529371c8d8ec507b20b1636a4", 
      "match_score": {
        "achievement": 50.83, 
        "cooperation": 59.24, 
        "final_score": 58.63, 
        "influence": 56.83, 
        "matched": 50.0, 
        "performance": 76.25
      }
    }
  ]
}

[31m2025-07-14 15:47:50.821[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-07-14 15:47:51.878[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - pageByFields index:nanping_innovation_expert param:{"query":{"ids":{"values":["instance_entity_expert-f1df4472717348c9001da83f5f00cd12","instance_entity_expert-bd01e46529371c8d8ec507b20b1636a4","instance_entity_expert-06c97edb6376ac162a14e128f3204d2b","instance_entity_expert-8ff2d15e1993744a51fc439f98b447c8","instance_entity_expert-2190e49629db37550bdd948d404eb5a0","instance_entity_expert-4fdb28ac580e1a5849781b46cdd6b04a","instance_entity_expert-3021b3870a765e996292ee9b5fe5c495","instance_entity_expert-b49045fa011d97c51f73697b071c0a4c","instance_entity_expert-6327a95317a8578511c2755ef2f998f2"],"boost":1.0}},"_source":{"includes":["id","name","logo","chain","chain_node","product_node","desc","org","title","final_edu_degree","prof_title"],"excludes":[]}} cost:129
[31m2025-07-14 15:47:52.020[0;39m [32m[http-nio-8804-exec-7][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:ChatController.match(..),success,cost:1082ms,uri:/api/chat/expert/match?authorization=be3505d93fc341829d88a257596fe72b
[31m2025-07-14 16:28:29.698[0;39m [32m[http-nio-8804-exec-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-07-14 16:28:29.819[0;39m [32m[http-nio-8804-exec-1][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.RateLimiterAspect[0;39m - 限制令牌 => 20, 剩余令牌 => 19, 缓存key => 'rate_limit:chat:0:0:0:0:0:0:0:1-com.quantchi.nanping.innovation.company.controller.ChatController-match'
[31m2025-07-14 16:28:40.522[0;39m [32m[http-nio-8804-exec-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 技术分析：### 技术点1：竹包装材料固化技术  
**技术分析：** -竹环保新型包装材料的固化定型技术涉及物理/化学方法处理竹纤维，以增强其稳定性、强度和耐用性，满足包装需求。  

### 技术点2：生物基粘合剂开发  
**技术分析：** -开发环保型生物基粘合剂是实现竹纤维高效固化的关键，需兼顾粘接强度、可降解性及低成本工艺。  

### 技术点3：热压成型工艺优化  
**技术分析：** -通过热压成型工艺参数（温度、压力、时间）的优化，可提升竹纤维材料的密实度与形状保持性，降低能耗。  

### 技术点4：竹纤维改性技术  
**技术分析：** -采用化学或物理改性技术（如碱处理、乙酰化）改善竹纤维的疏水性、抗菌性等性能，扩展包装应用场景。
[31m2025-07-14 16:28:40.609[0;39m [32m[http-nio-8804-exec-1][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.config.aop.aspect.MetricsAspect[0;39m - method:ChatController.match(..),success,cost:10822ms,uri:/api/chat/expert/start_match
[31m2025-07-14 16:28:56.512[0;39m [32m[task-8][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - 请求地址：http://192.168.1.33:8404/model/team_ai_pool，请求参数：{"point":"生物基粘合剂开发","pointAnalysis":"开发环保型生物基粘合剂是实现竹纤维高效固化的关键，需兼顾粘接强度、可降解性及低成本工艺。","pointId":"point_29c1ce39-d488-4e4b-8787-c557de53f8f4"}，返回结果：{
  "result": [
    {
      "id": "instance_entity_expert-ec3d5cafcab982b412d6e8c8e791b767", 
      "match_score": {
        "achievement": 51.59, 
        "cooperation": 60.25, 
        "final_score": 65.88, 
        "influence": 56.31, 
        "matched": 85.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-c404921c127998b45f5887aa6e64155b", 
      "match_score": {
        "achievement": 50.81, 
        "cooperation": 59.67, 
        "final_score": 64.69, 
        "influence": 55.64, 
        "matched": 80.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-b3670e281311056d0c6d115e3d9103f3", 
      "match_score": {
        "achievement": 55.91, 
        "cooperation": 62.3, 
        "final_score": 68.94, 
        "influence": 66.5, 
        "matched": 75.0, 
        "performance": 85.0
      }
    }
  ]
}

[31m2025-07-14 16:28:56.513[0;39m [32m[task-8][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 发起人才匹配：{
  "result": [
    {
      "id": "instance_entity_expert-ec3d5cafcab982b412d6e8c8e791b767", 
      "match_score": {
        "achievement": 51.59, 
        "cooperation": 60.25, 
        "final_score": 65.88, 
        "influence": 56.31, 
        "matched": 85.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-c404921c127998b45f5887aa6e64155b", 
      "match_score": {
        "achievement": 50.81, 
        "cooperation": 59.67, 
        "final_score": 64.69, 
        "influence": 55.64, 
        "matched": 80.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-b3670e281311056d0c6d115e3d9103f3", 
      "match_score": {
        "achievement": 55.91, 
        "cooperation": 62.3, 
        "final_score": 68.94, 
        "influence": 66.5, 
        "matched": 75.0, 
        "performance": 85.0
      }
    }
  ]
}

[31m2025-07-14 16:29:02.911[0;39m [32m[task-10][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - 请求地址：http://192.168.1.33:8404/model/team_ai_pool，请求参数：{"point":"竹纤维改性技术","pointAnalysis":"采用化学或物理改性技术（如碱处理、乙酰化）改善竹纤维的疏水性、抗菌性等性能，扩展包装应用场景。","pointId":"point_d086384f-48af-4c88-9913-b0e9f90ad48c"}，返回结果：{
  "result": [
    {
      "id": "instance_entity_expert-25c2194db0630b585a6313e847a71ce4", 
      "match_score": {
        "achievement": 52.44, 
        "cooperation": 59.77, 
        "final_score": 69.45, 
        "influence": 58.97, 
        "matched": 90.0, 
        "performance": 86.09
      }
    }, 
    {
      "id": "instance_entity_expert-8f86991ae60d65d3615385e757ccae53", 
      "match_score": {
        "achievement": 51.03, 
        "cooperation": 58.52, 
        "final_score": 65.7, 
        "influence": 56.63, 
        "matched": 85.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-738f8a72343acbad06f7480eb6a94082", 
      "match_score": {
        "achievement": 51.85, 
        "cooperation": 58.74, 
        "final_score": 64.87, 
        "influence": 57.52, 
        "matched": 80.0, 
        "performance": 76.25
      }
    }
  ]
}

[31m2025-07-14 16:29:02.912[0;39m [32m[task-10][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 发起人才匹配：{
  "result": [
    {
      "id": "instance_entity_expert-25c2194db0630b585a6313e847a71ce4", 
      "match_score": {
        "achievement": 52.44, 
        "cooperation": 59.77, 
        "final_score": 69.45, 
        "influence": 58.97, 
        "matched": 90.0, 
        "performance": 86.09
      }
    }, 
    {
      "id": "instance_entity_expert-8f86991ae60d65d3615385e757ccae53", 
      "match_score": {
        "achievement": 51.03, 
        "cooperation": 58.52, 
        "final_score": 65.7, 
        "influence": 56.63, 
        "matched": 85.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-738f8a72343acbad06f7480eb6a94082", 
      "match_score": {
        "achievement": 51.85, 
        "cooperation": 58.74, 
        "final_score": 64.87, 
        "influence": 57.52, 
        "matched": 80.0, 
        "performance": 76.25
      }
    }
  ]
}

[31m2025-07-14 16:29:11.179[0;39m [32m[task-9][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - 请求地址：http://192.168.1.33:8404/model/team_ai_pool，请求参数：{"point":"热压成型工艺优化","pointAnalysis":"通过热压成型工艺参数（温度、压力、时间）的优化，可提升竹纤维材料的密实度与形状保持性，降低能耗。","pointId":"point_6e9b0e23-a214-449e-b816-dd07ead1ed22"}，返回结果：{
  "result": [
    {
      "id": "instance_entity_expert-eae3f2e09ccf12f10e32548220e8b4e5", 
      "match_score": {
        "achievement": 52.18, 
        "cooperation": 58.64, 
        "final_score": 65.21, 
        "influence": 56.48, 
        "matched": 85.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-998b66bfa8115907ac31e5a25bd7d7f2", 
      "match_score": {
        "achievement": 52.64, 
        "cooperation": 62.01, 
        "final_score": 62.82, 
        "influence": 55.7, 
        "matched": 80.0, 
        "performance": 63.75
      }
    }, 
    {
      "id": "instance_entity_expert-56b0af32732b9f953f0817266008fd59", 
      "match_score": {
        "achievement": 50.0, 
        "cooperation": 58.73, 
        "final_score": 63.34, 
        "influence": 55.64, 
        "matched": 75.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-e6ea13d4ec7d78cb12c6b2bded6cd51b", 
      "match_score": {
        "achievement": 50.08, 
        "cooperation": 58.33, 
        "final_score": 60.6, 
        "influence": 55.85, 
        "matched": 70.0, 
        "performance": 68.75
      }
    }, 
    {
      "id": "instance_entity_expert-75b2dafcd1fe33f5dbbd22fca4bbc7dc", 
      "match_score": {
        "achievement": 52.37, 
        "cooperation": 59.92, 
        "final_score": 62.47, 
        "influence": 57.56, 
        "matched": 65.0, 
        "performance": 77.5
      }
    }, 
    {
      "id": "instance_entity_expert-cb7a16ac268b7cb1279b06946447c7da", 
      "match_score": {
        "achievement": 50.94, 
        "cooperation": 59.04, 
        "final_score": 60.55, 
        "influence": 56.53, 
        "matched": 60.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-78a1ca8a3f03f5905443a67a8be4254f", 
      "match_score": {
        "achievement": 50.99, 
        "cooperation": 59.24, 
        "final_score": 59.88, 
        "influence": 56.69, 
        "matched": 55.0, 
        "performance": 77.5
      }
    }, 
    {
      "id": "instance_entity_expert-2397fcd8b1b461808fb301e717714a24", 
      "match_score": {
        "achievement": 50.02, 
        "cooperation": 66.67, 
        "final_score": 57.49, 
        "influence": 54.53, 
        "matched": 50.0, 
        "performance": 66.25
      }
    }
  ]
}

[31m2025-07-14 16:29:11.180[0;39m [32m[task-9][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 发起人才匹配：{
  "result": [
    {
      "id": "instance_entity_expert-eae3f2e09ccf12f10e32548220e8b4e5", 
      "match_score": {
        "achievement": 52.18, 
        "cooperation": 58.64, 
        "final_score": 65.21, 
        "influence": 56.48, 
        "matched": 85.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-998b66bfa8115907ac31e5a25bd7d7f2", 
      "match_score": {
        "achievement": 52.64, 
        "cooperation": 62.01, 
        "final_score": 62.82, 
        "influence": 55.7, 
        "matched": 80.0, 
        "performance": 63.75
      }
    }, 
    {
      "id": "instance_entity_expert-56b0af32732b9f953f0817266008fd59", 
      "match_score": {
        "achievement": 50.0, 
        "cooperation": 58.73, 
        "final_score": 63.34, 
        "influence": 55.64, 
        "matched": 75.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-e6ea13d4ec7d78cb12c6b2bded6cd51b", 
      "match_score": {
        "achievement": 50.08, 
        "cooperation": 58.33, 
        "final_score": 60.6, 
        "influence": 55.85, 
        "matched": 70.0, 
        "performance": 68.75
      }
    }, 
    {
      "id": "instance_entity_expert-75b2dafcd1fe33f5dbbd22fca4bbc7dc", 
      "match_score": {
        "achievement": 52.37, 
        "cooperation": 59.92, 
        "final_score": 62.47, 
        "influence": 57.56, 
        "matched": 65.0, 
        "performance": 77.5
      }
    }, 
    {
      "id": "instance_entity_expert-cb7a16ac268b7cb1279b06946447c7da", 
      "match_score": {
        "achievement": 50.94, 
        "cooperation": 59.04, 
        "final_score": 60.55, 
        "influence": 56.53, 
        "matched": 60.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-78a1ca8a3f03f5905443a67a8be4254f", 
      "match_score": {
        "achievement": 50.99, 
        "cooperation": 59.24, 
        "final_score": 59.88, 
        "influence": 56.69, 
        "matched": 55.0, 
        "performance": 77.5
      }
    }, 
    {
      "id": "instance_entity_expert-2397fcd8b1b461808fb301e717714a24", 
      "match_score": {
        "achievement": 50.02, 
        "cooperation": 66.67, 
        "final_score": 57.49, 
        "influence": 54.53, 
        "matched": 50.0, 
        "performance": 66.25
      }
    }
  ]
}

[31m2025-07-14 16:29:27.372[0;39m [32m[task-7][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.utils.HttpClientUtils[0;39m - 请求地址：http://192.168.1.33:8404/model/team_ai_pool，请求参数：{"point":"我要找竹环保新型包装材料固化定型技术领域的人才","pointId":"point_294e60fb-c9df-46bb-af9c-d9be3968cbcb"}，返回结果：{
  "result": [
    {
      "id": "instance_entity_expert-06c97edb6376ac162a14e128f3204d2b", 
      "match_score": {
        "achievement": 52.94, 
        "cooperation": 60.97, 
        "final_score": 67.83, 
        "influence": 58.97, 
        "matched": 90.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-2190e49629db37550bdd948d404eb5a0", 
      "match_score": {
        "achievement": 50.69, 
        "cooperation": 59.04, 
        "final_score": 65.45, 
        "influence": 56.28, 
        "matched": 85.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-3021b3870a765e996292ee9b5fe5c495", 
      "match_score": {
        "achievement": 51.76, 
        "cooperation": 59.93, 
        "final_score": 64.43, 
        "influence": 56.73, 
        "matched": 80.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-6327a95317a8578511c2755ef2f998f2", 
      "match_score": {
        "achievement": 50.53, 
        "cooperation": 57.46, 
        "final_score": 63.15, 
        "influence": 56.52, 
        "matched": 75.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-f1df4472717348c9001da83f5f00cd12", 
      "match_score": {
        "achievement": 50.75, 
        "cooperation": 60.94, 
        "final_score": 62.63, 
        "influence": 55.19, 
        "matched": 70.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-b49045fa011d97c51f73697b071c0a4c", 
      "match_score": {
        "achievement": 55.09, 
        "cooperation": 61.92, 
        "final_score": 64.35, 
        "influence": 62.42, 
        "matched": 65.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-4fdb28ac580e1a5849781b46cdd6b04a", 
      "match_score": {
        "achievement": 50.41, 
        "cooperation": 59.19, 
        "final_score": 59.59, 
        "influence": 55.99, 
        "matched": 60.0, 
        "performance": 72.34
      }
    }, 
    {
      "id": "instance_entity_expert-2126b953d49aca124f04f35ef92b3e18", 
      "match_score": {
        "achievement": 50.54, 
        "cooperation": 58.94, 
        "final_score": 59.43, 
        "influence": 56.42, 
        "matched": 55.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-bd01e46529371c8d8ec507b20b1636a4", 
      "match_score": {
        "achievement": 50.83, 
        "cooperation": 59.24, 
        "final_score": 58.63, 
        "influence": 56.83, 
        "matched": 50.0, 
        "performance": 76.25
      }
    }
  ]
}

[31m2025-07-14 16:29:27.373[0;39m [32m[task-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.company.controller.ChatController[0;39m - 发起人才匹配：{
  "result": [
    {
      "id": "instance_entity_expert-06c97edb6376ac162a14e128f3204d2b", 
      "match_score": {
        "achievement": 52.94, 
        "cooperation": 60.97, 
        "final_score": 67.83, 
        "influence": 58.97, 
        "matched": 90.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-2190e49629db37550bdd948d404eb5a0", 
      "match_score": {
        "achievement": 50.69, 
        "cooperation": 59.04, 
        "final_score": 65.45, 
        "influence": 56.28, 
        "matched": 85.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-3021b3870a765e996292ee9b5fe5c495", 
      "match_score": {
        "achievement": 51.76, 
        "cooperation": 59.93, 
        "final_score": 64.43, 
        "influence": 56.73, 
        "matched": 80.0, 
        "performance": 73.75
      }
    }, 
    {
      "id": "instance_entity_expert-6327a95317a8578511c2755ef2f998f2", 
      "match_score": {
        "achievement": 50.53, 
        "cooperation": 57.46, 
        "final_score": 63.15, 
        "influence": 56.52, 
        "matched": 75.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-f1df4472717348c9001da83f5f00cd12", 
      "match_score": {
        "achievement": 50.75, 
        "cooperation": 60.94, 
        "final_score": 62.63, 
        "influence": 55.19, 
        "matched": 70.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-b49045fa011d97c51f73697b071c0a4c", 
      "match_score": {
        "achievement": 55.09, 
        "cooperation": 61.92, 
        "final_score": 64.35, 
        "influence": 62.42, 
        "matched": 65.0, 
        "performance": 77.34
      }
    }, 
    {
      "id": "instance_entity_expert-4fdb28ac580e1a5849781b46cdd6b04a", 
      "match_score": {
        "achievement": 50.41, 
        "cooperation": 59.19, 
        "final_score": 59.59, 
        "influence": 55.99, 
        "matched": 60.0, 
        "performance": 72.34
      }
    }, 
    {
      "id": "instance_entity_expert-2126b953d49aca124f04f35ef92b3e18", 
      "match_score": {
        "achievement": 50.54, 
        "cooperation": 58.94, 
        "final_score": 59.43, 
        "influence": 56.42, 
        "matched": 55.0, 
        "performance": 76.25
      }
    }, 
    {
      "id": "instance_entity_expert-bd01e46529371c8d8ec507b20b1636a4", 
      "match_score": {
        "achievement": 50.83, 
        "cooperation": 59.24, 
        "final_score": 58.63, 
        "influence": 56.83, 
        "matched": 50.0, 
        "performance": 76.25
      }
    }
  ]
}

