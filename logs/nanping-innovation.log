[31m2025-08-28 15:55:58.825[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-08-28 15:55:58.835[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 29952 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-08-28 15:55:58.835[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-08-28 15:56:00.444[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-08-28 15:56:00.447[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-08-28 15:56:00.497[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.697[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.697[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.697[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.822[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=f811856f-6ecc-320a-8f13-0a4f17de6b75
[31m2025-08-28 15:56:03.625[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-28 15:56:04.077[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-28 15:56:09.587[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-08-28 15:56:09.609[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-08-28 15:56:09.611[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-08-28 15:56:09.611[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-08-28 15:56:09.749[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-08-28 15:56:09.749[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 10820 ms
