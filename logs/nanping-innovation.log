[31m2025-08-28 15:55:58.825[0;39m [32m[background-preinit][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.hibernate.validator.internal.util.Version[0;39m - HV000001: Hibernate Validator 6.2.0.Final
[31m2025-08-28 15:55:58.835[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Starting NanPingInnovationApplication using Java 1.8.0_452 on jjwang with PID 29952 (D:\workspace\nanping-innovation\target\classes started by 14978 in D:\workspace\nanping-innovation)
[31m2025-08-28 15:55:58.835[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - The following profiles are active: dev
[31m2025-08-28 15:56:00.444[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode!
[31m2025-08-28 15:56:00.447[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[31m2025-08-28 15:56:00.497[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.data.repository.config.RepositoryConfigurationDelegate[0;39m - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'areaNodeRelationDAO' and 'com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'dmDivisionMapDAO' and 'com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'evaluationLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'fileInfoMapper' and 'com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompanyDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'indexCompositeDataDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeGroupDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTargetDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeTypeDefineMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDefineMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.693[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakDetailMapper' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'industryChainNodeWeakProjectDAO' and 'com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'localCompanyInvestedDAO' and 'com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'loginLogMapper' and 'com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'RDRatioDAO' and 'com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysOperLogDAO' and 'com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionDemandDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'techCommissionerDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'testCompanyMappingDAO' and 'com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'userInfoDAO' and 'com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysConstantMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.695[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysRoleMenuMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.697[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUsageSuggestionMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.697[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserChainScopeMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.697[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.mybatis.spring.mapper.ClassPathMapperScanner[0;39m - Skipping MapperFactoryBean with name 'sysUserRoleMapper' and 'com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper' mapperInterface. Bean already defined with the same name!
[31m2025-08-28 15:56:00.822[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.cloud.context.scope.GenericScope[0;39m - BeanFactory id=f811856f-6ecc-320a-8f13-0a4f17de6b75
[31m2025-08-28 15:56:03.625[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-28 15:56:04.077[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[0;39m - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[31m2025-08-28 15:56:09.587[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat initialized with port(s): 8804 (http)
[31m2025-08-28 15:56:09.609[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Initializing ProtocolHandler ["http-nio-8804"]
[31m2025-08-28 15:56:09.611[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardService[0;39m - Starting service [Tomcat]
[31m2025-08-28 15:56:09.611[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.StandardEngine[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.53]
[31m2025-08-28 15:56:09.749[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring embedded WebApplicationContext
[31m2025-08-28 15:56:09.749[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.servlet.context.ServletWebServerApplicationContext[0;39m - Root WebApplicationContext: initialization completed in 10820 ms
[31m2025-08-28 15:56:10.889[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure[0;39m - Init DruidDataSource
[31m2025-08-28 15:56:14.231[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.druid.pool.DruidDataSource[0;39m - {dataSource-1} inited
[31m2025-08-28 15:56:16.683[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration[0;39m - MPJSqlInjector init
[31m2025-08-28 15:56:19.211[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.Version[0;39m - Redisson 3.20.0
[31m2025-08-28 15:56:19.552[0;39m [32m[redisson-netty-4-9][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterPubSubConnectionPool[0;39m - 1 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-28 15:56:19.754[0;39m [32m[redisson-netty-4-18][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.redisson.connection.pool.MasterConnectionPool[0;39m - 24 connections initialized for 192.168.1.16/192.168.1.16:60379
[31m2025-08-28 15:56:21.802[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.elasticsearch.client.RestClient[0;39m - request [GET http://192.168.1.18:8199/_mapping?master_timeout=30s] returned 1 warnings: [299 Elasticsearch-7.16.0-6fc81662312141fe7691d7c1c91b8658ac17aa0d "this request accesses system indices: [.security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]
[31m2025-08-28 15:56:28.157[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentIpcEntity".
[31m2025-08-28 15:56:28.157[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentIpcEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-28 15:56:28.240[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis".
[31m2025-08-28 15:56:28.241[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-28 15:56:29.821[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.model.RDRatio".
[31m2025-08-28 15:56:29.821[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.model.RDRatio ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-28 15:56:35.972[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.alibaba.cloud.sentinel.SentinelWebAutoConfiguration[0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[31m2025-08-28 15:56:42.244[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.baomidou.mybatisplus.core.metadata.TableInfoHelper[0;39m - Can not find table primary key in Class: "com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension".
[31m2025-08-28 15:56:42.244[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mcom.github.yulichang.injector.MPJSqlInjector[0;39m - class com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[31m2025-08-28 15:56:42.285[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.coyote.http11.Http11NioProtocol[0;39m - Starting ProtocolHandler ["http-nio-8804"]
[31m2025-08-28 15:56:42.300[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.boot.web.embedded.tomcat.TomcatWebServer[0;39m - Tomcat started on port(s): 8804 (http) with context path '/api'
[31m2025-08-28 15:56:42.990[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.000[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.010[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.014[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.015[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.015[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.017[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.051[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.221[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.226[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.237[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: MultipartFile, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.242[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.245[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.245[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.488[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.495[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.509[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.509[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.516[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.522[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.530[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.535[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.536[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.537[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.541[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.542[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.542[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.545[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.545[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.546[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: ArrayList, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.549[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.549[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.552[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.554[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: String, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.555[0;39m [32m[main][0;39m [34m[][0;39m [33mWARN [0;39m [1;35mspringfox.documentation.swagger.readers.operation.OperationImplicitParameterReader[0;39m - Unable to interpret the implicit parameter configuration with dataType: Integer, dataTypeClass: class java.lang.Void
[31m2025-08-28 15:56:43.840[0;39m [32m[main][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.NanPingInnovationApplication[0;39m - Started NanPingInnovationApplication in 45.774 seconds (JVM running for 49.775)
[31m2025-08-28 15:56:52.100[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[31m2025-08-28 15:56:52.100[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Initializing Servlet 'dispatcherServlet'
[31m2025-08-28 15:56:52.102[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35morg.springframework.web.servlet.DispatcherServlet[0;39m - Completed initialization in 2 ms
[31m2025-08-28 15:56:52.327[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-28 15:56:53.392[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - pageByFields index:nanping_innovation_company param:{"from":0,"size":10000,"query":{"bool":{"filter":[{"term":{"chain_node.id":{"value":"instance_concept_node_fluorochemical_industry-01","boost":1.0}}}],"adjust_pure_negative":true,"boost":1.0}},"_source":{"includes":["id"],"excludes":[]},"track_total_hits":**********} cost:786
[31m2025-08-28 15:56:53.763[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - pageByFields index:nanping_innovation_company param:{"from":0,"size":10000,"query":{"bool":{"filter":[{"term":{"province.id":{"value":"division/*********","boost":1.0}}},{"term":{"chain_node.id":{"value":"instance_concept_node_fluorochemical_industry-01","boost":1.0}}}],"adjust_pure_negative":true,"boost":1.0}},"_source":{"includes":["id"],"excludes":[]},"track_total_hits":**********} cost:54
[31m2025-08-28 15:59:48.711[0;39m [32m[http-nio-8804-exec-2][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.knowledge.center.service.impl.Es8ServiceImpl[0;39m - es8 request: http://192.168.1.33:8404/model/es_v8_search, param: {"common_search":"{\"size\":5,\"query\":{\"bool\":{\"filter\":[{\"ids\":{\"values\":[\"instance_doc_patent-27c43c21c23f7061d66e572853041ca1\",\"instance_doc_patent-c189e3b90087f6dade8a17794e0a7042\",\"instance_doc_patent-439122090b4669ecff9739593e07f6c9\",\"instance_doc_patent-6608d36a40d515607528c14e5385d358\",\"instance_doc_patent-38c442532a61df72274e39316c54d0e5\"],\"boost\":1.0}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"_source\":{\"includes\":[],\"excludes\":[\"ti_vector\"]}}"}, response: {
  "message": "\u8bf7\u6c42\u6210\u529f", 
  "response": {
    "_shards": {
      "failed": 0, 
      "skipped": 0, 
      "successful": 1, 
      "total": 1
    }, 
    "hits": {
      "hits": [
        {
          "_id": "instance_doc_patent-439122090b4669ecff9739593e07f6c9", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u4e00\u79cd\u4e09\u7532\u57fa\u787c\u7684\u5236\u5907\u7cfb\u7edf\uff0c\u6d89\u53ca\u4e09\u7532\u57fa\u787c\u5236\u5907\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u4e09\u7532\u57fa\u787c\u7684\u5236\u5907\u7cfb\u7edf\u5305\u62ec\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u548c\u4f4e\u6e29\u53cd\u5e94\u88c5\u7f6e\uff1b\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u5305\u62ec\u4e09\u7ea7\u4e32\u8054\u4e14\u5916\u4fa7\u8bbe\u7f6e\u6709\u52a0\u70ed\u88c5\u7f6e\u7684\u53cd\u5e94\u91dc\uff0c\u53cd\u5e94\u91dc\u5747\u8fde\u63a5\u81f3\u9541\u52a0\u6599\u7f50\uff1b\u4f4e\u6e29\u53cd\u5e94\u88c5\u7f6e\u5305\u62ec\u53cd\u5e94\u5668\u3001\u901a\u8fc7\u7ba1\u9053\u8fde\u901a\u81f3\u53cd\u5e94\u5668\u4e0a\u65b9\u7684\u4e09\u6c2f\u5316\u787c\u50a8\u7f50\u3001\u8fde\u63a5\u81f3\u53cd\u5e94\u5668\u4e0b\u65b9\u7684\u60f0\u6027\u6c14\u4f53\u7f6e\u6362\u7ec4\u4ef6\uff1b\u53cd\u5e94\u5668\u5305\u62ec\u58f3\u4f53\u3001\u8bbe\u7f6e\u5728\u58f3\u4f53\u5185\u7684\u4e0a\u65b9\u4e14\u8fde\u901a\u81f3\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u548c\u4e09\u6c2f\u5316\u787c\u50a8\u7f50\u7684\u5206\u6d41\u5668\u3001\u591a\u4e2a\u8fde\u901a\u81f3\u5206\u6d41\u5668\u4e0b\u65b9\u4e14\u5f00\u8bbe\u6709\u8fc7\u6ee4\u5b54\u7684\u5206\u6db2\u7ba1\uff1b\u58f3\u4f53\u5916\u4fa7\u548c\u7ba1\u9053\u5916\u4fa7\u8bbe\u7f6e\u6709\u51b7\u5374\u88c5\u7f6e\u3002\u4e0a\u8ff0\u7cfb\u7edf\u901a\u8fc7\u9ad8\u6e29\u7684\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u548c\u4f4e\u6e29\u53cd\u5e94\u88c5\u7f6e\u5c06\u9ad8\u6e29\u7684\u7b2c\u4e00\u6b65\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u548c\u4f4e\u6e29\u7684\u7b2c\u4e8c\u6b65\u683c\u6c0f\u53cd\u5e94\u5206\u5f00\uff0c\u964d\u4f4e\u4e86\u683c\u6c0f\u53cd\u5e94\u7684\u6e29\u5ea6\uff0c\u63d0\u9ad8\u4e86\u6574\u4e2a\u5de5\u827a\u7684\u5b89\u5168\u6027\u3002", 
            "abstract_cn": "\u4e00\u79cd\u4e09\u7532\u57fa\u787c\u7684\u5236\u5907\u7cfb\u7edf\uff0c\u6d89\u53ca\u4e09\u7532\u57fa\u787c\u5236\u5907\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u4e09\u7532\u57fa\u787c\u7684\u5236\u5907\u7cfb\u7edf\u5305\u62ec\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u548c\u4f4e\u6e29\u53cd\u5e94\u88c5\u7f6e\uff1b\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u5305\u62ec\u4e09\u7ea7\u4e32\u8054\u4e14\u5916\u4fa7\u8bbe\u7f6e\u6709\u52a0\u70ed\u88c5\u7f6e\u7684\u53cd\u5e94\u91dc\uff0c\u53cd\u5e94\u91dc\u5747\u8fde\u63a5\u81f3\u9541\u52a0\u6599\u7f50\uff1b\u4f4e\u6e29\u53cd\u5e94\u88c5\u7f6e\u5305\u62ec\u53cd\u5e94\u5668\u3001\u901a\u8fc7\u7ba1\u9053\u8fde\u901a\u81f3\u53cd\u5e94\u5668\u4e0a\u65b9\u7684\u4e09\u6c2f\u5316\u787c\u50a8\u7f50\u3001\u8fde\u63a5\u81f3\u53cd\u5e94\u5668\u4e0b\u65b9\u7684\u60f0\u6027\u6c14\u4f53\u7f6e\u6362\u7ec4\u4ef6\uff1b\u53cd\u5e94\u5668\u5305\u62ec\u58f3\u4f53\u3001\u8bbe\u7f6e\u5728\u58f3\u4f53\u5185\u7684\u4e0a\u65b9\u4e14\u8fde\u901a\u81f3\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u548c\u4e09\u6c2f\u5316\u787c\u50a8\u7f50\u7684\u5206\u6d41\u5668\u3001\u591a\u4e2a\u8fde\u901a\u81f3\u5206\u6d41\u5668\u4e0b\u65b9\u4e14\u5f00\u8bbe\u6709\u8fc7\u6ee4\u5b54\u7684\u5206\u6db2\u7ba1\uff1b\u58f3\u4f53\u5916\u4fa7\u548c\u7ba1\u9053\u5916\u4fa7\u8bbe\u7f6e\u6709\u51b7\u5374\u88c5\u7f6e\u3002\u4e0a\u8ff0\u7cfb\u7edf\u901a\u8fc7\u9ad8\u6e29\u7684\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u548c\u4f4e\u6e29\u53cd\u5e94\u88c5\u7f6e\u5c06\u9ad8\u6e29\u7684\u7b2c\u4e00\u6b65\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u548c\u4f4e\u6e29\u7684\u7b2c\u4e8c\u6b65\u683c\u6c0f\u53cd\u5e94\u5206\u5f00\uff0c\u964d\u4f4e\u4e86\u683c\u6c0f\u53cd\u5e94\u7684\u6e29\u5ea6\uff0c\u63d0\u9ad8\u4e86\u6574\u4e2a\u5de5\u827a\u7684\u5b89\u5168\u6027\u3002", 
            "abstract_en": "The invention discloses a preparation system of trimethyl boron,  and relates to the technical field of preparation of trimethyl boron. The preparation system of trimethyl boron includes Grignard reagent preparation device and low temperature reaction device; The Grignard reagent preparation device comprises three stages of reaction kettles connected in series and provided with heating devices on the outer sides,  wherein the reaction kettles are connected to a magnesium feeding tank; The low-temperature reaction device comprises a reactor,  a boron trichloride storage tank communicated with the upper part of the reactor through a pipeline,  and an inert gas replacement component connected with the lower part of the reactor; The reactor comprises a shell,  a flow divider arranged above the shell and communicated with the Grignard reagent preparation device and the boron trichloride storage tank,  and a plurality of liquid separating pipes communicated with the lower part of the flow divider and provided with filtering holes; Cooling devices are arranged on the outer side of the shell and the outer side of the pipeline. According to the system,  the high-temperature first-step Grignard reagent preparation and the low-temperature second-step Grignard reaction are separated through the high-temperature Grignard reagent preparation device and the low-temperature reaction device,  so that the temperature of the Grignard reaction is reduced,  and the safety of the whole process is improved.", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u91d1\u5858\u5de5\u4e1a\u56ed\u533a\u4e03\u7267\u5e73\u53f0M3", 
            "agency": "\u82cf\u5dde\u77aa\u7f9a\u77e5\u8bc6\u4ea7\u6743\u4ee3\u7406\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 32438", 
            "agent": "\u5f20\u5b87", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }, 
              {
                "id": null, 
                "name": "\u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8", 
              " \u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
            ], 
            "applicants_num": 2, 
            "apply_code": "CN202210780990.X", 
            "apply_date": "2022-07-05", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 10, 
            "complete_family": [
              "CN115055135A", 
              "CN115055135B"
            ], 
            "complete_family_num": 2, 
            "cpc": [
              "B01J19/00", 
              "C07F5/02", 
              "C07F3/02"
            ], 
            "cpc_category": [
              "B01J", 
              "C07F"
            ], 
            "create_time": "2024-12-24 20:21:26", 
            "ct": [
              "CN101116804A", 
              "CN211546376U"
            ], 
            "ct_times": 2, 
            "ctfw": null, 
            "ctfw_times": null, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2042-07-05", 
            "expiry_date": null, 
            "fct": [
              "CN101116804A", 
              "CN211546376U"
            ], 
            "fct_times": 2, 
            "fctfw": null, 
            "fctfw_times": null, 
            "first_claim": "1.\u4e00\u79cd\u4e09\u7532\u57fa\u787c\u7684\u5236\u5907\u7cfb\u7edf\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff0c\u5176\u5305\u62ec\u76f8\u4e92\u8fde\u63a5\u7684\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u548c\u4f4e\u6e29\u53cd\u5e94\u88c5\u7f6e\uff1b\n\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u5305\u62ec\u4e09\u7ea7\u4e32\u8054\u7684\u53cd\u5e94\u91dc\uff0c\u6240\u8ff0\u53cd\u5e94\u91dc\u5747\u8fde\u63a5\u81f3\u9541\u52a0\u6599\u7f50\u4e14\u6240\u8ff0\u53cd\u5e94\u91dc\u7684\u5916\u4fa7\u8bbe\u7f6e\u6709\u52a0\u70ed\u88c5\u7f6e\uff1b\n\u6240\u8ff0\u4f4e\u6e29\u53cd\u5e94\u88c5\u7f6e\u5305\u62ec\u53cd\u5e94\u5668\u3001\u901a\u8fc7\u7ba1\u9053\u8fde\u901a\u81f3\u6240\u8ff0\u53cd\u5e94\u5668\u4e0a\u65b9\u7684\u4e09\u6c2f\u5316\u787c\u50a8\u7f50\u3001\u8fde\u63a5\u81f3\u6240\u8ff0\u53cd\u5e94\u5668\u4e0b\u65b9\u7684\u60f0\u6027\u6c14\u4f53\u7f6e\u6362\u7ec4\u4ef6\uff1b\u6240\u8ff0\u53cd\u5e94\u5668\u5305\u62ec\u58f3\u4f53\u3001\u8bbe\u7f6e\u5728\u6240\u8ff0\u58f3\u4f53\u5185\u7684\u4e0a\u65b9\u4e14\u8fde\u901a\u81f3\u6240\u8ff0\u683c\u6c0f\u8bd5\u5242\u5236\u5907\u88c5\u7f6e\u548c\u6240\u8ff0\u4e09\u6c2f\u5316\u787c\u50a8\u7f50\u7684\u5206\u6d41\u5668\u3001\u591a\u4e2a\u8fde\u901a\u81f3\u6240\u8ff0\u5206\u6d41\u5668\u4e0b\u65b9\u4e14\u5f00\u8bbe\u6709\u8fc7\u6ee4\u5b54\u7684\u5206\u6db2\u7ba1\uff1b\u6240\u8ff0\u58f3\u4f53\u5916\u4fa7\u548c\u6240\u8ff0\u7ba1\u9053\u5916\u4fa7\u8bbe\u7f6e\u6709\u51b7\u5374\u88c5\u7f6e\uff0c\u6240\u8ff0\u58f3\u4f53\u4e0b\u65b9\u8bbe\u7f6e\u6709\u8bbe\u7f6e\u51fa\u6599\u53e3\u548c\u6392\u6c61\u53e3\u3002", 
            "general_value": 4, 
            "id": "instance_doc_patent-439122090b4669ecff9739593e07f6c9", 
            "inventors": [
              {
                "id": null, 
                "name": "\u9a6c\u5efa\u4fee"
              }, 
              {
                "id": null, 
                "name": "\u9756\u5b87"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }
            ], 
            "inventors_num": 3, 
            "ipc": [
              "B01J19/00", 
              "C07F5/02", 
              "C07F3/02"
            ], 
            "ipc_category": [
              "B01J", 
              "C07F"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "B01J19/00", 
            "market_value": 2, 
            "maturity_date": "2042-07-05", 
            "nec": [
              "C2663", 
              "C2684", 
              "C2661", 
              "C3521", 
              "C2614"
            ], 
            "page": 8, 
            "patent_type": "\u53d1\u660e\u4e13\u5229", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }, 
              {
                "id": null, 
                "name": "\u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8", 
              "\u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "7", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN115055135A", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2022-09-16", 
            "publish_year": 2022, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": null, 
            "simple_family": [
              "CN115055135A", 
              "CN115055135B"
            ], 
            "simple_family_num": 2, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 1, 
            "title": "\u4e00\u79cd\u4e09\u7532\u57fa\u787c\u7684\u5236\u5907\u7cfb\u7edf", 
            "title_cn": "\u4e00\u79cd\u4e09\u7532\u57fa\u787c\u7684\u5236\u5907\u7cfb\u7edf", 
            "title_en": "The invention discloses a preparation system of trimethyl boron", 
            "update_time": "2024-12-24 20:21:26", 
            "value": 9
          }
        }, 
        {
          "_id": "instance_doc_patent-6608d36a40d515607528c14e5385d358", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u672c\u5b9e\u7528\u65b0\u578b\u6d89\u53ca\u6db2\u5316\u6c14\u4f53\u68c0\u6d4b\u6280\u672f\u9886\u57df\uff0c\u5177\u4f53\u6d89\u53ca\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e\uff0c\u5305\u62ec\u8f93\u6c14\u7ba1\u9053\u548c\u6c14\u5316\u7ec4\u4ef6\uff1b\u6240\u8ff0\u6c14\u5316\u7ec4\u4ef6\u5305\u62ec\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u6240\u8ff0\u7535\u4f34\u70ed\u5e26\u6cbf\u8f93\u6c14\u7ba1\u9053\u7684\u957f\u5ea6\u65b9\u5411\u6577\u8bbe\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\uff0c\u6240\u8ff0\u51cf\u538b\u5668\u8bbe\u7f6e\u5728\u8f93\u6c14\u7ba1\u9053\u4e0a\u3002\u672c\u5b9e\u7528\u65b0\u578b\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\u8bbe\u7f6e\u6709\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u5229\u7528\u7535\u4f34\u70ed\u5e26\u5bf9\u8f93\u6c14\u7ba1\u9053\u8fdb\u884c\u52a0\u70ed\uff0c\u6709\u52a9\u4e8e\u6db2\u5316\u6c14\u4f53\u5728\u8f93\u6c14\u7ba1\u9053\u5185\u7684\u6c14\u5316\uff0c\u540c\u65f6\u5229\u7528\u51cf\u538b\u5668\u5c06\u6c14\u74f6\u91ca\u653e\u51fa\u7684\u9ad8\u538b\u6c14\u4f53\u964d\u4e3a\u4f4e\u538b\u6c14\u4f53\uff0c\u5e76\u4fdd\u6301\u4ece\u51cf\u538b\u5668\u5904\u8f93\u51fa\u7684\u6c14\u4f53\u7684\u538b\u529b\u548c\u6d41\u91cf\u7a33\u5b9a\uff0c\u8fdb\u800c\u786e\u4fdd\u8272\u8c31\u4eea\u5206\u6790\u68c0\u6d4b\u7684\u6570\u636e\u7684\u7cbe\u786e\u6027\u3002", 
            "abstract_cn": "\u672c\u5b9e\u7528\u65b0\u578b\u6d89\u53ca\u6db2\u5316\u6c14\u4f53\u68c0\u6d4b\u6280\u672f\u9886\u57df\uff0c\u5177\u4f53\u6d89\u53ca\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e\uff0c\u5305\u62ec\u8f93\u6c14\u7ba1\u9053\u548c\u6c14\u5316\u7ec4\u4ef6\uff1b\u6240\u8ff0\u6c14\u5316\u7ec4\u4ef6\u5305\u62ec\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u6240\u8ff0\u7535\u4f34\u70ed\u5e26\u6cbf\u8f93\u6c14\u7ba1\u9053\u7684\u957f\u5ea6\u65b9\u5411\u6577\u8bbe\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\uff0c\u6240\u8ff0\u51cf\u538b\u5668\u8bbe\u7f6e\u5728\u8f93\u6c14\u7ba1\u9053\u4e0a\u3002\u672c\u5b9e\u7528\u65b0\u578b\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\u8bbe\u7f6e\u6709\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u5229\u7528\u7535\u4f34\u70ed\u5e26\u5bf9\u8f93\u6c14\u7ba1\u9053\u8fdb\u884c\u52a0\u70ed\uff0c\u6709\u52a9\u4e8e\u6db2\u5316\u6c14\u4f53\u5728\u8f93\u6c14\u7ba1\u9053\u5185\u7684\u6c14\u5316\uff0c\u540c\u65f6\u5229\u7528\u51cf\u538b\u5668\u5c06\u6c14\u74f6\u91ca\u653e\u51fa\u7684\u9ad8\u538b\u6c14\u4f53\u964d\u4e3a\u4f4e\u538b\u6c14\u4f53\uff0c\u5e76\u4fdd\u6301\u4ece\u51cf\u538b\u5668\u5904\u8f93\u51fa\u7684\u6c14\u4f53\u7684\u538b\u529b\u548c\u6d41\u91cf\u7a33\u5b9a\uff0c\u8fdb\u800c\u786e\u4fdd\u8272\u8c31\u4eea\u5206\u6790\u68c0\u6d4b\u7684\u6570\u636e\u7684\u7cbe\u786e\u6027\u3002", 
            "abstract_en": "The utility model relates to the technical field of liquefied gas detection,  in particular to a liquefied gas detection device,  which comprises a gas pipeline and a gasification assembly; the gasification assembly includes an electric heating tape and a pressure reducer. The electric heating tape is laid on the outer wall of the gas pipeline along the length direction of the gas pipeline. The pressure reducer is set on the gas pipeline. The utility model is provided with an electric tracing belt and a pressure reducer on the outer wall of the gas pipeline The gas transmission pipeline is heated by the electric heating tape,  so that the liquefied gas can be gasified in the gas transmission pipeline; meanwhile,  the high-pressure gas released by the gas cylinder is reduced into low-pressure gas by the pressure reducer,  and the pressure and the flow of the gas output from the pressure reducer are kept stable,  so that the accuracy of the data analyzed and detected by the chromatograph is ensured", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u5434\u5bb6\u5858\u9547\u91d1\u6c99\u5927\u905319\u53f7", 
            "agency": "\u798f\u5dde\u5e02\u535a\u6df1\u4e13\u5229\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 35214", 
            "agent": "\u5f20\u4e66\u6069", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "applicants_num": 1, 
            "apply_code": "CN202323240840.9", 
            "apply_date": "2023-11-28", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 5, 
            "complete_family": [
              "CN221631363U"
            ], 
            "complete_family_num": 1, 
            "cpc": null, 
            "cpc_category": [], 
            "create_time": "2024-12-30 14:14:09", 
            "ct": null, 
            "ct_times": null, 
            "ctfw": null, 
            "ctfw_times": null, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2033-11-28", 
            "expiry_date": null, 
            "fct": null, 
            "fct_times": null, 
            "fctfw": null, 
            "fctfw_times": null, 
            "first_claim": "1.\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff1a\u5305\u62ec\u8f93\u6c14\u7ba1\u9053\u548c\u6c14\u5316\u7ec4\u4ef6\uff1b\u6240\u8ff0\u6c14\u5316\u7ec4\u4ef6\u5305\u62ec\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u6240\u8ff0\u7535\u4f34\u70ed\u5e26\u6cbf\u8f93\u6c14\u7ba1\u9053\u7684\u957f\u5ea6\u65b9\u5411\u6577\u8bbe\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\uff0c\u6240\u8ff0\u8f93\u6c14\u7ba1\u9053\u7684\u8fdb\u6c14\u7aef\u548c\u51fa\u6c14\u7aef\u5206\u522b\u8bbe\u7f6e\u6709\u51cf\u538b\u5668\u3002", 
            "general_value": 3, 
            "id": "instance_doc_patent-6608d36a40d515607528c14e5385d358", 
            "inventors": [
              {
                "id": null, 
                "name": "\u674e\u667a"
              }, 
              {
                "id": null, 
                "name": "\u5510\u5fe0\u798f"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }, 
              {
                "id": null, 
                "name": "\u5f20\u660e\u78ca"
              }
            ], 
            "inventors_num": 4, 
            "ipc": [
              "G01N30/02", 
              "G01N30/06"
            ], 
            "ipc_category": [
              "G01N"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "G01N30/02", 
            "market_value": 2, 
            "maturity_date": "2033-11-28", 
            "nec": [
              "C4024", 
              "C4014", 
              "C3544", 
              "C3581", 
              "C4021", 
              "C4330"
            ], 
            "page": 7, 
            "patent_type": "\u5b9e\u7528\u65b0\u578b", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "6", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN221631363U", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2024-08-30", 
            "publish_year": 2024, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": [
              "4.4"
            ], 
            "simple_family": [
              "CN221631363U"
            ], 
            "simple_family_num": 1, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 1, 
            "title": "\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e", 
            "title_cn": "\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e", 
            "title_en": "A liquefied gas detection device", 
            "update_time": "2024-12-30 14:14:09", 
            "value": 8
          }
        }, 
        {
          "_id": "instance_doc_patent-38c442532a61df72274e39316c54d0e5", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u4e00\u79cd\u7528\u4e8e\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u53ca\u4e09\u6c2f\u5316\u787c\u7684\u7eaf\u5316\u7cfb\u7edf\uff0c\u6d89\u53ca\u4e09\u6c2f\u5316\u787c\u7eaf\u5316\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u5305\u62ec\u4e0a\u7ba1\u4f53\u3001\u81f3\u5c11\u4e24\u4e2a\u5e76\u8054\u8bbe\u7f6e\u7684\u53cd\u5e94\u5e8a\uff0c\u4e0e\u53cd\u5e94\u5e8a\u4e00\u4e00\u5bf9\u5e94\u7684\u6e29\u5ea6\u63a7\u5236\u7ec4\u4ef6\u3001\u4e0b\u7ba1\u4f53\u3001\u5e9f\u6e23\u50a8\u7f50\u3001\u52a0\u6599\u88c5\u7f6e\u3001\u6c2e\u6c14\u88c5\u7f6e\u548c\u6570\u636e\u7ec8\u7aef\uff1b\u53cd\u5e94\u5e8a\u7684\u52a0\u6599\u53e3\u3001\u6c2e\u6c14\u8fdb\u53e3\u3001\u51fa\u6e23\u53e3\u548c\u6e29\u5ea6\u63a7\u5236\u7ec4\u4ef6\u7684\u5f00\u5173\u8fde\u63a5\u81f3\u6570\u636e\u7ec8\u7aef\u3002\u4e0a\u8ff0\u4e09\u6c2f\u5316\u787c\u7684\u7eaf\u5316\u7cfb\u7edf\u5305\u62ec\u4f9d\u6b21\u8fde\u63a5\u7684\u7c97\u54c1\u4f9b\u7ed9\u88c5\u7f6e\u3001\u8131\u91cd\u7cbe\u998f\u5854\u3001\u8131\u8f7b\u7cbe\u998f\u5854\u3001\u7528\u4e8e\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u3001\u8fc7\u6ee4\u5668\uff0c\u4ee5\u53ca\u51b7\u51dd\u88c5\u7f6e\u3002\u4e0a\u8ff0\u7528\u4e8e\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u53ca\u4e09\u6c2f\u5316\u787c\u7684\u7eaf\u5316\u7cfb\u7edf\u901a\u8fc7\u5e76\u8054\u81f3\u5c11\u4e24\u4e2a\u53cd\u5e94\u5e8a\u5b9e\u73b0\u8fde\u7eed\u5316\u751f\u4ea7\uff0c\u5e76\u4e14\u901a\u8fc7\u53cd\u5e94\u5e8a\u3001\u52a0\u6599\u88c5\u7f6e\u7b49\u90e8\u4ef6\u914d\u5408\u5b9e\u73b0\u6d82\u8986\u6709\u78b1\u571f\u91d1\u5c5e\u787c\u5316\u7269\u7684\u586b\u6599\u7684\u81ea\u52a8\u66f4\u6362\u3002", 
            "abstract_cn": "\u4e00\u79cd\u7528\u4e8e\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u53ca\u4e09\u6c2f\u5316\u787c\u7684\u7eaf\u5316\u7cfb\u7edf\uff0c\u6d89\u53ca\u4e09\u6c2f\u5316\u787c\u7eaf\u5316\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u5305\u62ec\u4e0a\u7ba1\u4f53\u3001\u81f3\u5c11\u4e24\u4e2a\u5e76\u8054\u8bbe\u7f6e\u7684\u53cd\u5e94\u5e8a\uff0c\u4e0e\u53cd\u5e94\u5e8a\u4e00\u4e00\u5bf9\u5e94\u7684\u6e29\u5ea6\u63a7\u5236\u7ec4\u4ef6\u3001\u4e0b\u7ba1\u4f53\u3001\u5e9f\u6e23\u50a8\u7f50\u3001\u52a0\u6599\u88c5\u7f6e\u3001\u6c2e\u6c14\u88c5\u7f6e\u548c\u6570\u636e\u7ec8\u7aef\uff1b\u53cd\u5e94\u5e8a\u7684\u52a0\u6599\u53e3\u3001\u6c2e\u6c14\u8fdb\u53e3\u3001\u51fa\u6e23\u53e3\u548c\u6e29\u5ea6\u63a7\u5236\u7ec4\u4ef6\u7684\u5f00\u5173\u8fde\u63a5\u81f3\u6570\u636e\u7ec8\u7aef\u3002\u4e0a\u8ff0\u4e09\u6c2f\u5316\u787c\u7684\u7eaf\u5316\u7cfb\u7edf\u5305\u62ec\u4f9d\u6b21\u8fde\u63a5\u7684\u7c97\u54c1\u4f9b\u7ed9\u88c5\u7f6e\u3001\u8131\u91cd\u7cbe\u998f\u5854\u3001\u8131\u8f7b\u7cbe\u998f\u5854\u3001\u7528\u4e8e\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u3001\u8fc7\u6ee4\u5668\uff0c\u4ee5\u53ca\u51b7\u51dd\u88c5\u7f6e\u3002\u4e0a\u8ff0\u7528\u4e8e\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u53ca\u4e09\u6c2f\u5316\u787c\u7684\u7eaf\u5316\u7cfb\u7edf\u901a\u8fc7\u5e76\u8054\u81f3\u5c11\u4e24\u4e2a\u53cd\u5e94\u5e8a\u5b9e\u73b0\u8fde\u7eed\u5316\u751f\u4ea7\uff0c\u5e76\u4e14\u901a\u8fc7\u53cd\u5e94\u5e8a\u3001\u52a0\u6599\u88c5\u7f6e\u7b49\u90e8\u4ef6\u914d\u5408\u5b9e\u73b0\u6d82\u8986\u6709\u78b1\u571f\u91d1\u5c5e\u787c\u5316\u7269\u7684\u586b\u6599\u7684\u81ea\u52a8\u66f4\u6362\u3002", 
            "abstract_en": "The invention discloses a hydrogen chloride removal device for purifying boron trichloride and a boron trichloride purification system,  and relates to the technical field of boron trichloride purification. The hydrogen chloride removal device for purifying boron trichloride comprises an upper pipe body,  at least two reaction beds arranged in parallel,  temperature control assemblies corresponding to the reaction beds one to one,  a lower pipe body,  a waste residue storage tank,  a feeding device,  a nitrogen device and a data terminal; A feed inlet of the reaction bed,  a nitrogen inlet,  a slag outlet and a switch of the temperature control assembly are connected to a data terminal. The boron trichloride purification system comprises a crude product supply device,  a heavy-weight-removing rectifying tower,  a light-weight-removing rectifying tower,  a hydrogen chloride removing device for purifying boron trichloride,  a filter and a condensing device which are sequentially connected. According to the hydrogen chloride removal device for purifying boron trichloride and the boron trichloride purification system,  at least two reaction beds are connected in parallel to realize continuous production,  and the reaction beds,  the feeding device and other parts are matched to realize automatic replacement of the filler coated with alkaline earth metal boride.", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u91d1\u5858\u5de5\u4e1a\u56ed\u533a\u4e03\u7267\u5e73\u53f0M3", 
            "agency": "\u82cf\u5dde\u77aa\u7f9a\u77e5\u8bc6\u4ea7\u6743\u4ee3\u7406\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 32438", 
            "agent": "\u5f20\u5b87", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }, 
              {
                "id": null, 
                "name": "\u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8", 
              " \u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
            ], 
            "applicants_num": 2, 
            "apply_code": "CN202210222173.2", 
            "apply_date": "2022-03-09", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 6, 
            "complete_family": [
              "CN114572992A", 
              "CN114572992B"
            ], 
            "complete_family_num": 2, 
            "cpc": [
              "C01B35/061", 
              "Y02P20/10"
            ], 
            "cpc_category": [
              "Y02P", 
              "C01B"
            ], 
            "create_time": "2024-12-26 23:32:15", 
            "ct": null, 
            "ct_times": null, 
            "ctfw": null, 
            "ctfw_times": null, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2042-03-09", 
            "expiry_date": null, 
            "fct": [
              "CN103506056A", 
              "CN102040223A", 
              "CN110181068A", 
              "CN108408734A"
            ], 
            "fct_times": 4, 
            "fctfw": [
              "CN115350690A"
            ], 
            "fctfw_times": 1, 
            "first_claim": "1.\u4e00\u79cd\u7528\u4e8e\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff0c\u5176\u5305\u62ec\u8bbe\u7f6e\u6709\u7c97\u54c1\u8fdb\u53e3\u7684\u4e0a\u7ba1\u4f53\u3001\u81f3\u5c11\u4e24\u4e2a\u5e76\u8054\u8bbe\u7f6e\u4e14\u901a\u8fc7\u8fdb\u6c14\u652f\u7ba1\u8fde\u63a5\u81f3\u6240\u8ff0\u7684\u4e0a\u7ba1\u4f53\u7684\u53cd\u5e94\u5e8a\uff0c\u4e0e\u6240\u8ff0\u53cd\u5e94\u5e8a\u4e00\u4e00\u5bf9\u5e94\u7684\u6e29\u5ea6\u63a7\u5236\u7ec4\u4ef6\u3001\u901a\u8fc7\u51fa\u6c14\u652f\u7ba1\u8fde\u63a5\u81f3\u6240\u8ff0\u53cd\u5e94\u5e8a\u7684\u4e0b\u7ba1\u4f53\u3001\u8fde\u63a5\u81f3\u6240\u8ff0\u53cd\u5e94\u5e8a\u4e0b\u65b9\u4e14\u586b\u5145\u6709\u6c2e\u6c14\u7684\u5e9f\u6e23\u50a8\u7f50\u3001\u8fde\u63a5\u81f3\u6240\u8ff0\u53cd\u5e94\u5e8a\u4e0a\u65b9\u4e14\u586b\u5145\u6709\u6c2e\u6c14\u7684\u52a0\u6599\u88c5\u7f6e\u3001\u8fde\u63a5\u81f3\u6240\u8ff0\u53cd\u5e94\u5e8a\u7684\u6c2e\u6c14\u88c5\u7f6e\u548c\u6570\u636e\u7ec8\u7aef\uff1b\u6240\u8ff0\u6570\u636e\u7ec8\u7aef\u8fde\u63a5\u81f3\u6240\u8ff0\u8fdb\u6c14\u652f\u7ba1\u3001\u6240\u8ff0\u53cd\u5e94\u5e8a\u3001\u6240\u8ff0\u6e29\u5ea6\u63a7\u5236\u7ec4\u4ef6\u3001\u6240\u8ff0\u51fa\u6c14\u652f\u7ba1\u548c\u6240\u8ff0\u52a0\u6599\u88c5\u7f6e\uff1b\u6240\u8ff0\u52a0\u6599\u88c5\u7f6e\u5305\u62ec\uff1a\u5916\u7ba1\u4f53\uff0c\u6240\u8ff0\u5916\u7ba1\u4f53\u5305\u62ec\u4fa7\u58c1\u3001\u7b2c\u4e00\u7aef\u9762\u548c\u7b2c\u4e8c\u7aef\u9762\uff0c\u6240\u8ff0\u4fa7\u58c1\u4e0a\u8bbe\u7f6e\u6709\u4e00\u4e2a\u5411\u6240\u8ff0\u5916\u7ba1\u4f53\u5916\u90e8\u51f9\u9677\u7684\u51f9\u69fd\uff0c\u6240\u8ff0\u7b2c\u4e00\u7aef\u9762\u4e3a\u5706\u9525\u5f62\u4e14\u5e95\u90e8\u8fde\u63a5\u81f3\u51fa\u6e23\u53e3\uff0c\u6240\u8ff0\u7b2c\u4e8c\u7aef\u9762\u7684\u4e2d\u5fc3\u8bbe\u7f6e\u6709\u8d2f\u7a7f\u6240\u8ff0\u7b2c\u4e8c\u7aef\u9762\u4e14\u8fde\u63a5\u81f3\u7535\u673a\u7684\u8f6c\u8f74\uff1b\u586b\u6599\u5206\u88c5\u67f1\uff0c\u6240\u8ff0\u586b\u6599\u5206\u88c5\u67f1\u7684\u4fa7\u9762\u62b5\u89e6\u6240\u8ff0\u5916\u7ba1\u4f53\u7684\u5185\u58c1\u4e14\u4e2d\u5fc3\u8fde\u63a5\u81f3\u8f6c\u8f74\uff0c\u6240\u8ff0\u586b\u6599\u5206\u88c5\u67f1\u5185\u5f00\u8bbe\u6709\u591a\u4e2a\u7ad6\u76f4\u8bbe\u7f6e\u7684\u5706\u67f1\u5b58\u50a8\u8231\u548c\u6c34\u5e73\u8bbe\u7f6e\u7684\u6ed1\u5b54\uff1b\u6240\u8ff0\u5706\u67f1\u5b58\u50a8\u8231\u56f4\u6210\u4ee5\u6240\u8ff0\u586b\u6599\u5206\u88c5\u67f1\u7684\u8f74\u7ebf\u4e3a\u4e2d\u5fc3\u7684\u5706\uff0c\u6240\u8ff0\u6ed1\u5b54\u4ece\u6240\u8ff0\u5706\u67f1\u5b58\u50a8\u8231\u5ef6\u4f38\u81f3\u6240\u8ff0\u586b\u6599\u5206\u88c5\u67f1\u7684\u4fa7\u9762\uff1b\u6240\u8ff0\u5706\u67f1\u5b58\u50a8\u8231\u5185\u8bbe\u7f6e\u6709\u5706\u5f62\u6321\u677f\uff0c\u6240\u8ff0\u5706\u5f62\u6321\u677f\u7684\u4e00\u7aef\u4e0e\u6240\u8ff0\u586b\u6599\u5206\u88c5\u67f1\u7684\u5185\u58c1\u8f6c\u52a8\u8fde\u63a5\uff0c\u53e6\u4e00\u7aef\u4e0e\u8bbe\u7f6e\u5728\u6240\u8ff0\u6ed1\u5b54\u5185\u4e14\u7528\u4e8e\u5b9e\u73b0\u6240\u8ff0\u5706\u5f62\u6321\u677f\u7684\u56fa\u5b9a\u548c\u91ca\u653e\u7684\u56fa\u5b9a\u88c5\u7f6e\u62b5\u89e6\uff1b\u5f53\u6240\u8ff0\u56fa\u5b9a\u88c5\u7f6e\u65cb\u8f6c\u81f3\u6240\u8ff0\u51f9\u69fd\u65f6\uff0c\u6240\u8ff0\u56fa\u5b9a\u88c5\u7f6e\u91ca\u653e\u6240\u8ff0\u5706\u5f62\u6321\u677f\uff0c\u88ab\u6240\u8ff0\u5706\u5f62\u6321\u677f\u906e\u6321\u7684\u6240\u8ff0\u586b\u6599\u843d\u5165\u52a0\u6599\u53e3\uff1b\u6240\u8ff0\u586b\u6599\u4e0a\u6d82\u8986\u6709\u78b1\u571f\u91d1\u5c5e\u787c\u5316\u7269\uff1b\u6240\u8ff0\u8fdb\u6c14\u652f\u7ba1\u4e0a\u8bbe\u7f6e\u6709\u8fdb\u6c14\u5f00\u5173\uff0c\u6240\u8ff0\u51fa\u6c14\u652f\u7ba1\u4e0a\u8bbe\u7f6e\u6709\u51fa\u6c14\u5f00\u5173\uff1b\u6240\u8ff0\u53cd\u5e94\u5e8a\u7684\u4e0a\u65b9\u8bbe\u7f6e\u6709\u8fde\u63a5\u81f3\u6240\u8ff0\u52a0\u6599\u88c5\u7f6e\u7684\u52a0\u6599\u53e3\u548c\u8fde\u63a5\u81f3\u6240\u8ff0\u6c2e\u6c14\u88c5\u7f6e\u7684\u6c2e\u6c14\u8fdb\u53e3\uff0c\u6240\u8ff0\u53cd\u5e94\u5e8a\u7684\u4e0b\u65b9\u8bbe\u7f6e\u6709\u659c\u5411\u4e0b\u503e\u659c\u4e14\u8fde\u63a5\u81f3\u6240\u8ff0\u5e9f\u6e23\u50a8\u7f50\u7684\u51fa\u6e23\u53e3\uff0c\u6240\u8ff0\u53cd\u5e94\u5e8a\u5185\u8fd8\u8bbe\u7f6e\u6709\u659c\u5411\u4e0b\u5ef6\u4f38\u81f3\u6240\u8ff0\u51fa\u6e23\u53e3\u7684\u51fa\u6c14\u7b5b\u677f\uff1b\u6240\u8ff0\u52a0\u6599\u53e3\u3001\u6240\u8ff0\u6c2e\u6c14\u8fdb\u53e3\u3001\u6240\u8ff0\u51fa\u6e23\u53e3\u548c\u6240\u8ff0\u6e29\u5ea6\u63a7\u5236\u7ec4\u4ef6\u7684\u5f00\u5173\uff0c\u4ee5\u53ca\u6240\u8ff0\u8fdb\u6c14\u5f00\u5173\u3001\u6240\u8ff0\u51fa\u6c14\u5f00\u5173\u8fde\u63a5\u81f3\u6240\u8ff0\u6570\u636e\u7ec8\u7aef\u3002", 
            "general_value": 3, 
            "id": "instance_doc_patent-38c442532a61df72274e39316c54d0e5", 
            "inventors": [
              {
                "id": null, 
                "name": "\u9a6c\u5efa\u4fee"
              }, 
              {
                "id": null, 
                "name": "\u9756\u5b87"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }
            ], 
            "inventors_num": 3, 
            "ipc": [
              "C01B35/06"
            ], 
            "ipc_category": [
              "C01B"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "C01B35/06", 
            "market_value": 2, 
            "maturity_date": "2042-03-09", 
            "nec": [
              "C2619", 
              "C3521", 
              "C2613"
            ], 
            "page": 12, 
            "patent_type": "\u53d1\u660e\u4e13\u5229", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }, 
              {
                "id": null, 
                "name": "\u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8", 
              "\u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "7", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN114572992B", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2023-06-23", 
            "publish_year": 2023, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": [
              "3.4"
            ], 
            "simple_family": [
              "CN114572992B", 
              "CN114572992A"
            ], 
            "simple_family_num": 2, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 1, 
            "title": "\u4e00\u79cd\u7528\u4e8e\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u53ca\u4e09\u6c2f\u5316\u787c\u7684\u7eaf\u5316\u7cfb\u7edf", 
            "title_cn": "\u4e00\u79cd\u7528\u4e8e\u7eaf\u5316\u4e09\u6c2f\u5316\u787c\u7684\u9664\u6c2f\u5316\u6c22\u88c5\u7f6e\u53ca\u4e09\u6c2f\u5316\u787c\u7684\u7eaf\u5316\u7cfb\u7edf", 
            "title_en": "The invention relates to a hydrogen chloride removing device for purifying boron trichloride and a boron trichloride purifying system", 
            "update_time": "2024-12-26 23:32:15", 
            "value": 8
          }
        }, 
        {
          "_id": "instance_doc_patent-c189e3b90087f6dade8a17794e0a7042", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u672c\u5b9e\u7528\u65b0\u578b\u6d89\u53ca\u6c14\u74f6\u6e05\u6d17\u6280\u672f\u9886\u57df\uff0c\u5177\u4f53\u6d89\u53ca\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e\uff0c\u5305\u62ec\u6c14\u74f6\u652f\u6491\u67b6\uff1b\u6240\u8ff0\u6c14\u74f6\u652f\u6491\u67b6\u5305\u62ec\u652f\u6491\u90e8\u548c\u7528\u4e8e\u88c5\u8f7d\u6240\u8ff0\u6c14\u74f6\u7684\u6258\u67b6\uff1b\u6240\u8ff0\u652f\u6491\u90e8\u5177\u6709\u76f8\u4ea4\u7684\u7b2c\u4e00\u659c\u9762\u548c\u7b2c\u4e8c\u659c\u9762\uff0c\u6240\u8ff0\u6258\u67b6\u94f0\u63a5\u5728\u652f\u6491\u90e8\u4e0a\uff0c\u4e14\u6240\u8ff0\u6258\u67b6\u80fd\u591f\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u548c\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0a\u65b9\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0b\u65b9\u3002\u672c\u5b9e\u7528\u65b0\u578b\u80fd\u591f\u63d0\u9ad8\u5bf9\u6c14\u74f6\u5185\u8154\u7684\u6e05\u6d17\u6548\u679c\u3002", 
            "abstract_cn": "\u672c\u5b9e\u7528\u65b0\u578b\u6d89\u53ca\u6c14\u74f6\u6e05\u6d17\u6280\u672f\u9886\u57df\uff0c\u5177\u4f53\u6d89\u53ca\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e\uff0c\u5305\u62ec\u6c14\u74f6\u652f\u6491\u67b6\uff1b\u6240\u8ff0\u6c14\u74f6\u652f\u6491\u67b6\u5305\u62ec\u652f\u6491\u90e8\u548c\u7528\u4e8e\u88c5\u8f7d\u6240\u8ff0\u6c14\u74f6\u7684\u6258\u67b6\uff1b\u6240\u8ff0\u652f\u6491\u90e8\u5177\u6709\u76f8\u4ea4\u7684\u7b2c\u4e00\u659c\u9762\u548c\u7b2c\u4e8c\u659c\u9762\uff0c\u6240\u8ff0\u6258\u67b6\u94f0\u63a5\u5728\u652f\u6491\u90e8\u4e0a\uff0c\u4e14\u6240\u8ff0\u6258\u67b6\u80fd\u591f\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u548c\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0a\u65b9\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0b\u65b9\u3002\u672c\u5b9e\u7528\u65b0\u578b\u80fd\u591f\u63d0\u9ad8\u5bf9\u6c14\u74f6\u5185\u8154\u7684\u6e05\u6d17\u6548\u679c\u3002", 
            "abstract_en": "The utility model relates to the technical field of gas cylinder cleaning,  in particular to a gas cylinder cleaning device,  which comprises a gas cylinder support frame The gas cylinder support frame includes a support part and a bracket for loading the gas cylinder; the support part has a first inclined surface and a second inclined surface that intersect,  the bracket is hinged on the support part,  and the bracket can lie on the first inclined surface and the second inclined surface; when the bracket reclines on the first slope,  the mouth of the gas cylinder loaded on the bracket is above the bottom of the gas cylinder; when the bracket reclines on the second inclined surface,  the mouth of the gas cylinder loaded on the bracket is below the bottom of the gas cylinder The utility model can improve the cleaning effect on the inner cavity of the gas cylinder.", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u5434\u5bb6\u5858\u9547\u91d1\u6c99\u5927\u905319\u53f7", 
            "agency": "\u798f\u5dde\u5e02\u535a\u6df1\u4e13\u5229\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 35214", 
            "agent": "\u8463\u6657", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "applicants_num": 1, 
            "apply_code": "CN202323488343.0", 
            "apply_date": "2023-12-20", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 6, 
            "complete_family": [
              "CN221537531U"
            ], 
            "complete_family_num": 1, 
            "cpc": [
              "Y02E60/32"
            ], 
            "cpc_category": [
              "Y02E"
            ], 
            "create_time": "2024-12-30 14:16:09", 
            "ct": null, 
            "ct_times": null, 
            "ctfw": null, 
            "ctfw_times": null, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2033-12-20", 
            "expiry_date": null, 
            "fct": null, 
            "fct_times": null, 
            "fctfw": null, 
            "fctfw_times": null, 
            "first_claim": "1.\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff1a\u5305\u62ec\u6c14\u74f6\u652f\u6491\u67b6\uff1b\u6240\u8ff0\u6c14\u74f6\u652f\u6491\u67b6\u5305\u62ec\u652f\u6491\u90e8\u548c\u7528\u4e8e\u88c5\u8f7d\u6240\u8ff0\u6c14\u74f6\u7684\u6258\u67b6\uff1b\u6240\u8ff0\u652f\u6491\u90e8\u5177\u6709\u76f8\u4ea4\u7684\u7b2c\u4e00\u659c\u9762\u548c\u7b2c\u4e8c\u659c\u9762\uff0c\u6240\u8ff0\u6258\u67b6\u94f0\u63a5\u5728\u652f\u6491\u90e8\u4e0a\uff0c\u4e14\u6240\u8ff0\u6258\u67b6\u80fd\u591f\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u548c\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0a\u65b9\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0b\u65b9\u3002", 
            "general_value": 2, 
            "id": "instance_doc_patent-c189e3b90087f6dade8a17794e0a7042", 
            "inventors": [
              {
                "id": null, 
                "name": "\u5510\u5fe0\u798f"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }, 
              {
                "id": null, 
                "name": "\u9a6c\u5efa\u4fee"
              }, 
              {
                "id": null, 
                "name": "\u674e\u667a"
              }
            ], 
            "inventors_num": 4, 
            "ipc": [
              "B08B9/08"
            ], 
            "ipc_category": [
              "B08B"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "B08B9/08", 
            "market_value": 2, 
            "maturity_date": "2033-12-20", 
            "nec": [
              "C3599", 
              "O8191", 
              "C3360", 
              "C3492", 
              "C4330", 
              "O8113", 
              "C4090"
            ], 
            "page": 6, 
            "patent_type": "\u5b9e\u7528\u65b0\u578b", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "6", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN221537531U", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2024-08-16", 
            "publish_year": 2024, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": null, 
            "simple_family": [
              "CN221537531U"
            ], 
            "simple_family_num": 1, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 1, 
            "title": "\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e", 
            "title_cn": "\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e", 
            "title_en": "A gas cylinder cleaning device", 
            "update_time": "2024-12-30 14:16:09", 
            "value": 7
          }
        }, 
        {
          "_id": "instance_doc_patent-27c43c21c23f7061d66e572853041ca1", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u672c\u53d1\u660e\u63d0\u51fa\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u6d89\u53ca\u4e59\u70ef\u63d0\u7eaf\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\u5305\u62ec\u4ee5\u4e0b\u6b65\u9aa4\uff1a\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u5904\u7406\u5de5\u4e1a\u7ea7\u4e59\u70ef\u5236\u5f97\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\uff0c\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u7684\u6e29\u5ea6\u5206\u522b\u4e3aT1\u3001T2\uff0c\u538b\u529b\u5206\u522b\u4e3aP1\u3001P2\uff0c\u6db2\u6c2e\u8d28\u91cf\u6d41\u91cf\u5206\u522b\u4e3aN1\u3001N2\uff0c\u56de\u6d41\u5206\u522b\u4e3aR1\u3001R2\uff1b\u5176\u4e2d\uff0c\u2011100\u2103\u2264T2<T1\u2264\u201183\u2103\uff0c0.1MPa\u2264P2<P1\u22640.5MPa\uff0c40\u2264R1\u226450\uff0c40\u2264R2\u226450\uff0cN1\u2260N 2\uff1b\u5c06\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\u7ecf\u53d8\u538b\u5438\u9644\u5236\u5f97\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\uff1b\u91c7\u7528\u7ecf\u949d\u5316\u5904\u7406\u7684\u7279\u6b8a\u6cb8\u77f3\u5206\u5b50\u7b5b\u5bf9\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\u8fdb\u884c\u8131\u6c34\u3002\u4e0a\u8ff0\u63d0\u7eaf\u65b9\u6cd5\u901a\u8fc7\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u3001\u53d8\u538b\u5438\u9644\u3001\u8131\u6c34\u5b9e\u73b0\u5de5\u4e1a\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\uff0c\u5236\u5f97\u7684\u4e59\u70ef\u7eaf\u5ea6\u9ad8\u3002", 
            "abstract_cn": "\u672c\u53d1\u660e\u63d0\u51fa\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u6d89\u53ca\u4e59\u70ef\u63d0\u7eaf\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\u5305\u62ec\u4ee5\u4e0b\u6b65\u9aa4\uff1a\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u5904\u7406\u5de5\u4e1a\u7ea7\u4e59\u70ef\u5236\u5f97\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\uff0c\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u7684\u6e29\u5ea6\u5206\u522b\u4e3aT1\u3001T2\uff0c\u538b\u529b\u5206\u522b\u4e3aP1\u3001P2\uff0c\u6db2\u6c2e\u8d28\u91cf\u6d41\u91cf\u5206\u522b\u4e3aN1\u3001N2\uff0c\u56de\u6d41\u5206\u522b\u4e3aR1\u3001R2\uff1b\u5176\u4e2d\uff0c\u2011100\u2103\u2264T2<T1\u2264\u201183\u2103\uff0c0.1MPa\u2264P2<P1\u22640.5MPa\uff0c40\u2264R1\u226450\uff0c40\u2264R2\u226450\uff0cN1\u2260N 2\uff1b\u5c06\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\u7ecf\u53d8\u538b\u5438\u9644\u5236\u5f97\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\uff1b\u91c7\u7528\u7ecf\u949d\u5316\u5904\u7406\u7684\u7279\u6b8a\u6cb8\u77f3\u5206\u5b50\u7b5b\u5bf9\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\u8fdb\u884c\u8131\u6c34\u3002\u4e0a\u8ff0\u63d0\u7eaf\u65b9\u6cd5\u901a\u8fc7\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u3001\u53d8\u538b\u5438\u9644\u3001\u8131\u6c34\u5b9e\u73b0\u5de5\u4e1a\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\uff0c\u5236\u5f97\u7684\u4e59\u70ef\u7eaf\u5ea6\u9ad8\u3002", 
            "abstract_en": "The invention provides a method for purifying high-purity electronic grade ethylene,  and relates to the technical field of ethylene purification. The purification method of the high-purity electronic grade ethylene comprises the following steps :  preparing a primary ethylene crude product from the two-stage low-temperature rectification process of liquid nitrogen as a refrigerant,  and T1 for two-stage low-temperature rectification. T2,  pressure are P1,  P2 respectively,  liquid nitrogen mass flow is N1,  N2 respectively,  and the reflux is R1,  R2 respectively. Wherein -100 \u00b0C \u2264 T2 _AOMARKENCODTX0AOA T1 \u2264 -83 \u00b0C,  0.1 mpa \u2264 P2 _AOMARKENCODELTA AOA P1 \u2264 0.5 mpa,  40 \u2264 R1 \u2264 50,  40 \u2264 R2 \u2264 50,  N1 \u2260 N 2. A primary ethylene crude product is subjected to pressure swing adsorption to prepare a secondary ethylene crude product. The crude product of the second grade ethylene was dehydrated using a passivated special zeolite molecular sieve. The purification method realizes the purification of industrial grade ethylene by two-stage low-temperature rectification,  pressure swing adsorption and dehydration of liquid nitrogen as a refrigerant,  and the prepared ethylene is high in purity.", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u91d1\u5858\u5de5\u4e1a\u56ed\u533a\u4e03\u7267\u5e73\u53f0M3", 
            "agency": "\u82cf\u5dde\u77aa\u7f9a\u77e5\u8bc6\u4ea7\u6743\u4ee3\u7406\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 32438", 
            "agent": "\u5f20\u5b87", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "applicants_num": 1, 
            "apply_code": "CN202110979493.8", 
            "apply_date": "2021-08-25", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 10, 
            "complete_family": [
              "CN113582800B", 
              "CN113582800A"
            ], 
            "complete_family_num": 2, 
            "cpc": [
              "C07C7/005", 
              "C07C7/04", 
              "C07C7/13"
            ], 
            "cpc_category": [
              "C07C"
            ], 
            "create_time": "2024-12-24 00:30:08", 
            "ct": [
              "CN102675021A", 
              "CN107739024A", 
              "CN103951543A", 
              "CN110407658A", 
              "EP572239A1", 
              "CN107285986A"
            ], 
            "ct_times": 6, 
            "ctfw": [
              "CN115364821B", 
              "CN114213209A", 
              "CN114478172A", 
              "CN114213209B", 
              "CN114805007A", 
              "CN115364821A"
            ], 
            "ctfw_times": 6, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2041-08-25", 
            "expiry_date": null, 
            "fct": [
              "CN107739024A", 
              "EP572239A1", 
              "CN107285986A", 
              "CN110407658A", 
              "CN102675021A", 
              "CN103951543A"
            ], 
            "fct_times": 6, 
            "fctfw": [
              "CN114805007A", 
              "CN115364821A", 
              "CN114213209A", 
              "CN114478172A", 
              "CN114213209B"
            ], 
            "fctfw_times": 5, 
            "first_claim": "1.\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff0c\u5176\u5305\u62ec\u4ee5\u4e0b\u6b65\u9aa4\uff1a\nS1\u6b65\u9aa4\uff1a\u91c7\u7528\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u5904\u7406\u5de5\u4e1a\u7ea7\u4e59\u70ef\u53bb\u9664\u91cd\u7ec4\u5206\u548c\u8f7b\u7ec4\u5206\u5236\u5f97\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\uff0c\u6240\u8ff0\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u7684\u6e29\u5ea6\u5206\u522b\u4e3aT1\u3001T2\uff0c\u538b\u529b\u5206\u522b\u4e3aP1\u3001P2\uff0c\u6db2\u6c2e\u8d28\u91cf\u6d41\u91cf\u5206\u522b\u4e3aN1\u3001N2\uff0c\u56de\u6d41\u5206\u522b\u4e3aR1\u3001R2\uff1b\u5176\u4e2d\uff0c\u2011100\u2103\u2264T2<  T1\u2264\u201183\u2103,   0.1MPa\u2264P2<  P1\u22640.5MPa\uff0c40\u2264R1\u226450\uff0c40\u2264R2\u226450\uff0cN1\u2260N  2\uff1b\nS2\u6b65\u9aa4\uff1a\u5c06\u6240\u8ff0\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\u7ecf\u53d8\u538b\u5438\u9644\u540e\u5236\u5f97\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\uff1b\nS3\u6b65\u9aa4\uff1a\u91c7\u7528\u7ecf\u949d\u5316\u5904\u7406\u7684\u7279\u6b8a\u6cb8\u77f3\u5206\u5b50\u7b5b\u5bf9\u6240\u8ff0\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\u8fdb\u884c\u8131\u6c34\u3002", 
            "general_value": 5, 
            "id": "instance_doc_patent-27c43c21c23f7061d66e572853041ca1", 
            "inventors": [
              {
                "id": null, 
                "name": "\u9a6c\u5efa\u4fee"
              }, 
              {
                "id": null, 
                "name": "\u9756\u5b87"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }
            ], 
            "inventors_num": 3, 
            "ipc": [
              "C07C7/00", 
              "C07C7/04", 
              "C07C7/13", 
              "C07C11/04"
            ], 
            "ipc_category": [
              "C07C"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "C07C7/00", 
            "market_value": 2, 
            "maturity_date": "2041-08-25", 
            "nec": [
              "C2653", 
              "C2663", 
              "C2662", 
              "C2684", 
              "C2661", 
              "C3521", 
              "C2614"
            ], 
            "page": 9, 
            "patent_type": "\u53d1\u660e\u4e13\u5229", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "7", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN113582800A", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2021-11-02", 
            "publish_year": 2021, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": null, 
            "simple_family": [
              "CN113582800B", 
              "CN113582800A"
            ], 
            "simple_family_num": 2, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 3, 
            "title": "\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5", 
            "title_cn": "\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5", 
            "title_en": "Method for purifying high-purity electronic grade ethylene", 
            "update_time": "2024-12-24 00:30:08", 
            "value": 12
          }
        }
      ], 
      "max_score": 0.0, 
      "total": {
        "relation": "eq", 
        "value": 5
      }
    }, 
    "timed_out": false, 
    "took": 9
  }
}

[31m2025-08-28 16:34:36.730[0;39m [32m[http-nio-8804-exec-5][0;39m [34m[][0;39m [31mERROR[0;39m [1;35mcom.quantchi.nanping.innovation.config.interceptor.AuthInterceptor[0;39m - --------------请求来源ip：0:0:0:0:0:0:0:1
[31m2025-08-28 16:34:37.807[0;39m [32m[http-nio-8804-exec-5][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - pageByFields index:nanping_innovation_company param:{"from":0,"size":10000,"query":{"bool":{"filter":[{"term":{"chain_node.id":{"value":"instance_concept_node_fluorochemical_industry-01","boost":1.0}}}],"adjust_pure_negative":true,"boost":1.0}},"_source":{"includes":["id"],"excludes":[]},"track_total_hits":**********} cost:1000
[31m2025-08-28 16:34:38.317[0;39m [32m[http-nio-8804-exec-5][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.tianying.helper.ElasticsearchHelper[0;39m - pageByFields index:nanping_innovation_company param:{"from":0,"size":10000,"query":{"bool":{"filter":[{"term":{"province.id":{"value":"division/*********","boost":1.0}}},{"term":{"chain_node.id":{"value":"instance_concept_node_fluorochemical_industry-01","boost":1.0}}}],"adjust_pure_negative":true,"boost":1.0}},"_source":{"includes":["id"],"excludes":[]},"track_total_hits":**********} cost:59
[31m2025-08-28 16:36:20.266[0;39m [32m[redisson-timer-6-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.PingConnectionHandler[0;39m - Unable to send PING command over channel: [id: 0xc9ef6410, L:/192.168.45.152:59592 - R:192.168.1.16/192.168.1.16:60379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://192.168.1.16:60379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:255)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 16:41:08.655[0;39m [32m[redisson-timer-6-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.PingConnectionHandler[0;39m - Unable to send PING command over channel: [id: 0xdec9c131, L:/192.168.45.152:59594 - R:192.168.1.16/192.168.1.16:60379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://192.168.1.16:60379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:255)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 16:41:08.659[0;39m [32m[redisson-timer-6-1][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.PingConnectionHandler[0;39m - Unable to send PING command over channel: [id: 0xc76f3f1b, L:/192.168.45.152:59585 - R:192.168.1.16/192.168.1.16:60379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://192.168.1.16:60379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:255)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 16:41:08.773[0;39m [32m[http-nio-8804-exec-5][0;39m [34m[][0;39m [34mINFO [0;39m [1;35mcom.quantchi.nanping.innovation.knowledge.center.service.impl.Es8ServiceImpl[0;39m - es8 request: http://192.168.1.33:8404/model/es_v8_search, param: {"common_search":"{\"size\":5,\"query\":{\"bool\":{\"filter\":[{\"ids\":{\"values\":[\"instance_doc_patent-27c43c21c23f7061d66e572853041ca1\",\"instance_doc_patent-e1c047b0dcd2782b635ce73794cfd8fa\",\"instance_doc_patent-274640f06d29b63633dc428aab614ea0\",\"instance_doc_patent-c189e3b90087f6dade8a17794e0a7042\",\"instance_doc_patent-6608d36a40d515607528c14e5385d358\"],\"boost\":1.0}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"_source\":{\"includes\":[],\"excludes\":[\"ti_vector\"]}}"}, response: {
  "message": "\u8bf7\u6c42\u6210\u529f", 
  "response": {
    "_shards": {
      "failed": 0, 
      "skipped": 0, 
      "successful": 1, 
      "total": 1
    }, 
    "hits": {
      "hits": [
        {
          "_id": "instance_doc_patent-e1c047b0dcd2782b635ce73794cfd8fa", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u672c\u53d1\u660e\u63d0\u51fa\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e8c\u6c27\u5316\u786b\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u6d89\u53ca\u4e8c\u6c27\u5316\u786b\u63d0\u7eaf\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e8c\u6c27\u5316\u786b\u7684\u63d0\u7eaf\u65b9\u6cd5\u5305\u62ec\u4ee5\u4e0b\u6b65\u9aa4\uff1a\u6c34\u6d17\uff1a\u4e09\u7ea7\u6c34\u6d17\u6df1\u5ea6\u8131\u9664\u786b\u5316\u6c22\u4e0e\u4e09\u6c27\u5316\u786b\uff1b\u8131\u6c34\uff1a\u4ee5\u6c27\u5316\u94dd\u4e3a\u5438\u9644\u5242\u8fdb\u884c\u6df1\u5ea6\u8131\u6c34\uff1b\u8fc7\u6ee4\uff1a\u9ad8\u6548\u8fc7\u6ee4\u8131\u9664\u6b8b\u7559\u7269\u3001\u91d1\u5c5e\u6c27\u5316\u7269\u9897\u7c92\u548c\u786b\u9178\u76d0\uff1b\u7cbe\u998f\uff1a\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e8c\u7ea7\u7cbe\u998f\u8131\u9664\u8f7b\u7ec4\u5206\u548c\u91cd\u7ec4\u5206\u3002\u672c\u53d1\u660e\u63d0\u51fa\u7684\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e8c\u6c27\u5316\u786b\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u901a\u8fc7\u6c34\u6d17\u3001\u8131\u6c34\u3001\u8fc7\u6ee4\u548c\u7cbe\u998f\u6b65\u9aa4\uff0c\u6709\u6548\u8131\u9664\u4e86\u4e09\u6c27\u5316\u786b\u3001\u6b8b\u7559\u7269\u548c\u91d1\u5c5e\u6c27\u5316\u7269\u9897\u7c92\u3001\u4ee5\u53ca\u8f7b\u7ec4\u5206\u548c\u91cd\u7ec4\u5206\uff0c\u5927\u5927\u63d0\u9ad8\u4e86\u4e8c\u6c27\u5316\u786b\u7684\u7eaf\u5ea6\u548c\u7eaf\u5ea6\u7684\u7a33\u5b9a\u6027\u3002", 
            "abstract_cn": "\u672c\u53d1\u660e\u63d0\u51fa\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e8c\u6c27\u5316\u786b\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u6d89\u53ca\u4e8c\u6c27\u5316\u786b\u63d0\u7eaf\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e8c\u6c27\u5316\u786b\u7684\u63d0\u7eaf\u65b9\u6cd5\u5305\u62ec\u4ee5\u4e0b\u6b65\u9aa4\uff1a\u6c34\u6d17\uff1a\u4e09\u7ea7\u6c34\u6d17\u6df1\u5ea6\u8131\u9664\u786b\u5316\u6c22\u4e0e\u4e09\u6c27\u5316\u786b\uff1b\u8131\u6c34\uff1a\u4ee5\u6c27\u5316\u94dd\u4e3a\u5438\u9644\u5242\u8fdb\u884c\u6df1\u5ea6\u8131\u6c34\uff1b\u8fc7\u6ee4\uff1a\u9ad8\u6548\u8fc7\u6ee4\u8131\u9664\u6b8b\u7559\u7269\u3001\u91d1\u5c5e\u6c27\u5316\u7269\u9897\u7c92\u548c\u786b\u9178\u76d0\uff1b\u7cbe\u998f\uff1a\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e8c\u7ea7\u7cbe\u998f\u8131\u9664\u8f7b\u7ec4\u5206\u548c\u91cd\u7ec4\u5206\u3002\u672c\u53d1\u660e\u63d0\u51fa\u7684\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e8c\u6c27\u5316\u786b\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u901a\u8fc7\u6c34\u6d17\u3001\u8131\u6c34\u3001\u8fc7\u6ee4\u548c\u7cbe\u998f\u6b65\u9aa4\uff0c\u6709\u6548\u8131\u9664\u4e86\u4e09\u6c27\u5316\u786b\u3001\u6b8b\u7559\u7269\u548c\u91d1\u5c5e\u6c27\u5316\u7269\u9897\u7c92\u3001\u4ee5\u53ca\u8f7b\u7ec4\u5206\u548c\u91cd\u7ec4\u5206\uff0c\u5927\u5927\u63d0\u9ad8\u4e86\u4e8c\u6c27\u5316\u786b\u7684\u7eaf\u5ea6\u548c\u7eaf\u5ea6\u7684\u7a33\u5b9a\u6027\u3002", 
            "abstract_en": "The invention provides a method for purifying high-purity electronic grade sulfur dioxide,  and relates to the technical field of sulfur dioxide purification. The purification method of the high-purity electronic-grade sulfur dioxide comprises the following steps :  washing with water;  removing hydrogen sulfide and sulfur trioxide at a three-level water washing depth.  :  Deep dehydration is carried out with aluminum oxide as an adsorbent. Filter :  Removal of residues,  metal oxide particles and sulfate by high efficiency filtration. Rectification :  The light components and the heavies are removed by two-stage rectification of liquid nitrogen as a refrigerant. The invention provides a method for purifying high-purity electronic grade sulfur dioxide. Dehydration,  filtration and rectification steps,  sulfur trioxide,  residues and metal oxide particles,  and light components and heavy components are effectively removed,  and the purity and the purity of the sulfur dioxide are greatly improved.", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u91d1\u5858\u5de5\u4e1a\u56ed\u533a\u4e03\u7267\u5e73\u53f0M3", 
            "agency": "\u82cf\u5dde\u77aa\u7f9a\u77e5\u8bc6\u4ea7\u6743\u4ee3\u7406\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 32438", 
            "agent": "\u5f20\u5b87", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "applicants_num": 1, 
            "apply_code": "CN202110979509.5", 
            "apply_date": "2021-08-25", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 10, 
            "complete_family": [
              "CN113562702B", 
              "CN113562702A"
            ], 
            "complete_family_num": 2, 
            "cpc": [
              "C01B17/56"
            ], 
            "cpc_category": [
              "C01B"
            ], 
            "create_time": "2024-12-24 00:44:12", 
            "ct": [
              "CN112933880A", 
              "CN110040691A", 
              "JP56045740A", 
              "CN110040692A", 
              "CN103920365A"
            ], 
            "ct_times": 5, 
            "ctfw": [
              "CN114291795A", 
              "CN114291795B"
            ], 
            "ctfw_times": 2, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2041-08-25", 
            "expiry_date": null, 
            "fct": [
              "CN112933880A", 
              "CN110040691A", 
              "JP56045740A", 
              "CN110040692A", 
              "CN103920365A"
            ], 
            "fct_times": 5, 
            "fctfw": [
              "CN114291795A", 
              "CN114291795B"
            ], 
            "fctfw_times": 2, 
            "first_claim": "1.\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e8c\u6c27\u5316\u786b\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff0c\u5176\u5305\u62ec\u4ee5\u4e0b\u6b65\u9aa4\uff1a\n\u6c34\u6d17\uff1a\u4e09\u7ea7\u6c34\u6d17\u6df1\u5ea6\u8131\u9664\u786b\u5316\u6c22\u4e0e\u4e09\u6c27\u5316\u786b\uff1b\n\u8131\u6c34\uff1a\u4ee5\u6c27\u5316\u94dd\u4e3a\u5438\u9644\u5242\u8fdb\u884c\u6df1\u5ea6\u8131\u6c34\uff1b\n\u8fc7\u6ee4\uff1a\u9ad8\u6548\u8fc7\u6ee4\u8131\u9664\u6b8b\u7559\u7269\u3001\u91d1\u5c5e\u6c27\u5316\u7269\u9897\u7c92\u548c\u786b\u9178\u76d0\uff1b\n\u7cbe\u998f\uff1a\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e8c\u7ea7\u7cbe\u998f\u8131\u9664\u8f7b\u7ec4\u5206\u548c\u91cd\u7ec4\u5206\u3002", 
            "general_value": 2, 
            "id": "instance_doc_patent-e1c047b0dcd2782b635ce73794cfd8fa", 
            "inventors": [
              {
                "id": null, 
                "name": "\u9a6c\u5efa\u4fee"
              }, 
              {
                "id": null, 
                "name": "\u9756\u5b87"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }
            ], 
            "inventors_num": 3, 
            "ipc": [
              "C01B17/56"
            ], 
            "ipc_category": [
              "C01B"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "C01B17/56", 
            "market_value": 2, 
            "maturity_date": "2041-08-25", 
            "nec": [
              "C2619", 
              "C3521"
            ], 
            "page": 8, 
            "patent_type": "\u53d1\u660e\u4e13\u5229", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "7", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN113562702A", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2021-10-29", 
            "publish_year": 2021, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": [
              "3.4"
            ], 
            "simple_family": [
              "CN113562702B", 
              "CN113562702A"
            ], 
            "simple_family_num": 2, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 1, 
            "title": "\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e8c\u6c27\u5316\u786b\u7684\u63d0\u7eaf\u65b9\u6cd5", 
            "title_cn": "\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e8c\u6c27\u5316\u786b\u7684\u63d0\u7eaf\u65b9\u6cd5", 
            "title_en": "Method for purifying high-purity electronic grade sulfur dioxide", 
            "update_time": "2024-12-24 00:44:12", 
            "value": 7
          }
        }, 
        {
          "_id": "instance_doc_patent-6608d36a40d515607528c14e5385d358", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u672c\u5b9e\u7528\u65b0\u578b\u6d89\u53ca\u6db2\u5316\u6c14\u4f53\u68c0\u6d4b\u6280\u672f\u9886\u57df\uff0c\u5177\u4f53\u6d89\u53ca\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e\uff0c\u5305\u62ec\u8f93\u6c14\u7ba1\u9053\u548c\u6c14\u5316\u7ec4\u4ef6\uff1b\u6240\u8ff0\u6c14\u5316\u7ec4\u4ef6\u5305\u62ec\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u6240\u8ff0\u7535\u4f34\u70ed\u5e26\u6cbf\u8f93\u6c14\u7ba1\u9053\u7684\u957f\u5ea6\u65b9\u5411\u6577\u8bbe\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\uff0c\u6240\u8ff0\u51cf\u538b\u5668\u8bbe\u7f6e\u5728\u8f93\u6c14\u7ba1\u9053\u4e0a\u3002\u672c\u5b9e\u7528\u65b0\u578b\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\u8bbe\u7f6e\u6709\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u5229\u7528\u7535\u4f34\u70ed\u5e26\u5bf9\u8f93\u6c14\u7ba1\u9053\u8fdb\u884c\u52a0\u70ed\uff0c\u6709\u52a9\u4e8e\u6db2\u5316\u6c14\u4f53\u5728\u8f93\u6c14\u7ba1\u9053\u5185\u7684\u6c14\u5316\uff0c\u540c\u65f6\u5229\u7528\u51cf\u538b\u5668\u5c06\u6c14\u74f6\u91ca\u653e\u51fa\u7684\u9ad8\u538b\u6c14\u4f53\u964d\u4e3a\u4f4e\u538b\u6c14\u4f53\uff0c\u5e76\u4fdd\u6301\u4ece\u51cf\u538b\u5668\u5904\u8f93\u51fa\u7684\u6c14\u4f53\u7684\u538b\u529b\u548c\u6d41\u91cf\u7a33\u5b9a\uff0c\u8fdb\u800c\u786e\u4fdd\u8272\u8c31\u4eea\u5206\u6790\u68c0\u6d4b\u7684\u6570\u636e\u7684\u7cbe\u786e\u6027\u3002", 
            "abstract_cn": "\u672c\u5b9e\u7528\u65b0\u578b\u6d89\u53ca\u6db2\u5316\u6c14\u4f53\u68c0\u6d4b\u6280\u672f\u9886\u57df\uff0c\u5177\u4f53\u6d89\u53ca\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e\uff0c\u5305\u62ec\u8f93\u6c14\u7ba1\u9053\u548c\u6c14\u5316\u7ec4\u4ef6\uff1b\u6240\u8ff0\u6c14\u5316\u7ec4\u4ef6\u5305\u62ec\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u6240\u8ff0\u7535\u4f34\u70ed\u5e26\u6cbf\u8f93\u6c14\u7ba1\u9053\u7684\u957f\u5ea6\u65b9\u5411\u6577\u8bbe\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\uff0c\u6240\u8ff0\u51cf\u538b\u5668\u8bbe\u7f6e\u5728\u8f93\u6c14\u7ba1\u9053\u4e0a\u3002\u672c\u5b9e\u7528\u65b0\u578b\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\u8bbe\u7f6e\u6709\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u5229\u7528\u7535\u4f34\u70ed\u5e26\u5bf9\u8f93\u6c14\u7ba1\u9053\u8fdb\u884c\u52a0\u70ed\uff0c\u6709\u52a9\u4e8e\u6db2\u5316\u6c14\u4f53\u5728\u8f93\u6c14\u7ba1\u9053\u5185\u7684\u6c14\u5316\uff0c\u540c\u65f6\u5229\u7528\u51cf\u538b\u5668\u5c06\u6c14\u74f6\u91ca\u653e\u51fa\u7684\u9ad8\u538b\u6c14\u4f53\u964d\u4e3a\u4f4e\u538b\u6c14\u4f53\uff0c\u5e76\u4fdd\u6301\u4ece\u51cf\u538b\u5668\u5904\u8f93\u51fa\u7684\u6c14\u4f53\u7684\u538b\u529b\u548c\u6d41\u91cf\u7a33\u5b9a\uff0c\u8fdb\u800c\u786e\u4fdd\u8272\u8c31\u4eea\u5206\u6790\u68c0\u6d4b\u7684\u6570\u636e\u7684\u7cbe\u786e\u6027\u3002", 
            "abstract_en": "The utility model relates to the technical field of liquefied gas detection,  in particular to a liquefied gas detection device,  which comprises a gas pipeline and a gasification assembly; the gasification assembly includes an electric heating tape and a pressure reducer. The electric heating tape is laid on the outer wall of the gas pipeline along the length direction of the gas pipeline. The pressure reducer is set on the gas pipeline. The utility model is provided with an electric tracing belt and a pressure reducer on the outer wall of the gas pipeline The gas transmission pipeline is heated by the electric heating tape,  so that the liquefied gas can be gasified in the gas transmission pipeline; meanwhile,  the high-pressure gas released by the gas cylinder is reduced into low-pressure gas by the pressure reducer,  and the pressure and the flow of the gas output from the pressure reducer are kept stable,  so that the accuracy of the data analyzed and detected by the chromatograph is ensured", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u5434\u5bb6\u5858\u9547\u91d1\u6c99\u5927\u905319\u53f7", 
            "agency": "\u798f\u5dde\u5e02\u535a\u6df1\u4e13\u5229\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 35214", 
            "agent": "\u5f20\u4e66\u6069", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "applicants_num": 1, 
            "apply_code": "CN202323240840.9", 
            "apply_date": "2023-11-28", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 5, 
            "complete_family": [
              "CN221631363U"
            ], 
            "complete_family_num": 1, 
            "cpc": null, 
            "cpc_category": [], 
            "create_time": "2024-12-30 14:14:09", 
            "ct": null, 
            "ct_times": null, 
            "ctfw": null, 
            "ctfw_times": null, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2033-11-28", 
            "expiry_date": null, 
            "fct": null, 
            "fct_times": null, 
            "fctfw": null, 
            "fctfw_times": null, 
            "first_claim": "1.\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff1a\u5305\u62ec\u8f93\u6c14\u7ba1\u9053\u548c\u6c14\u5316\u7ec4\u4ef6\uff1b\u6240\u8ff0\u6c14\u5316\u7ec4\u4ef6\u5305\u62ec\u7535\u4f34\u70ed\u5e26\u548c\u51cf\u538b\u5668\uff0c\u6240\u8ff0\u7535\u4f34\u70ed\u5e26\u6cbf\u8f93\u6c14\u7ba1\u9053\u7684\u957f\u5ea6\u65b9\u5411\u6577\u8bbe\u5728\u8f93\u6c14\u7ba1\u9053\u7684\u5916\u58c1\u4e0a\uff0c\u6240\u8ff0\u8f93\u6c14\u7ba1\u9053\u7684\u8fdb\u6c14\u7aef\u548c\u51fa\u6c14\u7aef\u5206\u522b\u8bbe\u7f6e\u6709\u51cf\u538b\u5668\u3002", 
            "general_value": 3, 
            "id": "instance_doc_patent-6608d36a40d515607528c14e5385d358", 
            "inventors": [
              {
                "id": null, 
                "name": "\u674e\u667a"
              }, 
              {
                "id": null, 
                "name": "\u5510\u5fe0\u798f"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }, 
              {
                "id": null, 
                "name": "\u5f20\u660e\u78ca"
              }
            ], 
            "inventors_num": 4, 
            "ipc": [
              "G01N30/02", 
              "G01N30/06"
            ], 
            "ipc_category": [
              "G01N"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "G01N30/02", 
            "market_value": 2, 
            "maturity_date": "2033-11-28", 
            "nec": [
              "C4024", 
              "C4014", 
              "C3544", 
              "C3581", 
              "C4021", 
              "C4330"
            ], 
            "page": 7, 
            "patent_type": "\u5b9e\u7528\u65b0\u578b", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "6", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN221631363U", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2024-08-30", 
            "publish_year": 2024, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": [
              "4.4"
            ], 
            "simple_family": [
              "CN221631363U"
            ], 
            "simple_family_num": 1, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 1, 
            "title": "\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e", 
            "title_cn": "\u4e00\u79cd\u6db2\u5316\u6c14\u4f53\u7684\u68c0\u6d4b\u88c5\u7f6e", 
            "title_en": "A liquefied gas detection device", 
            "update_time": "2024-12-30 14:14:09", 
            "value": 8
          }
        }, 
        {
          "_id": "instance_doc_patent-c189e3b90087f6dade8a17794e0a7042", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u672c\u5b9e\u7528\u65b0\u578b\u6d89\u53ca\u6c14\u74f6\u6e05\u6d17\u6280\u672f\u9886\u57df\uff0c\u5177\u4f53\u6d89\u53ca\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e\uff0c\u5305\u62ec\u6c14\u74f6\u652f\u6491\u67b6\uff1b\u6240\u8ff0\u6c14\u74f6\u652f\u6491\u67b6\u5305\u62ec\u652f\u6491\u90e8\u548c\u7528\u4e8e\u88c5\u8f7d\u6240\u8ff0\u6c14\u74f6\u7684\u6258\u67b6\uff1b\u6240\u8ff0\u652f\u6491\u90e8\u5177\u6709\u76f8\u4ea4\u7684\u7b2c\u4e00\u659c\u9762\u548c\u7b2c\u4e8c\u659c\u9762\uff0c\u6240\u8ff0\u6258\u67b6\u94f0\u63a5\u5728\u652f\u6491\u90e8\u4e0a\uff0c\u4e14\u6240\u8ff0\u6258\u67b6\u80fd\u591f\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u548c\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0a\u65b9\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0b\u65b9\u3002\u672c\u5b9e\u7528\u65b0\u578b\u80fd\u591f\u63d0\u9ad8\u5bf9\u6c14\u74f6\u5185\u8154\u7684\u6e05\u6d17\u6548\u679c\u3002", 
            "abstract_cn": "\u672c\u5b9e\u7528\u65b0\u578b\u6d89\u53ca\u6c14\u74f6\u6e05\u6d17\u6280\u672f\u9886\u57df\uff0c\u5177\u4f53\u6d89\u53ca\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e\uff0c\u5305\u62ec\u6c14\u74f6\u652f\u6491\u67b6\uff1b\u6240\u8ff0\u6c14\u74f6\u652f\u6491\u67b6\u5305\u62ec\u652f\u6491\u90e8\u548c\u7528\u4e8e\u88c5\u8f7d\u6240\u8ff0\u6c14\u74f6\u7684\u6258\u67b6\uff1b\u6240\u8ff0\u652f\u6491\u90e8\u5177\u6709\u76f8\u4ea4\u7684\u7b2c\u4e00\u659c\u9762\u548c\u7b2c\u4e8c\u659c\u9762\uff0c\u6240\u8ff0\u6258\u67b6\u94f0\u63a5\u5728\u652f\u6491\u90e8\u4e0a\uff0c\u4e14\u6240\u8ff0\u6258\u67b6\u80fd\u591f\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u548c\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0a\u65b9\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0b\u65b9\u3002\u672c\u5b9e\u7528\u65b0\u578b\u80fd\u591f\u63d0\u9ad8\u5bf9\u6c14\u74f6\u5185\u8154\u7684\u6e05\u6d17\u6548\u679c\u3002", 
            "abstract_en": "The utility model relates to the technical field of gas cylinder cleaning,  in particular to a gas cylinder cleaning device,  which comprises a gas cylinder support frame The gas cylinder support frame includes a support part and a bracket for loading the gas cylinder; the support part has a first inclined surface and a second inclined surface that intersect,  the bracket is hinged on the support part,  and the bracket can lie on the first inclined surface and the second inclined surface; when the bracket reclines on the first slope,  the mouth of the gas cylinder loaded on the bracket is above the bottom of the gas cylinder; when the bracket reclines on the second inclined surface,  the mouth of the gas cylinder loaded on the bracket is below the bottom of the gas cylinder The utility model can improve the cleaning effect on the inner cavity of the gas cylinder.", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u5434\u5bb6\u5858\u9547\u91d1\u6c99\u5927\u905319\u53f7", 
            "agency": "\u798f\u5dde\u5e02\u535a\u6df1\u4e13\u5229\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 35214", 
            "agent": "\u8463\u6657", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "applicants_num": 1, 
            "apply_code": "CN202323488343.0", 
            "apply_date": "2023-12-20", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 6, 
            "complete_family": [
              "CN221537531U"
            ], 
            "complete_family_num": 1, 
            "cpc": [
              "Y02E60/32"
            ], 
            "cpc_category": [
              "Y02E"
            ], 
            "create_time": "2024-12-30 14:16:09", 
            "ct": null, 
            "ct_times": null, 
            "ctfw": null, 
            "ctfw_times": null, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2033-12-20", 
            "expiry_date": null, 
            "fct": null, 
            "fct_times": null, 
            "fctfw": null, 
            "fctfw_times": null, 
            "first_claim": "1.\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff1a\u5305\u62ec\u6c14\u74f6\u652f\u6491\u67b6\uff1b\u6240\u8ff0\u6c14\u74f6\u652f\u6491\u67b6\u5305\u62ec\u652f\u6491\u90e8\u548c\u7528\u4e8e\u88c5\u8f7d\u6240\u8ff0\u6c14\u74f6\u7684\u6258\u67b6\uff1b\u6240\u8ff0\u652f\u6491\u90e8\u5177\u6709\u76f8\u4ea4\u7684\u7b2c\u4e00\u659c\u9762\u548c\u7b2c\u4e8c\u659c\u9762\uff0c\u6240\u8ff0\u6258\u67b6\u94f0\u63a5\u5728\u652f\u6491\u90e8\u4e0a\uff0c\u4e14\u6240\u8ff0\u6258\u67b6\u80fd\u591f\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u548c\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e00\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0a\u65b9\uff1b\u5f53\u6258\u67b6\u659c\u8eba\u5728\u6240\u8ff0\u7b2c\u4e8c\u659c\u9762\u4e0a\u65f6\uff0c\u88c5\u8f7d\u5728\u6258\u67b6\u4e0a\u7684\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u53e3\u5728\u6240\u8ff0\u6c14\u74f6\u7684\u74f6\u5e95\u4e0b\u65b9\u3002", 
            "general_value": 2, 
            "id": "instance_doc_patent-c189e3b90087f6dade8a17794e0a7042", 
            "inventors": [
              {
                "id": null, 
                "name": "\u5510\u5fe0\u798f"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }, 
              {
                "id": null, 
                "name": "\u9a6c\u5efa\u4fee"
              }, 
              {
                "id": null, 
                "name": "\u674e\u667a"
              }
            ], 
            "inventors_num": 4, 
            "ipc": [
              "B08B9/08"
            ], 
            "ipc_category": [
              "B08B"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "B08B9/08", 
            "market_value": 2, 
            "maturity_date": "2033-12-20", 
            "nec": [
              "C3599", 
              "O8191", 
              "C3360", 
              "C3492", 
              "C4330", 
              "O8113", 
              "C4090"
            ], 
            "page": 6, 
            "patent_type": "\u5b9e\u7528\u65b0\u578b", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "6", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN221537531U", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2024-08-16", 
            "publish_year": 2024, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": null, 
            "simple_family": [
              "CN221537531U"
            ], 
            "simple_family_num": 1, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 1, 
            "title": "\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e", 
            "title_cn": "\u4e00\u79cd\u6c14\u74f6\u6e05\u6d17\u88c5\u7f6e", 
            "title_en": "A gas cylinder cleaning device", 
            "update_time": "2024-12-30 14:16:09", 
            "value": 7
          }
        }, 
        {
          "_id": "instance_doc_patent-27c43c21c23f7061d66e572853041ca1", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u672c\u53d1\u660e\u63d0\u51fa\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u6d89\u53ca\u4e59\u70ef\u63d0\u7eaf\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\u5305\u62ec\u4ee5\u4e0b\u6b65\u9aa4\uff1a\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u5904\u7406\u5de5\u4e1a\u7ea7\u4e59\u70ef\u5236\u5f97\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\uff0c\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u7684\u6e29\u5ea6\u5206\u522b\u4e3aT1\u3001T2\uff0c\u538b\u529b\u5206\u522b\u4e3aP1\u3001P2\uff0c\u6db2\u6c2e\u8d28\u91cf\u6d41\u91cf\u5206\u522b\u4e3aN1\u3001N2\uff0c\u56de\u6d41\u5206\u522b\u4e3aR1\u3001R2\uff1b\u5176\u4e2d\uff0c\u2011100\u2103\u2264T2<T1\u2264\u201183\u2103\uff0c0.1MPa\u2264P2<P1\u22640.5MPa\uff0c40\u2264R1\u226450\uff0c40\u2264R2\u226450\uff0cN1\u2260N 2\uff1b\u5c06\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\u7ecf\u53d8\u538b\u5438\u9644\u5236\u5f97\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\uff1b\u91c7\u7528\u7ecf\u949d\u5316\u5904\u7406\u7684\u7279\u6b8a\u6cb8\u77f3\u5206\u5b50\u7b5b\u5bf9\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\u8fdb\u884c\u8131\u6c34\u3002\u4e0a\u8ff0\u63d0\u7eaf\u65b9\u6cd5\u901a\u8fc7\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u3001\u53d8\u538b\u5438\u9644\u3001\u8131\u6c34\u5b9e\u73b0\u5de5\u4e1a\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\uff0c\u5236\u5f97\u7684\u4e59\u70ef\u7eaf\u5ea6\u9ad8\u3002", 
            "abstract_cn": "\u672c\u53d1\u660e\u63d0\u51fa\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u6d89\u53ca\u4e59\u70ef\u63d0\u7eaf\u6280\u672f\u9886\u57df\u3002\u4e0a\u8ff0\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\u5305\u62ec\u4ee5\u4e0b\u6b65\u9aa4\uff1a\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u5904\u7406\u5de5\u4e1a\u7ea7\u4e59\u70ef\u5236\u5f97\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\uff0c\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u7684\u6e29\u5ea6\u5206\u522b\u4e3aT1\u3001T2\uff0c\u538b\u529b\u5206\u522b\u4e3aP1\u3001P2\uff0c\u6db2\u6c2e\u8d28\u91cf\u6d41\u91cf\u5206\u522b\u4e3aN1\u3001N2\uff0c\u56de\u6d41\u5206\u522b\u4e3aR1\u3001R2\uff1b\u5176\u4e2d\uff0c\u2011100\u2103\u2264T2<T1\u2264\u201183\u2103\uff0c0.1MPa\u2264P2<P1\u22640.5MPa\uff0c40\u2264R1\u226450\uff0c40\u2264R2\u226450\uff0cN1\u2260N 2\uff1b\u5c06\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\u7ecf\u53d8\u538b\u5438\u9644\u5236\u5f97\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\uff1b\u91c7\u7528\u7ecf\u949d\u5316\u5904\u7406\u7684\u7279\u6b8a\u6cb8\u77f3\u5206\u5b50\u7b5b\u5bf9\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\u8fdb\u884c\u8131\u6c34\u3002\u4e0a\u8ff0\u63d0\u7eaf\u65b9\u6cd5\u901a\u8fc7\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u3001\u53d8\u538b\u5438\u9644\u3001\u8131\u6c34\u5b9e\u73b0\u5de5\u4e1a\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\uff0c\u5236\u5f97\u7684\u4e59\u70ef\u7eaf\u5ea6\u9ad8\u3002", 
            "abstract_en": "The invention provides a method for purifying high-purity electronic grade ethylene,  and relates to the technical field of ethylene purification. The purification method of the high-purity electronic grade ethylene comprises the following steps :  preparing a primary ethylene crude product from the two-stage low-temperature rectification process of liquid nitrogen as a refrigerant,  and T1 for two-stage low-temperature rectification. T2,  pressure are P1,  P2 respectively,  liquid nitrogen mass flow is N1,  N2 respectively,  and the reflux is R1,  R2 respectively. Wherein -100 \u00b0C \u2264 T2 _AOMARKENCODTX0AOA T1 \u2264 -83 \u00b0C,  0.1 mpa \u2264 P2 _AOMARKENCODELTA AOA P1 \u2264 0.5 mpa,  40 \u2264 R1 \u2264 50,  40 \u2264 R2 \u2264 50,  N1 \u2260 N 2. A primary ethylene crude product is subjected to pressure swing adsorption to prepare a secondary ethylene crude product. The crude product of the second grade ethylene was dehydrated using a passivated special zeolite molecular sieve. The purification method realizes the purification of industrial grade ethylene by two-stage low-temperature rectification,  pressure swing adsorption and dehydration of liquid nitrogen as a refrigerant,  and the prepared ethylene is high in purity.", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u91d1\u5858\u5de5\u4e1a\u56ed\u533a\u4e03\u7267\u5e73\u53f0M3", 
            "agency": "\u82cf\u5dde\u77aa\u7f9a\u77e5\u8bc6\u4ea7\u6743\u4ee3\u7406\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 32438", 
            "agent": "\u5f20\u5b87", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "applicants_num": 1, 
            "apply_code": "CN202110979493.8", 
            "apply_date": "2021-08-25", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 10, 
            "complete_family": [
              "CN113582800B", 
              "CN113582800A"
            ], 
            "complete_family_num": 2, 
            "cpc": [
              "C07C7/005", 
              "C07C7/04", 
              "C07C7/13"
            ], 
            "cpc_category": [
              "C07C"
            ], 
            "create_time": "2024-12-24 00:30:08", 
            "ct": [
              "CN102675021A", 
              "CN107739024A", 
              "CN103951543A", 
              "CN110407658A", 
              "EP572239A1", 
              "CN107285986A"
            ], 
            "ct_times": 6, 
            "ctfw": [
              "CN115364821B", 
              "CN114213209A", 
              "CN114478172A", 
              "CN114213209B", 
              "CN114805007A", 
              "CN115364821A"
            ], 
            "ctfw_times": 6, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2041-08-25", 
            "expiry_date": null, 
            "fct": [
              "CN107739024A", 
              "EP572239A1", 
              "CN107285986A", 
              "CN110407658A", 
              "CN102675021A", 
              "CN103951543A"
            ], 
            "fct_times": 6, 
            "fctfw": [
              "CN114805007A", 
              "CN115364821A", 
              "CN114213209A", 
              "CN114478172A", 
              "CN114213209B"
            ], 
            "fctfw_times": 5, 
            "first_claim": "1.\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff0c\u5176\u5305\u62ec\u4ee5\u4e0b\u6b65\u9aa4\uff1a\nS1\u6b65\u9aa4\uff1a\u91c7\u7528\u4ee5\u6db2\u6c2e\u4e3a\u51b7\u5a92\u7684\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u5904\u7406\u5de5\u4e1a\u7ea7\u4e59\u70ef\u53bb\u9664\u91cd\u7ec4\u5206\u548c\u8f7b\u7ec4\u5206\u5236\u5f97\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\uff0c\u6240\u8ff0\u4e24\u7ea7\u4f4e\u6e29\u7cbe\u998f\u7684\u6e29\u5ea6\u5206\u522b\u4e3aT1\u3001T2\uff0c\u538b\u529b\u5206\u522b\u4e3aP1\u3001P2\uff0c\u6db2\u6c2e\u8d28\u91cf\u6d41\u91cf\u5206\u522b\u4e3aN1\u3001N2\uff0c\u56de\u6d41\u5206\u522b\u4e3aR1\u3001R2\uff1b\u5176\u4e2d\uff0c\u2011100\u2103\u2264T2<  T1\u2264\u201183\u2103,   0.1MPa\u2264P2<  P1\u22640.5MPa\uff0c40\u2264R1\u226450\uff0c40\u2264R2\u226450\uff0cN1\u2260N  2\uff1b\nS2\u6b65\u9aa4\uff1a\u5c06\u6240\u8ff0\u4e00\u7ea7\u4e59\u70ef\u7c97\u54c1\u7ecf\u53d8\u538b\u5438\u9644\u540e\u5236\u5f97\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\uff1b\nS3\u6b65\u9aa4\uff1a\u91c7\u7528\u7ecf\u949d\u5316\u5904\u7406\u7684\u7279\u6b8a\u6cb8\u77f3\u5206\u5b50\u7b5b\u5bf9\u6240\u8ff0\u4e8c\u7ea7\u4e59\u70ef\u7c97\u54c1\u8fdb\u884c\u8131\u6c34\u3002", 
            "general_value": 5, 
            "id": "instance_doc_patent-27c43c21c23f7061d66e572853041ca1", 
            "inventors": [
              {
                "id": null, 
                "name": "\u9a6c\u5efa\u4fee"
              }, 
              {
                "id": null, 
                "name": "\u9756\u5b87"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }
            ], 
            "inventors_num": 3, 
            "ipc": [
              "C07C7/00", 
              "C07C7/04", 
              "C07C7/13", 
              "C07C11/04"
            ], 
            "ipc_category": [
              "C07C"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "C07C7/00", 
            "market_value": 2, 
            "maturity_date": "2041-08-25", 
            "nec": [
              "C2653", 
              "C2663", 
              "C2662", 
              "C2684", 
              "C2661", 
              "C3521", 
              "C2614"
            ], 
            "page": 9, 
            "patent_type": "\u53d1\u660e\u4e13\u5229", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "7", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN113582800A", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2021-11-02", 
            "publish_year": 2021, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": null, 
            "simple_family": [
              "CN113582800B", 
              "CN113582800A"
            ], 
            "simple_family_num": 2, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 3, 
            "title": "\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5", 
            "title_cn": "\u4e00\u79cd\u9ad8\u7eaf\u7535\u5b50\u7ea7\u4e59\u70ef\u7684\u63d0\u7eaf\u65b9\u6cd5", 
            "title_en": "Method for purifying high-purity electronic grade ethylene", 
            "update_time": "2024-12-24 00:30:08", 
            "value": 12
          }
        }, 
        {
          "_id": "instance_doc_patent-274640f06d29b63633dc428aab614ea0", 
          "_index": "nanping_innovation_patent_portrait", 
          "_score": 0.0, 
          "_source": {
            "abstract": "\u672c\u53d1\u660e\u5177\u4f53\u516c\u5f00\u4e86\u4e00\u79cd\u5de5\u4e1a\u4e59\u7094\u63d0\u7eaf\u88c5\u7f6e\u53ca\u4f7f\u7528\u8be5\u88c5\u7f6e\u63d0\u7eaf\u5de5\u4e1a\u4e59\u7094\u7684\u65b9\u6cd5\u3002\u5177\u4f53\u6765\u8bf4\uff0c\u8be5\u63d0\u7eaf\u88c5\u7f6e\u5305\u62ec\u7cbe\u998f\u88c5\u7f6e\u3001\u964d\u6e29\u88c5\u7f6e\u548c\u6d17\u8131\u88c5\u7f6e\uff0c\u6240\u8ff0\u7cbe\u998f\u88c5\u7f6e\u4e0a\u8bbe\u6709\u8fdb\u6599\u53e3\u548c\u51fa\u6599\u53e3\uff0c\u6240\u8ff0\u5de5\u4e1a\u4e59\u7094\u4ece\u8fdb\u6599\u53e3\u8fdb\u5165\u7cbe\u998f\u88c5\u7f6e\u540e\u88ab\u7cbe\u998f\u70ed\u89e3\u53bb\u9664\u4e19\u916e\uff0c\u5f97\u5230\u7684\u70ed\u84b8\u6c7d\uff0c\u6240\u8ff0\u70ed\u84b8\u6c7d\u4ece\u51fa\u6599\u53e3\u51fa\u6765\uff1b\u6240\u8ff0\u964d\u6e29\u88c5\u7f6e\u4e0a\u8bbe\u6709\u84b8\u6c7d\u5165\u53e3\u548c\u51b7\u6c14\u51fa\u53e3\uff0c\u6240\u8ff0\u70ed\u84b8\u6c7d\u4ece\u84b8\u6c7d\u5165\u53e3\u8fdb\u5165\u964d\u6e29\u88c5\u7f6e\u540e\u88ab\u964d\u6e29\uff0c\u5f97\u5230\u51b7\u6c14\uff0c\u6240\u8ff0\u51b7\u6c14\u4ece\u51b7\u6c14\u51fa\u53e3\u51fa\u6765\uff1b\u6240\u8ff0\u6d17\u8131\u88c5\u7f6e\u5305\u62ec\u8fdb\u6c14\u7ec4\u4ef6\u3001\u6d17\u8131\u5854\u3001\u6405\u62cc\u5668(2)\u548c\u5438\u9644\u7ec4\u4ef6\u3002\u91c7\u7528\u8be5\u88c5\u7f6e\u53ef\u4ee5\u6709\u6548\u63d0\u7eaf\u5de5\u4e1a\u4e59\u7094\uff0c\u5f97\u5230\u7eaf\u5ea699.6%\u4ee5\u4e0a\u3001\u78f7\u70f7\u542b\u91cf10ppm\u4ee5\u4e0b\u3001\u786b\u5316\u6c22\u542b\u91cf10ppm\u4ee5\u4e0b\u7684\u9ad8\u7eaf\u4e59\u7094\uff0c\u4ee5\u6ee1\u8db3\u5e02\u573a\u9700\u6c42\u3002", 
            "abstract_cn": "\u672c\u53d1\u660e\u5177\u4f53\u516c\u5f00\u4e86\u4e00\u79cd\u5de5\u4e1a\u4e59\u7094\u63d0\u7eaf\u88c5\u7f6e\u53ca\u4f7f\u7528\u8be5\u88c5\u7f6e\u63d0\u7eaf\u5de5\u4e1a\u4e59\u7094\u7684\u65b9\u6cd5\u3002\u5177\u4f53\u6765\u8bf4\uff0c\u8be5\u63d0\u7eaf\u88c5\u7f6e\u5305\u62ec\u7cbe\u998f\u88c5\u7f6e\u3001\u964d\u6e29\u88c5\u7f6e\u548c\u6d17\u8131\u88c5\u7f6e\uff0c\u6240\u8ff0\u7cbe\u998f\u88c5\u7f6e\u4e0a\u8bbe\u6709\u8fdb\u6599\u53e3\u548c\u51fa\u6599\u53e3\uff0c\u6240\u8ff0\u5de5\u4e1a\u4e59\u7094\u4ece\u8fdb\u6599\u53e3\u8fdb\u5165\u7cbe\u998f\u88c5\u7f6e\u540e\u88ab\u7cbe\u998f\u70ed\u89e3\u53bb\u9664\u4e19\u916e\uff0c\u5f97\u5230\u7684\u70ed\u84b8\u6c7d\uff0c\u6240\u8ff0\u70ed\u84b8\u6c7d\u4ece\u51fa\u6599\u53e3\u51fa\u6765\uff1b\u6240\u8ff0\u964d\u6e29\u88c5\u7f6e\u4e0a\u8bbe\u6709\u84b8\u6c7d\u5165\u53e3\u548c\u51b7\u6c14\u51fa\u53e3\uff0c\u6240\u8ff0\u70ed\u84b8\u6c7d\u4ece\u84b8\u6c7d\u5165\u53e3\u8fdb\u5165\u964d\u6e29\u88c5\u7f6e\u540e\u88ab\u964d\u6e29\uff0c\u5f97\u5230\u51b7\u6c14\uff0c\u6240\u8ff0\u51b7\u6c14\u4ece\u51b7\u6c14\u51fa\u53e3\u51fa\u6765\uff1b\u6240\u8ff0\u6d17\u8131\u88c5\u7f6e\u5305\u62ec\u8fdb\u6c14\u7ec4\u4ef6\u3001\u6d17\u8131\u5854\u3001\u6405\u62cc\u5668(2)\u548c\u5438\u9644\u7ec4\u4ef6\u3002\u91c7\u7528\u8be5\u88c5\u7f6e\u53ef\u4ee5\u6709\u6548\u63d0\u7eaf\u5de5\u4e1a\u4e59\u7094\uff0c\u5f97\u5230\u7eaf\u5ea699.6%\u4ee5\u4e0a\u3001\u78f7\u70f7\u542b\u91cf10ppm\u4ee5\u4e0b\u3001\u786b\u5316\u6c22\u542b\u91cf10ppm\u4ee5\u4e0b\u7684\u9ad8\u7eaf\u4e59\u7094\uff0c\u4ee5\u6ee1\u8db3\u5e02\u573a\u9700\u6c42\u3002", 
            "abstract_en": "The invention particularly discloses an industrial acetylene purification device and a method for purifying industrial acetylene by using the device. Specifically,  the purification device comprises a rectification device,  a cooling device and an elution device,  wherein the rectification device is provided with a feed inlet and a discharge outlet,  the industrial acetylene enters the rectification device from the feed inlet and is rectified and pyrolyzed to remove acetone to obtain hot steam,  and the hot steam is discharged from the discharge outlet; The cooling device is provided with a steam inlet and a cold air outlet,  the hot steam enters the cooling device from the steam inlet and then is cooled to obtain cold air,  and the cold air is discharged from the cold air outlet; The elution device comprises an air inlet assembly,  an elution tower,  a stirrer (2) and an adsorption assembly. By adopting the device,  industrial acetylene can be effectively purified to obtain high-purity acetylene with the purity of more than 99.6 percent,  the phosphane content of less than 10ppm and the hydrogen sulfide content of less than 10ppm,  so as to meet the market demand.", 
            "address": "354000 \u798f\u5efa\u7701\u5357\u5e73\u5e02\u90b5\u6b66\u5e02\u91d1\u5858\u5de5\u4e1a\u56ed\u533a\u4e03\u7267\u5e73\u53f0M3", 
            "agency": "\u82cf\u5dde\u77aa\u7f9a\u77e5\u8bc6\u4ea7\u6743\u4ee3\u7406\u4e8b\u52a1\u6240(\u666e\u901a\u5408\u4f19) 32438", 
            "agent": "\u5f20\u5b87", 
            "applicant_type": [
              "\u4f01\u4e1a"
            ], 
            "applicants": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }, 
              {
                "id": null, 
                "name": "\u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
              }
            ], 
            "applicants_country": [
              "\u4e2d\u56fd"
            ], 
            "applicants_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8", 
              " \u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
            ], 
            "applicants_num": 2, 
            "apply_code": "CN202210502673.1", 
            "apply_date": "2022-05-10", 
            "area": "\u90b5\u6b66\u5e02", 
            "assign_times": null, 
            "cite_self_times": null, 
            "city": "\u5357\u5e73\u5e02", 
            "claims_num": 10, 
            "complete_family": [
              "CN114887344B", 
              "CN114887344A"
            ], 
            "complete_family_num": 2, 
            "cpc": [
              "B01D3/14", 
              "B01D3/143", 
              "C07C7/005", 
              "C07C7/04", 
              "C07C7/12", 
              "C07C7/1485"
            ], 
            "cpc_category": [
              "B01D", 
              "C07C"
            ], 
            "create_time": "2024-12-24 20:24:09", 
            "ct": [
              "CN209596811U", 
              "CN2690385Y", 
              "CN1041935A", 
              "JP2002348580A", 
              "CN104496740A", 
              "CN204502750U", 
              "CN105693452A", 
              "GB950971A", 
              "GB787865A", 
              "CN107778125A", 
              "CN205635417U", 
              "CN214486815U"
            ], 
            "ct_times": 12, 
            "ctfw": null, 
            "ctfw_times": null, 
            "current_pledgee": null, 
            "estimated_maturity_date": "2042-05-10", 
            "expiry_date": null, 
            "fct": [
              "GB787865A", 
              "CN214486815U", 
              "CN204502750U", 
              "CN1041935A", 
              "JP2002348580A", 
              "CN209596811U", 
              "CN105693452A", 
              "GB950971A", 
              "CN104496740A", 
              "CN2690385Y", 
              "CN107778125A", 
              "CN205635417U"
            ], 
            "fct_times": 12, 
            "fctfw": null, 
            "fctfw_times": null, 
            "first_claim": "1.\u4e00\u79cd\u5de5\u4e1a\u4e59\u7094\u63d0\u7eaf\u88c5\u7f6e\uff0c\u5176\u7279\u5f81\u5728\u4e8e\uff0c\u5305\u62ec\u7cbe\u998f\u88c5\u7f6e\uff1b\u6240\u8ff0\u7cbe\u998f\u88c5\u7f6e\u4e0a\u8bbe\u6709\u8fdb\u6599\u53e3\u548c\u51fa\u6599\u53e3\uff0c\u6240\u8ff0\u5de5\u4e1a\u4e59\u7094\u4ece\u8fdb\u6599\u53e3\u8fdb\u5165\u7cbe\u998f\u88c5\u7f6e\u540e\u88ab\u7cbe\u998f\u70ed\u89e3\u53bb\u9664\u4e19\u916e\uff0c\u5f97\u5230\u7684\u70ed\u84b8\u6c7d\uff0c\u6240\u8ff0\u70ed\u84b8\u6c7d\u4ece\u51fa\u6599\u53e3\u51fa\u6765\uff1b\n\u964d\u6e29\u88c5\u7f6e\uff1b\u6240\u8ff0\u964d\u6e29\u88c5\u7f6e\u4e0a\u8bbe\u6709\u84b8\u6c7d\u5165\u53e3\u548c\u51b7\u6c14\u51fa\u53e3\uff0c\u6240\u8ff0\u70ed\u84b8\u6c7d\u4ece\u84b8\u6c7d\u5165\u53e3\u8fdb\u5165\u964d\u6e29\u88c5\u7f6e\u540e\u88ab\u964d\u6e29\uff0c\u5f97\u5230\u51b7\u6c14\uff0c\u6240\u8ff0\u51b7\u6c14\u4ece\u51b7\u6c14\u51fa\u53e3\u51fa\u6765\uff1b\n\u6d17\u8131\u88c5\u7f6e\uff1b\u6240\u8ff0\u6d17\u8131\u88c5\u7f6e\u5305\u62ec\u8fdb\u6c14\u7ec4\u4ef6\u3001\u6d17\u8131\u5854(1)\u3001\u6405\u62cc\u5668(2)\u548c\u5438\u9644\u7ec4\u4ef6\uff0c\u6240\u8ff0\u6d17\u8131\u5854(1)\u5185\u6709\u6d17\u8131\u6db2(3)\uff0c\u6240\u8ff0\u8fdb\u6c14\u7ec4\u4ef6\u8fdb\u6c14\u7aef\u8bbe\u7f6e\u5728\u6d17\u8131\u5854\u9876\u3001\u51fa\u6c14\u7aef\u6d78\u6ca1\u4e8e\u6d17\u8131\u6db2\u4e2d\uff0c\u6240\u8ff0\u6405\u62cc\u5668(2)\u8bbe\u7f6e\u5728\u6d17\u8131\u5854\u5185\u3001\u4ee5\u6405\u62cc\u6d17\u8131\u6db2\uff1b\u6240\u8ff0\u51b7\u6c14\u4ece\u8fdb\u6c14\u7ec4\u4ef6\u8fdb\u6c14\u7aef\u8fdb\u5165\u8fdb\u6c14\u7ec4\u4ef6\uff0c\u4ece\u51fa\u6c14\u7aef\u5206\u6563\u8fdb\u5165\u6d17\u8131\u6db2\u4e2d\u8fdb\u884c\u6d17\u8131\uff1b\u6240\u8ff0\u5438\u9644\u7ec4\u4ef6\u8bbe\u7f6e\u5728\u6d17\u8131\u5854\u9876\u3001\u7528\u4e8e\u6536\u96c6\u6d17\u8131\u540e\u7684\u6c14\u4f53\u3002", 
            "general_value": 6, 
            "id": "instance_doc_patent-274640f06d29b63633dc428aab614ea0", 
            "inventors": [
              {
                "id": null, 
                "name": "\u9a6c\u5efa\u4fee"
              }, 
              {
                "id": null, 
                "name": "\u9756\u5b87"
              }, 
              {
                "id": null, 
                "name": "\u675c\u6587\u4e1c"
              }
            ], 
            "inventors_num": 3, 
            "ipc": [
              "B01D3/14", 
              "C07C7/00", 
              "C07C7/04", 
              "C07C7/12", 
              "C07C7/148", 
              "C07C11/24"
            ], 
            "ipc_category": [
              "B01D", 
              "C07C"
            ], 
            "is_valid": 1, 
            "legal_value": 1, 
            "licence_times": null, 
            "main_ipc": "B01D3/14", 
            "market_value": 2, 
            "maturity_date": "2042-05-10", 
            "nec": [
              "C2653", 
              "C2663", 
              "C2662", 
              "C2684", 
              "C2661", 
              "C3463", 
              "C3521", 
              "C2614", 
              "C4330", 
              "C4090"
            ], 
            "page": 10, 
            "patent_type": "\u53d1\u660e\u4e13\u5229", 
            "patentees": [
              {
                "id": "instance_entity_company-3036afd76f684ed61cedfefe17275fd3", 
                "name": "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8"
              }, 
              {
                "id": null, 
                "name": "\u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
              }
            ], 
            "patentees_norm": [
              "\u798f\u5efa\u798f\u8c46\u65b0\u6750\u6599\u6709\u9650\u516c\u53f8", 
              "\u821f\u5c71\u798f\u5143\u4f01\u4e1a\u7ba1\u7406\u5408\u4f19\u4f01\u4e1a(\u6709\u9650\u5408\u4f19)"
            ], 
            "pct_apply_code": null, 
            "pct_public_code": null, 
            "pdf_url": null, 
            "pledge_times": null, 
            "pledgee": null, 
            "pledgor": null, 
            "priority_code": null, 
            "priority_date": null, 
            "protection_scope": "7", 
            "province": "\u798f\u5efa\u7701", 
            "public_code": "CN114887344A", 
            "public_country": "\u4e2d\u56fd", 
            "public_date": "2022-08-12", 
            "publish_year": 2022, 
            "reexamine_invalid_decision_date": [], 
            "reexamine_times": null, 
            "sec": null, 
            "simple_family": [
              "CN114887344B", 
              "CN114887344A"
            ], 
            "simple_family_num": 2, 
            "status": "\u6709\u6548", 
            "strategic_value": 1, 
            "technical_value": 1, 
            "title": "\u4e00\u79cd\u5de5\u4e1a\u4e59\u7094\u63d0\u7eaf\u88c5\u7f6e\u53ca\u4f7f\u7528\u8be5\u88c5\u7f6e\u63d0\u7eaf\u5de5\u4e1a\u4e59\u7094\u7684\u65b9\u6cd5", 
            "title_cn": "\u4e00\u79cd\u5de5\u4e1a\u4e59\u7094\u63d0\u7eaf\u88c5\u7f6e\u53ca\u4f7f\u7528\u8be5\u88c5\u7f6e\u63d0\u7eaf\u5de5\u4e1a\u4e59\u7094\u7684\u65b9\u6cd5", 
            "title_en": "The invention relates to an industrial acetylene purification device and a method for purifying industrial acetylene by using the same", 
            "update_time": "2024-12-24 20:24:09", 
            "value": 11
          }
        }
      ], 
      "max_score": 0.0, 
      "total": {
        "relation": "eq", 
        "value": 5
      }
    }, 
    "timed_out": false, 
    "took": 9
  }
}

[31m2025-08-28 16:41:08.798[0;39m [32m[http-nio-8804-exec-5][0;39m [34m[][0;39m [33mWARN [0;39m [1;35morg.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver[0;39m - Failure in @ExceptionHandler com.quantchi.nanping.innovation.config.handle.GlobalHandler#otherExceptionHandle(Exception, HttpServletRequest)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:310)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:273)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at java.io.FilterOutputStream.flush(FilterOutputStream.java:140)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1187)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1008)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:454)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1327)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1138)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.quantchi.nanping.innovation.config.filter.CorsFilter.doFilter(CorsFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1363)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:766)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:719)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:709)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:573)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:221)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1205)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:306)
	... 60 common frames omitted
[31m2025-08-28 19:58:40.384[0;39m [32m[redisson-netty-4-3][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xcb2b93f1, L:/192.168.45.152:59577 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.391[0;39m [32m[redisson-netty-4-11][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xaf8f71ca, L:/192.168.45.152:59600 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.387[0;39m [32m[redisson-netty-4-9][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x1dc75657, L:/192.168.45.152:59599 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.388[0;39m [32m[redisson-netty-4-8][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x63bcd335, L:/192.168.45.152:59582 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.434[0;39m [32m[redisson-netty-4-8][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xf6f8f23f, L:/192.168.45.152:59598 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.386[0;39m [32m[redisson-netty-4-7][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xd469be79, L:/192.168.45.152:59581 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.440[0;39m [32m[redisson-netty-4-15][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xfbbf9edb, L:/192.168.45.152:59602 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.392[0;39m [32m[redisson-netty-4-13][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xc02f3ae5, L:/192.168.45.152:59584 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.486[0;39m [32m[redisson-netty-4-13][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x61b8ae1e, L:/192.168.45.152:59601 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.488[0;39m [32m[redisson-netty-4-29][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xde9459b8, L:/192.168.45.152:59591 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.490[0;39m [32m[redisson-netty-4-32][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x26a552e5, L:/192.168.45.152:59595 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.393[0;39m [32m[redisson-netty-4-12][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xdd60a0b3, L:/192.168.45.152:59583 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.394[0;39m [32m[redisson-netty-4-17][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x9c7fe7b0, L:/192.168.45.152:59603 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.512[0;39m [32m[redisson-netty-4-17][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x920f566a, L:/192.168.45.152:59586 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.399[0;39m [32m[redisson-netty-4-24][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xa269a54b, L:/192.168.45.152:59590 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.382[0;39m [32m[redisson-netty-4-2][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xa8fa93b8, L:/192.168.45.152:59576 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.386[0;39m [32m[redisson-netty-4-6][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xe7a5884c, L:/192.168.45.152:59597 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.428[0;39m [32m[redisson-netty-4-4][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xa6e48bc9, L:/192.168.45.152:59578 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.441[0;39m [32m[redisson-netty-4-21][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x5f2d95a9, L:/192.168.45.152:58532 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.445[0;39m [32m[redisson-netty-4-23][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xf6fda851, L:/192.168.45.152:58534 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.552[0;39m [32m[redisson-netty-4-21][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xcd075685, L:/192.168.45.152:59588 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.446[0;39m [32m[redisson-netty-4-20][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x313f7981, L:/192.168.45.152:58533 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.446[0;39m [32m[redisson-netty-4-25][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x885b76ea, L:/192.168.45.152:59589 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.564[0;39m [32m[redisson-netty-4-20][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0x046848a8, L:/192.168.45.152:59587 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
[31m2025-08-28 19:58:40.573[0;39m [32m[redisson-netty-4-4][0;39m [34m[][0;39m [31mERROR[0;39m [1;35morg.redisson.client.handler.ErrorsLoggingHandler[0;39m - Exception occured. Channel: [id: 0xcd61d826, L:/192.168.45.152:59596 - R:192.168.1.16/192.168.1.16:60379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
