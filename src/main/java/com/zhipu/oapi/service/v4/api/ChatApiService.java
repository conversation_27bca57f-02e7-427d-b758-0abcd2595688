//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.zhipu.oapi.service.v4.api;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.zhipu.oapi.service.v4.embedding.EmbeddingRequest;
import com.zhipu.oapi.service.v4.embedding.EmbeddingResult;
import com.zhipu.oapi.service.v4.file.File;
import com.zhipu.oapi.service.v4.file.QueryFileResult;
import com.zhipu.oapi.service.v4.file.QueryFilesRequest;
import com.zhipu.oapi.service.v4.fine_turning.FineTuningEvent;
import com.zhipu.oapi.service.v4.fine_turning.FineTuningJob;
import com.zhipu.oapi.service.v4.fine_turning.FineTuningJobRequest;
import com.zhipu.oapi.service.v4.fine_turning.PersonalFineTuningJob;
import com.zhipu.oapi.service.v4.image.ImageResult;
import com.zhipu.oapi.service.v4.model.*;
import io.reactivex.BackpressureStrategy;
import io.reactivex.Flowable;
import io.reactivex.Single;
import okhttp3.*;
import okhttp3.MultipartBody.Part;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.HttpException;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.jackson.JacksonConverterFactory;

import javax.net.ssl.*;
import java.io.IOException;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

public class ChatApiService {

    public static OkHttpClient getUnsafeOkHttpClient() {
        try {
            // 创建一个不检查证书链的证书信任管理器。
            final TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) throws java.security.cert.CertificateException {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) throws java.security.cert.CertificateException {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    }
            };

            // 安装一个所有证书都信任的 SSL 上下文。
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            // 创建一个允许信任所有 SSL 证书的 ssl socket factory
            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.sslSocketFactory(sslSocketFactory, (X509TrustManager)trustAllCerts[0]);

            // 创建一个允许所有主机名称的主机名验证器。
            builder.hostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            });

            return builder.build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static final String BASE_URL = "https://open.bigmodel.cn/";
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(300L);
    private static final ObjectMapper mapper = defaultObjectMapper();
    private final ChatApi api;
    private final ExecutorService executorService;

    public ChatApiService(String token) {
        this(token, DEFAULT_TIMEOUT);
    }

    public ChatApiService(String token, Duration timeout) {
        ObjectMapper mapper = defaultObjectMapper();
        OkHttpClient client = defaultClient(token, timeout);
        Retrofit retrofit = defaultRetrofit(client, mapper);
        this.api = (ChatApi)retrofit.create(ChatApi.class);
        this.executorService = client.dispatcher().executorService();
    }

    public static <T> T execute(Single<T> apiCall) {
        try {
            return apiCall.blockingGet();
        } catch (HttpException var6) {
            HttpException e = var6;

            try {
                if (e.response() != null && e.response().errorBody() != null) {
                    String errorBody = e.response().errorBody().string();
                    ZhiPuAiError error = (ZhiPuAiError)mapper.readValue(errorBody, ZhiPuAiError.class);
                    String message = error.getError().getMessage();
                    message = message + "&" + error.getError().getCode() + "&" + e.code();
                    error.getError().setMessage(message);
                    throw new ZhiPuAiHttpException(error, e, e.code());
                } else {
                    throw e;
                }
            } catch (IOException var5) {
                throw e;
            }
        }
    }

    public Flowable<ModelData> streamChatCompletion(Map<String, Object> request) {
        return this.stream(this.api.createChatCompletionStream(request), ModelData.class);
    }

    public ChatCompletionAsyncResult createChatCompletionAsync(Map<String, Object> request) {
        return (ChatCompletionAsyncResult)execute(this.api.createChatCompletionAsync(request));
    }

    public ChatCompletionResult createChatCompletion(Map<String, Object> request) {
        return (ChatCompletionResult)execute(this.api.createChatCompletion(request));
    }

    public EmbeddingResult createEmbeddings(EmbeddingRequest request) {
        return (EmbeddingResult)execute(this.api.createEmbeddings(request));
    }

    public QueryFileResult queryFileList(QueryFilesRequest queryFilesRequest) {
        return (QueryFileResult)execute(this.api.queryFileList(queryFilesRequest.getAfter(), queryFilesRequest.getPurpose(), queryFilesRequest.getOrder(), queryFilesRequest.getLimit()));
    }

    public FineTuningEvent listFineTuningJobEvents(String fineTuningJobId, Integer limit, String after) {
        return (FineTuningEvent)execute(this.api.listFineTuningJobEvents(fineTuningJobId, limit, after));
    }

    public FineTuningJob retrieveFineTuningJob(String fineTuningJobId, Integer limit, String after) {
        return (FineTuningJob)execute(this.api.retrieveFineTuningJob(fineTuningJobId, limit, after));
    }

    public PersonalFineTuningJob queryPersonalFineTuningJobs(Integer limit, String after) {
        return (PersonalFineTuningJob)execute(this.api.queryPersonalFineTuningJobs(limit, after));
    }

    public ChatCompletionResult queryAsyncResult(String id) {
        return (ChatCompletionResult)execute(this.api.queryAsyncResult(id));
    }

    public File uploadFile(String purpose, String filepath) {
        java.io.File file = new java.io.File(filepath);
        MultipartBody.Part filePart = Part.createFormData("file", file.getName(), RequestBody.create(MediaType.parse("application/octet-stream"), file));
        MultipartBody.Builder formBodyBuilder = (new MultipartBody.Builder()).setType(MultipartBody.FORM);
        formBodyBuilder.addPart(filePart);
        formBodyBuilder.addFormDataPart("purpose", purpose);
        MultipartBody multipartBody = formBodyBuilder.build();
        return (File)execute(this.api.uploadFile(multipartBody));
    }

    public ImageResult createImage(Map<String, Object> request) {
        return (ImageResult)execute(this.api.createImage(request));
    }

    public FineTuningJob createFineTuningJob(FineTuningJobRequest request) {
        return (FineTuningJob)execute(this.api.createFineTuningJob(request));
    }

    private Flowable<ModelData> stream(Call<ResponseBody> apiCall, Class<ModelData> cl) {
        return stream(apiCall).map((sse) -> {
            return (ModelData)mapper.readValue(sse.getData(), cl);
        });
    }

    public static Flowable<SSE> stream(Call<ResponseBody> apiCall) {
        return stream(apiCall, false);
    }

    public static Flowable<SSE> stream(Call<ResponseBody> apiCall, boolean emitDone) {
        return Flowable.create((emitter) -> {
            apiCall.enqueue(new ResponseBodyCallback(emitter, emitDone));
        }, BackpressureStrategy.BUFFER);
    }

    public ChatApiService(ChatApi api) {
        this.api = api;
        this.executorService = null;
    }

    public ChatApiService(ChatApi api, ExecutorService executorService) {
        this.api = api;
        this.executorService = executorService;
    }

    public static ObjectMapper defaultObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setSerializationInclusion(Include.NON_NULL);
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        mapper.addMixIn(ChatFunction.class, ChatFunctionMixIn.class);
        mapper.addMixIn(ChatCompletionRequest.class, ChatCompletionRequestMixIn.class);
        mapper.addMixIn(ChatFunctionCall.class, ChatFunctionCallMixIn.class);
        return mapper;
    }

    public static OkHttpClient defaultClient(String token, Duration timeout) {
        try {
            // 创建一个不检查证书链的证书信任管理器。
            final TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) throws java.security.cert.CertificateException {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) throws java.security.cert.CertificateException {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    }
            };

            // 安装一个所有证书都信任的 SSL 上下文。
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            // 创建一个允许信任所有 SSL 证书的 ssl socket factory
            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            return (new OkHttpClient.Builder()).addInterceptor(new AuthenticationInterceptor(token)).connectionPool(new ConnectionPool(5, 1L, TimeUnit.SECONDS)).readTimeout(timeout.toMillis(), TimeUnit.MILLISECONDS)
                    .sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0])
                    .hostnameVerifier(new HostnameVerifier() {
                        @Override
                        public boolean verify(String hostname, SSLSession session) {
                            return true;
                        }
                    })
                    .build();
        } catch (final Exception e) {
            System.out.println(e);
            return (new OkHttpClient.Builder()).addInterceptor(new AuthenticationInterceptor(token)).connectionPool(new ConnectionPool(5, 1L, TimeUnit.SECONDS)).readTimeout(timeout.toMillis(), TimeUnit.MILLISECONDS).build();
        }
    }

    public static Retrofit defaultRetrofit(OkHttpClient client, ObjectMapper mapper) {
        return (new Retrofit.Builder()).baseUrl("https://open.bigmodel.cn/").client(client).addConverterFactory(JacksonConverterFactory.create(mapper)).addCallAdapterFactory(RxJava2CallAdapterFactory.create()).build();
    }

    public Flowable<ChatMessageAccumulator> mapStreamToAccumulator(Flowable<ModelData> flowable) {
        return flowable.map((chunk) -> {
            return new ChatMessageAccumulator(((Choice)chunk.getChoices().get(0)).getDelta(), (ChatMessage)null, (Choice)chunk.getChoices().get(0), chunk.getUsage(), chunk.getCreated(), chunk.getId());
        });
    }
}
