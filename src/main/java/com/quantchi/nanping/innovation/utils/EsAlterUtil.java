package com.quantchi.nanping.innovation.utils;

import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.model.SearchSourceQuery;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.SumAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;

import java.math.BigDecimal;
import java.util.*;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @create 2023/1/4 16:30
 */
public class EsAlterUtil {

    public final static String BUCKET = "bucket.count";

    /**
     * 关键字匹配搜索,从keywords字段里面取
     *
     * @param filterBuilder 搜索对象
     * @param keyword       关键词
     * @param keywordFields 查询字段数组
     * @return 筛选对象
     */
    public static BoolQueryBuilder keywordsForMutiFields(BoolQueryBuilder filterBuilder, String keyword, String[] keywordFields) {
        BoolQueryBuilder tmpfilterBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(keyword)) {
            for (String field : keywordFields) {
                tmpfilterBuilder.should(QueryBuilders.matchQuery(field, keyword));
            }
            tmpfilterBuilder.minimumShouldMatch(1);
        }
        filterBuilder.must(tmpfilterBuilder);
        return filterBuilder;
    }

    /**
     * 关键字匹配搜索,从keywords字段里面取
     *
     * @param filterBuilder 搜索对象
     * @param keywordsList  关键词列表
     * @param matchField    查询字段
     * @return 筛选对象
     */
    public static BoolQueryBuilder mutiKeywordsForField(BoolQueryBuilder filterBuilder, List<String> keywordsList, String matchField) {
        BoolQueryBuilder tmpfilterBuilder = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(keywordsList)) {
            for (String keywords : keywordsList) {
                tmpfilterBuilder.should(QueryBuilders.matchQuery(matchField, keywords));
            }
            tmpfilterBuilder.minimumShouldMatch(1);
        }
        filterBuilder.must(tmpfilterBuilder);
        return filterBuilder;
    }

    /**
     * 按字段统计
     *
     * @param esIndexEnum
     * @param field
     * @param boolQueryBuilder
     * @return
     */
    public static Map<String, Long> getAggregation(ElasticsearchHelper elasticsearchHelper, EsIndexEnum esIndexEnum, BoolQueryBuilder boolQueryBuilder, String field) {
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(BUCKET).field(field).size(10000).order(BucketOrder.count(false));
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(esIndexEnum.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        Map<String, Long> resultMap = new LinkedHashMap<>();
        if (pageResult.getSearchResponse() == null) {
            return resultMap;
        }
        Terms terms = (Terms) pageResult.getSearchResponse().getAggregations().asMap().get(BUCKET);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            resultMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return resultMap;
    }

    /**
     * 按字段Sum聚合
     *
     * @param esIndexEnum
     * @param groupField
     * @param field
     * @param boolQueryBuilder
     * @return
     */
    public static Map<String, BigDecimal> getSumAggregation(ElasticsearchHelper elasticsearchHelper, EsIndexEnum esIndexEnum, BoolQueryBuilder boolQueryBuilder, String groupField, String field) {
        Map<String, BigDecimal> res = new LinkedHashMap<>();
        TermsAggregationBuilder countryAggregation = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "sum").field(groupField).size(100);
        SumAggregationBuilder countrySum = AggregationBuilders.sum("totalAmount").field(field);
        countryAggregation.subAggregation(countrySum);
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(esIndexEnum.getEsIndex(), boolQueryBuilder, countryAggregation);
        Terms terms = (Terms) pageResult.getSearchResponse().getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "sum");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            ParsedSum inventorNum = (ParsedSum) bucket.getAggregations().asMap().get("totalAmount");
            BigDecimal val = new BigDecimal(String.valueOf(inventorNum.getValue()));
            res.put(bucket.getKeyAsString(), val);
        }
        return res;
    }


    /**
     * 简单的es搜索
     *
     * @param elasticsearchHelper
     * @param query                  查询条件
     * @param indexEnum              对应索引
     * @param alterQuery             自定义额外条件
     * @param filterFunctionBuilders 自定义权重条件
     * @return
     */
    public static EsPageResult esPageSearch(ElasticsearchHelper elasticsearchHelper, EsSimpleQuery query, EsIndexEnum indexEnum, BoolQueryBuilder alterQuery, List<FunctionScoreQueryBuilder.FilterFunctionBuilder> filterFunctionBuilders) {
        SearchSourceQuery sourceQuery = new SearchSourceQuery();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(query.getRegionId())) {
            EsAlterUtil.keywordsForMutiFields(queryBuilder, query.getRegionId(), "province.id,city.id,area.id".split(","));
        }
        if (StringUtils.isNotBlank(query.getChainId())) {
            queryBuilder.must(QueryBuilders.termsQuery("chain.id", query.getChainId()));
        }
        if (StringUtils.isNotBlank(query.getChainNodeId())) {
            queryBuilder.must(QueryBuilders.termsQuery("chain_node.id", query.getChainNodeId()));
        }
        if (StringUtils.isNotBlank(query.getKeywords()) && StringUtils.isNotBlank(indexEnum.getTitleColumn())) {
            BoolQueryBuilder tempQuery = QueryBuilders.boolQuery();
            String[] keywords = indexEnum.getTitleColumn().split(":");
            if (keywords[0].equals("text")) {
                tempQuery.should(QueryBuilders.matchPhraseQuery(keywords[1], query.getKeywords()));
            } else {
                tempQuery.should(QueryBuilders.wildcardQuery(keywords[1], "*" + query.getKeywords() + "*"));
            }
            queryBuilder.must(tempQuery);
        }
        if (alterQuery != null && alterQuery.hasClauses()) {
            queryBuilder.must(alterQuery);
        }
        sourceQuery.setQueryBuilder(queryBuilder);
        sourceQuery.setPageNum(query.getPageNum());
        sourceQuery.setPageSize(query.getPageSize());
        SearchSourceBuilder searchSource = ElasticsearchBuilder.buildSearchSource(sourceQuery);
        if (indexEnum.getEsSearchFields() != null) {
            FetchSourceContext fetchSourceContext = new FetchSourceContext(true, indexEnum.getEsSearchFields(), null);
            searchSource.fetchSource(fetchSourceContext);
        }
        addSort(searchSource, indexEnum.getSort(), filterFunctionBuilders);
        SearchResponse searchResponse = elasticsearchHelper
                .pageByFields(searchSource, indexEnum.getEsIndex());
        EsPageResult pageResult = ElasticsearchBuilder
                .buildPageResultWithHighlight(searchResponse, null, null);
        pageResult.setPageSize(query.getPageSize());
        return pageResult;
    }

    /**
     * 设置排序
     *
     * @param searchBuilder
     * @param sort                   排序字段:排序方式 eg: publishDate:desc
     * @param filterFunctionBuilders 自定义权重排序
     */
    public static void addSort(SearchSourceBuilder searchBuilder, String sort, List<FunctionScoreQueryBuilder.FilterFunctionBuilder> filterFunctionBuilders) {
        if (StringUtils.isBlank(sort) && CollectionUtils.isEmpty(filterFunctionBuilders)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(filterFunctionBuilders)) {
            FunctionScoreQueryBuilder functionScoreQueryBuilder =
                    QueryBuilders.functionScoreQuery(filterFunctionBuilders.toArray(new FunctionScoreQueryBuilder.FilterFunctionBuilder[0]));
            searchBuilder.postFilter(searchBuilder.query())
                    .query(functionScoreQueryBuilder)
                    .sort("_score", SortOrder.DESC);
        }
        if (StringUtils.isBlank(sort)) {
            return;
        }
        String[] sortSplit = sort.split(":");
        if (sortSplit != null && sortSplit.length == 2) {
            String field = sortSplit[0];
            String order = sortSplit[1].toLowerCase().trim();
            FieldSortBuilder fieldSortBuilder = new FieldSortBuilder(field);
            if (Objects.equals(order, "desc")) {
                fieldSortBuilder.order(SortOrder.DESC);
                fieldSortBuilder.missing("_last");
            } else if (Objects.equals(order, "asc")) {
                fieldSortBuilder.order(SortOrder.ASC);
                fieldSortBuilder.missing("_first");
            }
            searchBuilder.sort(fieldSortBuilder);
        }
    }

    /**
     * 构建一个SearchSourceBuilder
     *
     * @param query
     * @param pageNum
     * @param pageSize
     * @param includes
     * @param excludes
     * @param sort     排序字段:排序方式 eg: publishDate:desc
     * @return
     */
    public static SearchSourceBuilder buildSearchSource(QueryBuilder query, Integer pageNum, Integer pageSize,
                                                        String[] includes, String[] excludes, String sort) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(query);
        if (pageNum != null && pageSize != null) {
            searchSourceBuilder.from((pageNum - 1) * pageSize);
            searchSourceBuilder.size(pageSize);
        }
        searchSourceBuilder.fetchSource(includes, excludes);
        addSort(searchSourceBuilder, sort, null);
        searchSourceBuilder.trackTotalHits(true);
        return searchSourceBuilder;
    }

    /**
     * 创建受权限控制的基础查询
     *
     * @param chainId
     * @param chainNodeId
     * @param cityId
     * @param areaId
     * @param auth
     * @return
     */
    public static BoolQueryBuilder buildAuthQuery(String chainId, String chainNodeId, String cityId, String areaId, boolean auth) {
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(chainId)) {
            boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
        }
        if (StringUtils.isNotBlank(chainNodeId)) {
            boolQuery.filter(QueryBuilders.termQuery("chain_node.id", chainNodeId));
        }
        final String targetCityId = auth ? RequestContext.getCityId() : cityId,
                targetAreaId = auth ? RequestContext.getAreaId() : areaId;
        if (StringUtils.isNotBlank(targetCityId)) {
            boolQuery.filter(QueryBuilders.termQuery("city.id", targetCityId));
        }
        if (StringUtils.isNotBlank(targetAreaId)) {
            boolQuery.filter(QueryBuilders.termQuery("area.id", targetAreaId));
        }
        return boolQuery;
    }

    public static BoolQueryBuilder buildAuthQueryTimeLimit(String chainId, String chainNodeId, String cityId, String areaId, boolean auth) {
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(chainId)) {
            boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
        }
        if (StringUtils.isNotBlank(chainNodeId)) {
            boolQuery.filter(QueryBuilders.termQuery("chain_node.id", chainNodeId));
        }
        final String targetCityId = auth ? RequestContext.getCityId() : cityId,
                targetAreaId = auth ? RequestContext.getAreaId() : areaId;
        if (StringUtils.isNotBlank(targetCityId)) {
            boolQuery.filter(QueryBuilders.termQuery("city.id", targetCityId));
        }
        if (StringUtils.isNotBlank(targetAreaId)) {
            boolQuery.filter(QueryBuilders.termQuery("area.id", targetAreaId));
        }

        boolQuery.must(QueryBuilders.rangeQuery("financing_time").gte("2023-01-01"));

        return boolQuery;
    }

    /**
     * 展示前几位分类计数，其余分类统一计入“其他”
     *
     * @param countMap
     * @param topLimit 默认展示前五
     * @param unit
     * @return
     */
    public static List<CommonIndexBO> dealWithTopDistribution(Map<String, Long> countMap, Integer topLimit, String unit) {
        List<CommonIndexBO> boList = new ArrayList<>();
        if (topLimit == null) {
            topLimit = 5;
        }
        int index = 0, otherNum = Math.toIntExact(countMap.containsKey("其他") ? countMap.get("其他") : 0);
        countMap.remove("其他");
        for (Map.Entry<String, Long> count : countMap.entrySet()) {
            if (index < topLimit) {
                boList.add(new CommonIndexBO(count.getKey(), count.getValue(), unit));
                index++;
            } else if (index >= topLimit && index < countMap.size()) {
                otherNum += count.getValue();
                index++;
            }
        }
        if (otherNum != 0) {
            boList.add(new CommonIndexBO("其他", otherNum, unit));
        }
        return boList;
    }

    public static List<FunctionScoreQueryBuilder.FilterFunctionBuilder> buildCustomSortRule(String index, String nodeId) {
        List<FunctionScoreQueryBuilder.FilterFunctionBuilder> builders = new ArrayList<>();
        if (EsIndexEnum.COMPANY.getEsIndex().equals(index)) {
            if (StringUtils.isEmpty(nodeId)) {
                builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.rangeQuery("field_rank"), ScoreFunctionBuilders.fieldValueFactorFunction("field_rank")));
            } else {
                String idOrCode = "def total = 0.0;for(item in params['_source']['node_rank_list']){if(params['node_id'] == item['chain_node_id']){total=item['value'];}}return total;";
                Map<String, Object> params = new HashMap<>();
                params.put("node_id", nodeId);
                builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(ScoreFunctionBuilders.scriptFunction(new Script(Script.DEFAULT_SCRIPT_TYPE, "painless", idOrCode, Collections.emptyMap(), params))));
            }
            // XXX合作社置后
            builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.matchPhraseQuery("name", "合作社"), ScoreFunctionBuilders.weightFactorFunction(0)));
        } else if (EsIndexEnum.EXPERT.getEsIndex().equals(index)) {
            builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(ScoreFunctionBuilders.fieldValueFactorFunction("field_rank")));
        } else if (EsIndexEnum.POLICY.getEsIndex().equals(index)) {
        } else if (!EsIndexEnum.NEWS.getEsIndex().equals(index)) {
            builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.existsQuery("chain.id"), ScoreFunctionBuilders.weightFactorFunction(10)));
        }
        return builders;
    }

    /**
     * ES分页条件限制
     *
     * @param pageNum
     * @param pageSize
     * @param limitPageSize
     */
    public static void checkPageRange(Integer pageNum, Integer pageSize, Integer limitPageSize) {
        if (pageNum == null || pageSize == null) {
            throw new BusinessException("缺少必要查询条件");
        }
        if (pageNum <= 0 || pageSize <= 0) {
            throw new BusinessException("分页参数不能为负数");
        }
        if (limitPageSize == null) {
            limitPageSize = 20;
        }
        if (pageNum * pageSize >= 10000 || pageSize > limitPageSize) {
            throw new BusinessException("超出搜索范围，请调整搜索条件");
        }
    }

    /**
     * 仅保留指定的产业链节点信息
     *
     * @param dataList
     * @param chainId
     */
    public static void filterChainByChainId(List<Map<String, Object>> dataList, String chainId) {
        if (StringUtils.isEmpty(chainId) || CollectionUtils.isEmpty(dataList)) {
            return;
        }
        for (Map<String, Object> info : dataList) {
            List<Map<String, Object>> chainList = (List<Map<String, Object>>) info.get("chain");
            if (CollectionUtils.isEmpty(chainList)) {
                continue;
            }
            chainList.removeIf(c -> !chainId.equals(c.get("id")));
            List<Map<String, Object>> chainNodeList = (List<Map<String, Object>>) info.get("chain_node");
            if (CollectionUtils.isEmpty(chainNodeList)) {
                continue;
            }
            chainNodeList.removeIf(n -> !chainId.equals(n.get("chain_id")));
        }

    }

    /**
     * 构建向量搜索条件
     *
     * @param vectors
     * @param preQueryBuilder
     * @return
     */
    public static QueryBuilder buildVectorSearch(List<Double> vectors, String idOrCode, QueryBuilder preQueryBuilder) {
        //构建脚本查询
        Script script = new Script(
                Script.DEFAULT_SCRIPT_TYPE,
                "painless",
                //pic_vector - 搜索字段
                idOrCode,
                new HashMap<String, Object>() {{
                    put("queryVector", vectors);
                }});
        if (preQueryBuilder == null) {
            return QueryBuilders.scriptScoreQuery(preQueryBuilder, script).setMinScore(0.4f);
        } else {
            return QueryBuilders.scriptScoreQuery(preQueryBuilder, script).setMinScore(0.4f);
        }
    }

    /**
     * 找技术-构建向量搜索条件
     *
     * @param vectors
     * @param preQueryBuilder
     * @param vectorColumn
     * @return
     */
    public static QueryBuilder buildKeywordVectorSearch(List<Double> vectors, QueryBuilder preQueryBuilder, String vectorColumn) {
        String idOrCode = "cosineSimilarity(params.queryVector, '" + vectorColumn + "') + 1.0";
        //构建脚本查询
        Script script = new Script(
                Script.DEFAULT_SCRIPT_TYPE,
                "painless",
                //pic_vector - 搜索字段
                idOrCode,
                new HashMap<String, Object>() {{
                    put("queryVector", vectors);
                }});
        BoolQueryBuilder existsQuery = QueryBuilders.boolQuery();
        existsQuery.filter(QueryBuilders.existsQuery(vectorColumn));
        if (preQueryBuilder == null) {
            return QueryBuilders.scriptScoreQuery(existsQuery, script).setMinScore(1.0f);
        } else {
            existsQuery.filter(preQueryBuilder);
            return QueryBuilders.scriptScoreQuery(existsQuery, script).setMinScore(1.0f);
        }
    }
}
