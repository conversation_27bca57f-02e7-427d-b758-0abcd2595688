package com.quantchi.nanping.innovation.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.quantchi.nanping.innovation.demand.model.DemandDTO;

import javax.swing.filechooser.FileSystemView;
import java.io.File;
import java.text.SimpleDateFormat;

/**
 * 需求excel导入字段
 * <AUTHOR>
 * @date 2024/6/11 16:25
 */
public class DemandImportUtil {


//    public static void main(String[] args) {
//        // C:\Users\<USER>\Desktop
//        File homeDirectory = FileSystemView
//                .getFileSystemView().getHomeDirectory();
//        String fileName = "test";
//        //  读取桌面路径excel文件
//        String file = homeDirectory.getAbsolutePath()
//                + File.separator + fileName + ".xlsx";
//        EasyExcel.read(file, DemandDTO.class,
//                new PageReadListener<DemandDTO>(dataList -> {
//                    for (DemandDTO goodsDto : dataList) {
//                        System.out.println("读取到excel数据：" +
//                                JSON.toJSONString(goodsDto));
//                    }
//                })).sheet().doRead();
//    }
}
