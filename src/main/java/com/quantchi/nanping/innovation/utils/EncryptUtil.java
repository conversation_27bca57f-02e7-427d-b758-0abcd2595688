package com.quantchi.nanping.innovation.utils;

import cn.hutool.crypto.SecureUtil;
import com.kind.crypto.common.ConfigBean;
import com.kind.crypto.crypto.CryptoService;
import com.kind.crypto.entity.CryptResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27 11:15
 */
@Slf4j
public class EncryptUtil {

    /**
     * 加密
     *
     * @param data
     * @return
     */
    public static String encryptSM4FixIndex(String data, Integer encryptIndex) {
        if (SpringUtils.getActiveProfile().startsWith("testk8s") || SpringUtils.getActiveProfile().startsWith("dev")){
            return StringUtils.EMPTY;
        }
        if (StringUtils.isEmpty(data)){
            return null;
        }
        if(encryptIndex == null){
            encryptIndex = ConfigBean.getIntVal("kind.common.encrypt.keyIndex");
        }
        try{
            CryptResult cryptResult = new CryptoService().encryptSM4FixIndex(encryptIndex, data.getBytes());
            if (cryptResult.getSuccess()) {
                return new String(cryptResult.getData());
            }else{
                log.error("加密失败：{}", cryptResult.getData());
            }
        }catch(Exception e){
            log.error("加密：{}异常", data, e);
        }
        return data;
    }

    /**
     * 解密
     *
     * @param encryptedData
     * @return
     */
    public static String decryptSM4FixIndex(String encryptedData, Integer encryptIndex) {
        if (StringUtils.isEmpty(encryptedData)){
            return null;
        }
        try{
            CryptResult cryptResult = new CryptoService().decryptSM4FixIndex(encryptIndex, encryptedData.getBytes());
            if (cryptResult.getSuccess()) {
                return new String(cryptResult.getData());
            }else{
                log.error("解密失败：{}", cryptResult.getData());
            }
        }catch(Exception e){
            log.error("解密：{}异常", encryptedData, e);
        }
        return "(数据解密失败，请联系管理员)";
    }

//    public static void main(String[] args) {
//        String s = EncryptUtil.encryptSM4FixIndex(SecureUtil.md5(AESUtil.decrypt("H4qr9felXe59XkroTnZWqw==")), 3);
//        System.out.println(s);
//    }
}
