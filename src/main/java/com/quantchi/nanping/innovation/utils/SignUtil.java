package com.quantchi.nanping.innovation.utils;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.kind.crypto.common.ConfigBean;
import com.kind.crypto.entity.SignResult;
import com.kind.crypto.sign.SignService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 加签、验签工具
 *
 * <AUTHOR>
 * @date 2023/11/22 10:17
 */
@Component
@Slf4j
public class SignUtil {

    /**
     * 加签
     *
     */
    public static Pair<String,String> signData(String data) {
        if (SpringUtils.getActiveProfile().startsWith("testk8s") || SpringUtils.getActiveProfile().startsWith("dev")){
            return new ImmutablePair<>(StringUtils.EMPTY, StringUtils.EMPTY);
        }
        int keyIndex = Integer.parseInt(ConfigBean.getStringVal("kind.common.svs.keyIndex"));
        String keyPwd = ConfigBean.getStringVal("kind.common.svs.keyPwd");
        try {
            SignResult signResult = new SignService().signData(keyIndex, keyPwd, data);
            if (signResult.getSuccess()) {
                return new ImmutablePair<>(Base64.encode(signResult.getSign()), Base64.encode(signResult.getPubKeyCert()));
            }
        } catch (Exception e) {
            log.error("加签:{}发生异常", data, e);
        }
        return new ImmutablePair<>(StringUtils.EMPTY, StringUtils.EMPTY);
    }

//    public static String signData(String data) {
//        int keyIndex = Integer.parseInt(ConfigBean.getStringVal("kind.common.svs.keyIndex"));
//        String keyPwd = ConfigBean.getStringVal("kind.common.svs.keyPwd");
//        try {
//            SignResult signResult = new SignService().signData(keyIndex, keyPwd, data);
//            if (signResult.getSuccess()) {
//                return Base64.encode(signResult.getSign());
//            }
//        } catch (Exception e) {
//            log.error("加签:{}发生异常", data, e);
//        }
//        return StringUtils.EMPTY;
//    }

    /**
     * 验签
     *
     * @param sign
     * @param data
     * @return
     */
    public static boolean verifySignedData(String sign, String data, String pubKeyCert) {
        try {
            log.info("验签sign:{}，data:{}", sign, data);
            SignResult signResult = new SignService().verifySignedData(data, sign, pubKeyCert);
            return signResult.getSuccess();
        } catch (Exception e) {
            log.error("验签sign:{}，data:{}发生异常", sign, data, e);
        }
        return false;
    }

//    public static boolean verifySignedData(String sign, String data) {
//        String pubKeyCert = ConfigBean.getStringVal("pubKeyCert");
//        try {
//            log.info("验签sign:{}，data:{}", sign, data);
//            SignResult signResult = new SignService().verifySignedData(data, sign, pubKeyCert);
//            return signResult.getSuccess();
//        } catch (Exception e) {
//            log.error("验签sign:{}，data:{}发生异常", sign, data, e);
//        }
//        return false;
//    }

    /**
     * CA证书认证
     *
     * @param data
     * @param sign
     * @param pubKeyCert
     * @return 证书序列号
     */
    public static String verifySignedDataByWeb(String data, String sign, String pubKeyCert) {
        try {
            SignResult signResult = new SignService().verifySignedDataByWeb(data, sign, pubKeyCert);
            if (signResult.getSuccess()) {
                return signResult.getSerialNum();
            }
        } catch (Exception e) {
            log.error("CA证书认证失败,sign:{},data:{},pubKeyCert:{}发生异常", sign, data, pubKeyCert, e);
        }
        return null;
    }

    /**
     * 批量验签
     *
     * @param signDataMap<签名，<原始数据，加签证书>>
     * @return
     */
    public static boolean batchVerifySignedData(Map<String, Pair<String, String>> signDataMap) {
        List<String> errorDataList = new ArrayList<>();
        for (Map.Entry<String, Pair<String, String>> entry : signDataMap.entrySet()) {
            if (!verifySignedData(entry.getKey(), entry.getValue().getLeft(), entry.getValue().getRight())) {
                errorDataList.add(entry.getValue().getLeft());
            }
        }
        if (CollectionUtils.isNotEmpty(errorDataList)) {
            log.error("用户数据发生篡改，数据：{}", StringUtils.join("!", errorDataList));
            return false;
        }
        return true;
    }

    /**
     * 获得CA认证随机数
     *
     * @return
     */
    public static String getSignRandom() {
        SignResult signResult = new SignService().getSignRandom(6);
        if (signResult.getSuccess()){
            return signResult.getRandom();
        }
        return null;
    }
}
