package com.quantchi.nanping.innovation.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/3 15:01
 */
public class DecimalConvertUtil {

    public static String bigDecimal2String(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.stripTrailingZeros().toString();
    }

    public static Integer bigDecimal2Integer(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.intValue();
    }
}
