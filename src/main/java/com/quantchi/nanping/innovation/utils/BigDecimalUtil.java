package com.quantchi.nanping.innovation.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2023/3/3 15:01
 */
public class BigDecimalUtil {

    public static String bigDecimal2String(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.stripTrailingZeros().toString();
    }

    public static Integer bigDecimal2Integer(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.intValue();
    }

    public static BigDecimal divide(Long b1, Long b2, int scale, RoundingMode mode){
        return divide(new BigDecimal(b1), new BigDecimal(b2), scale, mode);
    }

    public static BigDecimal divide(BigDecimal b1, BigDecimal b2, int scale, RoundingMode mode){
        if (b1 == null || b2 == null){
            return null;
        }
        if (mode == null){
            mode = RoundingMode.HALF_UP;
        }
        if (b2.compareTo(new BigDecimal(0)) == 0){
            return new BigDecimal(0).setScale(scale, mode);
        }
        return b1.divide(b2, scale, mode);
    }

    public static BigDecimal subtract(BigDecimal b1, BigDecimal b2) {
        if (b1 == null && b2 == null){
            return BigDecimal.ZERO;
        }
        if (b1 == null){
            b1 = BigDecimal.ZERO;
        }
        if (b2 == null){
            b2 = BigDecimal.ZERO;
        }
        return b1.subtract(b2);
    }

    public static BigDecimal multiply(BigDecimal b1, BigDecimal b2) {
        if (b1 == null || b2 == null){
            return null;
        }
        return b1.multiply(b2);
    }

}
