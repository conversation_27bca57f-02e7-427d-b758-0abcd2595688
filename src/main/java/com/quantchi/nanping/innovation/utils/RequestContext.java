package com.quantchi.nanping.innovation.utils;

import java.util.*;

/**
 * 请求上下文
 *
 * <AUTHOR>
 * @date 2022/9/30 14:06
 */
public class RequestContext {

    public static final String CITY = "cityId";
    public static final String AREA = "areaId";
    public static final String IP = "ip";

    private final static ThreadLocal<Map<String, Object>> CONTEXT = ThreadLocal.withInitial(() -> new HashMap<>());

    private RequestContext() {
    }

    /**
     * 设置上下文属性
     *
     * @param name
     * @param val
     */
    public static void setAttribute(final String name, final Object val) {
        Map<String, Object> map = CONTEXT.get();
        if (map == null) {
            map = new HashMap<>();
            CONTEXT.set(map);
        }
        map.put(name, val);
    }

    /**
     * 获取上下文属性
     *
     * @param name
     * @return
     */
    public static Object getAttribute(final String name) {
        Map<String, Object> map = CONTEXT.get();
        return map != null ? map.get(name) : null;
    }

    /**
     * 清空上下文
     */
    public static void remove() {
        CONTEXT.remove();
    }

    /**
     * 获取区域id
     *
     * @return
     */
    public static String getRegionId() {
        Map<String, Object> map = CONTEXT.get();
        if (map == null) {
            return null;
        }
        return (String) (map.containsKey(AREA) && map.get(AREA) != null ? map.get(AREA) : map.get(CITY));
    }

    /**
     * 获取城市id
     *
     * @return
     */
    public static String getCityId() {
        Map<String, Object> map = CONTEXT.get();
        if (map == null) {
            return null;
        }
        return map != null ? (String) map.get(CITY) : null;
    }

    /**
     * 获取区县id
     *
     * @return
     */
    public static String getAreaId() {
        Map<String, Object> map = CONTEXT.get();
        if (map == null) {
            return null;
        }
        return map != null ? (String) map.get(AREA) : null;
    }

}
