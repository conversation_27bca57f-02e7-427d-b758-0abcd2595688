package com.quantchi.nanping.innovation.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <p>Title:HttpClientUtils.java</p>
 * <p>Company: quant-chi</p>
 * <p>Description:</p>
 *
 * @author:maxj
 * @date: 2019年4月24日
 */
@Slf4j
public class HttpClientUtils {


    public static HttpEntity execute(CloseableHttpClient client, CloseableHttpResponse response, HttpRequestBase base) {
        setTimeout(base);
        try {
            response = client.execute(base);
            int status = response.getStatusLine().getStatusCode();
            if (status == 200) {
                return response.getEntity();
            } else {
                log.error(base.getURI() + " 请求出错!响应码：" + status);
            }
        } catch (Exception e) {
            log.error(base.getURI() + " 请求出错!", e);
        }
        return null;
    }

    public static void setTimeout(HttpRequestBase base) {
        // 4.3设置超时
        RequestConfig config = RequestConfig.custom().setSocketTimeout(600000).setConnectTimeout(600000).build();
        base.setConfig(config);
    }

    public static void close(CloseableHttpClient client, CloseableHttpResponse response) {
        try {
            if (client != null) {
                client.close();
            }
        } catch (IOException e) {
            log.error("HttpClient关闭失败!", e);
        }
        try {
            if (response != null) {
                response.close();
            }
        } catch (IOException e) {
            log.error("HttpResponse关闭失败!", e);
        }
    }

    /**
     * post方式请求JSON字符串
     */
    public static String postForJson(String uri, JSONObject json, Map<String, String> headers) {
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = null;
        try {
            // 创建一个信任所有证书的SSLContext
            SSLContextBuilder builder = new SSLContextBuilder();
            builder.loadTrustMaterial(null, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            });
            SSLContext sslContext = builder.build();
            // 设置SSLContext
            httpClient = HttpClients.custom()
                    .setSSLContext(sslContext)
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .setConnectionTimeToLive(180, TimeUnit.SECONDS)
                    .build();
            HttpPost post = new HttpPost(uri);
            StringEntity stringEntity = new StringEntity(json.toString(), "utf-8");
            stringEntity.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
            post.setEntity(stringEntity);
            post.setHeader("Content-Type", "application/json");
            if (headers != null && headers.size() > 0) {
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    post.setHeader(header.getKey(), header.getValue());
                }
            }
            HttpEntity entity = execute(httpClient, response, post);
            if (entity != null) {
                try {
                    String result = EntityUtils.toString(entity, "utf-8");
                    log.debug("request url = " + uri + ",params = " + json
                            + ",result= " + result);
                    return result;
                } catch (Exception e) {
                    log.error("request url = " + uri + ",params = " + json + ",error= ");
                    throw new RuntimeException(e);
                }
            }
        } catch (Exception e) {
            log.error("request url = " + uri + ",params = " + json + ",error= ");
            throw new RuntimeException(e);
        } finally {
            close(httpClient, response);
        }

        return null;
    }

    /**
     * 发送请求HTTP-POST请求 url:请求地址; entity:json格式请求参数
     */
    public static String post(String url, Object params) {
        try {
            String entity = "";
            if (params != null) {
                entity = JSONObject.toJSONString(params);
            }
            long timestamp = System.currentTimeMillis();
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("timestamp", timestamp + "");
            httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(entity)) {
                StringEntity se = new StringEntity(entity, "UTF-8");
                httpPost.setEntity(se);
            }
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity1 = response.getEntity();
            String resStr = null;
            if (entity1 != null) {
                resStr = EntityUtils.toString(entity1, "UTF-8");
            }
            httpClient.close();
            response.close();
            log.info("请求地址：" + url + "，请求参数：" + entity + "，返回结果：" + resStr);
            return resStr;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 表单形式的post请求
     *
     * @param url
     * @param params
     * @return
     */
    public static String postParams(String url, Map<String, String> params) {
        //创建自定义的httpclient对象
        CloseableHttpClient client = HttpClients.createDefault();

        HttpPost post = new HttpPost(url);
        CloseableHttpResponse res = null;
        try {
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            Set<String> keySet = params.keySet();
            for (String key : keySet) {
                nvps.add(new BasicNameValuePair(key, params.get(key)));
            }
            post.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));
            res = client.execute(post);
            HttpEntity entity = res.getEntity();
            String result = EntityUtils.toString(entity, "utf-8");
            log.info("请求" + url + "参数:" + params + "返回结果:" + result);
            return result;
        } catch (Exception e) {
            log.error("请求" + url + "发生异常:", e);
        } finally {
            try {
                res.close();
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 发送multipart表单请求，支持Spring MultipartFile上传
     *
     * @param url 请求地址
     * @param fileParams MultipartFile参数，key为参数名，value为MultipartFile对象
     * @param stringParams 字符串参数，key为参数名，value为参数值
     * @param headers 请求头信息
     * @return 响应结果字符串
     */
    public static String postMultipart(String url, Map<String, MultipartFile> fileParams,
                                       Map<String, String> stringParams,
                                       Map<String, String> headers) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            // 创建一个信任所有证书的SSLContext
            SSLContextBuilder contextBuilder = new SSLContextBuilder();
            contextBuilder.loadTrustMaterial(null, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            });
            SSLContext sslContext = contextBuilder.build();
            // 设置SSLContext
            httpClient = HttpClients.custom()
                    .setSSLContext(sslContext)
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .setConnectionTimeToLive(180, TimeUnit.SECONDS)
                    .build();

            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }

            // 使用MultipartEntityBuilder构建多部分实体
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();

            // 添加文件参数
            if (fileParams != null && !fileParams.isEmpty()) {
                for (Map.Entry<String, MultipartFile> entry : fileParams.entrySet()) {
                    final String paramName = entry.getKey();
                    final MultipartFile file = entry.getValue();
                    if (file != null && !file.isEmpty()) {
                        builder.addBinaryBody(paramName, file.getInputStream(),
                                ContentType.APPLICATION_OCTET_STREAM, file.getOriginalFilename());
                        log.debug("添加文件参数：{}，文件名：{}", paramName, file.getOriginalFilename());
                    }
                }
            }

            // 添加字符串参数
            if (stringParams != null && !stringParams.isEmpty()) {
                for (Map.Entry<String, String> entry : stringParams.entrySet()) {
                    final String paramName = entry.getKey();
                    final String paramValue = entry.getValue();
                    if (paramValue != null) {
                        builder.addTextBody(paramName, paramValue, ContentType.TEXT_PLAIN.withCharset("UTF-8"));
                        log.debug("添加字符串参数：{}={}", paramName, paramValue);
                    }
                }
            }

            // 设置请求实体
            HttpEntity reqEntity = builder.build();
            httpPost.setEntity(reqEntity);

            // 执行请求
            setTimeout(httpPost);
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    String responseString = EntityUtils.toString(resEntity, "UTF-8");
                    log.info("请求地址：{}，文件参数：{}，字符串参数：{}，响应状态：{}，响应结果：{}",
                            url,
                            fileParams,
                            stringParams,
                            statusCode,
                            responseString);
                    return responseString;
                }
            } else {
                log.error("请求失败，地址：{}，状态码：{}", url, statusCode);
            }
        } catch (Exception e) {
            log.error("执行multipart请求发生异常，地址：" + url, e);
        } finally {
            close(httpClient, response);
        }
        return null;
    }


    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 判断是否为PC端
     *
     * @param request
     * @return
     */
    public static boolean isPC(HttpServletRequest request) {
        String userAgent = request.getHeader("user-agent");
        if (userAgent.indexOf("Android") != -1 || userAgent.indexOf("iPhone") != -1 || userAgent.indexOf("iPad") != -1) {
            return false;
        }
        return true;
    }

    /**
     * get请求
     *
     * @param url
     * @param headers
     * @return
     */
    public static String get(String url, Map<String, String> headers) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        HttpGet get = new HttpGet(url);
        try {
            if (headers != null && headers.size() > 0) {
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    get.setHeader(header.getKey(), header.getValue());
                }
            }
            HttpEntity entity = execute(httpClient, response, get);
            if (entity != null) {
                try {
                    String result = EntityUtils.toString(entity, "utf-8");
                    log.debug("request url = " + url + ",params = " + JSONObject.toJSONString(headers)
                            + ",result= " + result);
                    return result;
                } catch (Exception e) {
                    log.error("request url = " + url + ",params = " + JSONObject.toJSONString(headers) + ",error= "
                            + e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("request url = " + url + ",params = " + JSONObject.toJSONString(headers) + ",error= "
                    + e.getMessage());
        } finally {
            close(httpClient, response);
        }
        return null;
    }
}
