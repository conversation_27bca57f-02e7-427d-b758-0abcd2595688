package com.quantchi.nanping.innovation.utils;

import cn.dev33.satoken.exception.NotLoginException;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.security.SecureRandom;
import java.util.Objects;

@Slf4j
public class AesEncryptUtils {
//    private static final String KEY = "U0Xg55zo9-TglUjxvBG_Dac_2NAsAyQc";
    private static final String KEY = "U0Xg55zo9-TglUjxvBG_Dac_2NAsAyQc";
    //参数分别代表 算法名称/加密模式/数据填充方式
    private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";

    /**
     * 加密
     * @param content 加密的字符串
     * @param encryptKey key值
     * @return
     * @throws Exception
     */
    public static String encrypt(String content, String encryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));
        byte[] b = cipher.doFinal(content.getBytes("utf-8"));
        // 采用base64算法进行转码,避免出现中文乱码
        return Base64.encodeBase64String(b);

    }

    /**
     * 解密
     * @param encryptStr 解密的字符串
     * @param decryptKey 解密的key值
     * @return
     * @throws Exception
     */
    public static String decrypt(String encryptStr, String decryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));
        // 采用base64算法进行转码,避免出现中文乱码
        byte[] encryptBytes = Base64.decodeBase64(encryptStr);
        byte[] decryptBytes = cipher.doFinal(encryptBytes);
        return new String(decryptBytes);
    }

    public static String encrypt(String content) throws Exception {
        return encrypt(content, KEY);
    }
    public static String decrypt(String encryptStr) throws Exception {
        return decrypt(encryptStr, KEY);
    }


    /**
     * 加密
     * @param content 加密的字符串
     * @param encryptKey key值
     * @return
     * @throws Exception
     */
    public static String zwEncrypt(String content, String encryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
        random.setSeed(encryptKey.getBytes());
        kgen.init(128, random);
        SecretKey secretKey = kgen.generateKey();
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secretKey.getEncoded(), "AES"));
        byte[] b = cipher.doFinal(content.getBytes("utf-8"));
        // 采用base64算法进行转码,避免出现中文乱码
        return Base64.encodeBase64String(b);

    }

    /**
     * 解密
     * @param encryptStr 解密的字符串
     * @param decryptKey 解密的key值
     * @return
     * @throws Exception
     */
    public static String zwDecrypt(String encryptStr, String decryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
        random.setSeed(decryptKey.getBytes());
        kgen.init(128, random);
        SecretKey secretKey = kgen.generateKey();
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(secretKey.getEncoded(), "AES"));
        // 采用base64算法进行转码,避免出现中文乱码
        byte[] encryptBytes = Base64.decodeBase64(encryptStr);
        byte[] decryptBytes = cipher.doFinal(encryptBytes);
        return new String(decryptBytes);
    }

//    public static void main(String[] args) throws Exception {
////        String content = "1q2w3e4r!@#";
////        String content = "420984199707264022";
////        System.out.println("加密前：" + content);
////
////        String encrypt = encrypt(content, KEY);
////        System.out.println("加密后：" + encrypt);
////        System.out.println("解密前：" + encrypt);
////
////        String decrypt1 = decrypt(encrypt, KEY);
////        System.out.println("解密后：" + decrypt1);
//
//
//        try {
////            BufferedReader re = new BufferedReader(new FileReader("E:\\桌面\\信息.txt"));
////            StringBuilder stringBuilder = new StringBuilder();
////            String line ;
////            while ((line = re.readLine())!=null){
////                stringBuilder.append(line);
////            }
////            String longString = stringBuilder.toString();
////
////
////            String decrypt = decrypt(longString, KEY);
////            System.out.println("解密后：" + decrypt);
//
//            String userInfoStr = "Py0Hz54GLZT6EyRb1hmOXguLnv6r9vYHpOialJ2ErtARIynKWOii+VR8t3w/PrlXcGAg3Q22mBVw626/q4TCBfJi4FwPLVeXldTvIRUIZ9hRLL0XH/QdSVDG3eGh7Smgq1wrsiAGJLQ9U33Pb6G3jr88y9VVEAzj0roJ6EXBFcH2jwotxM3PX1U5TBKhPkiuSeqRiZm2qSEo1St9Gu2Ndv2tfUEEdBbDwsTtolQeOPTVWzYN51EkuYwdoomPZgRC9uRNlYZCq4pmYFKjYo9Dl5Usz8QEqXpaNPIKNcff8k7O6THli+0au54G+3oacFeoeXYTi1bjntFGWkkIMI2Chc8AaqdYxzMtdXLK9Xi1PkblWxTS7MNP5yYHgWFDxJXvv1GB5LFiV+L0uzZ+8Y0h2TgWimJwKQbEDDXg5f8KpBxrIrjBnkE5XLL4l4IFCXAoUX1dMCowZpdzlQr7i1pJN8YsamvKtIh3gryrtfVkaqpQ4NjQdOFEqWNLJJbOFz6e7revNEUOJ9TpfJ9DVmVW+M3Zp/DMYrvEG2+d1ggWSFEuxKJdJ7VsRFvtkd1Dax1q2KfDzzVpkKwc6pf3Gal9cVIMEQmJcDDJz68ZaaYawCb7En1lXDcnu222a2P5ZJq2It6XYoq5rol5QElxYW5xcDnFS331Qnqk4F0jM/Qau58oKPn8+Ht97nS1rGXtKC3DDd+R7MAIXIT4HKVW4v8u7+G7vnnuVSKgYaSKmz7vHUDuTTIIJPQqqdfVZA0/+FQlhy5WWYrvJUhvTqNcfbQf76yj60ix00XWqvN7TeBbHOj8+7kr9oaHO9/yrR/hlPKTGvzxbzv0vt1csIf15JfT1U7oc3tMxGeflAvN73Gd//cJ+3yr0keOyHiqc0bPi7UNXK9zVm1RCWSRk9Oytc8lAtjyWQV2\n" +
//                    "N/yWIx+VthjIfn+uEeqMw86oDWEWQyg/r56jKRvCZrfOwnz29ySHIWBSmAZ88w2gedGLTvlvltFslxDF1HryGCZtWPCHdDMVYY/wv625nYB+AVlk3UE9ESHnW2Uk/kmv+z/nTWQUzEZQ+JYgOO3VUTd1iL4WFbUtAjVfydXcd24sAt+QlAFN2VJKVq3y1mqGh0ydBbG7+R5FcTHxJitWbfhNu9PHDCIMdC3TB9HklUy8lULuyy/t5N8ikSNo6rLMvyfp6k9kanwrDc+QOkZjUG9srDZ0sl7vGH689a5NMkms8EAUcu5Ou8PE8d2TAr4HW2zz3U4uTkLTvurwrFYZ5eodvrOkWmZ54hLkxlBr+5JJAXkOljytxYjWhOVM9PcijcVxJ2CMPDtQTEOVQXfBr6gRaK0/dktISY+pg3L12+cfMay1OM/yCx6YMTEw+XPjFDEvNJO9xeTa7zpTdz71wKZjNxX3LJwt94ANinULF3v2TK7qrqIdNFTkqcKZ+9eR6RcVio72fDdu8s+PjeDU5bGP5PmVCNdJE256qqKYtdH1htC6RKf+Wc9nEcJWQHKigwFMk0HmZCxZBbES8xMN9qO5LySZW8vqoEiAJUw88DdFvbjA8HK4F095J7NymVmKHK40+UaZBmF1MskidBrgo5up0bVZ3Lgz00E9ciIsqnx96lkx+AUxzNXNCfexSVoKQ/fbZ+i3tVyCHWZRZOGc8M/2lNqppm+TboOIC+vR9Inrb//XrlEwK4U9wWChsI4Gjvk1bpc0hHZ/1oi8DA+5OIE5V3RdXrob28Q1wLFz+UkvCNDHZDRmnyOAT7LJfBEL6ndKjb7NXv6aezCCeNGOCDHzn6UjV0xAfGnk+MWKnEX659K+TzmcvrYAYEQJyNd2MpaD6NPCbClt/tJXAirduEAcqa7Z8dDKJI8hljKk87Bs4E7rctYLqofqKcqfrrEQYQMYKapIGe0Ebc8R8ILF5dpH93PP+1ZNRO66LrLiq3z8AjHYupoya95XIrjho/471H5on9PCddLqOGXWq0lTt/P3cUGfUSdZsZFP3ijxeKBTjfgMy9PNDeHHskPHEYg0A3esdSfZobxNSNZuBF2kPSLUgrtCoNku+DkpKOlVfrZoA5v+fDr/lsM9QwHklWIM2xHmTQce/bUCvGJxjK7IRMIfg/Ulz7pemzPEc7UeHq7IKOstHb9BTXAe91I0DnbRZZQcT3LPh2gqN8qUP7Xwk+apw8ObtbVG04AZqV5E3mTupZImY+khFtH12ZJNdbtbJggOf4obZSretKDv7PuWelJoIggpuTfDTjTN0ktuwJdKzs/brxenV0uXk/VPI3YL4ZAS8yoS/CGVYnKbdCU+YKDGH+yl1XPCWzhABGAaTh3uzgLJE0pmqz6pE2olxRB3sMMa6X/4NSyuN3nTnasPSgL9JIL87hM2qysadaIaQA+myM51dKJFbM9CKa2/QgeIsiA6y/5Dp/7b9xy4sli8HagE8Qmbu/GS0y0XTo3ohvhHv7huWJZjqATbxgszgb8Qt+LGWRLRJEBLjsj58VdToOMfgqE0X3eyq2Y4p+uiD2QUiAeGWu5lODIDTiM3slemRBftjtIM6Q6Xjm2Rsy2FDAKmBkaj4Md4HxaPbIib5XGMHYz4VzWwTz7Ql5WrUX7pRqFT1K5SyF/o/novJzbPO/9qBF1hP/zq/SAVgxSQ59rlNmMIJPr0cGnAUP0XbhWZT7gpigb4tF7ATFCdrZGltPCiDqnFp1gVoWQFu/cLRMWT71nzPO99Eaxvf1gpnVg6UhjcJAFosyz/b6lXdHzIlDryc08UvjlpCBoInUb4aN3BEThQuN1WYRwALiX+T8SLrZVMZfJNuyxCu/X/xGvtlsK3SgCmCqkHkyPBr9kIzDxEGJOU46XiryTBUUWhd41d64JMPFxsU5VkuZPH2fL2ZJ7H8blChetnHtpbKVyizeEhD8jsMw8Cjw3UZAU8LURHU4jlq426zjxGmyfXt2efN8maBpaTQ2TbPtrueI6GhWF4gIzPUoHLE39mEN4n1+eX\n" +
//                    "3+Dn/UqAOkt8ve13jlLfbRi9zsO6r8yasammz6KQ1YnC7PQg+MK/G1Ai/DXhuaR2iag2VXWHYqZvMc9VIWnsIlxnw2lQcFap3GJrzr6dR3hUflsmw/QePqd05lADtreH0Ge3k8T5qiSs0F0zs27vJRqzyUaNKIw50V7aPeKgAFESP+aJavJW34fpXUXfWyqnJYbgKS0FV0EY4ZWA5wfIWPtEDwke4wRULMdjFg+0tdLmBy66A55qUpMTLTPSR2IBiiVXpDkPgs2azm8gn30EoXqNYX6eTdRGJPLuHtELRjGbBj3S/n+PwwdAwCrn5zvwIXQRHeCARod53yOOrK0fVtzBo/8V8mJi1wFmvI+mLSZNXoO75xNdYvqkpm40Se1kiiGAnMlMAlZ2hb+S/hZOZt5JbQ+XjwzWYwgUbIllMr+IM/3RAfprfCFTL1yzfgDt63/zgyRQpA+p5ULoCf3g2R6z0+S6FK3kIDz4LLu8EgSrWVPofO/rvtOLlB/s8ZB5E2woOis/+Um6Si3t5qvmVWqgRqYAbsqAHwGmhXwIGXMzRNCBgpzqxiA4bWmlqDik7TEWgLdur529FJD+9prjIoENoIF+Gb/XuOEeOeT01CK3knsA2Gu7fE2tu7+Oloj094UW+4sInGQg9KvKbVlmr3H/PhoqIsWVHCFFNx9s0CFKUXZcCTn/yY+mupqLgRy1P27kfTHe6tjZw/KMuJRaWYhGOe4ce6zlW+isvgV1Ihithl7zGiMSosgypT59fafQ7FI8Mw5rL6TO61nuJk9f/Ke6i5foOptMd6iB6HjBGTmYF538UJzfjWOxd6Az8YW2VpaXCpabAebpO4NorZWFyasymL3Db71+rhJ37ANldLp4qJU4TESOuzI6tqC3sOgGiXumHW6DWszJSDxEG7h9YVkEHO9VAp2MPLENkJgVd9Py47hSKQXOCWZgMTDAI5CEnGjZTwBthSK+BU6uyAqCZ/6kGx8SLej2q1SpACXYjG8cKRcup5SQh2buXL2Z8u7YZNr/Bm3iEWkvYlKJ8nG8GKXbaD/PUqZ/kpwENKpIuXQJ/dNmxPLZNmCme+96Znq4GY728+/PYvTg6pQK+7rfc5GejQZZXdh0doEh9ftTVU0CuqWyPSJWVCL7KJctwT6ItOBsNR71SiO4+aY=";
//
//            userInfoStr = decrypt(userInfoStr, KEY);
//
//            JSONObject userInfoObj = JSONObject.parseObject(userInfoStr);
//
//            if (!userInfoObj.containsKey("data") || Objects.isNull(userInfoObj.get("data"))) {
//                log.error("获取user_info异常，闵政通返回结果user_info为空！userInfoStr: {}", userInfoStr);
//                throw new NotLoginException(NotLoginException.DEFAULT_MESSAGE, null, "500");
//            }
//
//            JSONObject userInfoData = userInfoObj.getJSONObject("data");
//
//            if (!userInfoData.containsKey("userInfo") || Objects.isNull(userInfoData.get("userInfo"))) {
//                log.error("获取userInfo异常，闵政通返回结果userInfo为空！userInfoData: {}", userInfoData);
//                throw new NotLoginException(NotLoginException.DEFAULT_MESSAGE, null, "500");
//            }
//
//            log.info("userInfoData: {}", userInfoData.getJSONObject("userInfo"));
//
//            JSONObject userInfo = userInfoData.getJSONObject("userInfo");
//
//            UserInfoEntity userInfoEntity = new UserInfoEntity();
//            userInfoEntity.setId(userInfo.getString("id"));
//            userInfoEntity.setUsername(userInfo.getString("username"));
//            userInfoEntity.setPhone(userInfo.getString("phone"));
//            userInfoEntity.setPlatformType(UserInfoEntity.PLATFORM_USER);
//            userInfoEntity.setStatus(userInfo.getString("status"));
//            userInfoEntity.setAccount(userInfo.getString("name"));
//            System.out.println(userInfoEntity);
//
//        }catch (IOException e){
//            e.printStackTrace();
//        }
//
//
//    }
}
