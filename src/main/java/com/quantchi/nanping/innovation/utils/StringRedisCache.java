package com.quantchi.nanping.innovation.utils;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/4/25 10:17
 */
@Component
@Slf4j
public class StringRedisCache {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    public void put(final String key, final String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void put(final String key, final String value, final long timeout, final TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    public void remove(final String key) {
        redisTemplate.delete(key);
    }

    public String get(final String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 解锁脚本,防止线程将其他线程的锁释放
     */
    private static String UN_LOCK_SCRIPT =
            "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";

    public boolean lock(String key, String value) {
        return lock(key, value, 10, 1, 1000);
    }

    /**
     * redis分布式锁-加锁
     *
     * @param key           分布式锁key
     * @param value         分布式锁value 一般为随机数
     * @param timeout       分布式锁过期时间 秒
     * @param number        重试次数
     * @param interval      重试间隔 毫秒
     * @return
     */
    public boolean lock(String key, String value, int timeout, int number, int interval) {
        //加锁
        for (int i = 0; i < number; i++) {
            //尝试获取锁,成功则返回不成功则重试
            if (redisTemplate.opsForValue().setIfAbsent(key, value, Duration.ofSeconds(timeout))) {
                return true;
            }
            //暂停
            try {
                TimeUnit.MILLISECONDS.sleep(interval);
            } catch (InterruptedException e) {
                log.error("第" + i + "次获取锁失败，exception：", e);
            }
        }
        //最终获取不到锁返回失败
        return false;
    }

    /**
     * redis分布式锁-解锁
     *
     * @param key           分布式锁key
     * @param value         分布式锁value 一般为随机数
     * @return
     */
    public void unLock(String key, String value) {
        //解锁
        redisTemplate.execute(new DefaultRedisScript<>(UN_LOCK_SCRIPT, Long.class), Collections.singletonList(key), value);
    }

    /**
     * 限流
     *
     * @param key          限流key
     * @param rateType     限流类型
     * @param rate         速率
     * @param rateInterval 速率间隔
     * @return -1 表示失败
     */
    public long rateLimiter(final String key, final RateType rateType, final int rate, final int rateInterval) {
        final RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
        rateLimiter.trySetRate(rateType, rate, rateInterval, RateIntervalUnit.SECONDS);
        if (rateLimiter.tryAcquire()) {
            return rateLimiter.availablePermits();
        } else {
            return -1L;
        }
    }

    /**
     * 获取客户端实例
     */
    public RedissonClient getClient() {
        return redissonClient;
    }
}
