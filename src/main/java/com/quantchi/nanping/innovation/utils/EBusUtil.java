package com.quantchi.nanping.innovation.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 易企办验证工具
 *
 * <AUTHOR>
 * @date 2024/11/19 14:15
 */
@Component
@Slf4j
public class EBusUtil {

    private static String staticEBusToken;

    @Value("${ebus.token}")
    private String eBusToken;

    @PostConstruct
    public void setStaticPort() {
        staticEBusToken = this.eBusToken;
    }

    public static Map<String, String> getSignMap(String paasid, String paastoken) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-tif-paasid", paasid);
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        headerMap.put("x-tif-timestamp", timestamp);
        String nonce = UUID.randomUUID().toString().replace("-", "");
        headerMap.put("x-tif-nonce", nonce);
        String signData = timestamp + paastoken + nonce + timestamp;
        headerMap.put("x-tif-signature", sha256(signData));
        return headerMap;
    }

    /**
     * 签名验证
     *
     * @param httprequest
     * @return
     */
    public static boolean checkSign(HttpServletRequest httprequest) {
        //从请求头获取相关签名信息
        String signature = httprequest.getHeader("x-tif-signature");
        String nonce = httprequest.getHeader("x-tif-nonce");
        String timestamp = httprequest.getHeader("x-tif-timestamp");
        String uid = httprequest.getHeader("x-tif-uid");
        String uinfo = httprequest.getHeader("x-tif-uinfo");
        String ext = httprequest.getHeader("x-tif-ext");
        String signdata;
        log.error("易企办登录鉴权,signature:{},nonce:{},timestamp:{},uid:{},uinfo:{},ext:{}", signature, nonce, timestamp, uid, uinfo, ext);
        if (StringUtils.isNotBlank(uid) || StringUtils.isNotBlank(uinfo) ||
                StringUtils.isNotBlank(ext)) {
            signdata = timestamp + staticEBusToken + nonce + "," + uid + "," + uinfo + "," + ext
                    + timestamp;
        } else {
            signdata = timestamp + staticEBusToken + nonce + timestamp;
        }
        log.error("易企办登录鉴权,signdata:{}", signdata);
        if (StringUtils.isBlank(signature) || !signature.equals(sha256(signdata).toUpperCase())) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 利用 java 原生的类实现 SHA256 加密
     *
     * @param str 加密后的报文
     * @return
     */
    public static String sha256(String str) {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodestr = byte2Hex(messageDigest.digest());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return encodestr;
    }

    /**
     * 将 byte 转为 16 进制
     *
     * @param bytes
     * @return
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                // 1 得到一位的进行补 0 操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }
}
