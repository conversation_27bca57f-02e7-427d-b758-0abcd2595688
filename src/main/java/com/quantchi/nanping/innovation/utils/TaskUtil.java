package com.quantchi.nanping.innovation.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 异步任务工具类
 * <AUTHOR>
 * @date 2023/5/5 14:26
 */
@Slf4j
public class TaskUtil {

    public static void get(List<CompletableFuture<Void>> futureList){
        final CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            allOf.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void get(CompletableFuture<?>... cfs){
        final CompletableFuture<Void> allOf = CompletableFuture.allOf(cfs);
        try {
            allOf.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
