package com.quantchi.nanping.innovation.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.security.spec.AlgorithmParameterSpec;
import org.apache.commons.codec.binary.Base64;

/**
 * <AUTHOR>
 */
@Slf4j
public class AESUtil {

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final String KEY = "liangzhikey@1021";
    private static final String IV = "Dx3n0gM8iE,sR@#L";
    private static final String CHARSET_NAME = "UTF-8";
    private static final String AES_NAME = "AES";
    /**
     * 加密模式
     */
    public static final String ALGORITHM = "AES/CBC/PKCS7Padding";

    /**
     * 加密
     *
     * @param content
     * @return
     */
    public static String encrypt(String content) {
        if (StringUtils.isEmpty(content)){
            return StringUtils.EMPTY;
        }
        byte[] result = null;
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(CHARSET_NAME), AES_NAME);
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(IV.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);
            result = cipher.doFinal(content.getBytes(CHARSET_NAME));
        } catch (Exception e) {
            log.error("加密失败，", e);
        }
        return Base64.encodeBase64String(result);
    }

    /**
     * 解密
     *
     * @param content
     * @return
     */
    public static String decrypt(String content) {
        if (StringUtils.isEmpty(content)){
            return StringUtils.EMPTY;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(CHARSET_NAME), AES_NAME);
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(IV.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, keySpec, paramSpec);
            return new String(cipher.doFinal(Base64.decodeBase64(content)), CHARSET_NAME);
        } catch (Exception e) {
            log.error("解密失败，", e);
        }
        return StringUtils.EMPTY;
    }

//    public static void main(String[] args) throws FileNotFoundException {
//        /*File file = new File("C:\\Users\\<USER>\\Desktop\\file.sql");
//        Map<String, Integer> chainCountMap = new LinkedHashMap<>();
//        chainCountMap.put("1001", 89);
//        chainCountMap.put("1002", 100);
//        chainCountMap.put("1003", 73);
//        chainCountMap.put("1004", 26);
//        chainCountMap.put("1005", 99);
//        chainCountMap.put("1006", 13);
//        chainCountMap.put("1007", 74);
//        chainCountMap.put("1008", 76);
//        PrintStream stream = new PrintStream(file);
//        System.setOut(stream);
//        for (Map.Entry<String, Integer> entry : chainCountMap.entrySet()) {
//            for (int i = 1; i <= entry.getValue(); i++) {
//                String jpgName = entry.getKey() + "-" + String.format("%03d", i);
//                System.out.println(String.format("INSERT INTO `nanping_innovation`.`file_info`(`id`, `create_time`, `update_time`, `create_user_id`, `original_file_name`, `file_name_alias`, `download_url`, `relative_download_url`, `related_id`, `related_type`, `deleted`) VALUES ('%s', '2023-08-14 10:07:24', '2023-08-14 10:07:24', NULL, '%s.jpg', '%s.jpg', 'http://218.67.108.63:9178/h5-report/%s/%s.jpg', '/home/<USER>/nanping-innovation/upload/h5-report/%s/%s.jpg', NULL, 'h5_report', 0);",
//                        jpgName, jpgName, jpgName, entry.getKey(),jpgName, entry.getKey(), jpgName));
//            }
//        }*/
//        String contents = "121456465";
//        String encrypt = AESUtil.encrypt(contents);
//        System.out.println("加密后:" + encrypt);
//        String decrypt = AESUtil.decrypt("C6E4933A9AFC5FFD7E6492F224F52CEF");
//        System.out.println("解密后:" + decrypt);
//    }

}
