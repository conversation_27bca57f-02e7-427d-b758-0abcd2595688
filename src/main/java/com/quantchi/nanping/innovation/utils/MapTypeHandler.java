package com.quantchi.nanping.innovation.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/11 15:52
 */
@Slf4j
public class MapTypeHandler extends BaseTypeHandler<Map<String, Object>> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Map<String, Object> stringObjectMap, JdbcType jdbcType) throws SQLException {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            preparedStatement.setString(i, stringObjectMap != null? objectMapper.writeValueAsString(stringObjectMap): null);
        } catch (JsonProcessingException e) {
            log.error("存储map转换失败", e);
        }
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String columnValue = resultSet.getString(s);
        return StringUtils.isEmpty(columnValue)? null: JSON.parseObject(columnValue, new TypeReference<HashMap<String, Object>>() {});
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String columnValue = resultSet.getString(i);
        return StringUtils.isEmpty(columnValue)? null: JSON.parseObject(columnValue, new TypeReference<HashMap<String, Object>>() {});
    }

    @Override
    public Map<String, Object> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String columnValue = callableStatement.getString(i);
        return StringUtils.isEmpty(columnValue)? null: JSON.parseObject(columnValue, new TypeReference<HashMap<String, Object>>() {});
    }
}
