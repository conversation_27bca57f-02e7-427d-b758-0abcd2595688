package com.quantchi.nanping.innovation.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/9/17
 */
@Slf4j
public class DateUtils {

    private static ThreadLocal<DateFormat> yyyyMMddDateFormat = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd");
        }
    };

    private static ThreadLocal<DateFormat> yyyyMMDateFormat = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM");
        }
    };

    private static ThreadLocal<DateFormat> yyyyDateFormat = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy");
        }
    };

    public static String addOneMonth(String date) {
        Date result = null;
        try {
            result = yyyyMMDateFormat.get().parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cl = Calendar.getInstance();
        cl.setTime(result);
        cl.add(Calendar.MONTH, 1);
        result = cl.getTime();
        return yyyyMMDateFormat.get().format(result);
    }

    public static String format2yyyyMMdd(Date date) {
        String format = yyyyMMddDateFormat.get().format(date);
        return format;
    }

    public static String transformDate2yyyyMM(String date) throws ParseException {
        Date parse = yyyyMMDateFormat.get().parse(date);
        String format = yyyyMMDateFormat.get().format(parse);
        return format;
    }

    public static String transformDate2yyyy(String date) {
        Date parse = null;
        try {
            parse = yyyyDateFormat.get().parse(date);
        } catch (ParseException e) {
            log.error("日期格式转换发生异常", e.getCause());
            return null;
        }
        return yyyyDateFormat.get().format(parse);
    }

    public static boolean checkDateFormatWithyyyyMMdd(String date) {
        try {
            yyyyMMddDateFormat.get().parse(date);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public static boolean checkDateFormatWithyyyyMM(String date) {
        try {
            yyyyMMDateFormat.get().parse(date);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /***
     * 日期月份减一个月
     *
     * @param datetime
     *            日期(2014-11)
     * @return 2014-10
     */
    public static String dateFormat(String datetime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Date date = null;
        try {
            date = sdf.parse(datetime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.MONTH, -1);
        date = cl.getTime();
        return sdf.format(date);
    }

    /**
     * 转换Date类型
     *
     * @param date
     * @return
     */
    public static Date getDateObject(String date) {
        Date parse = null;
        try {
            parse = yyyyMMddDateFormat.get().parse(date);
        } catch (ParseException e) {
            log.error("日期格式转换发生异常", e.getCause());
            return null;
        }
        return parse;
    }

    /***
     * 日期月份减i个月
     *
     * @param i
     *            日期(2014-11)
     * @return 2014-10
     */
    public static String getBeforeMonthStartDate(Integer i) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-01");
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.MONTH, -i);
        date = cl.getTime();
        return sdf.format(date);
    }

    /**
     * 获取 i 个季度之前的日期
     *
     * @param i
     * @return
     */
    public static String getBeforeQuarterStartDate(Integer i) {
        Calendar cl = Calendar.getInstance();
        cl.set(Calendar.MONTH, ((int) cl.get(Calendar.MONTH) / 3 - i) * 3);
        cl.set(Calendar.DAY_OF_MONTH, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = cl.getTime();
        return sdf.format(date);
    }

    /***
     * 日期年份减i年
     *
     * @param i
     *            日期(2014-11)
     * @return 2014-10
     */
    public static String getBeforeYearStartDate(Integer i) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-01-01");
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.YEAR, -i);
        date = cl.getTime();
        return sdf.format(date);
    }

    /***
     * 日期年份减i年
     *
     * @param i
     *            日期(2014-11)
     * @return 2014-10
     */
    public static String getBeforeYearEndDate(Integer i) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-12-31");
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.YEAR, -i);
        date = cl.getTime();
        return sdf.format(date);
    }

    /***
     * 日期年份减i年
     *
     * @param i
     *            日期(2014-11)
     * @return 2014-10
     */
    public static String getBeforeYear(Integer i) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.YEAR, -i);
        date = cl.getTime();
        return sdf.format(date);
    }

    public static String dateFormat(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    /**
     * 获取当月所有天
     *
     * @return
     */
    public static List<String> getDayListOfMonth(Date date) {
        List<String> list = new ArrayList<>();
        Calendar aCalendar = Calendar.getInstance(Locale.CHINA);
        aCalendar.setTime(date);
        //年份
        int year = aCalendar.get(Calendar.YEAR);
        //月份
        int month = aCalendar.get(Calendar.MONTH) + 1;
        int day = aCalendar.getActualMaximum(Calendar.DATE);
        for (int i = 1; i <= day; i++) {
            String aDate = null;
            if (month < 10 && i < 10) {
                aDate = year + "-0" + month + "-0" + i;
            } else if (month < 10) {
                aDate = year + "-0" + month + "-" + i;
            } else if (i < 10) {
                aDate = year + "-" + month + "-0" + i;
            } else {
                aDate = year + "-" + month + "-" + i;
            }
            list.add(aDate);
        }
        return list;
    }

    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取某年最后一天日期
     *
     * @param i 年份
     * @return Date
     */
    public static String getYearLast(int i) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        calendar.clear();
        calendar.set(Calendar.YEAR, currentYear);
        calendar.add(Calendar.YEAR, -i);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        Date currYearLast = calendar.getTime();
        String yearLast = sdf.format(currYearLast);
        return yearLast;
    }

    /**
     * 罗列近几年的年份
     *
     * @return
     */
    public static List<String> getRecentYears(int interval) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        List<String> years = new ArrayList<>(interval);
        for (int i = 0; i < interval; i++) {
            years.add(sdf.format(cl.getTime()));
            cl.add(Calendar.YEAR, -1);
        }
        return years;
    }

    /**
     * 罗列近几年的年份
     *
     * @return
     */
    public static String getBeforeYear(int interval) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.YEAR, 0 - interval);
        return sdf.format(cl.getTime());
    }

//    public static void main(String[] args) {
//        String date = getBeforeYear(5);
//        System.out.println(date);
//    }
}
