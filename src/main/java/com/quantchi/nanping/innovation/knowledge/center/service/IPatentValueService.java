package com.quantchi.nanping.innovation.knowledge.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentValue;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentValueUnit;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/10 10:29
 */
public interface IPatentValueService extends IService<PatentValue> {
    /**
     * 按小类统计数量
     *
     * @param subClass
     * @return
     */
    Long countBySubClass(String subClass);

    /**
     * 统计同小类下小于等于value的数量
     *
     * @param subClass
     * @param value
     * @return
     */
    Long countLteBySubClass(String subClass, BigDecimal value);

    /**
     * 按主分类号统计数量
     *
     * @param mainIpc
     * @return
     */
    Long countByMainIpc(String mainIpc);

    /**
     * 统计主分类号下小于等于value的数量
     *
     * @param mainIpc
     * @param value
     * @return
     */
    Long countLteByMainIpc(String mainIpc, BigDecimal value);

    /**
     * 统计主分类号下的平均值
     *
     * @param mainIpc
     * @return
     */
    BigDecimal getAvgValueByMainIpc(String mainIpc);

    /**
     * 查询专利价值
     *
     * @param id
     * @return
     */
    PatentValue getById(String id);

    /**
     * 查询有效专利价值
     *
     * @param ids
     * @return
     */
    List<PatentValue> listValidByIds(List<String> ids);

    /**
     * 获取当前汇率
     *
     * @return
     */
    PatentValueUnit getCurrentUnit();
}
