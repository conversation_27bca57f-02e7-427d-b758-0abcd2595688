package com.quantchi.nanping.innovation.knowledge.center.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.quantchi.nanping.innovation.knowledge.center.utils.serializer.DoubleSerializer;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/19 11:12
 */
@ApiModel(description = "排名信息")
@Data
@TableName("company_technical_info")
@NoArgsConstructor
@AllArgsConstructor
public class CompanyTechnicalInfo extends BaseTime {

    @ApiModelProperty("主键id")
    @TableId
    private String id;

    @ApiModelProperty(value = "科创评分")
    @TableField(value = "value")
    private Integer evaluationScore;

    @ApiModelProperty(value = "研发资源规模评分")
    private Integer scaleValue;

    @ApiModelProperty(value = "技术成果评分")
    private Integer achievementValue;

    @ApiModelProperty(value = "协同创新评分")
    private Integer cooperateValue;

    @ApiModelProperty(value = "技术质量评分")
    private Integer qualityValue;

    @ApiModelProperty(value = "研发持续创新评分")
    private Integer sustainedValue;

    @ApiModelProperty(value = "综合评价")
    @TableField(exist = false)
    private String overallEvaluation;

    @ApiModelProperty(value = "国内相对排名最高的指标")
    @TableField(exist = false)
    private String topNationalIndex;

//    @ApiModelProperty(value = "全国排名")
//    @TableField(exist = false)
//    private Integer nationalRank;

    @ApiModelProperty(value = "全国总数")
    @TableField(exist = false)
    private Integer nationalTotal;

    @ApiModelProperty(value = "全国百分位")
    @TableField(exist = false)
    @JsonSerialize(using = DoubleSerializer.class)
    private double nationalPercentile;

    @ApiModelProperty(value = "省内相对排名最高的指标")
    @TableField(exist = false)
    private String topProvinceIndex;

//    @ApiModelProperty(value = "省内排名")
//    @TableField(exist = false)
//    private Integer provincialRank;

    @ApiModelProperty(value = "省内总数")
    @TableField(exist = false)
    private Integer provincialTotal;

    @ApiModelProperty(value = "省内百分位")
    @TableField(exist = false)
    @JsonSerialize(using = DoubleSerializer.class)
    private double provincialPercentile;
}
