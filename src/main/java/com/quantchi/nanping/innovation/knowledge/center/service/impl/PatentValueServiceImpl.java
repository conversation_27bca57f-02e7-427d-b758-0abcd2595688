package com.quantchi.nanping.innovation.knowledge.center.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.knowledge.center.dao.PatentValueMapper;
import com.quantchi.nanping.innovation.knowledge.center.dao.PatentValueUnitMapper;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentValue;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentValueUnit;
import com.quantchi.nanping.innovation.knowledge.center.service.IPatentValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/10 10:30
 */
@Service
public class PatentValueServiceImpl extends ServiceImpl<PatentValueMapper, PatentValue> implements IPatentValueService {

    /**
     * 用于计算的专利状态范围
     */
    private static final List<String> VALID_STATUS = Arrays.asList("有效", "审中","");

    @Autowired
    private PatentValueMapper patentValueMapper;

    @Autowired
    private PatentValueUnitMapper unitMapper;

    @Override
    public Long countBySubClass(String subClass) {
        return this.count(Wrappers.lambdaQuery(PatentValue.class).eq(PatentValue::getMainIpcCategory, subClass).in(PatentValue::getStatus, VALID_STATUS));
    }

    @Override
    public Long countLteBySubClass(String subClass, BigDecimal value) {
        return this.count(Wrappers.lambdaQuery(PatentValue.class).eq(PatentValue::getMainIpcCategory, subClass)
                .le(PatentValue::getValue, value).in(PatentValue::getStatus, VALID_STATUS));
    }

    @Override
    public Long countByMainIpc(String mainIpc) {
        return this.count(Wrappers.lambdaQuery(PatentValue.class).eq(PatentValue::getMainIpc, mainIpc)
                .in(PatentValue::getStatus, VALID_STATUS));
    }

    @Override
    public Long countLteByMainIpc(String mainIpc, BigDecimal value) {
        return this.count(Wrappers.lambdaQuery(PatentValue.class).eq(PatentValue::getMainIpc, mainIpc)
                .le(PatentValue::getValue, value).in(PatentValue::getStatus, VALID_STATUS));
    }

    @Override
    public BigDecimal getAvgValueByMainIpc(String mainIpc) {
        return patentValueMapper.getAvgValueByMainIpc(mainIpc);
    }

    @Override
    public PatentValue getById(String id) {
        return this.getOne(Wrappers.lambdaQuery(PatentValue.class).eq(PatentValue::getId, id)
                .in(PatentValue::getStatus, VALID_STATUS));
    }

    @Override
    public List<PatentValue> listValidByIds(List<String> ids) {
        return this.list(Wrappers.lambdaQuery(PatentValue.class).in(PatentValue::getId, ids)
                .in(PatentValue::getStatus, VALID_STATUS)
                .orderByDesc(PatentValue::getValue));
    }

    @Override
    public PatentValueUnit getCurrentUnit() {
        return unitMapper.selectOne(Wrappers.lambdaQuery(PatentValueUnit.class).orderByDesc(PatentValueUnit::getUnitId).last("limit 1"));
    }
}
