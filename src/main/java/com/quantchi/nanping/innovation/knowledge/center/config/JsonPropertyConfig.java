package com.quantchi.nanping.innovation.knowledge.center.config;

import com.alibaba.fastjson.JSON;
import com.quantchi.tianying.config.property.NavigationSettings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * json文件格式配置读取和注册对应的bean
 *
 * <AUTHOR>
 * @date 2022/4/20 14:12
 */
@Configuration
public class JsonPropertyConfig {

    @Bean
    public EsBoostProperties esBoostProperties() throws IOException {
        String fileName = "es_boost.json";
        Resource resource = new ClassPathResource(fileName);
        String jsonStr = readResourceAsString(resource);
        return JSON.parseObject(jsonStr, EsBoostProperties.class);
    }

    @Bean
    public NavigationSettings navigationSettings() throws IOException {
        String fileName = "navigation_setting.json";
        Resource resource = new ClassPathResource(fileName);
        String jsonStr = readResourceAsString(resource);
        return JSON.parseObject(jsonStr, NavigationSettings.class);
    }

    /**
     * 将资源加载为一个字符串
     *
     * @param resource
     * @return
     * @throws IOException
     */
    private String readResourceAsString(Resource resource) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();
        InputStream in = resource.getInputStream();
        InputStreamReader inputStreamReader = new InputStreamReader(in);
        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        String line;
        while (((line = bufferedReader.readLine()) != null)) {
            stringBuilder.append(line);
        }
        bufferedReader.close();
        inputStreamReader.close();
        in.close();
        return stringBuilder.toString();
    }
}