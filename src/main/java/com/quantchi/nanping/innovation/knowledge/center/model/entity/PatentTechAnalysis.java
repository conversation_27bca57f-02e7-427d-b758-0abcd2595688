package com.quantchi.nanping.innovation.knowledge.center.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@TableName("patent_tech_analysis")
@ApiModel(value = "PatentTechAnalysis对象", description = "专利技术分析")
public class PatentTechAnalysis extends BaseTime implements Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("专利ID")
    private String id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("摘要")
    private String abstractContent;

    @ApiModelProperty("解决问题")
    private String problem;

    @ApiModelProperty("关键技术VO")
    @TableField(exist = false)
    private List<Map<String, String>> technology;

    @ApiModelProperty("效果")
    private String effect;

    @ApiModelProperty("关键技术")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String technologies;
}
