package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PatentValueOverallEvaluationMarketValueVO {

    @ApiModelProperty(value = "预估市场价值")
    private String marketValue;

    @ApiModelProperty(value = "IPC分类技术集群")
    private String ipcLevel3Code;

    @ApiModelProperty(value = "在IPC分类技术集群排名")
    private String ipcLevel3CodeRatio;

    @ApiModelProperty(value = "IPC分类细分")
    private String mainIpc;

    @ApiModelProperty(value = "在IPC分类细分排名")
    private String mainIpcRatio;

    @ApiModelProperty(value = "较技术集群平均价值")
    private String compareAvgValue;


}
