package com.quantchi.nanping.innovation.knowledge.center.utils;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import com.quantchi.nanping.innovation.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Random;


@Slf4j
public class LocalFileUtil {

    //public static final String FILE_SAVE_PATH = System.getProperty("user.dir") + "/file/";

    public static final String FILE_MODULE_NAME = "icir";


    /**
     * 文件名 格式模块名+时间戳+4位随机数
     *
     * @param moduleName
     * @param suffix
     * @return
     */
    public static String getStorageFileName(final String moduleName, final String suffix) {
        return moduleName + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(9999)) + "." + suffix;
    }

    /**
     * 获取文件目录
     *
     * @param moduleName
     * @param fileName
     * @return
     */
    public static String getActualFilePath(final String moduleName, final String fileName) {
        final String dirPath = SpringUtils.getApplicationContext().getEnvironment().getProperty("pdf.generate.dir.patent") + moduleName + File.separator;
        if (!FileUtil.isDirectory(dirPath)) {
            FileUtil.mkdir(dirPath);
        }
        return dirPath + fileName;
    }

    /**
     * 保存文件
     *
     * @param file
     * @param moduleName
     * @return
     * @throws Exception
     */
    public static String saveFile(final MultipartFile file, final String moduleName) throws Exception {
        final String suffix = FileNameUtil.getSuffix(file.getOriginalFilename());
        final String fileName = getStorageFileName(moduleName, suffix);
        final File saveFile = new File(getActualFilePath(moduleName, fileName));
        file.transferTo(saveFile);
        return fileName;
    }
}
