package com.quantchi.nanping.innovation.knowledge.center.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:20
 */
@Data
@Accessors(chain = true)
@TableName("patent_update_record")
@ApiModel(value = "PatentUpdateRecord对象", description = "专利更新记录")
public class PatentUpdateRecord extends BaseTime {

    @TableId
    private Integer updateId;

    @ApiModelProperty("专利ID")
    private String patentId;

    @ApiModelProperty("更新状态：-1 历史数据未点击过更新 0未更新完成 1更新完成")
    private Integer updateStatus;

}
