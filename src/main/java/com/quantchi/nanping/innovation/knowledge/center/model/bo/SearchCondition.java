package com.quantchi.nanping.innovation.knowledge.center.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 09:53
 */
@Data
public class SearchCondition {

    @ApiModelProperty("条件连接符,AND/OR/NOT")
    private String operator;

    @ApiModelProperty("字段")
    private String field;

    @ApiModelProperty("值")
    private List<String> valueList;

    @ApiModelProperty("搜索类型，0下拉框选择/1日期自定义/2金额自定义/3级联搜索/4文本框输入")
    private Integer customType;
}
