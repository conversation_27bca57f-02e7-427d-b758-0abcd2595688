package com.quantchi.nanping.innovation.knowledge.center.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("patent_value")
@ApiModel(value = "PatentValue对象", description = "")
public class PatentValue implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("专利ID")
    private String id;

    @ApiModelProperty("主分类号")
    private String mainIpc;

    @ApiModelProperty("小类")
    private String mainIpcCategory;

    @ApiModelProperty("经济价值")
    private BigDecimal value;

    @ApiModelProperty("专利状态")
    private String status;

}
