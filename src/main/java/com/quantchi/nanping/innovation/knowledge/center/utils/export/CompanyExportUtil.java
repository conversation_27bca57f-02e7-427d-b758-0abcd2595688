package com.quantchi.nanping.innovation.knowledge.center.utils.export;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.data.*;
import com.quantchi.nanping.innovation.component.FjBigDataComponent;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.PatentValueAnalysis;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.StInnovationLevel;
import com.quantchi.nanping.innovation.knowledge.center.service.ICompanyTechnicalService;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.FjBigDataAuthorizeBO;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.service.library.FinanceService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.utils.DateUtils;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.xlsx4j.sml.Col;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 企业画像导出
 *
 * <AUTHOR>
 * @date 2024/10/31 15:57
 */
@Component
@Slf4j
public class CompanyExportUtil {

    private static final String NO_DATA_MESSAGE = "暂无相关信息，不排除存在时间相对滞后或相关部门无此数据的情况，仅供参考";

    private static final String NO_DATA = "暂无数据";

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private ICompanyTechnicalService technicalService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private CompanyService companyService;

    public void setCreditInfo(JSONObject jsonObject, Integer year, Map<String, Object> dataMap) throws Exception {

        //企业资质核验
        dataMap.put("is_a_tax", "1".equals(jsonObject.get("is_a_tax")) ? "是" : "否");
        dataMap.put("is_zjtx", "1".equals(jsonObject.get("is_zjtx")) ? "是" : "否");
        dataMap.put("is_gxqy", "1".equals(jsonObject.get("is_gxqy")) ? "是" : "否");

        //不动产评估
        // qycybdczhzs指数取值0-100，使用百分比指数的分档函数
        dataMap.put("qycybdczhzs", handlePercentageRange(jsonObject.getString("qycybdczhzs")));
        // qyfrbdcjzpgzs指数取值0-15, 3分为一档
        dataMap.put("qyfrbdcjzpgzs", handleRange0To15(jsonObject.getString("qyfrbdcjzpgzs")));

        //社保&公积金&纳税情况
        dataMap.put("qybndzgrsqj", jsonObject.get("qybndzgrsqj") != null ? jsonObject.get("qybndzgrsqj") : NO_DATA);
        dataMap.put("qysynzgrsqj", jsonObject.get("qysynzgrsqj") != null ? jsonObject.get("qysynzgrsqj") : NO_DATA);
        // qyjsnzgpjs使用特殊的转换规则
        dataMap.put("qyjsnzgpjs", handleQyjsnzgpjs(jsonObject.getString("qyjsnzgpjs")));
        dataMap.put("qysbjnrsqjdm", jsonObject.get("qysbjnrsqjdm") != null ? jsonObject.get("qysbjnrsqjdm") : NO_DATA);
        dataMap.put("qysblxjncsqj", jsonObject.get("qysblxjncsqj") != null ? jsonObject.get("qysblxjncsqj") : NO_DATA);
        // 百分比字段使用分档处理，每5%为一档，共20档
        dataMap.put("gjjjnjebndqshipmbfb", handlePercentageRange(jsonObject.getString("gjjjnjebndqshipmbfb")));
        dataMap.put("gjjjnjebndqspmbfb", handlePercentageRange(jsonObject.getString("gjjjnjebndqspmbfb")));
        dataMap.put("qygjjjnrsqshipmbfb", handlePercentageRange(jsonObject.getString("qygjjjnrsqshipmbfb")));
        dataMap.put("qygjjjnrsqspmbfb", handlePercentageRange(jsonObject.getString("qygjjjnrsqspmbfb")));
        dataMap.put("qysbjnjeqshipmbfb", handlePercentageRange(jsonObject.getString("qysbjnjeqshipmbfb")));
        dataMap.put("qysbjnjeqspmbfb", handlePercentageRange(jsonObject.getString("qysbjnjeqspmbfb")));

        // 企业年报
        final List<RowRenderData> stockRowDataList = new ArrayList<>();
        TableRenderData stockTable = null;
        RowRenderData stockRow0 = Rows.of("序号", "报告期", "资产总额", "负债总额", "营业总收入", "利润总额", "净利润")
                .textColor("000000").textBold()
                .bgColor("e5f0ff").center().create();
        stockRowDataList.add(stockRow0);
        // 渲染表格
        if (jsonObject.getString("zcze") != null || jsonObject.getString("fzze") != null || jsonObject.getString("yyzsr") != null
                || jsonObject.getString("lrze") != null || jsonObject.getString("jlr") != null) {
            final AtomicInteger index = new AtomicInteger(0);
            stockRowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                    String.valueOf(year),
                    PatentExportUtil.getObject(handleValueRange(jsonObject.getString("zcze"))),
                    PatentExportUtil.getObject(handleValueRange(jsonObject.getString("fzze"))),
                    PatentExportUtil.getObject(handleValueRange(jsonObject.getString("yyzsr"))),
                    PatentExportUtil.getObject(handleValueRange(jsonObject.getString("lrze"))),
                    PatentExportUtil.getObject(handleValueRange(jsonObject.getString("jlr")))
            ).center().create());

            stockTable = Tables.create(stockRowDataList.toArray(new RowRenderData[0]));

        } else if (jsonObject.get("qylrbxxGrid") != null || jsonObject.get("qyzcfzbxxGrid") != null) {
            Map<String, String> zcze = new HashMap<>();
            Map<String, String> fzze = new HashMap<>();
            Map<String, String> yyzsr = new HashMap<>();
            Map<String, String> lrze = new HashMap<>();
            Map<String, String> jlr = new HashMap<>();

            if (jsonObject.get("qylrbxxGrid") != null && jsonObject.getJSONArray("qylrbxxGrid") != null) {
                JSONArray qylrbxxGrid = jsonObject.getJSONArray("qylrbxxGrid");
                for (int i = 0; i < qylrbxxGrid.size(); i++) {
                    JSONObject item = qylrbxxGrid.getJSONObject(i);
                    if (item.get("hmc") == null || StringUtils.isBlank(item.getString("hmc"))) {
                        continue;
                    }
                    String hmc = item.getString("hmc");

                    // 处理营业收入：找到满足条件的"一、营业**"项
                    if ("一、营业**".equals(hmc)) {
                        handleQylrbxxGrid(item, yyzsr);
                    }

                    // 处理利润总额：找到满足条件的"三、利润总额(亏损总额以–号填**"项
                    if ("三、利润总额(亏损总额以–号填**".equals(hmc)) {
                        handleQylrbxxGrid(item, lrze);
                    }

                    // 处理净利润：找到满足条件的"四、净利润(净亏损以–号填**"项
                    if ("四、净利润(净亏损以–号填**".equals(hmc)) {
                        handleQylrbxxGrid(item, jlr);
                    }
                }
            }

            if (jsonObject.get("qyzcfzbxxGrid") != null && jsonObject.getJSONArray("qyzcfzbxxGrid") != null) {
                JSONArray qyzcfzbxxGrid = jsonObject.getJSONArray("qyzcfzbxxGrid");
                for (int i = 0; i < qyzcfzbxxGrid.size(); i++) {
                    JSONObject item = qyzcfzbxxGrid.getJSONObject(i);

                    if (item.get("zcxmmc") != null && StringUtils.isNotBlank(item.getString("zcxmmc"))) {
                        String zcxmmc = item.getString("zcxmmc");
                        // 处理资产合计：找到满足条件的"资产合*"项
                        if ("资产合*".equals(zcxmmc)) {
                            handleQyzcfzbxxGrid(item, zcze);
                        }
                    }

                    if (item.get("fzhsyzqyxmmc") != null && StringUtils.isNotBlank(item.getString("fzhsyzqyxmmc"))) {
                        String fzhsyzqyxmmc = item.getString("fzhsyzqyxmmc");
                        // 处理负债合计：找到满足条件的"负债**"项
                        if ("负债**".equals(fzhsyzqyxmmc)) {
                            handleQyzcfzbxxGrid(item, fzze);
                        }
                    }
                }
            }

            // 检查是否有任何数据
            if (zcze.isEmpty() && fzze.isEmpty() && yyzsr.isEmpty() && lrze.isEmpty() && jlr.isEmpty()) {
                stockTable = setNoDataTable(stockRowDataList, stockRow0.obtainColSize());
            } else {
                // 收集所有年份并排序
                Set<String> allYears = new TreeSet<>();
                allYears.addAll(zcze.keySet());
                allYears.addAll(fzze.keySet());
                allYears.addAll(yyzsr.keySet());
                allYears.addAll(lrze.keySet());
                allYears.addAll(jlr.keySet());

                final AtomicInteger index = new AtomicInteger(0);
                for (String yearKey : allYears) {
                    stockRowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                            yearKey,
                            PatentExportUtil.getObject(zcze.getOrDefault(yearKey, "")),
                            PatentExportUtil.getObject(fzze.getOrDefault(yearKey, "")),
                            PatentExportUtil.getObject(yyzsr.getOrDefault(yearKey, "")),
                            PatentExportUtil.getObject(lrze.getOrDefault(yearKey, "")),
                            PatentExportUtil.getObject(jlr.getOrDefault(yearKey, ""))
                    ).center().create());
                }

                stockTable = Tables.create(stockRowDataList.toArray(new RowRenderData[0]));
            }

        } else {
            stockTable = setNoDataTable(stockRowDataList, stockRow0.obtainColSize());
        }
        dataMap.put("stockTable", stockTable);

    }

    private void handleQylrbxxGrid(JSONObject item, Map<String, String> resultMap) {
        if (item == null || item.get("ssrqq") == null || StringUtils.isBlank(item.getString("ssrqq"))
                || item.get("ssrqz") == null || StringUtils.isBlank(item.getString("ssrqz"))) {
//                || item.get("tjlb") == null || !"3".equals(item.getString("tjlb"))) {
            return;
        }

        String ssrqq = item.getString("ssrqq");
        String ssrqz = item.getString("ssrqz");

        resultMap.put(ssrqq + "-" + ssrqz, item.getString("bnljs"));

        // 检查ssrqq包含"-01"，ssrqz包含"-12"
//        if (ssrqq.matches(".*-01.*") && ssrqz.matches(".*-12.*")) {
//            // 使用handleValueRange方法处理bnljs字段
//            String bnljs = item.getString("bnljs");
//            if (bnljs != null && !bnljs.trim().isEmpty() && ssrqq.length() >= 4) {
//                String year = ssrqq.substring(0, 4);
//                String result = handleValueRange(bnljs);
//                if (!StringUtils.isBlank(result)) {
//                    resultMap.put(year, result);
//                }
//            }
//        }
    }

    private void handleQyzcfzbxxGrid(JSONObject item, Map<String, String> resultMap) {
        if (item == null || item.get("ssrqq") == null || StringUtils.isBlank(item.getString("ssrqq"))
                || item.get("ssrqz") == null || StringUtils.isBlank(item.getString("ssrqz"))) {
            return;
        }

        String ssrqq = item.getString("ssrqq");
        String ssrqz = item.getString("ssrqz");

        resultMap.put(ssrqq + "-" + ssrqz, item.getString("qmszc"));

//        // 检查ssrqq包含"-01"，ssrqz包含"-12"
//        if (ssrqq.matches(".*-01.*") && ssrqz.matches(".*-12.*")) {
//            // 使用handleValueRange方法处理qmszc字段
//            String qmszc = item.getString("qmszc");
//            if (qmszc != null && !qmszc.trim().isEmpty() && ssrqq.length() >= 4) {
//                String year = ssrqq.substring(0, 4);
//                String result = handleValueRange(qmszc);
//                if (!StringUtils.isBlank(result)) {
//                    resultMap.put(year, result);
//                }
//            }
//        }
    }

    /**
     * 处理百分比分档，每5%为一档，共20档
     * 规则：v = min(int((data_str//5) + 1), 20)
     * [0%-5%)对应1，[95%-100%]对应20
     */
    private String handlePercentageRange(String percentageStr) {
        if (percentageStr == null || percentageStr.trim().isEmpty()) {
            return NO_DATA;
        }

        try {
            // 去除百分号并转换为数字
            String cleanStr = percentageStr.replace("%", "").trim();
            double percentage = Double.parseDouble(cleanStr);

            // 计算分档：v = min(int((data_str//5) + 1), 20)
            int level = Math.min((int)(percentage / 5) + 1, 20);

            // 根据档位返回范围描述
            if (level == 1) {
                return "[0%-5%)";
            } else if (level == 20) {
                return "[95%-100%]";
            } else {
                int lowerBound = (level - 1) * 5;
                int upperBound = level * 5;
                return "[" + lowerBound + "%-" + upperBound + "%)";
            }
        } catch (NumberFormatException e) {
            return "数据格式错误";
        }
    }

    /**
     * 处理0-15范围的分档，每3为一档，共5档
     * 规则：value = min(int(result//3) + 1, 5)
     */
    private String handleRange0To15(String valueStr) {
        if (valueStr == null || valueStr.trim().isEmpty()) {
            return NO_DATA;
        }

        try {
            double value = Double.parseDouble(valueStr.trim());

            // 计算分档：value = min(int(result//3) + 1, 5)
            int level = Math.min((int)(value / 3) + 1, 5);

            // 根据档位返回范围描述
            if (level == 1) {
                return "[0-3)";
            } else if (level == 5) {
                return "[12-15]";
            } else {
                int lowerBound = (level - 1) * 3;
                int upperBound = level * 3;
                return "[" + lowerBound + "-" + upperBound + ")";
            }
        } catch (NumberFormatException e) {
            return "数据格式错误";
        }
    }

    /**
     * 处理qyjsnzgpjs字段的特殊转换规则
     * 将档位值（"0"到"8"）还原为对应的区间范围
     */
    private String handleQyjsnzgpjs(String levelStr) {
        if (levelStr == null || levelStr.trim().isEmpty()) {
            return NO_DATA;
        }

        try {
            String level = levelStr.trim();

            switch (level) {
                case "0":
                    return "0-10";
                case "1":
                    return "11-29";
                case "2":
                    return "30-59";
                case "3":
                    return "60-99";
                case "4":
                    return "100-199";
                case "5":
                    return "200-499";
                case "6":
                    return "500-999";
                case "7":
                    return "1000-1999";
                case "8":
                    return "2000以上";
                default:
                    return "未知档位";
            }
        } catch (Exception e) {
            return "数据格式错误";
        }
    }

    private String handleValueRange(String value) {
        String range = "";
        boolean isNegative = false;
        if (value.startsWith("-")) {
            isNegative = true;
            value = value.substring(1);
        }
        switch (value) {
            case "0":
                range += isNegative ? "-49万以下" : "49万以下";
                break;
            case "1":
                range += isNegative ? "-99万至-50万" : "50万-99万";
                break;
            case "2":
                range += isNegative ? "-199万至-100万" : "100万-199万";
                break;
            case "3":
                range += isNegative ? "-499万至-200万" : "200万-499万";
                break;
            case "4":
                range += isNegative ? "-999万至-500万" : "500万-999万";
                break;
            case "5":
                range += isNegative ? "-1999万至-1000万" : "1000万-1999万";
                break;
            case "6":
                range += isNegative ? "-4999万至-2000万" : "2000万-4999万";
                break;
            case "7":
                range += isNegative ? "-9999万至-5000万" : "5000万-9999万";
                break;
            case "8":
                range += isNegative ? "-1亿9999万至-1亿" : "1亿-1亿9999万";
                break;
            case "9":
                range += isNegative ? "-4亿9999万至-2亿" : "2亿-4亿9999万";
                break;
            case "10":
                range += isNegative ? "-5亿以下" : "5亿以上";
                break;
            default:
                range = NO_DATA;
                break;
        }
        return range;
    }

    public void setBaseInfo(String id, Map<String, Object> dataMap) throws IOException {
        Map<String, Object> baseInfo = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY.getEsIndex(), id, null, null);
        // 工商信息
        dataMap.put("company_name", baseInfo.get("name"));
        dataMap.put("social_credit_code", baseInfo.get("social_credit_code"));
        dataMap.put("company_type", baseInfo.get("company_type"));
        dataMap.put("reg_capital_text", baseInfo.get("reg_capital_text"));
        dataMap.put("establish_date", baseInfo.get("establish_date"));
        dataMap.put("register_status", baseInfo.get("register_status"));
        dataMap.put("organization_code", baseInfo.get("organization_code"));
        dataMap.put("address", baseInfo.get("address"));
        dataMap.put("business_scope", baseInfo.get("business_scope"));
        dataMap.put("legal_person", baseInfo.get("legal_person"));
        // 核心成员
        final List<Map<String, Object>> leaderList = (List<Map<String, Object>>) baseInfo.get("leader");
        List<String> leaderColumnNames = new ArrayList<>();
        leaderColumnNames.addAll(Arrays.asList("姓名", "职位"));
        List<String> leaderColumnKeys = Arrays.asList("name", "position");
        dataMap.put("leaderTable", buildDataTable(leaderColumnNames, leaderColumnKeys, leaderList));
        // 股东信息
        final List<Map<String, Object>> shareholderList = (List<Map<String, Object>>) baseInfo.get("shareholder");
        sortBySizeDesc(shareholderList, "stock_amount");
        List<String> shareholderColumnNames = new ArrayList<>();
        shareholderColumnNames.addAll(Arrays.asList("股东名称", "出资额", "出资比例"));
        List<String> shareholderColumnKeys = Arrays.asList("name", "subscribed_amount", "stock_percent");
        dataMap.put("shareholderTable", buildDataTable(shareholderColumnNames, shareholderColumnKeys, shareholderList));
    }

    public void setMetaInfo(String id, Map<String, Object> dataMap) throws IOException {
        Map<String, Object> metaInfo = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY_META.getEsIndex(), id, null, null);
        if (metaInfo == null) {
            return;
        }
        // 对外投资
        final List<Map<String, Object>> investList = (List<Map<String, Object>>) metaInfo.get("invest");
        sortByDateDesc(investList, "invested_established_date");
        List<String> investColumnNames = new ArrayList<>();
        investColumnNames.addAll(Arrays.asList("被投资企业名称", "法定代表人", "成立日期", "成立状态"));
        if (CollUtil.isEmpty(investList)) {
            dataMap.put("investTable", buildDataTable(investColumnNames, null, investList));
        } else {
            List<String> investColumnKeys = Arrays.asList("invested_name", "invested_legal_person", "invested_established_date", "invested_status");
            dataMap.put("investTable", buildDataTable(investColumnNames, investColumnKeys, investList));
        }
        // 分支机构
        final List<Map<String, Object>> branchList = (List<Map<String, Object>>) metaInfo.get("branch");
        List<String> branchColumnNames = new ArrayList<>();
        branchColumnNames.addAll(Arrays.asList("企业名称"));
        List<String> branchColumnKeys = Arrays.asList("name");
        dataMap.put("branchTable", buildDataTable(branchColumnNames, branchColumnKeys, branchList));
        // 变更记录
        final List<Map<String, Object>> changeList = (List<Map<String, Object>>) metaInfo.get("change");
        sortByDateDesc(changeList, "change_date");
        List<String> changeColumnNames = new ArrayList<>();
        changeColumnNames.addAll(Arrays.asList("变更日期", "变更项目", "变更前", "变更后"));
        List<String> changeColumnKeys = Arrays.asList("change_date", "change_project", "change_before", "change_after");
        dataMap.put("changeTable", buildDataTable(changeColumnNames, changeColumnKeys, changeList));
        // 企业年报
        final Map<String, Object> stockInfo = (Map<String, Object>) metaInfo.get("stock");
        final List<RowRenderData> stockRowDataList = new ArrayList<>();
        TableRenderData stockTable = null;
        RowRenderData stockRow0 = Rows.of("序号", "报告期", "资产总额", "资产负债率", "营业总收入", "利润总额", "净利润")
                .textColor("000000").textBold()
                .bgColor("e5f0ff").center().create();
        stockRowDataList.add(stockRow0);
        if (stockInfo == null) {
            stockTable = setNoDataTable(stockRowDataList, stockRow0.obtainColSize());
        } else {
            final List<Map<String, Object>> operIncomeList = (List<Map<String, Object>>) stockInfo.get("oper_income");
            final int isListed = (int) metaInfo.get("is_listed");
            // 获取已有的年报年份
            List<Integer> yearList = new ArrayList<>();
            for (Map<String, Object> operIncome : operIncomeList) {
                if (yearList.contains(Integer.parseInt(String.valueOf(operIncome.get("year"))))) {
                    continue;
                }
                yearList.add(Integer.parseInt(String.valueOf(operIncome.get("year"))));
            }
            // 按照年份进行排序
            Collections.sort(yearList, new Comparator<Integer>() {
                @Override
                public int compare(Integer o1, Integer o2) {
                    return o2 - o1;
                }
            });
            // 组合年报
            Map<Integer, Map<String, Object>> reportMap = new HashMap<>();
            generateReportInfo(reportMap, yearList, "total_profit", (List<Map<String, Object>>) stockInfo.get("total_profit"), isListed);
            generateReportInfo(reportMap, yearList, "net_profit", (List<Map<String, Object>>) stockInfo.get("net_profit"), isListed);
            generateReportInfo(reportMap, yearList, "total_assets", (List<Map<String, Object>>) stockInfo.get("total_assets"), isListed);
            generateReportInfo(reportMap, yearList, "asset_liabilty", (List<Map<String, Object>>) stockInfo.get("asset_liabilty"), isListed);
            generateReportInfo(reportMap, yearList, "oper_income", (List<Map<String, Object>>) stockInfo.get("oper_income"), isListed);
            // 渲染表格
            if (CollUtil.isNotEmpty(reportMap)) {
                final AtomicInteger index = new AtomicInteger(0);
                for (Integer year : yearList) {
                    Map<String, Object> reportInfo = reportMap.get(year);
                    stockRowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                            String.valueOf(year),
                            PatentExportUtil.getObject(reportInfo.get("total_assets")) + "万元",
                            PatentExportUtil.getObject(reportInfo.get("asset_liabilty")) + "%",
                            PatentExportUtil.getObject(reportInfo.get("oper_income")) + "万元",
                            PatentExportUtil.getObject(reportInfo.get("total_profit")) + "万元",
                            PatentExportUtil.getObject(reportInfo.get("net_profit")) + "万元"
                    ).center().create());
                }
                stockTable = Tables.create(stockRowDataList.toArray(new RowRenderData[0]));
            } else {
                stockTable = setNoDataTable(stockRowDataList, stockRow0.obtainColSize());
            }
        }
        dataMap.put("stockTable", stockTable);
        // 经营异常
        final List<Map<String, Object>> abnormalList = (List<Map<String, Object>>) metaInfo.get("abnormal");
        sortByDateDesc(abnormalList, "date_in");
        List<String> abnormalColumnNames = new ArrayList<>();
        abnormalColumnNames.addAll(Arrays.asList("列入异常名录原因", "列入异常名录日期", "作出决定机关", "移除名录原因", "移除名录日期", "决定移除异常机关"));
        List<String> abnormalColumnKeys = Arrays.asList("reason_in", "date_in", "org_in", "reason_out", "date_out", "org_out");
        dataMap.put("abnormalTable", buildDataTable(abnormalColumnNames, abnormalColumnKeys, abnormalList));
        // 行政处罚
        final List<Map<String, Object>> punishList = (List<Map<String, Object>>) metaInfo.get("punish");
        sortByDateDesc(punishList, "decision_date");
        List<String> punishColumnNames = new ArrayList<>();
        punishColumnNames.addAll(Arrays.asList("决策文书号", "行政处罚类别", "行政处罚内容", "作出决定机关", "处罚决定日期"));
        List<String> punishColumnKeys = Arrays.asList("symbol_no", "type", "desc", "decision_org", "decision_date");
        dataMap.put("punishTable", buildDataTable(punishColumnNames, punishColumnKeys, punishList));
        // 严重违法失信情况
        final List<Map<String, Object>> illegalList = (List<Map<String, Object>>) metaInfo.get("illegal");
        sortByDateDesc(illegalList, "date_in");
        List<String> illegalColumnNames = new ArrayList<>();
        illegalColumnNames.addAll(Arrays.asList("名称", "列入原因", "列入日期", "作出决定机关", "移除原因", "移除日期", "决定移除机关"));
        List<String> illegalColumnKeys = Arrays.asList("type", "reason_in", "date_in", "org_in", "reason_out", "date_out", "org_out");
        dataMap.put("illegalTable", buildDataTable(illegalColumnNames, illegalColumnKeys, illegalList));
        // 司法风险
        final List<Map<String, Object>> judgementList = (List<Map<String, Object>>) metaInfo.get("judgement");
        sortByDateDesc(judgementList, "judgment_date");
        List<String> judgementColumnNames = new ArrayList<>();
        judgementColumnNames.addAll(Arrays.asList("案件名称", "案号", "案由", "案件类型", "案件身份", "判决时间"));
        List<String> judgementColumnKeys = Arrays.asList("judgment_name", "case_no", "case_point", "case_type", "subject_identity", "judgment_date");
        dataMap.put("judgementTable", buildDataTable(judgementColumnNames, judgementColumnKeys, judgementList));
    }

    /**
     * 构建数据表
     *
     * @param columnNames
     * @param columnKeys
     * @param dataItems
     * @return
     */
    private TableRenderData buildDataTable(List<String> columnNames, List<String> columnKeys, List<Map<String, Object>> dataItems) {
        final List<RowRenderData> rowDataList = new ArrayList<>();
        TableRenderData table = null;
        columnNames.add(0, "序号");
        RowRenderData row0 = Rows.of(columnNames.toArray(new String[0]))
                .textColor("000000").textBold()
                .bgColor("e5f0ff").center().create();
        rowDataList.add(row0);
        if (CollUtil.isNotEmpty(dataItems)) {
            final AtomicInteger index = new AtomicInteger(0);
            dataItems.forEach(item -> {
                List<String> values = new ArrayList<>();
                values.add(String.valueOf(index.incrementAndGet()));
                for (String columnKey : columnKeys) {
                    values.add(PatentExportUtil.getObject(item.get(columnKey)));
                }
                rowDataList.add(Rows.of(values.toArray(new String[0])).center().create());
            });
            table = Tables.create(rowDataList.toArray(new RowRenderData[0]));
        } else {
            table = setNoDataTable(rowDataList, row0.obtainColSize());
        }
        return table;
    }

    private void generateReportInfo(Map<Integer, Map<String, Object>> reportMap, List<Integer> yearList, String itemName, List<Map<String, Object>> itemList, int isListed) {
        for (Integer year : yearList) {
            reportMap.computeIfAbsent(year, y -> new HashMap<>());
            Map<String, Object> reportInfo = reportMap.get(year);
            for (Map<String, Object> item : itemList) {
                if (String.valueOf(year).equals(item.get("year")) && "4".equals(item.get("quarter")) && item.get("value") != null) {
                    if (0 == isListed && !"asset_liabilty".equals(itemName)) {
                        double value = Double.parseDouble(item.getOrDefault("value", "").toString());
                        reportInfo.put(itemName, convertToRange(value));
                    } else {
                        reportInfo.put(itemName, item.getOrDefault("value", "").toString());
                    }
                }
            }
        }
    }

    private void generateYearReportInfo(Map<Integer, Map<String, Object>> reportMap, Integer year, String itemName, Map<String, Object> item, int isListed) {

            reportMap.computeIfAbsent(year, y -> new HashMap<>());
            Map<String, Object> reportInfo = reportMap.get(year);

            if (String.valueOf(year).equals(item.get("year")) && "4".equals(item.get("quarter")) && item.get("value") != null) {
                if (0 == isListed && !"asset_liabilty".equals(itemName)) {
                    double value = Double.parseDouble(item.getOrDefault("value", "").toString());
                    reportInfo.put(itemName, convertToRange(value));
                } else {
                    reportInfo.put(itemName, item.getOrDefault("value", "").toString());
                }
            }

        }

    /**
     * 将一个具体的值转化为对应的区间值
     */
    private String convertToRange(double value) {
        int digitCount = (int) Math.log10(value) + 1;
        int step = (int) Math.pow(10, digitCount - 1);
        // 计算区间的下限
        int lowerBound = (int) (Math.floor(value / step) * step);
        // 计算区间的上限
        int upperBound = lowerBound + step - 1;
        // 组合成区间字符串
        return lowerBound + " - " + upperBound;
    }


    /**
     * 构建无数据的表格
     *
     * @param rowDataList
     * @param columnNum
     */
    private TableRenderData setNoDataTable(List<RowRenderData> rowDataList, int columnNum) {
        List<String> cellDataList = new ArrayList<>();
        cellDataList.add(NO_DATA_MESSAGE);
        for (int i = 0; i < columnNum - 1; i++) {
            cellDataList.add(StringUtils.EMPTY);
        }
        RowRenderData row1 = Rows.create(cellDataList.toArray(new String[0]));
        rowDataList.add(row1);
        TableRenderData table = Tables.create(rowDataList.toArray(new RowRenderData[0]));
        table.setMergeRule(MergeCellRule.builder().map(MergeCellRule.Grid.of(1, 0), MergeCellRule.Grid.of(1, columnNum - 1)).build());
        return table;
    }

    public void setTechInfo(String id, Map<String, Object> dataMap) throws IOException {
        // 科创能级评价
        List<IndustryChainNode> nodeList = companyService.getFirstNodeListById(id);
        List<List<String>> nationRowDataList = new ArrayList<>(), provinceRowDataList = new ArrayList<>();
        for (IndustryChainNode node : nodeList) {
            StInnovationLevel innovationLevel = technicalService.getOverviewById(id, node.getId());
            List<String> nationRowData = new ArrayList<>(), provinceRowData = new ArrayList<>();
            nationRowData.add(node.getName());
            nationRowData.add("超过" + new BigDecimal(innovationLevel.getRanking().getNationalPercentile()).setScale(2, RoundingMode.HALF_UP) + "%企业");
            for (CompanyTechnicalDimension dimension : innovationLevel.getDimensionDetails()) {
                double median = dimension.getNationalMedian();
                Integer value = dimension.getEvaluationScore();
                nationRowData.add(new BigDecimal((value - median) / median * 100).setScale(2, RoundingMode.HALF_UP) + "%");
            }
            nationRowDataList.add(nationRowData);
            provinceRowData.add(node.getName());
            provinceRowData.add("超过" + new BigDecimal(innovationLevel.getRanking().getProvincialPercentile()).setScale(2, RoundingMode.HALF_UP) + "%企业");
            for (CompanyTechnicalDimension dimension : innovationLevel.getDimensionDetails()) {
                double median = dimension.getProvincialMedian();
                Integer value = dimension.getEvaluationScore();
                provinceRowData.add(new BigDecimal((value - median) / median * 100).setScale(2, RoundingMode.HALF_UP) + "%");
            }
            provinceRowDataList.add(provinceRowData);
            dataMap.put("overallEvaluation", innovationLevel.getOverallEvaluation());
            dataMap.put("evaluationScore", innovationLevel.getEvaluationScore());
        }
        RowRenderData row0 = Rows.of("行业", "同行业排名", "与同行业中位数差距", "", "", "", "")
                .textColor("000000").textBold()
                .bgColor("e5f0ff").center().create();
        RowRenderData row1 = Rows.of("", "", "研发资源", "技术质量", "协同创新", "技术成果", "创新持续")
                .textColor("000000").textBold()
                .bgColor("e5f0ff").center().create();
        List<RowRenderData> nationRowRenders = new ArrayList<>(), provinceRowRenders = new ArrayList<>();
        nationRowRenders.add(row0);
        nationRowRenders.add(row1);
        nationRowDataList.forEach(r -> nationRowRenders.add(Rows.of(r.toArray(new String[0])).create()));
        TableRenderData nationTable = Tables.create(nationRowRenders.toArray(new RowRenderData[0]));
        nationTable.setMergeRule(MergeCellRule.builder()
                .map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(1, 0))
                .map(MergeCellRule.Grid.of(0, 1), MergeCellRule.Grid.of(1, 1))
                .map(MergeCellRule.Grid.of(0, 2), MergeCellRule.Grid.of(0, 6))
                .build());
        dataMap.put("nationTable", nationTable);
        provinceRowRenders.add(row0);
        provinceRowRenders.add(row1);
        provinceRowDataList.forEach(r -> provinceRowRenders.add(Rows.of(r.toArray(new String[0])).create()));
        TableRenderData provinceTable = Tables.create(provinceRowRenders.toArray(new RowRenderData[0]));
        provinceTable.setMergeRule(MergeCellRule.builder()
                .map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(1, 0))
                .map(MergeCellRule.Grid.of(0, 1), MergeCellRule.Grid.of(1, 1))
                .map(MergeCellRule.Grid.of(0, 2), MergeCellRule.Grid.of(0, 6))
                .build());
        dataMap.put("provinceTable", provinceTable);
        // 技术质量分析
        PatentValueAnalysis patentValueAnalysis = technicalService.getPatentAnalysisById(id);
        dataMap.put("totalPatentValue", patentValueAnalysis.getTotalPatentValue().setScale(0, RoundingMode.HALF_UP));
        dataMap.put("averagePatentValue", patentValueAnalysis.getAveragePatentValue().setScale(0, RoundingMode.HALF_UP));
        dataMap.put("inventionPatentNum", patentValueAnalysis.getInventionPatentNum());
        dataMap.put("inventionPatentRatio", patentValueAnalysis.getInventionPatentRatio().setScale(2, RoundingMode.HALF_UP));
        dataMap.put("mostInventType", patentValueAnalysis.getMostInventType());
        dataMap.put("mostInventTypePercentile", patentValueAnalysis.getMostInventTypePercentile().setScale(2, RoundingMode.HALF_UP));
    }

    public void setPatentInfo(String id, Map<String, Object> dataMap) throws IOException {
        Map<String, Object> patentPage = patentService.pageByCompanyId(id, 1, 1000);
        List<String> patentColumnNames = new ArrayList<>();
        patentColumnNames.addAll(Arrays.asList("专利名称", "公开（公告）号", "公开日", "专利类型"));
        if (patentPage == null || CollUtil.isEmpty((List<Map<String, Object>>) patentPage.get("list"))) {
            dataMap.put("patentTable", buildDataTable(patentColumnNames, null, null));
            return;
        }
        List<Map<String, Object>> patentList = (List<Map<String, Object>>) patentPage.get("list");
        for (Map<String, Object> patent : patentList) {
            if (patent.get("title") == null) {
                patent.put("title", patent.get("title_cn"));
            }
        }
        List<String> patentColumnKeys = Arrays.asList("title", "public_code", "public_date", "patent_type");
        dataMap.put("patentTable", buildDataTable(patentColumnNames, patentColumnKeys, patentList));

    }

    public void setFinanceInfo(String id, Map<String, Object> dataMap) throws IOException {
        EsPageResult financePage = financeService.pageByCompanyId(id, 1, 10000);
        List<String> financeColumnNames = new ArrayList<>();
        financeColumnNames.addAll(Arrays.asList("日期", "融资轮次", "融资金额", "投资方"));
        if (financePage == null || CollUtil.isEmpty(financePage.getList())) {
            dataMap.put("financeTable", buildDataTable(financeColumnNames, null, null));
            return;
        }
        List<Map<String, Object>> financeList = financePage.getList();
        List<String> financeColumnKeys = Arrays.asList("financing_time", "financing_round", "financing_scale", "investment_agency");
        dataMap.put("financeTable", buildDataTable(financeColumnNames, financeColumnKeys, financeList));
    }

    public static void sortByDateDesc(List<Map<String, Object>> itemList, String dateColumn) {
        if (CollUtil.isEmpty(itemList)) {
            return;
        }
        Collections.sort(itemList, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                if (o1.get(dateColumn) != null && o2.get(dateColumn) != null) {
                    return DateUtils.getDateObject(String.valueOf(o2.get(dateColumn)))
                            .compareTo(DateUtils.getDateObject(String.valueOf(o1.get(dateColumn))));
                } else {
                    return o1.get(dateColumn) == null ? 1 : -1;
                }
            }
        });
    }

    public static void sortBySizeDesc(List<Map<String, Object>> itemList, String sizeColumn) {
        if (CollUtil.isEmpty(itemList)) {
            return;
        }
        Collections.sort(itemList, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                String o1Value = o1.get(sizeColumn) == null ? "0" : String.valueOf(o1.get(sizeColumn)),
                        o2Value = o2.get(sizeColumn) == null ? "0" : String.valueOf(o2.get(sizeColumn));
                return Double.valueOf(o2Value)
                        .compareTo(Double.valueOf(o1Value));
            }
        });
    }
}
