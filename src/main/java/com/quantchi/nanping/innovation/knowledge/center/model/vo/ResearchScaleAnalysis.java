package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.quantchi.nanping.innovation.knowledge.center.utils.serializer.BigDecimalSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "研发规模分析响应数据结构")
@NoArgsConstructor
@AllArgsConstructor
public class ResearchScaleAnalysis {

    @ApiModelProperty(value = "专利申请数量")
    private Integer patentApplications = 0;

    @ApiModelProperty(value = "专利申请数量行业均值百分比")
    private double patentApplicationsIndustryAvgPercent;

    @ApiModelProperty(value = "非外观专利申请数量")
    private Integer nonAppearancePatentApplications;

    @ApiModelProperty(value = "非外观专利申请数量行业均值百分比")
    private double nonAppearancePatentApplicationsIndustryAvgPercent;

    @ApiModelProperty(value = "发明人总数量")
    private Integer totalInventors;

    @ApiModelProperty(value = "发明人总数量行业均值百分比")
    private double totalInventorsIndustryAvgPercent;

    @ApiModelProperty(value = "软件著作权登记数量")
    private Integer softwareCopyrightRegistrations;

    @ApiModelProperty(value = "软件著作权登记数量行业均值百分比")
    private double softwareCopyrightRegistrationsIndustryAvgPercent;

    @ApiModelProperty(value = "整体研发规模行业超越百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal overallResearchScaleIndustrySurpassPercent;

    @ApiModelProperty(value = "研发规模变化曲线数据")
    private List<ResearchScaleChange> researchScaleChanges;

    @Data
    @ApiModel(description = "研发规模变化数据结构")
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResearchScaleChange {

        @ApiModelProperty(value = "年份")
        private int year;

        @ApiModelProperty(value = "专利申请数量")
        private int patentApplications = 0;

        @ApiModelProperty(value = "专利申请人数")
        private int inventorNum;
    }
}

