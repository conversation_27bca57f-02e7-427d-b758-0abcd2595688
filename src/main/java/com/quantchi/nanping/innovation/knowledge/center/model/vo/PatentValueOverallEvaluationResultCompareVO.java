package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import com.quantchi.nanping.innovation.knowledge.center.model.bo.CommonDataForCountBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PatentValueOverallEvaluationResultCompareVO {

    @ApiModelProperty(value = "本专利价值雷达图")
    private List<CommonDataForCountBO> patentValueList;

    @ApiModelProperty(value = "技术集群中位数价值雷达图")
    private List<CommonDataForCountBO> patentMedianValueList;

    @ApiModelProperty(value = "表现最好")
    private String best;

    @ApiModelProperty(value = "表现最差")
    private String worst;

    @ApiModelProperty(value = "低于集群中位数项数")
    private Integer count;
}
