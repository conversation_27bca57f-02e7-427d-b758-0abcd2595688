package com.quantchi.nanping.innovation.knowledge.center.model.enums;

import io.swagger.annotations.ApiModel;
import lombok.Getter;

@ApiModel("专利类型枚举")
@Getter
public enum ApplicantTypeEnum {

    ONE(1, "个人/其他"),
    TWO(2, "机关团体"),
    THREE(3, "高校院所"),
    FOUR(4, "企业"),
    ;

    private final Integer id;

    private final String name;

    ApplicantTypeEnum(final Integer id, final String name) {
        this.id = id;
        this.name = name;
    }

    public static String getNameByKey(final Integer id) {
        for (final ApplicantTypeEnum value : ApplicantTypeEnum.values()) {
            if (value.getId().equals(id)) {
                return value.getName();
            }
        }
        return null;
    }

}
