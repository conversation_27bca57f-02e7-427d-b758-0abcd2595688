package com.quantchi.nanping.innovation.knowledge.center.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@TableName("knowledge_count_config")
@ApiModel(value = "count对象", description = "")
public class KnowledgeCountConfig {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("总数")
    private Long knowledgeSum;

}
