package com.quantchi.nanping.innovation.knowledge.center.model.enums;

import cn.hutool.extra.spring.SpringUtil;
import com.quantchi.common.core.exception.base.BusinessException;
import com.quantchi.common.core.utils.StringUtils;
import com.quantchi.nanping.innovation.common.exception.MessageException;
import com.quantchi.nanping.innovation.model.enums.ResultCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/2/7
 * 知识中心es 索引
 */
@Getter
public enum KnowledgeCenterEnum {

    /**
     * 索引集合
     */
    COMPANY("company", "企业", "nanping_innovation_company", "text:name", "", false),
    EXPERT("expert", "人才", "nanping_innovation_expert", "text:name", "", false),
    PLATFORM("platform", "载体", "nanping_innovation_platform", "text:name", "", false),
    PROJECT("project", "项目", "nanping_innovation_project", "text:name", "", false),
    ACHIEVEMENT("achievement", "成果", "nanping_innovation_achievement", "text:name", "", false),
    POLICY("policy", "政策", "nanping_innovation_policy", "text:name", "", false),
    FINANCING("financing", "融资", "nanping_innovation_company_financing", "keyword:financing_company.name", "financing_time:desc", false),
    NEWS("news", "资讯", "nanping_innovation_news", "text:name", "", false),
    PATENT("patent", "专利", "nanping_innovation_patent", "text:name", "", false),
    ;

    @ApiModelProperty("索引类型")
    private final String type;

    @ApiModelProperty("索引名称")
    private final String indexName;

    @ApiModelProperty("实际库里的索引")
    private final String esIndex;

    @ApiModelProperty("是否禁用")
    private Boolean disabled;

    @ApiModelProperty("标题对应字段及格式")
    private final String titleColumn;

    @ApiModelProperty("默认排序")
    private final String sort;

    KnowledgeCenterEnum(String type, String indexName, String esIndex, String titleColumn, String sort, Boolean disabled) {
        this.type = type;
        this.indexName = indexName;
        this.esIndex = esIndex;
        this.disabled = disabled;
        this.titleColumn = titleColumn;
        this.sort = sort;
    }

    public static String getEsIndexByType(final String type) {
        for (final KnowledgeCenterEnum value : KnowledgeCenterEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getEsIndex();
            }
        }
        throw new BusinessException("这个类型" + type + "找不到对应的实际索引");
    }

    public static KnowledgeCenterEnum getEsIndexByIndex(final String index) {
        for (final KnowledgeCenterEnum value : KnowledgeCenterEnum.values()) {
            if (value.getEsIndex().equals(index)) {
                return value;
            }
        }
        throw new BusinessException("这个索引" + index + "找不到对应的实际索引");
    }

    public static KnowledgeCenterEnum getEsIndexEnumByType(final String type) {
        for (final KnowledgeCenterEnum value : KnowledgeCenterEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new MessageException(ResultCodeEnum.INPUT_ERROR);
    }

}
