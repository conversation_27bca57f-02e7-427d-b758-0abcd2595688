package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.quantchi.nanping.innovation.knowledge.center.utils.serializer.DoubleSerializer;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/19 11:12
 */
@ApiModel(description = "细分维度详细信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanyTechnicalDimension {

    @ApiModelProperty(value = "维度名称")
    private String dimensionName;

    @ApiModelProperty(value = "评价分数")
    private Integer evaluationScore;

    @ApiModelProperty(value = "全国同行业中位数")
    private double nationalMedian;

    @ApiModelProperty(value = "省内同行业中位数")
    private double provincialMedian;

    @ApiModelProperty(value = "全国百分比")
    @JsonSerialize(using = DoubleSerializer.class)
    private double nationalPercentage;

    @ApiModelProperty(value = "省内百分比")
    @JsonSerialize(using = DoubleSerializer.class)
    private double provincialPercentage;
}
