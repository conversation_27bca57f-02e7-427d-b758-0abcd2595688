package com.quantchi.nanping.innovation.knowledge.center.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.collection.AhoCorasick.AhoCorasickDoubleArrayTrie;
import com.hankcs.hanlp.seg.common.Term;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.FinanceArea;
import com.quantchi.nanping.innovation.knowledge.center.config.FieldBoostProperty;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.config.property.KeywordSearchProperty;
import com.quantchi.tianying.enums.WildCardQueryStrategyEnum;
import com.quantchi.tianying.model.EsPageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.MultiGetRequest;
import org.elasticsearch.action.get.MultiGetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.*;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.range.ParsedRange;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.NumericMetricsAggregation;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.quantchi.tianying.constant.TianyingConstants.ES_SCORE;

/**
 * es的帮助类，整体参考<br />
 * https://www.elastic.co/guide/en/elasticsearch/client/java-api/current/index.html<br />
 * 本类只做一些低级封装，高级搜索请自己使用getClient()获取TransportClient进行编写，如aggregations等
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ElasticsearchBuilder {

    private Logger logger = LoggerFactory.getLogger(ElasticsearchBuilder.class);

    private RestHighLevelClient restHighLevelClient;

    @Autowired
    public ElasticsearchBuilder(final RestHighLevelClient restHighLevelClient) {
        this.restHighLevelClient = restHighLevelClient;
    }

    private static final float MIN_SCORE = 0.001f;

    /**
     * bucket聚合统计字段名默认前缀
     * why:不管是bucket统计还是metric统计,字段名都不会改变,避免混淆,使用前缀区分开来
     */
    public static final String TERMS_BUCKET_PREFIX = "bucket.";

    /**
     * metric统计字段名默认前缀
     * why:与bucket统计同理
     */
    public static final String METRIC_PREFIX = "metric.";


    /**
     * 获取一条数据，默认开个新的线程去处理，后面将不在加入setOperationThreaded
     *
     * @param index 索引
     * @param id    id
     * @return 响应
     */
    public SearchResponse get(String index, String id) {
        //return client.prepareGet(index, type, id).setOperationThreaded(true).get();
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(new IdsQueryBuilder().addIds(id));
        searchRequest.source(searchSourceBuilder);
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("get index:{},id:{},error:", index, id, e);
        }
        //获取到结果
        return response;
    }


    /**
     * 新增一条数据不指定id
     *
     * @param index 索引
     * @param json  json数据
     * @return 响应
     */
    public IndexResponse index(String index, String json) {
        //获取到结果
        return index(index, null, json);
    }

    /**
     * 在制定id重新索引一条数据
     *
     * @param index 索引
     * @param id    id
     * @param json  json数据
     * @return 响应
     */
//    public IndexResponse index(String index, String id, String json) {
//        IndexRequest indexRequest = new IndexRequest(index).source(json, XContentType.JSON);
//        if (StringUtils.isNotEmpty(id)) {
//            indexRequest.id(id);
//            indexRequest.create(true);
//        }
//        IndexResponse response = null;
//        try {
//            response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
//        } catch (IOException e) {
//            logger.error("index index:{},id:{},json:{},error:", index, id, json, e);
//        }
//        //获取到结果
//        return response;
//    }

    /**
     * 新增一条数据
     *
     * @param index 索引
     * @param id    id
     * @param obj   数据
     * @return 响应
     */
    public IndexResponse index(String index, String id, Object... obj) {
        IndexRequest indexRequest = new IndexRequest(index).source(obj);
        if (StringUtils.isNotEmpty(id)) {
            indexRequest.id(id);
            indexRequest.create(true);
        }
        IndexResponse response = null;
        try {
            response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("index index:{},id:{},obj:{},error:", index, id, obj, e);
        }
        //获取到结果
        return response;
    }

    /**
     * 删除一条数据
     *
     * @param index 索引
     * @param id    id
     * @return 响应
     */
    public DeleteResponse delete(String index, String id) {
        DeleteRequest deleteRequest = new DeleteRequest();
        deleteRequest.index(index);
        deleteRequest.id(id);
        DeleteResponse response = null;
        try {
            response = restHighLevelClient.delete(deleteRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("delete index:{},id:{},error:", index, id, e);
        }
        return response;
    }

    /**
     * 根据条件删除
     *
     * @param index 索引
     * @param query QueryBuilder查询对象
     * @return 响应
     */
    public BulkByScrollResponse deleteByQuery(String index, QueryBuilder query) {
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest();
        deleteByQueryRequest.indices(index);
        deleteByQueryRequest.setQuery(query);
        BulkByScrollResponse response = null;
        try {
            response = restHighLevelClient.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("delete index:{},query:{},error:", index, query, e);
        }
        return response;
    }

    /**
     * 根据条件删除，可以自定义onResponse, onFailure等操作
     * 详细可以参考：https://www.elastic.co/guide/en/elasticsearch/client/java-api/current/java-docs-delete-by-query.html
     *
     * @param index    索引
     * @param query    QueryBuilder查询对象
     * @param listener
     */
    public void deleteByQuery(String index, QueryBuilder query, ActionListener<BulkByScrollResponse> listener) {
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest();
        deleteByQueryRequest.indices(index);
        deleteByQueryRequest.setQuery(query);
        restHighLevelClient.deleteByQueryAsync(deleteByQueryRequest, RequestOptions.DEFAULT, listener);
    }

    /**
     * 更新一个数据
     *
     * @param index 索引
     * @param id    id
     * @param obj   数据
     * @return 响应
     */
    public UpdateResponse update(String index, String id, Object obj) {
        UpdateRequest updateRequest = new UpdateRequest(index, id);
        updateRequest.doc(obj);
        UpdateResponse response = null;
        try {
            response = restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("update index:{},id:{},obj:{},error:", index, id, obj, e);
        }
        return response;
    }

    /**
     * 批量获取文档
     *
     * @param index 索引
     * @param ids   id数组
     * @return 响应
     */
    public MultiGetResponse multiGet(String index, String... ids) {
        MultiGetRequest multiGetRequest = new MultiGetRequest();
        for (String id : ids) {
            multiGetRequest.add(index, id);
        }
        MultiGetResponse response = null;
        try {
            response = restHighLevelClient.mget(multiGetRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("multiGet index:{},ids:{},error:", index, ids, e);
        }
        return response;
    }


    /**
     * 组合查询
     *
     * @param indexSearchBuilderMap <index, SearchSourceBuilder>
     * @return 响应结果
     */
    public MultiSearchResponse multiSearch(Map<String, SearchSourceBuilder> indexSearchBuilderMap) {
        MultiSearchRequest multiSearchRequest = new MultiSearchRequest();
        for (Map.Entry<String, SearchSourceBuilder> entry : indexSearchBuilderMap.entrySet()) {
            logger.info("多表查询 表名:{}  参数:{}", entry.getKey(), entry.getValue());
            multiSearchRequest.add(getBaseSearchRequest(entry.getKey(), entry.getValue()));
        }
        MultiSearchResponse response = null;
        try {

            response = restHighLevelClient.msearch(multiSearchRequest, RequestOptions.DEFAULT);
//            logger.info("多表查询结果:{}",response);
        } catch (IOException e) {
            logger.error("multiSearch builders:{},error:", indexSearchBuilderMap, e);
        }
        return response;
    }

    /**
     * 修改index一条数据信息
     *
     * @param index
     * @param id          数据的id
     * @param fieldName   被修改的字段
     * @param updateValue 修改的值
     * @return
     */
    public UpdateResponse updateInfo(String index, String id, String fieldName, String updateValue) {
        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest.index(index);
        updateRequest.id(id);
        Map<String, Object> map = new HashMap<>();
        map.put(fieldName, updateValue);
        updateRequest.doc(map);
        UpdateResponse response = null;
        try {
            response = restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("update index:{},id:{},fieldName:{},updateValue:{},error:", index, id, fieldName, updateValue, e);
        }
        return response;
    }

    /**
     * 初始化搜索对象
     *
     * @param keyword 关键词
     * @return 搜索对象
     */
    public SearchSourceBuilder initSearchRequest(String keyword) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (StringUtils.isNotBlank(keyword)) {
            searchSourceBuilder.minScore(MIN_SCORE);
        }
        return searchSourceBuilder;
    }

    public boolean isNeedHandleField(String index, String field) {
        boolean flag = false;
        //融资库-融资金额
        if (index.contains("technology_center_financing") && field.equals("financing_value")) {
            flag = true;
        }
        //融资库-融资轮次
        if (index.contains("technology_center_financing") && field.equals("financing_round")) {
            flag = true;
        }
        //项目库-项目金额
        if (index.contains("technology_center_project") && field.equals("funding")) {
            flag = true;
        }
        //文献库-语言
        if (index.contains("technology_center_article") && field.equals("language")) {
            flag = true;
        }
        //专利库-授权
        if (index.contains("technology_center_patent") && field.equals("status")) {
            flag = true;
        }
        //企业库-注册资本
        if (index.contains("technology_center_company") && field.equals("regist_capi_value")) {
            flag = true;
        }
        //专家库-需要支持模糊搜索
        if (index.contains("technology_center_expert") || index.equals("technology_center_expert")) {
            flag = true;
        }


        return flag;
    }

    /**
     * 添加过滤条件
     * <p>
     * 达到的效果类似以下sql:
     * 同一个filterBuilder调用一次:
     * select ... from table where f1=v1 or f1=v2
     * 同一个filterBuilder累计调用多次:
     * select ... from table where (f1=v1 or f1=v2) and (f2=v3 or f3=v4) and (f3=v5 or f4=v6)...
     *
     * @param filterBuilder 查询过滤对象
     * @param field         字段名
     * @return 查询过滤对象
     */
    public static BoolQueryBuilder addFilterCondition(BoolQueryBuilder filterBuilder, String field, Object[] values) {
        if (field == null || values == null || values.length == 0) {
            return filterBuilder;
        }
        BoolQueryBuilder temp = QueryBuilders.boolQuery();
        boolean flag = false;
        for (Object v : values) {
            if (v == null) {
                break;
            }
            if (v instanceof String) {
                if (StringUtils.isBlank(v.toString())) {
                    break;
                }
                //范围查询 只支持金额等数字类型
                if (((String) v).contains(",")) {
                    Double from = Double.valueOf(((String) v).split(",")[0]);
                    Double to = Double.valueOf(((String) v).split(",")[1]);
                    temp.should(QueryBuilders.rangeQuery(field).gte(from).lte(to));
                    flag = true;
                } else if (((String) v).contains("_to_")) {//时间范围过滤
                    String from = String.valueOf(v).split("_to_")[0];
                    String to = String.valueOf(v).split("_to_")[1];
                    if (from.equals("0")) {//如果近X天 初始化查询条件
                        org.joda.time.LocalDate today = new org.joda.time.LocalDate();
                        from = today.minusDays(Integer.valueOf(to)).toString("yyyy-MM-dd");
                        to = today.toString("yyyy-MM-dd");
                    }
                    temp.should(QueryBuilders.rangeQuery(field).gte(from).lte(to));
                    flag = true;

                } else {
                    temp.should(QueryBuilders.termQuery(field, v));
                    flag = true;
                }
            } else {
                temp.should(QueryBuilders.termQuery(field, v));
                flag = true;
            }
        }
        if (flag) {
            log.info("综合temp参数:{}", temp);
            filterBuilder.filter(temp);
        }

        return filterBuilder;
    }

    public static BoolQueryBuilder addComplexFilterCondition(BoolQueryBuilder filterBuilder, String field, Object[] values, String type, Operator operate) {

        if (field == null || values == null || values.length == 0) {
            return filterBuilder;
        }
        BoolQueryBuilder temp = QueryBuilders.boolQuery();
//        boolean flag = false;
        for (Object v : values) {
            if (v == null) {
                break;
            }
            if (v instanceof String) {
                if (StringUtils.isBlank(v.toString())) {
                    break;
                }
            }

            if (StringUtils.isNotBlank(type)) {

                if (null != operate) {
                    if (operate.equals(Operator.OR)) { //或者
                        if (type.equals("模糊")) {//默认全模糊匹配
                            if (field.equals("name") || field.equals("title") || field.equals("content") || field.equals("keyword")
                                    || field.equals("desc") || field.equals("province") || field.equals("abstract") || field.equals("year")
                                    || field.equals("business_scope")) {
                                //TODO 模糊匹配存在的坑：如果使用wild查询，前提为index的字段必须是keyword，不能是text的，否则搜索不到数据
                                temp.should(QueryBuilders.matchQuery(field, String.valueOf(v)));
                            } else {
                                temp.should(QueryBuilders.wildcardQuery(field,
                                        WildCardQueryStrategyEnum.getStrategyKeyword(String.valueOf(v),
                                                WildCardQueryStrategyEnum.ALL_LINK_ALL_QUERY.getType())));
                            }
                        } else if (type.equals("精确")) {
                            temp.should(QueryBuilders.matchPhraseQuery(field, v));
                        } else if (type.equals("<=")) {
                            temp.should(QueryBuilders.rangeQuery(field).lte(v));
                        } else if (type.equals(">=")) {
                            temp.should(QueryBuilders.rangeQuery(field).gte(v));
                        } else if (type.equals("=")) {
                            temp.should(QueryBuilders.termQuery(field, v));
                        } else {
                            temp.should(QueryBuilders.termQuery(field, v));
                        }

                    } else { //并且
                        if (type.equals("模糊")) {
                            if (field.equals("name") || field.equals("title") || field.equals("content") || field.equals("keyword")
                                    || field.equals("desc") || field.equals("province") || field.equals("abstract") || field.equals("year")
                                    || field.equals("business_scope")) {
                                //TODO 模糊匹配存在的坑：如果使用wild查询，前提为index的字段必须是keyword，不能是text的，否则搜索不到数据
                                temp.must(QueryBuilders.matchQuery(field, String.valueOf(v)));
                            } else {//默认全模糊匹配
                                temp.must(QueryBuilders.wildcardQuery(field,
                                        WildCardQueryStrategyEnum.getStrategyKeyword(String.valueOf(v),
                                                WildCardQueryStrategyEnum.ALL_LINK_ALL_QUERY.getType())));
                            }
                        } else if (type.equals("精确")) {
                            temp.must(QueryBuilders.matchPhraseQuery(field, v));
                        } else if (type.equals("<=")) {
                            temp.must(QueryBuilders.rangeQuery(field).lte(v));
                        } else if (type.equals(">=")) {
                            temp.must(QueryBuilders.rangeQuery(field).gte(v));
                        } else if (type.equals("=")) {
                            temp.must(QueryBuilders.termQuery(field, v));
                        } else {
                            temp.must(QueryBuilders.termQuery(field, v));
                        }
                    }
                } else {
                    temp.should(QueryBuilders.termQuery(field, v));
                }
//                flag = true;
            } else {
                temp.should(QueryBuilders.termQuery(field, v));
            }

//            if (flag) {
//                filterBuilder.filter(temp);
//            }
        }
        return temp;
    }


    /**
     * 添加过滤条件
     * <p>
     * 达到的效果类似以下sql:
     * 同一个filterBuilder调用一次:
     * select ... from table where f1=v1 or f1=v2
     * 同一个filterBuilder累计调用多次:
     * select ... from table where (f1=v1 or f1=v2) and (f2=v3 or f3=v4) and (f3=v5 or f4=v6)...
     *
     * @param filterBuilder 查询过滤对象
     * @param field         字段名
     * @return 查询过滤对象
     */
    public static BoolQueryBuilder addFilterCondition2(BoolQueryBuilder filterBuilder, String field, Object[] values) {

        if (field == null || values == null || values.length == 0) {
            return filterBuilder;
        }
        BoolQueryBuilder temp = QueryBuilders.boolQuery();
        boolean flag = false;
        for (Object v : values) {
            if (v == null) {
                break;
            }
            if (v instanceof String) {
                if (StringUtils.isBlank(v.toString())) {
                    break;
                }
            }
            temp.should(QueryBuilders.termQuery(field, v));
            flag = true;
        }
        if (flag) {
            filterBuilder.filter(temp);
        }
        return filterBuilder;
    }

    /**
     * 日期过滤
     *
     * @param filterBuilder 查询过滤对象
     * @param fieldName     字段名
     * @param begin         开始日期
     * @param end           结束日期
     * @param format        日期格式
     * @return 查询过滤对象
     */
    public static BoolQueryBuilder dateRangFilter(BoolQueryBuilder filterBuilder, String fieldName, String begin,
                                                  String end, String format) {
        begin = begin == null ? "" : begin;
        end = end == null ? "" : end;

        if (StringUtils.isNotBlank(begin) && StringUtils.isNotBlank(end)) {
            filterBuilder.filter(QueryBuilders.rangeQuery(fieldName).from(begin).to(end).format(format));
        } else if (StringUtils.isNotBlank(begin)) {
            filterBuilder.filter(QueryBuilders.rangeQuery(fieldName).from(begin).format(format));
        } else if (StringUtils.isNotBlank(end)) {
            filterBuilder.filter(QueryBuilders.rangeQuery(fieldName).to(end).format(format));
        }
        return filterBuilder;
    }

    /**
     * 设置分页
     *
     * @param searchBuilder
     * @param from          起始页数
     * @param size          每页显示数量
     */
    public static void pagingSet(SearchSourceBuilder searchBuilder, Integer from, Integer size) {
        from = from == null || from <= 0 ? 0 : from - 1;
        size = size == null || size < 0 ? 5 : size;
        //分页数量不能超过每页20,防止恶意爬取数据
        //size = size > 20 ? 20 : size;
        searchBuilder.from(from * size);
        searchBuilder.size(size);
    }

    /**
     * 设置排序
     *
     * @param searchBuilder
     * @param sort          排序字段:排序方式 eg: publishDate:desc
     */
    public static void addSort(SearchSourceBuilder searchBuilder, String sort) {
        //设置排序
        if (StringUtils.isNotBlank(sort)) {
            String[] sortSplit = sort.split(":");
            if (sortSplit != null && sortSplit.length == 2) {
                String field = sortSplit[0];
                String order = sortSplit[1].toLowerCase().trim();
                FieldSortBuilder fieldSortBuilder = new FieldSortBuilder(field);
                if (Objects.equals(order, "desc")) {
                    fieldSortBuilder.order(SortOrder.DESC);
                    fieldSortBuilder.missing("_last");
                } else if (Objects.equals(order, "asc")) {
                    fieldSortBuilder.order(SortOrder.ASC);
                    fieldSortBuilder.missing("_first");
                }
                searchBuilder.sort(fieldSortBuilder);
            }
        }
    }

    /**
     * 添加聚合条件
     * !!!默认在字段名前面加上:bucket. !!!
     */
    public static void addBucketAggrs(SearchSourceBuilder searchBuilder, String[] bucketsFields) {
        for (String field : bucketsFields) {
            searchBuilder.aggregation(AggregationBuilders.terms(TERMS_BUCKET_PREFIX + field)
                    .field(field).size(1000));
        }
    }

    /**
     * 添加分组聚合条件
     * !!!默认在字段名前面加上:bucket. !!!
     */
    public static void addBucketAggregationList(SearchSourceBuilder searchBuilder, String bucketsFields, Map<String, FinanceArea> financeAreaMap) {
//        for (String field : bucketsFields) {
//            searchBuilder.aggregation(AggregationBuilders.range(TERMS_BUCKET_PREFIX + field)
//                    .field("regist_capi_value").addRange());
//        }
        log.info("打印financeAreaMap:{}", JSON.toJSONString(financeAreaMap));
        for (Map.Entry<String, FinanceArea> entry : financeAreaMap.entrySet()) {
            String field = entry.getKey();
            FinanceArea financeArea = entry.getValue();
            log.info("打印financeArea:{}", JSON.toJSONString(financeArea));
            searchBuilder.aggregation(AggregationBuilders.range(TERMS_BUCKET_PREFIX + field).keyed(true)
                    .field(bucketsFields).addRange(field, financeArea.getFrom(), financeArea.getTo()));
        }
    }


    /**
     * 添加聚合条件 时间-年
     * !!!默认在字段名前面加上:bucket. !!!
     */
    public static void addYearBucketAggrs(SearchSourceBuilder searchBuilder, String[] bucketsFields) {
        for (String field : bucketsFields) {
//            searchBuilder.aggregation(AggregationBuilders.terms(TERMS_BUCKET_PREFIX + field)
//                    .field(field).size(1000));
            searchBuilder.aggregation(AggregationBuilders.dateHistogram(TERMS_BUCKET_PREFIX + field).calendarInterval(DateHistogramInterval.YEAR).format("yyyy").field(field));
        }
    }

    /**
     * 获取bucket聚合结果
     *
     * @param searchResponse
     * @param fields
     * @return
     */
    public static Map<String, Map<String, Long>> getYearBucketsAggretionsFromResponse(SearchResponse searchResponse, String... fields) {
        Map<String, Map<String, Long>> aggregationResult = new HashMap<>();
        if (searchResponse == null) {
            return aggregationResult;
        }
        Aggregations aggregations = searchResponse.getAggregations();
        log.info("searchResponse:{}", searchResponse);
        for (String field : fields) {
            ParsedDateHistogram dateHistogram = aggregations.get(TERMS_BUCKET_PREFIX + field);

            Map<String, Long> aggregation = new LinkedHashMap<>();
            for (Histogram.Bucket bucket : dateHistogram.getBuckets()) {
                Object key = bucket.getKeyAsString();
                long docCount = bucket.getDocCount();
                aggregation.put(key.toString(), docCount);
            }
            aggregationResult.put(field, aggregation);
//            Terms terms = aggregations.get(TERMS_BUCKET_PREFIX + field);
//            log.info("terms:{}",terms);
//            //按照文档数量降序添加
//            Map<String, Long> aggregation = new LinkedHashMap<>();
//            for (Terms.Bucket bucket : terms.getBuckets()) {
//                Object key = bucket.getKey();
//                long docCount = bucket.getDocCount();
//                aggregation.put(key.toString(), docCount);
//            }
//            aggregationResult.put(field, aggregation);
        }
        return aggregationResult;
    }

    /**
     * 获取bucket聚合结果
     *
     * @param searchResponse
     * @param fields
     * @return
     */
    public static Map<String, Map<String, Long>> getBucketsAggretionsFromResponse(SearchResponse searchResponse, String... fields) {
        Map<String, Map<String, Long>> aggregationResult = new HashMap<>();
        if (searchResponse == null) {
            return aggregationResult;
        }
        Aggregations aggregations = searchResponse.getAggregations();
        log.info("searchResponse:{}", searchResponse);
        for (String field : fields) {
            Terms terms = aggregations.get(TERMS_BUCKET_PREFIX + field);
            log.info("terms:{}", terms);
            //按照文档数量降序添加
            Map<String, Long> aggregation = new LinkedHashMap<>();
            for (Terms.Bucket bucket : terms.getBuckets()) {
                Object key = bucket.getKey();
                long docCount = bucket.getDocCount();
                aggregation.put(key.toString(), docCount);
            }
            aggregationResult.put(field, aggregation);
        }
        return aggregationResult;
    }

    public static Map<String, Map<String, Long>> getBucketsAggregationsFromResponse(SearchResponse searchResponse, List<String> areaNameList, String fieldName) {
        Map<String, Map<String, Long>> aggregationResult = new HashMap<>();
        if (searchResponse == null) {
            return aggregationResult;
        }
        Aggregations aggregations = searchResponse.getAggregations();

        Map<String, Long> aggregation = new LinkedHashMap<>();
        for (String field : areaNameList) {
            ParsedRange parsedRange = aggregations.get(TERMS_BUCKET_PREFIX + field);
            //按照文档数量降序添加
            for (Range.Bucket bucket : parsedRange.getBuckets()) {
                Object key = bucket.getKey();
                long docCount = bucket.getDocCount();
                aggregation.put(key.toString(), docCount);
            }
        }
        aggregationResult.put(fieldName, aggregation);
        return aggregationResult;
    }

    /**
     * !!!默认在字段名前面加上:metric.!!!
     * 方便从聚合结果中获取字段
     * 获取metric统计
     *
     * @param searchResponse
     * @param fields
     * @return
     */
    public static Map<String, Double> getMetricAggregationsFromResponseWithType(SearchResponse searchResponse, String type, String... fields) {
        Map<String, Double> aggregationResult = new HashMap<>();
        Aggregations aggregations = searchResponse.getAggregations();
        for (String field : fields) {
            NumericMetricsAggregation.SingleValue singleValue = aggregations.get(METRIC_PREFIX + field + "." + type);
            aggregationResult.put(field, singleValue.value());
        }
        return aggregationResult;
    }


    /**
     * !!!默认在字段名前面加上:metric.!!!
     * 方便从聚合结果中获取字段
     * 添加metric聚合
     *
     * @param searchBuilder
     * @param type          聚合类型: sum/min/avg/max
     * @param fields        聚合字段
     */
    public static void addMetricAggrs(SearchRequestBuilder searchBuilder, String type, String... fields) {
        for (String field : fields) {
            // 根据不同的type选择不同的metric
            if ("min".equals(type)) {
                searchBuilder.addAggregation(AggregationBuilders.min(METRIC_PREFIX + field + "." + type).field(field));
            } else if ("max".equals(type)) {
                searchBuilder.addAggregation(AggregationBuilders.max(METRIC_PREFIX + field + "." + type).field(field));

            } else if ("sum".equals(type)) {
                searchBuilder.addAggregation(AggregationBuilders.sum(METRIC_PREFIX + field + "." + type).field(field));

            } else if ("avg".equals(type)) {
                searchBuilder.addAggregation(AggregationBuilders.avg(METRIC_PREFIX + field + "." + type).field(field));

            }
        }
    }

    /**
     * 关键字匹配搜索,默认从keywords字段里面取
     *
     * @param filterBuilder 搜索对象
     * @param keyword       关键词
     * @return 筛选对象
     */
    public static BoolQueryBuilder keywordQueryWithOperator(BoolQueryBuilder filterBuilder, String keyword, Operator operator, String[] keywordFields) {
        BoolQueryBuilder tmpfilterBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(keyword)) {
            for (String field : keywordFields) {
                tmpfilterBuilder.should(QueryBuilders.matchQuery(field, keyword).operator(operator));
            }
            tmpfilterBuilder.minimumShouldMatch(1);
        }
        filterBuilder.must(tmpfilterBuilder);
        return filterBuilder;
    }

    /**
     * 关键字匹配搜索,默认从keywords字段里面取
     *
     * @param filterBuilder 搜索对象
     * @param keyword       关键词
     * @return 筛选对象
     */
    public static BoolQueryBuilder keywordQueryWithOperator(BoolQueryBuilder filterBuilder, String keyword, Operator operator, List<FieldBoostProperty> keywordFields) {
        BoolQueryBuilder tmpfilterBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(keyword)) {
            for (FieldBoostProperty keywordField : keywordFields) {
                final String field = keywordField.getField();
                final String type = keywordField.getFieldType();
                if (Objects.equals(type, "keyword")) {
                    tmpfilterBuilder.should(QueryBuilders.wildcardQuery(field, "*" + keyword + "*").boost(keywordField.getWeight()));
                } else {
                    tmpfilterBuilder.should(QueryBuilders.matchPhraseQuery(field, keyword)
                            .boost(keywordField.getWeight()));
                }
            }
        }
        if (tmpfilterBuilder != null) {
            tmpfilterBuilder.minimumShouldMatch(1);
            filterBuilder.must(tmpfilterBuilder);
        }
        return filterBuilder;
    }

    /**
     * 关键字匹配搜索,默认从keywords字段里面取
     *
     * @param filterBuilder 搜索对象
     * @param keywordList   关键词列表
     * @param fields        查询字段
     * @return 筛选对象
     */
    public static BoolQueryBuilder mutiKeywrodsQueryForFields(BoolQueryBuilder filterBuilder, List<String> keywordList, String fields) {
        BoolQueryBuilder tmpfilterBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(fields) && CollectionUtils.isNotEmpty(keywordList)) {
            String[] fieldAry = fields.split(",");
            for (String keywords : keywordList) {
                for (String field : fieldAry) {
                    tmpfilterBuilder.should(QueryBuilders.matchPhraseQuery(field, keywords));
                }
            }
            tmpfilterBuilder.minimumShouldMatch(1);
        }
        filterBuilder.must(tmpfilterBuilder);
        return filterBuilder;
    }

    /**
     * 关键字模糊匹配搜索,默认从keywords字段里面取
     *
     * @param filterBuilder 搜索对象
     * @param keyword       关键词
     * @return 筛选对象
     */
    public static BoolQueryBuilder keywordWildCardQueryWithOperator(BoolQueryBuilder filterBuilder, String keyword, Integer Type, String[] keywordFields) {
        if (StringUtils.isNotBlank(keyword)) {
            for (String field : keywordFields) {
                WildcardQueryBuilder wildQueryBuilder = QueryBuilders.wildcardQuery(field, WildCardQueryStrategyEnum.getStrategyKeyword(keyword, Type));
                filterBuilder.must(wildQueryBuilder);
            }
        }
        return filterBuilder;
    }

    /**
     * 关键字匹配,默认从keywords字段里面取
     *
     * @param filterBuilder
     * @param terms
     * @param keywordFields
     * @return
     */
    public static BoolQueryBuilder keywordQueryByTerm(BoolQueryBuilder filterBuilder, String[] terms, String[] keywordFields) {
        BoolQueryBuilder tmpfilterBuilder = QueryBuilders.boolQuery();
        if (terms != null && terms.length > 0) {
            for (String term : terms) {
                for (String field : keywordFields) {
                    tmpfilterBuilder.should(QueryBuilders.termQuery(field, term));
                }
            }
            tmpfilterBuilder.minimumShouldMatch(1);
        }
        filterBuilder.must(tmpfilterBuilder);
        return filterBuilder;
    }


    /**
     * 封装搜索结果
     * 格式eg:
     * {
     * "total":100,
     * "hits":[
     * {
     * "_id":"1111",
     * "_type":"type",
     * "_index":"index",
     * "_source":{
     * "field1":"v1",
     * "field2":"v2"
     * }
     * }
     * ]
     * }
     *
     * @param searchResponse 搜索结果
     * @return
     */
    public static JSONObject buildSearchResult(SearchResponse searchResponse) {
        JSONObject searchResult = new JSONObject();
        try {
            SearchHits hits = searchResponse.getHits();
            List<Map<String, Object>> hitsTmp = new ArrayList<>();
            for (SearchHit hit : hits) {
                Map<String, Object> hitTmp = new HashMap<>();
                hitTmp.put("_index", hit.getIndex());
                hitTmp.put("_type", hit.getType());
                hitTmp.put("_id", hit.getId());
//				hitTmp.put("_source", hit.getSource());
                hitTmp.put("_source", hit.getSourceAsMap());
                Map<String, HighlightField> highlightFields = hit.getHighlightFields();
                if (highlightFields != null) {
                    Map<String, Object> highLight = new HashMap<>();
                    for (String field : highlightFields.keySet()) {
                        List<String> highLightFragmentList = new ArrayList<>();
                        Text[] fragments = highlightFields.get(field).getFragments();
                        for (Text fragment : fragments) {
                            highLightFragmentList.add(fragment.string());
                        }
                        highLight.put(field, highLightFragmentList);
                    }
                    hitTmp.put("highlight", highLight);
                }
                hitsTmp.add(hitTmp);
            }
            searchResult.put("hits", hitsTmp);
            searchResult.put("total", hits.getTotalHits());
            return searchResult;
        } catch (Exception e) {
            log.error(e.getMessage());
            searchResult.put("hits", Collections.EMPTY_LIST);
            searchResult.put("total", 0);
            return searchResult;
        }
    }

    public static Map<String, Object> buildPageResult(SearchResponse searchResponse) {
        Map<String, Object> searchResult = new HashMap<>();
        try {
            SearchHits hits = searchResponse.getHits();
            List<Map<String, Object>> hitsTmp = new ArrayList<>();
            for (SearchHit hit : hits) {
                hit.getSourceAsMap().put("id", hit.getId());
                hit.getSourceAsMap().put("_highlight", convertHighlightFields(hit.getHighlightFields()));
                hitsTmp.add(hit.getSourceAsMap());
            }
            searchResult.put("list", hitsTmp);
            searchResult.put("total", hits.getTotalHits().value);
            return searchResult;
        } catch (Exception e) {
            log.error(e.getMessage());
            searchResult.put("hits", Collections.EMPTY_LIST);
            searchResult.put("list", 0);
            return searchResult;
        }
    }

    public static Map<String, Object> buildPageResultWithHighLight(SearchResponse searchResponse) {
        Map<String, Object> searchResult = new HashMap<>();
        try {
            SearchHits hits = searchResponse.getHits();
            List<Map<String, Object>> hitsTmp = new ArrayList<>();
            for (SearchHit hit : hits) {
                Map<String, Object> hitMap = hit.getSourceAsMap();
                hitMap.put("id", hit.getId());
                Map<String, List<String>> highlightMap = convertHighlightFields(hit.getHighlightFields());
                hitMap.put("_highlight", highlightMap);
                if (highlightMap != null) {
                    for (String key : highlightMap.keySet()) {
                        if (hitMap.containsKey(key)) {
                            hitMap.put(key, highlightMap.get(key).get(0));
                        }
                    }
                }
                hitsTmp.add(hitMap);
            }
            searchResult.put("list", hitsTmp);
            searchResult.put("total", hits.getTotalHits().value);
            return searchResult;
        } catch (Exception e) {
            log.error(e.getMessage());
            searchResult.put("hits", Collections.EMPTY_LIST);
            searchResult.put("list", 0);
            return searchResult;
        }
    }

    public static Map<String, List<String>> convertHighlightFields(Map<String, HighlightField> highlightFieldMap) {
        Map<String, List<String>> result = new HashMap<>();
        for (Map.Entry<String, HighlightField> entry : highlightFieldMap.entrySet()) {
            HighlightField highlightField = entry.getValue();
            List<String> contentList = new ArrayList<>();
            for (Text text : highlightField.getFragments()) {
                contentList.add(text.string());
            }
            result.put(highlightField.getName(), contentList);
        }
        return result;
    }

    /**
     * 适用于招商web中渠道信息处理
     * 对渠道结果中的时间字段增加文字说明,如一周内,一个月内
     *
     * @param searchResponse
     * @return
     */
    public static JSONObject buildSearchResultByTime(SearchResponse searchResponse) {
        JSONObject searchResult = new JSONObject();
        SearchHits hits = searchResponse.getHits();
        List<Map<String, Object>> hitsTmp = new ArrayList<>();
        for (SearchHit hit : hits) {
            Map<String, Object> hitTmp = new HashMap<>();
            hitTmp.put("_index", hit.getIndex());
            hitTmp.put("_type", hit.getType());
            hitTmp.put("_id", hit.getId());
            Map<String, Object> source = hit.getSourceAsMap();
            if (StringUtils.isNotBlank(source.get("channelTime").toString())) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Date endDate = new Date();
                try {
                    endDate = format.parse(source.get("channelTime").toString());
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
                Instant instant = endDate.toInstant();
                ZoneId zone = ZoneId.systemDefault();
                LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
                LocalDate localDate = localDateTime.toLocalDate();
                long day = LocalDate.now().until(localDate, ChronoUnit.DAYS);
                //long day=(endDate.getTime()-System.currentTimeMillis())/(24*60*60*1000);
                if (day < 0) {
                    source.put("emergency", "其他");
                } else if (day <= 3) {
                    source.put("emergency", "三天内");
                } else if (day <= 7) {
                    source.put("emergency", "一周内");
                } else if (day <= 30) {
                    source.put("emergency", "一个月内");
                } else if (day <= 90) {
                    source.put("emergency", "三个月内");
                } else {
                    source.put("emergency", "三个月外");
                }
            } else {
                source.put("emergency", "暂未确定");
            }
            hitTmp.put("_source", hit.getSourceAsMap());

            hitsTmp.add(hitTmp);
        }
        searchResult.put("hits", hitsTmp);
        searchResult.put("total", hits.getTotalHits());
        return searchResult;
    }


    /**
     * 将两层聚合结果转成List,用户招商web
     *
     * @param searchResponse
     * @param parentBucketName
     * @param childBucketName
     * @return
     */
    public static List<Map<String, Object>> getAggretionsFromResponse(SearchResponse searchResponse, String parentBucketName, String childBucketName) {
        Terms terms = searchResponse.getAggregations().get(parentBucketName);
        List<Map<String, Object>> list = new ArrayList<>();
        for (Terms.Bucket entry : terms.getBuckets()) {
            Map<String, Object> map = new HashMap<>();
            map.put("parent", entry.getKeyAsString());
            map.put("count", entry.getDocCount());
            Terms child = entry.getAggregations().get(childBucketName);
            List<String> childList = new ArrayList<>();
            for (Terms.Bucket childEntry : child.getBuckets()) {
                childList.add(childEntry.getKeyAsString());
            }
            map.put("child", childList);
            list.add(map);
        }
        return list;
    }

    /**
     * 根据值精准匹配字段,用于招商web
     *
     * @param index    索引
     * @param field    字段
     * @param value    值
     * @param page     起始页
     * @param pageSize 每页结果
     * @return
     */
    public JSONObject listByField(String index, String field, String value, Integer page, Integer pageSize) {
        // 初始化布尔查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(value)) {
            boolQuery.filter(QueryBuilders.termQuery(field, value));
        }
        SearchResponse searchResponse = pageByFields(index, null, boolQuery, page, pageSize, null, new ArrayList<>(0));
        return buildSearchResult(searchResponse);
    }

    /**
     * 条件分页查询
     * {
     * "value": 10000,
     * "relation": "GREATER_THAN_OR_EQUAL_TO"
     * }
     * 总数上限有一万限制
     *
     * @param index
     * @param keyword
     * @param queryBuilder
     * @param page
     * @param pageSize
     * @param sort
     * @return
     */
    public SearchResponse pageByFields(String index, String keyword, QueryBuilder queryBuilder, Integer page, Integer pageSize, String sort, List<FunctionScoreQueryBuilder.FilterFunctionBuilder> filterFunctionBuilders) {
        // TODO: 深分页 search_after
        // 初始化查询请求对象
        SearchSourceBuilder searchBuilder = initSearchRequest(keyword);
        searchBuilder.trackTotalHits(true);
        searchBuilder.query(queryBuilder);
        if (StringUtils.isEmpty(keyword)) {
            EsAlterUtil.addSort(searchBuilder, sort, filterFunctionBuilders);
        } else {
            searchBuilder.sort("_score", SortOrder.DESC);
        }
        String[] includes = EsIndexEnum.getEsIndexByIndex(index).getEsSearchFields();
        if (includes != null && includes.length > 0) {
            searchBuilder.fetchSource(includes, null);
        }
        pagingSet(searchBuilder, page, pageSize);
        searchBuilder.explain(false);
        System.out.println("filter->" + JSONObject.toJSONString(searchBuilder));
        SearchResponse searchResponse = null;
        try {
            logger.info("分页查询参数:{} {}", index, searchBuilder);
            log.info("searchRequest:{}", getBaseSearchRequest(index, searchBuilder));
            searchResponse = restHighLevelClient.search(getBaseSearchRequest(index, searchBuilder), RequestOptions.DEFAULT);
            //logger.debug("分页查询结果:{}", searchResponse);
        } catch (Exception e) {
            log.error("listByField, index:{},searchBuilder:{},error:", index, searchBuilder, e);
        }
        return searchResponse;
    }

    /**
     * 条件分页查询
     * {
     * "value": 10000,
     * "relation": "GREATER_THAN_OR_EQUAL_TO"
     * }
     * 总数上限有一万限制
     *
     * @param index
     * @param keyword
     * @param queryBuilder
     * @param page
     * @param pageSize
     * @param sort
     * @return
     */
    public SearchResponse pageByFields(String index, String keyword, QueryBuilder queryBuilder, Integer page, Integer pageSize, String sort, String includeFields) {
        // TODO: 深分页 search_after
        // 初始化查询请求对象
        SearchSourceBuilder searchBuilder = initSearchRequest(keyword);
        searchBuilder.trackTotalHits(true);
        searchBuilder.query(queryBuilder);
        if (StringUtils.isNotBlank(includeFields)) {
            FetchSourceContext fetchSourceContext = new FetchSourceContext(true, includeFields.split(","), null);
            searchBuilder.fetchSource(fetchSourceContext);
        }
        pagingSet(searchBuilder, page, pageSize);
        searchBuilder.explain(false);
        System.out.println("filter->" + JSONObject.toJSONString(searchBuilder));
        if (StringUtils.isNotEmpty(sort)) {
            addSort(searchBuilder, sort);
        }
        SearchResponse searchResponse = null;
        try {
            logger.info("分页查询参数:{} {}", index, searchBuilder);
            log.info("searchRequest:{}", getBaseSearchRequest(index, searchBuilder));
            searchResponse = restHighLevelClient.search(getBaseSearchRequest(index, searchBuilder), RequestOptions.DEFAULT);
            logger.info("分页查询结果:{}", searchResponse);
        } catch (Exception e) {
            log.error("listByField, index:{},searchBuilder:{},error:", index, searchBuilder, e);
        }
        return searchResponse;
    }

    /**
     * 条件分页查询
     * {
     * "value": 10000,
     * "relation": "GREATER_THAN_OR_EQUAL_TO"
     * }
     * 总数上限有一万限制
     *
     * @param index
     * @param keyword
     * @param queryBuilder
     * @param page
     * @param pageSize
     * @param sort
     * @param highlightFields
     * @return
     */
    public SearchResponse pageByFields(String index, String keyword, QueryBuilder queryBuilder, Integer page, Integer pageSize, String sort, Set<String> highlightFields) {
        // TODO: 深分页 search_after
        // 初始化查询请求对象
        SearchSourceBuilder searchBuilder = initSearchRequest(keyword);
        searchBuilder.trackTotalHits(true);
        if (CollectionUtils.isNotEmpty(highlightFields)) {
            HighlightBuilder highlightBuilder = setHighlightBuilder(highlightFields);
            searchBuilder.highlighter(highlightBuilder);
        }
        searchBuilder.query(queryBuilder);
        pagingSet(searchBuilder, page, pageSize);
        searchBuilder.explain(false);
        if (StringUtils.isNotEmpty(sort)) {
            addSort(searchBuilder, sort);
        }
        SearchResponse searchResponse = null;
        try {
            logger.info("分页查询参数:{} {}", index, searchBuilder);
            log.info("searchRequest:{}", getBaseSearchRequest(index, searchBuilder));
            searchResponse = restHighLevelClient.search(getBaseSearchRequest(index, searchBuilder), RequestOptions.DEFAULT);
            logger.info("分页查询结果:{}", searchResponse);
        } catch (Exception e) {
            log.error("listByField, index:{},searchBuilder:{},error:", index, searchBuilder, e);
        }
        return searchResponse;
    }

    /**
     * 构建基础 SearchRequest
     *
     * @param index
     * @param searchBuilder
     * @return
     */
    private SearchRequest getBaseSearchRequest(String index, SearchSourceBuilder searchBuilder) {
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchBuilder);
        searchRequest.searchType(SearchType.DEFAULT);
        return searchRequest;
    }

    /**
     * 条件查询
     *
     * @param index
     * @param keyword
     * @param queryBuilder
     * @param sort
     * @return
     */
    public SearchResponse findByQueryCondition(String index, String keyword, QueryBuilder queryBuilder, String sort) {
        // 初始化查询请求对象
        SearchSourceBuilder searchSourceBuilder = initSearchRequest(keyword);
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.explain(false);
        if (StringUtils.isNotEmpty(sort)) {
            addSort(searchSourceBuilder, sort);
        }
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(getBaseSearchRequest(index, searchSourceBuilder), RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("listByField, index:{},searchBuilder:{},error:", index, searchSourceBuilder, e);
        }
        return searchResponse;
    }

    /**
     * 获取相关对象的属性
     *
     * @param index
     * @param filterFiled
     * @param filterValue
     * @return
     */
    public List getRelateEntities(String index, String filterFiled, String[] filterValue) {
        // 初始化布尔查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        ElasticsearchBuilder.addFilterCondition(boolQuery, filterFiled, filterValue);
        // 获取结果
        SearchResponse searchResponse = pageByFields(index, null, boolQuery, 0, 10000, null, new ArrayList<>(0));
        List<Map<String, Object>> list = new ArrayList<>();
        if (searchResponse == null) {
            return list;
        }
        SearchHits hits = searchResponse.getHits();
        for (SearchHit hit : hits) {
            hit.getSourceAsMap().put("id", hit.getId());
            list.add(hit.getSourceAsMap());
        }
        return list;
    }

    /**
     * 按照chain name条件查询
     *
     * @param index
     * @param chainName
     * @param pageSize
     * @return
     */
    public List searchByChainName(String index, String chainName, int pageSize) {
        // 初始化布尔查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        ElasticsearchBuilder.addFilterCondition(boolQuery, "chain_node.chain_name", new String[]{chainName});
        SearchResponse searchResponse = pageByFields(index, null, boolQuery,
                0, pageSize, null, new ArrayList<>(0));
        return EsResultUtil.getListFromSearchResponse(searchResponse);
    }

    /**
     * 应用于招商web,按时间字段聚合
     *
     * @param searchBuilder
     * @param field
     * @return
     */
    public JSONObject agressByYear(SearchSourceBuilder searchBuilder, String field) {
        JSONObject searchResult = new JSONObject();
        searchBuilder.aggregation(AggregationBuilders.dateHistogram("year")
                .calendarInterval(DateHistogramInterval.YEAR).format("yyyy").field(field));
        // 分页
        pagingSet(searchBuilder, 0, 0);
        searchBuilder.explain(false);
        log.info(searchBuilder.toString());
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.source(searchBuilder);
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("listByField, field:{},searchBuilder:{},error:", field, searchBuilder, e);
        }
        if (null == searchResponse) {
            return searchResult;
        }
        // 封装结果
        Histogram histogram = searchResponse.getAggregations().get("year");
        List<Map<String, Object>> list = new ArrayList<>();
        for (Histogram.Bucket bucket : histogram.getBuckets()) {
            Map<String, Object> timeMap = new HashMap<>();
            timeMap.put("time", bucket.getKeyAsString());
            timeMap.put("count", bucket.getDocCount());
            list.add(timeMap);
        }
        searchResult = ElasticsearchBuilder.buildSearchResult(searchResponse);
        searchResult.put("statitic", list);
        return searchResult;
    }

    /**
     * 只获取bucket聚合结果
     *
     * @param index
     * @param type
     * @param bucketsFields
     * @return
     */
    public Map<String, Map<String, Long>> getOnlyBucketsAggretions(String index, String type, String[] bucketsFields) {
        return getOnlyBucketsAggretions(index, bucketsFields, null, null, null, null);
    }

    /**
     * 区间聚合
     * 只获取bucket聚合结果
     *
     * @param index
     * @param filterBuilder
     * @return
     * @Param bucketsField 聚合字段
     */
    public Map<String, Map<String, Long>> getBucketsAggregationsByArea(String index, String bucketsField, Map<String, FinanceArea> financeAreaMap,
                                                                       BoolQueryBuilder filterBuilder) {
        Map<String, Map<String, Long>> aggregationResult = new HashMap<>();
        // 初始化查询请求对象
        SearchSourceBuilder searchBuilder = initSearchRequest(null);

        //分区桶列表
        List<String> areaNameList = new ArrayList<>();

        if (null != financeAreaMap) {
            for (Map.Entry<String, FinanceArea> entry : financeAreaMap.entrySet()) {
                //设置分区桶名称
                String areaName = entry.getKey();
                //保存区间名称
                areaNameList.add(areaName);
            }
        }
        addBucketAggregationList(searchBuilder, bucketsField, financeAreaMap);


        searchBuilder.size(0);
        if (filterBuilder != null) {
            searchBuilder.query(filterBuilder);
        }
        log.info("打印分段聚合 index :{} 参数: {}", index, searchBuilder);
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchBuilder);
        // 获取结果
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("getBucketsAggregationsByArea,index:{},searchBuilder:{},error:", index, searchBuilder, e);
            return aggregationResult;
        }
        return getBucketsAggregationsFromResponse(searchResponse, areaNameList, bucketsField);
    }


    /**
     * 只获取bucket聚合结果
     *
     * @param index
     * @param bucketsFields
     * @param keyword
     * @param keywordQueryFields
     * @param filterBuilder
     * @return
     */
    public Map<String, Map<String, Long>> getOnlyBucketsAggretions(String index, String[] bucketsFields,
                                                                   String keyword, String[] keywordQueryFields,
                                                                   Operator queryLogic,
                                                                   BoolQueryBuilder filterBuilder) {
        Map<String, Map<String, Long>> aggregationResult = new HashMap<>();
        // 初始化查询请求对象
        SearchSourceBuilder searchBuilder = initSearchRequest(null);
        addBucketAggrs(searchBuilder, bucketsFields);

        //关键词
        if (queryLogic != null && StringUtils.isNotBlank(keyword) && keywordQueryFields != null && keywordQueryFields.length > 0) {
            keywordQueryWithOperator(filterBuilder, keyword, queryLogic, keywordQueryFields);
        }
        searchBuilder.size(0);
        if (filterBuilder != null) {
            searchBuilder.query(filterBuilder);
        }
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchBuilder);
        // 获取结果
        SearchResponse searchResponse = null;

        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("getOnlyBucketsAggretions,index:{},searchBuilder:{},error:", index, searchBuilder, e);
            return aggregationResult;
        }
        return getBucketsAggretionsFromResponse(searchResponse, bucketsFields);
    }


    public Map<String, Map<String, Long>> getTimeOfYearAggregations(String index, String[] bucketsFields,
                                                                    String keyword, String[] keywordQueryFields,
                                                                    Operator queryLogic,
                                                                    BoolQueryBuilder filterBuilder) {
        Map<String, Map<String, Long>> aggregationResult = new HashMap<>();
        // 初始化查询请求对象
        SearchSourceBuilder searchBuilder = initSearchRequest(null);
        //年份聚合
        addYearBucketAggrs(searchBuilder, bucketsFields);
        logger.info("打印年份聚合条件:{}", searchBuilder);

        //关键词
        if (queryLogic != null && StringUtils.isNotBlank(keyword) && keywordQueryFields != null && keywordQueryFields.length > 0) {
            keywordQueryWithOperator(filterBuilder, keyword, queryLogic, keywordQueryFields);
        }
        searchBuilder.size(0);
        if (filterBuilder != null) {
            searchBuilder.query(filterBuilder);
        }
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchBuilder);
        // 获取结果
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            logger.info("按年份聚合结果:{}", searchResponse);
        } catch (Exception e) {
            log.error("getTimeOfYearAggregations,index:{},searchBuilder:{},error:", index, searchBuilder, e);
            return aggregationResult;
        }
        return getYearBucketsAggretionsFromResponse(searchResponse, bucketsFields);
    }

    /**
     * 设置需要高亮的字段
     *
     * @param fields
     * @return
     */
    public HighlightBuilder setHighlightBuilder(Set<String> fields) {
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        //设置高亮字段
        for (String field : fields) {
            highlightBuilder.field(field);
        }
        //如果要多个字段高亮,这项要为false
        highlightBuilder.requireFieldMatch(false);
        highlightBuilder.preTags("<em>");
        highlightBuilder.postTags("</em>");
//        //下面这两项,如果你要高亮如文字内容等有很多字的字段,必须配置,不然会导致高亮不全,文章内容缺失等
//        highlightBuilder.fragmentSize(800000); //最大高亮分片数
//        highlightBuilder.numOfFragments(0); //从第一个分片获取高亮片段
        return highlightBuilder;
    }

    /**
     * 从原始的SearchResponse结果中构建需要分页结果，并高亮结果
     */
    public static EsPageResult buildPageResultWithHighlight(final SearchResponse searchResponse, Map<String, String> keywordHighLightMap) {
        final EsPageResult pageResult = new EsPageResult();
        if (searchResponse == null) {
            return pageResult;
        }
        try {
            final SearchHits hits = searchResponse.getHits();
            final SearchHit[] searchHits = hits.getHits();
            final List<Map<String, Object>> list = new ArrayList<>(searchHits.length);
            final List<String> alreadyHighlightFields = new ArrayList<>();
            for (final SearchHit searchHit : searchHits) {
                final Map<String, Object> sourceAsMap = searchHit.getSourceAsMap();
                // 设置es算分
                sourceAsMap.put(ES_SCORE, searchHit.getScore());
                final Map<String, HighlightField> highlightFields = searchHit.getHighlightFields();
                // 设置高亮字段
                for (final Map.Entry<String, HighlightField> entry : highlightFields.entrySet()) {
                    final HighlightField highlightField = entry.getValue();
                    final String key = entry.getKey();
                    alreadyHighlightFields.add(key);
                    // 判断这个key字段是否为对象内的字段，是的话不能直接put，需要获取对应的字段
                    if (key.contains(".")) {
                        final String[] split = key.split("\\.");
                        final String objectKey = split[0];
                        final String actualKey = split[1];
                        final Object object = sourceAsMap.get(objectKey);
                        String keyword = keywordHighLightMap.get(key);
                        if (object instanceof Map) {
                            final Map objectMap = (Map) object;
                            final String fieldValue = (String) objectMap.get(actualKey);
                            objectMap.put(actualKey,
                                    replacementInfo(fieldValue, keyword, "<span style=\"color:rgb(30, 132, 246)\">", "</span>"));
                        } else if (object instanceof List) {
                            final List<Map> objectList = (List) object;
                            objectList.forEach(item -> {
                                final String fieldValue = (String) item.get(actualKey);
                                item.put(actualKey,
                                        replacementInfo(fieldValue, keyword, "<span style=\"color:rgb(30, 132, 246)\">", "</span>"));
                            });
                        }
                        sourceAsMap.put(objectKey, object);
                    } else {
                        sourceAsMap.put(key, StringUtils.join(
                                Arrays.stream(highlightField.getFragments()).map(Text::string).collect(Collectors.toList()), ","));
                    }
                }
                for (Map.Entry<String, String> keywordHighlight : keywordHighLightMap.entrySet()) {
                    // 获取原本需要高亮的字段列表，手动进行高亮
                    final String fieldValue = (String) sourceAsMap.get(keywordHighlight.getKey());
                    if (fieldValue != null) {
                        // 分词
                        List<Term> terms = HanLP.segment(keywordHighlight.getValue());
                        sourceAsMap.put(keywordHighlight.getKey(),
                                replacementInfo(fieldValue, terms, "<span style=\"color:rgb(30, 132, 246)\">", "</span>"));
                    }
                }

                list.add(sourceAsMap);
            }
            pageResult.setList(list);
            pageResult.setTotal(hits.getTotalHits().value);
            return pageResult;
        } catch (final Exception e) {
            log.error("构建分页查询结果失败", e);
            return pageResult;
        }
    }

    /**
     * 在某字符前后添加字段
     *
     * @param string：原字符串
     * @param terms：字符
     * @param before：在字符前需要插入的字段
     * @param rear：在字符后需要插入的字段
     * @return
     */
    public static String replacementInfo(final String string, final List<Term> terms, final String before, final String rear) {
        if (StringUtils.isBlank(string) || CollectionUtils.isEmpty(terms)) {
            return string;
        }
        List<Term> subTerms = terms;
        if (terms.size() >= 50) {
            subTerms = terms.subList(0, 50);
        }
        List<String> keywords = new ArrayList<>();
        for (Term term : subTerms) {
            keywords.add(term.word);
        }
        AhoCorasickDoubleArrayTrie<String> acdt = buildAcdt(keywords);
        return highLight(string, acdt, before, rear);
    }

    /**
     * 在某字符前后添加字段
     *
     * @param string：原字符串
     * @param keyword：字符
     * @param before：在字符前需要插入的字段
     * @param rear：在字符后需要插入的字段
     * @return
     */
    public static String replacementInfo(final String string, final String keyword, final String before, final String rear) {
        if (StringUtils.isBlank(string) || StringUtils.isBlank(keyword)) {
            return string;
        }
        AhoCorasickDoubleArrayTrie<String> acdt = buildAcdt(Arrays.asList(keyword));
        return highLight(string, acdt, before, rear);
    }

    /**
     * 构建ac自动机
     */
    public static AhoCorasickDoubleArrayTrie<String> buildAcdt(List<String> keywords) {
        AhoCorasickDoubleArrayTrie<String> acdt = new AhoCorasickDoubleArrayTrie<>();
        TreeMap<String, String> map = new TreeMap<>();
        for (String keyword : keywords) {
            map.put(keyword, keyword);
        }
        acdt.build(map);
        return acdt;
    }

    public static String highLight(String originText, AhoCorasickDoubleArrayTrie<String> acdt, String beforeTag, String endTag) {
        List<int[]> hitLocationList = new ArrayList<>();
        // ac算法匹配关键词
        acdt.parseText(originText, (begin, end, value) -> {
            int[] indexPair = new int[2];
            indexPair[0] = begin;
            indexPair[1] = end - 1;
            hitLocationList.add(indexPair);
        });
        // 合并前后词差距仅有一个
        List<int[]> combineHitLocationList = hitLocationList.size() > 1?new ArrayList<>(): hitLocationList;
        for (int i = 0; i < hitLocationList.size() - 1; i++) {
            int[] currentItem = hitLocationList.get(i);
            int[] nextItem = hitLocationList.get(i + 1);
            if (nextItem[0] - currentItem[1] <= 1) {
                int[] indexPair = new int[2];
                indexPair[0] = currentItem[0];
                indexPair[1] = nextItem[1];
                combineHitLocationList.add(indexPair);
                i++;
            } else if (currentItem[0] == currentItem[1]){
                continue;
            }else{
                combineHitLocationList.add(currentItem);
            }
        }
        // 构建bitmap
        byte[] posStatus = new byte[originText.length()];
        for (int[] item : combineHitLocationList) {
            posStatus[item[0]] = 1;
            for (int i = item[0]; i <= item[1]; i++) {
                posStatus[i] = 1;
            }
        }
        // 字符串拼接
        int lastStatus = 0;
        char[] charArray = originText.toCharArray();
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < posStatus.length; i++) {
            if (posStatus[i] == lastStatus) {
                stringBuilder.append(charArray[i]);
            } else if (0 == lastStatus) {
                stringBuilder.append(beforeTag).append(charArray[i]);
                lastStatus = 1;
            } else if (1 == lastStatus) {
                stringBuilder.append(endTag).append(charArray[i]);
                lastStatus = 0;
            }
            if (i == posStatus.length - 1 && 1 == lastStatus) {
                stringBuilder.append(endTag);
            }
        }

        return stringBuilder.toString();
    }
}
