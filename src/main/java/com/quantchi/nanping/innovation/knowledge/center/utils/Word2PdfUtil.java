package com.quantchi.nanping.innovation.knowledge.center.utils;

import com.aspose.words.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2024/9/6 上午9:33
 */
public class Word2PdfUtil {
    public static boolean getLicense() {
        boolean result = false;
        try {
            // license.xml应放在..\WebRoot\WEB-INF\classes路径下
            InputStream is = Word2PdfUtil.class.getClassLoader().getResourceAsStream("WEB-INF/license.xml");
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 设置字体资源
     * 不设置会导致word转pdf的时候字体丢失
     */
    public static void setFontsSources() {
        String fontsDir = "/usr/share/fonts/";

        //将用户目录字体添加到字体源中
        FontSourceBase[] originalFontSources = FontSettings.getFontsSources();
        FolderFontSource folderFontSource = new FolderFontSource(fontsDir, true);

        FontSourceBase[] updatedFontSources = {originalFontSources[0], folderFontSource};
        FontSettings.setFontsSources(updatedFontSources);
    }

    public static void doc2pdf(String inPath, String outPath) {
        setFontsSources();
        if (!getLicense()) { // 验证License 若不验证则转化出的pdf文档会有水印产生
            return;
        }
        try {
            long old = System.currentTimeMillis();
            File file = new File(outPath); // 新建一个空白pdf文档
            FileOutputStream os = new FileOutputStream(file);
            Document doc = new Document(inPath); // Address是将要被转化的word文档
            FontSettings.setFontsFolder("/usr/share/fonts/", false);
            doc.save(os, SaveFormat.PDF);// 全面支持DOC, DOCX, OOXML, RTF HTML, OpenDocument, PDF,
            // EPUB, XPS, SWF 相互转换
            long now = System.currentTimeMillis();
            System.out.println("共耗时：" + ((now - old) / 1000.0) + "秒"); // 转化用时
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
