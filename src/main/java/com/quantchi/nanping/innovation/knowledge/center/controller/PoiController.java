package com.quantchi.nanping.innovation.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.component.FjBigDataComponent;
import com.quantchi.nanping.innovation.knowledge.center.service.ICompanyTechnicalService;
import com.quantchi.nanping.innovation.knowledge.center.service.PatentPortraitService;
import com.quantchi.nanping.innovation.knowledge.center.utils.LocalFileUtil;
import com.quantchi.nanping.innovation.knowledge.center.utils.Word2PdfUtil;
import com.quantchi.nanping.innovation.knowledge.center.utils.export.CompanyExportUtil;
import com.quantchi.nanping.innovation.knowledge.center.utils.export.PatentExportUtil;
import com.quantchi.nanping.innovation.model.bo.FjBigDataAuthorizeBO;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.docx4j.Docx4J;
import org.docx4j.fonts.IdentityPlusMapper;
import org.docx4j.fonts.Mapper;
import org.docx4j.fonts.PhysicalFonts;
import org.docx4j.math.STJc;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.xml.XMLConstants;
import javax.xml.transform.TransformerFactory;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;


@Slf4j
@RestController
@RequestMapping("/export")
@Api(tags = "专利画像接口")
@RequiredArgsConstructor
public class PoiController {

    private final PatentPortraitService patentPortraitService;

    private final PatentExportUtil patentExportUtil;

    private final CompanyExportUtil companyExportUtil;

    private final ICompanyTechnicalService companyTechnicalService;

    private final FjBigDataComponent fjBigDataComponent;

    private final ElasticsearchHelper elasticsearchHelper;

    private final ThreadPoolTaskExecutor docExecutor;

    public static final String REPORT_FILE_MODULE_NAME = "report";

    @GetMapping("/patentReport")
    @ApiOperation("专利画像分析报告pdf数据")
    public void getPatentReport(@RequestParam final String id,
                                final HttpServletResponse response) throws Exception {
        TimeInterval timer = new TimeInterval();
        final Map<String, Object> dataMap = Collections.synchronizedMap(new HashMap<>());
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(5);
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                patentExportUtil.setPatentOverview(id, dataMap);
            } catch (IOException e) {
                log.error("setPatentOverview error", e);
            }
        }, docExecutor));
        boolean valueExist = patentPortraitService.displayValue(id);
        if (valueExist) {
            futureList.add(CompletableFuture.runAsync(() -> {
                try {
                    patentExportUtil.setValueEvaluation(id, dataMap);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }, docExecutor));
        }
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                patentExportUtil.setCitedPatent(id, dataMap);
            } catch (IOException e) {
                log.error("setCitedPatent error", e);
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                patentExportUtil.setCognatePatent(id, dataMap);
            } catch (IOException e) {
                log.error("setCognatePatent error", e);
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            patentExportUtil.setSimilarPatent(id, dataMap);
        }, docExecutor));
        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取并填充分析报告数据耗时{}ms", timer.interval());
        timer = new TimeInterval();

        // 构建文件
        final String fileName = dataMap.get("patentName") + ".docx";
        final String pdfFileName = fileName.replace("docx", "pdf");
        final String wordPath = LocalFileUtil.getActualFilePath(REPORT_FILE_MODULE_NAME, fileName);
        final String targetPdfPath = wordPath.replace("docx", "pdf");

        // 使用 getResourceAsStream 加载模板
        final InputStream templateStream = this.getClass().getClassLoader()
                .getResourceAsStream(valueExist ? "templates/patentTemplate.docx" : "templates/patentTemplate_noValue.docx");

        if (templateStream != null) {
            XWPFTemplate.compile(templateStream)
                    .render(dataMap)
                    .writeToFile(wordPath);
        }
        log.info("word文件生成耗时{}ms", timer.interval());
        timer = new TimeInterval();
        // word转pdf
        Word2PdfUtil.doc2pdf(wordPath, targetPdfPath);
//        docxToPdf(wordPath, targetPdfPath);
        log.info("pdf文件生成耗时{}ms", timer.interval());
        // 将文件转成下载链接
        final File file = new File(targetPdfPath);
        if (!file.exists()) {
            throw new BusinessException("文件生成失败");
        }
        // 将文件写入输入流
        final FileInputStream fileInputStream = new FileInputStream(file);
        generateFileResponse(pdfFileName, true, fileInputStream, response);
    }

    @GetMapping("/companyReport")
    @ApiOperation("企业画像分析报告pdf数据")
    public void getCompanyReport(@RequestParam final String id,
                                final HttpServletResponse response) throws Exception {
        TimeInterval timer = new TimeInterval();
        final Map<String, Object> dataMap = Collections.synchronizedMap(new HashMap<>());
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(5);
        // 企业基本信息
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                companyExportUtil.setBaseInfo(id, dataMap);
            } catch (IOException e) {
                log.error("setBaseInfo error", e);
            }
        }, docExecutor));
        // 企业补充信息
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                companyExportUtil.setMetaInfo(id, dataMap);
            } catch (IOException e) {
                log.error("setMetaInfo error", e);
            }
        }, docExecutor));
        // 企业科创能级评价
        boolean techExist = companyTechnicalService.dsiplayTechnicalEvaluation(id);
        if (techExist) {
            futureList.add(CompletableFuture.runAsync(() -> {
                try {
                    companyExportUtil.setTechInfo(id, dataMap);
                } catch (IOException e) {
                    log.error("setTechInfo error", e);
                }
            }, docExecutor));
        }
        // 企业专利
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                companyExportUtil.setPatentInfo(id, dataMap);
            } catch (IOException e) {
                log.error("setPatentInfo error", e);
            }
        }, docExecutor));
        // 企业融资
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                companyExportUtil.setFinanceInfo(id, dataMap);
            } catch (IOException e) {
                log.error("setFinanceInfo error", e);
            }
        }, docExecutor));
        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取并填充分析报告数据耗时{}ms", timer.interval());
        timer = new TimeInterval();

        // 构建文件
        final String fileName = dataMap.get("company_name") + ".docx";
        final String pdfFileName = fileName.replace("docx", "pdf");
        final String wordPath = LocalFileUtil.getActualFilePath(REPORT_FILE_MODULE_NAME, fileName);
        final String targetPdfPath = wordPath.replace("docx", "pdf");

        // 使用 getResourceAsStream 加载模板
        final InputStream templateStream = this.getClass().getClassLoader()
                .getResourceAsStream(techExist ? "templates/companyTemplate.docx" : "templates/companyTemplate_noTech.docx");

        if (templateStream != null) {
            XWPFTemplate.compile(templateStream)
                    .render(dataMap)
                    .writeToFile(wordPath);
        }
        log.info("word文件生成耗时{}ms", timer.interval());
        timer = new TimeInterval();
        // word转pdf
        Word2PdfUtil.doc2pdf(wordPath, targetPdfPath);
        log.info("pdf文件生成耗时{}ms", timer.interval());
        // 将文件转成下载链接
        final File file = new File(targetPdfPath);
        if (!file.exists()) {
            throw new BusinessException("文件生成失败");
        }
        // 将文件写入输入流
        final FileInputStream fileInputStream = new FileInputStream(file);
        generateFileResponse(pdfFileName, true, fileInputStream, response);
    }

    @PostMapping(value = "/companyReportFromBigData", consumes = "multipart/form-data")
    @ApiOperation("企业画像分析报告pdf数据")
    public void getCompanyReportFromBigData(FjBigDataAuthorizeBO param,
                                final HttpServletResponse response) throws Exception {
        if (StringUtils.isBlank(param.getUscc())) {
            log.error("/companyReportFromBigData: uscc is blank!");
            throw new IllegalArgumentException("uscc cannot be blank!");
        }
        TimeInterval timer = new TimeInterval();
        final Map<String, Object> dataMap = Collections.synchronizedMap(new HashMap<>());
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("social_credit_code", param.getUscc()));
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, 1,
                null, null, "field_rank:desc");
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.COMPANY.getEsIndex());
        SearchHits hits = searchResponse.getHits();
        if (hits == null || hits.getHits() == null || hits.getHits().length == 0 || hits.getHits()[0].getSourceAsMap() == null) {
            log.error("there is no company with the social_credit_code!");
            throw new IllegalArgumentException("there is no company with the social_credit_code!");
        }
        String id = Optional.ofNullable(hits.getHits()[0].getSourceAsMap().get("id")).map(Object::toString).orElse(null);
        if(StringUtils.isBlank(id)) {
            log.error("there is no company with the social_credit_code!");
            throw new IllegalArgumentException("there is no company with the social_credit_code!");
        }
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(6);
        // 企业基本信息
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                companyExportUtil.setBaseInfo(id, dataMap);
            } catch (IOException e) {
                log.error("setBaseInfo error", e);
            }
        }, docExecutor));
        // 企业补充信息
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                companyExportUtil.setMetaInfo(id, dataMap);
            } catch (IOException e) {
                log.error("setMetaInfo error", e);
            }
        }, docExecutor));
        // 企业科创能级评价
        boolean techExist = companyTechnicalService.dsiplayTechnicalEvaluation(id);
        if (techExist) {
            futureList.add(CompletableFuture.runAsync(() -> {
                try {
                    companyExportUtil.setTechInfo(id, dataMap);
                } catch (IOException e) {
                    log.error("setTechInfo error", e);
                }
            }, docExecutor));
        }
        // 企业专利
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                companyExportUtil.setPatentInfo(id, dataMap);
            } catch (IOException e) {
                log.error("setPatentInfo error", e);
            }
        }, docExecutor));
        // 企业融资
        futureList.add(CompletableFuture.runAsync(() -> {
            try {
                companyExportUtil.setFinanceInfo(id, dataMap);
            } catch (IOException e) {
                log.error("setFinanceInfo error", e);
            }
        }, docExecutor));
        // 企业信用评估与信贷服务
        JSONObject creditInfo = fjBigDataComponent.getCreditInfo(param);
        boolean creditInfoExist = creditInfo != null && "200".equals(creditInfo.getString("code"));
        if (creditInfoExist) {
            futureList.add(CompletableFuture.runAsync(() -> {
                try {
                    companyExportUtil.setCreditInfo(creditInfo, param.getYear(), dataMap);
                } catch (Exception e) {
                    log.error("setTechInfo error", e);
                }
            }, docExecutor));
        }
        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取并填充分析报告数据耗时{}ms", timer.interval());
        timer = new TimeInterval();

        // 构建文件
        final String fileName = dataMap.get("company_name") + ".docx";
        final String pdfFileName = fileName.replace("docx", "pdf");
        final String wordPath = LocalFileUtil.getActualFilePath(REPORT_FILE_MODULE_NAME, fileName);
        final String targetPdfPath = wordPath.replace("docx", "pdf");

        // 使用 getResourceAsStream 加载模板
        String template = "";
        if (techExist && creditInfoExist) {
            template = "templates/companyTemplate_credit.docx";
        } else if (techExist && !creditInfoExist) {
            template = "templates/companyTemplate.docx";
        } else if (!techExist && creditInfoExist) {
            template = "templates/companyTemplate_credit_noTech.docx";
        } else {
            template = "templates/companyTemplate_noTech.docx";
        }
        final InputStream templateStream = this.getClass().getClassLoader()
                .getResourceAsStream(template);

        if (templateStream != null) {
            XWPFTemplate.compile(templateStream)
                    .render(dataMap)
                    .writeToFile(wordPath);
        }
        log.info("word文件生成耗时{}ms", timer.interval());
        timer = new TimeInterval();
        // word转pdf
        Word2PdfUtil.doc2pdf(wordPath, targetPdfPath);
        log.info("pdf文件生成耗时{}ms", timer.interval());
        // 将文件转成下载链接
        final File file = new File(targetPdfPath);
        if (!file.exists()) {
            throw new BusinessException("文件生成失败");
        }
        // 将文件写入输入流
        final FileInputStream fileInputStream = new FileInputStream(file);
        generateFileResponse(pdfFileName, true, fileInputStream, response);

    }

    /**
     * 使用docx4j将word转成pdf
     *
     * @param wordPath
     * @param targetPdfPath
     */
    public void docxToPdf(final String wordPath, final String targetPdfPath) {
        try {
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            transformerFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_STYLESHEET, "all");
        } catch (Exception e) {
            log.error("docxToPdf error", e);
        }
        try {
            WordprocessingMLPackage pkg = Docx4J.load(new File(wordPath));
            final Mapper fontMapper = new IdentityPlusMapper();
            fontMapper.put("宋体", PhysicalFonts.get("SimSun"));
            pkg.setFontMapper(fontMapper);
            Docx4J.toPDF(pkg, new FileOutputStream(targetPdfPath));
        } catch (Exception e) {
            // 忽略不支持的属性设置
            log.error("word转pdf失败", e);
        }
    }

    /**
     * 构建文件数据流返回
     */
    public void generateFileResponse(final String originFileName,
                                     final Boolean preview,
                                     final InputStream inputStream,
                                     final HttpServletResponse response) {
        OutputStream outputStream = null;
        try {
            final byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            inputStream.close();
            response.reset();
            // 设置response的Header
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "*");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            if (originFileName.contains("png")) {
                response.setContentType(MediaType.IMAGE_PNG_VALUE);
            } else if (originFileName.contains("jpg")) {
                response.setContentType(MediaType.IMAGE_JPEG_VALUE);
            } else {
                //attachment表示以附件方式下载   inline表示在线打开   "Content-Disposition: inline; filename=文件名.mp3"
                if (preview) {
                    response.addHeader("Content-Disposition", "inline; filename=" + URLEncoder.encode(originFileName, "UTF-8"));
                    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
                } else {
                    response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originFileName, "UTF-8"));
                    response.setContentType("application/octet-stream");
                }
            }
            // 文件的大小
            outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
        } catch (final Exception e) {
            log.error("下载文件异常，文件名{}", originFileName, e);
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(outputStream);
        }
    }

    @GetMapping("/generateWord")
    public void testRenderTemplate() throws Exception {
        Map<String, Object> datas = new HashMap<String, Object>();

        // create table
        RowRenderData header = Rows.of("Word处理方案", "是否跨平台", "易用性")
                .textColor("FFFFFF")
                .bgColor("ff9800")
                .center()
                .rowHeight(2.5f)
                .create();
        RowRenderData row0 = Rows.create("Poi-tl", "纯Java组件，跨平台", "简单：模板引擎功能，并对POI进行了一些封装");
        RowRenderData row1 = Rows.create("Apache Poi", "纯Java组件，跨平台", "简单，缺少一些功能的封装");
        RowRenderData row2 = Rows.create("Freemarker", "XML操作，跨平台", "复杂，需要理解XML结构");
        RowRenderData row3 = Rows.create("OpenOffice", "需要安装OpenOffice软件", "复杂，需要了解OpenOffice的API");
        TableRenderData table = Tables.create(header, row0, row1, row2, row3);

        // text
        datas.put("header", "Deeply love what you love.");
        datas.put("name", "Poi-tl");
        datas.put("word", "模板引擎");
        datas.put("time", "2020-08-31");
        datas.put("what", "Java Word模板引擎： Minimal Microsoft word(docx) templating with {{template}} in Java.");
        datas.put("author", Texts.of("Sayi卅一").color("000000").create());

        // hyperlink
        datas.put("introduce", Texts.of("http://www.deepoove.com").link("http://www.deepoove.com").create());
        // table
        datas.put("solution_compare", table);
        // numbering
        datas.put("feature",
                Numberings.create("Plug-in grammar, add new grammar by yourself",
                        "Supports word text, local pictures, web pictures, table, list, header, footer...",
                        "Templates, not just templates, but also style templates"));

        // chart
        datas.put("chart",
                Charts.ofMultiSeries("易用性", new String[]{"代码量", "维护量"})
                        .addSeries("poi-tl", new Double[]{10.0, 5.0})
                        .addSeries("freemark", new Double[]{90.0, 70.0})
                        .create());

        XWPFTemplate.compile("src/main/resources/template/template.docx")
                .render(datas)
                .writeToFile("target/out_template.docx");
    }



}
