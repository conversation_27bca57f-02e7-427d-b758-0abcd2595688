package com.quantchi.nanping.innovation.knowledge.center.model.enums;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 专利聚合字段
 *
 * <AUTHOR>
 * @date 2024/5/21 14:32
 */
public enum PatentAggregationEnum {

    APPLICANT("applicants.name", "申请人", String.class, null, null, false),
    COUNTRY("applicants_country", "国家", String.class, null, null, false),
    PROVINCE("province", "省份", String.class, null, null, false),
    TYPE("patent_type", "专利类型", String.class, null, Arrays.asList("发明专利","实用新型","发明公布","发明授权","实用新型更正","发明公布更正","其他"), false),
    STATUS("status", "专利状态", String.class, null, Arrays.asList("有效","失效","审中","其他"), false),
//    REFERENCE("ctfw_times", "被引证次数", Integer.class, null, Arrays.asList(1,11,21,31,41,51,61,71,81,91), true),
    APPLY_YEAR("apply_date", "申请年份", Date.class, "yyyy", null, false),
    PUBLISH_YEAR("publish_year", "公开年份", Long.class, null,null, false),
    IPC("ipc_category", "IPC分类号", String.class, null, null, false),
    CPC("cpc_category", "CPC分类号", String.class, null, null, false),
    CHAIN("chain.name", "产业领域", String.class, null, null, false);

    /**
     * 聚合字段
     */
    private String field;

    /**
     * 聚合字段名称
     */
    private String fieldName;

    /**
     * 聚合字段类型
     */
    private Class fieldClazz;

    /**
     * 聚合字段匹配规则
     */
    private String fieldFormat;

    /**
     * 聚合字段范围
     */
    private List<Object> fieldRange;

    /**
     * 聚合字段范围是否为区间
     */
    private boolean isInterval;

    PatentAggregationEnum(String field, String fieldName, Class fieldClazz, String fieldFormat, List<Object> fieldRange, boolean isInterval){
        this.field = field;
        this.fieldName = fieldName;
        this.fieldClazz = fieldClazz;
        this.fieldFormat = fieldFormat;
        this.fieldRange = fieldRange;
        this.isInterval = isInterval;
    }

    public static PatentAggregationEnum findByField(String field){
        for (PatentAggregationEnum e: PatentAggregationEnum.values()){
            if (e.field.equals(field)){
                return e;
            }
        }
        return null;
    }

    public String getField() {
        return field;
    }

    public String getFieldName() {
        return fieldName;
    }

    public Class getFieldClazz() {
        return fieldClazz;
    }

    public String getFieldFormat() {
        return fieldFormat;
    }

    public List<Object> getFieldRange() {
        return fieldRange;
    }

    public boolean isInterval() {
        return isInterval;
    }
}
