package com.quantchi.nanping.innovation.knowledge.center.service;

import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentTechAnalysis;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface PatentPortraitService {

    /**
     * 专利概览
     *
     * @param id
     * @return
     */
    Map<String, Object> patentOverview(String id, boolean showCollected) throws IOException;

    /**
     * 判断是否展示价值维度
     *
     * @param id
     * @return
     */
    boolean displayValue(String id);

    /**
     * 引证专利-引证分析图
     *
     * @param id
     * @param type
     */
    Map<String, Object> quoteChartOrList(String id, Integer type) throws IOException;

    /**
     * 同族专利-同族地图
     *
     * @param id
     * @param type
     * @param country
     * @return
     */
    Map<String, Object> cognateMap(String id, Integer type, List<String> country) throws IOException;

    /**
     * 同族专利-同族列表
     *
     * @param id
     * @param type
     * @param country
     * @return
     */
    List<Map<String, Object>> cognateList(String id, Integer type, List<String> country) throws IOException;

    /**
     * 同族专利-同族时间轴
     *
     * @param id
     * @param type
     * @return
     */
    Map<String, Object> cognateTimeline(String id, Integer type) throws IOException;

    /**
     * 技术分析
     *
     * @param id
     * @return
     */
    PatentTechAnalysis techAnalysis(String id);
}
