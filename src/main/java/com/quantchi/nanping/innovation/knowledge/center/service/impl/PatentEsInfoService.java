package com.quantchi.nanping.innovation.knowledge.center.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.knowledge.center.utils.ElasticsearchBuilder;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/24 下午3:17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PatentEsInfoService {

    @Autowired
    private IEs8Service es8Service;

    @Autowired
    private StringRedisCache redisCache;

    /**
     * 相似专利查询，根据专利名字进行模糊匹配
     *
     * @param patentId
     * @return
     */
    public EsPageResult querySimilarPatent(final String patentId, final Integer pageNum, final Integer pageSize) {
        final EsPageResult esPageResult = new EsPageResult();
        // 获取专利信息
        Map<String, Object> source = null;
        String existedSource = redisCache.get("patent_portrait:" + patentId);
        if (StringUtils.isEmpty(existedSource)){
            final String[] includes = {"id", "title", "simple_family", "complete_family"};
            source = es8Service.getDataById("nanping_innovation_patent_portrait", patentId, includes, new String[]{});
        }else{
            source = JSONObject.parseObject(existedSource, Map.class);
        }
        if (MapUtil.isEmpty(source)) {
            return esPageResult;
        }
        final Object title = source.get("title");
        if (!(title instanceof String)) {
            return esPageResult;
        }
        final Object simpleFamily = source.get("simple_family");
        final List<String> simpleFamilyList;
        if (simpleFamily instanceof List) {
            simpleFamilyList = (List<String>) simpleFamily;
        } else {
            simpleFamilyList = Collections.emptyList();
        }

        final Object completeFamily = source.get("complete_family");
        final List<String> completeFamilyList;
        if (completeFamily instanceof List) {
            completeFamilyList = (List<String>) completeFamily;
        } else {
            completeFamilyList = Collections.emptyList();
        }

        // 查询相似专利
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        final String[] includeFields = new String[]{"id", "title", "apply_code", "apply_date", "public_code", "public_date", "applicants"};
        boolQuery.must(QueryBuilders.matchQuery("title", title));
        // 不可与当前专利相同
        boolQuery.mustNot(QueryBuilders.termQuery("id", patentId));
        final List<Map<String, Object>> sourceMapFromPatent = getSourceMapFromPatent(includeFields, boolQuery, 10, false);
        for (final Map<String, Object> sourceAsMap : sourceMapFromPatent) {
            final List<String> homoclanCategoryList = new ArrayList<>(2);
            if (simpleFamilyList.contains((String) sourceAsMap.get("public_code"))) {
                homoclanCategoryList.add("简单");
            }
            if (completeFamilyList.contains((String) sourceAsMap.get("public_code"))) {
                homoclanCategoryList.add("扩展");
            }
            sourceAsMap.put("homoclan_category", homoclanCategoryList);
        }
        esPageResult.setList(sourceMapFromPatent);
        esPageResult.setTotal(10L);
        return esPageResult;
    }

    public List<Map<String, Object>> getSourceMapFromPatent(final String[] includeFields, final BoolQueryBuilder boolQuery, final int size, final Boolean needSort) {
        int pageNum = 1;
        final SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
        searchBuilder.trackTotalHits(true);
        searchBuilder.trackScores(true);
        searchBuilder.query(boolQuery);
        if (needSort) {
            searchBuilder.sort(new FieldSortBuilder("score").order(SortOrder.DESC));
        }
        final FetchSourceContext fetchSourceContext = new FetchSourceContext(true, includeFields, null);
        searchBuilder.fetchSource(fetchSourceContext);
        pagingSet(searchBuilder, pageNum, size);
        searchBuilder.explain(false);
        // 获取结果
        final SearchResponse searchResponse = es8Service.request(searchBuilder, EsIndexEnum.PATENT.getEsIndex(), null, null);
        // 获取结果，格式化数据
        final SearchHits hits = searchResponse.getHits();
        final List<Map<String, Object>> resultList = new ArrayList<>();
        for (final SearchHit hit : hits) {
            resultList.add(hit.getSourceAsMap());
        }
        return resultList;
    }

    private void pagingSet(final SearchSourceBuilder searchBuilder, Integer from, Integer size) {
        from = from == null || from <= 0 ? 0 : from - 1;
        size = size == null || size < 0 ? 5 : size;
        //分页数量不能超过每页20,防止恶意爬取数据
        //size = size > 20 ? 20 : size;
        searchBuilder.from(from * size);
        searchBuilder.size(size);
    }

}
