package com.quantchi.nanping.innovation.knowledge.center.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonFactory;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.utils.HttpClientUtils;
import com.quantchi.tianying.model.AggregationPageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.io.stream.BytesStreamOutput;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.IdsQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchModule;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.*;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.xcontent.*;
import org.elasticsearch.xcontent.json.JsonXContent;
import org.elasticsearch.xcontent.json.JsonXContentParser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/8 9:41
 */
@Service
@Slf4j
public class Es8ServiceImpl implements IEs8Service {

    @Value("${model.patentMatch}")
    private String patentMatchUrl;

    @Override
    public SearchResponse request(SearchSourceBuilder sourceBuilder, String index, String vectorColumn, String keyword) {
        JSONObject requestParam = new JSONObject();
        requestParam.put("common_search", StringUtils.isNotEmpty(vectorColumn)? JSONObject.parseObject(sourceBuilder.toString()).getJSONObject("query"): sourceBuilder.toString());
        requestParam.put("search_content", keyword);
        requestParam.put("vector_field", vectorColumn);
        String result = HttpClientUtils.postForJson(patentMatchUrl, requestParam, null);
        log.info("es8 request: {}, param: {}, response: {}", patentMatchUrl, requestParam, result);
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj == null) {
            return null;
        }
        return getSearchResponseFromJson(resultObj.getString("response"));
    }

    public JSONObject aggRequest(SearchSourceBuilder sourceBuilder, String index, String vectorColumn, String keyword) {
        JSONObject requestParam = new JSONObject();
        requestParam.put("common_search", sourceBuilder.toString());
        requestParam.put("search_content", keyword);
        requestParam.put("vector_field", vectorColumn);
        String result = HttpClientUtils.postForJson(patentMatchUrl, requestParam, null);
        JSONObject resultObj = JSONObject.parseObject(result);
        return resultObj.getJSONObject("response");
    }

    private List<NamedXContentRegistry.Entry> getDefaultNamedXContents() {
        Map<String, ContextParser<Object, ? extends Aggregation>> map = new HashMap<>();
        map.put(TopHitsAggregationBuilder.NAME, (p, c) -> ParsedTopHits.fromXContent(p, (String) c));
        map.put(TermsAggregationBuilder.NAME, (p, c) -> ParsedStringTerms.fromXContent(p, (String) c));
        map.put(StringTerms.NAME, (p, c) -> ParsedStringTerms.fromXContent(p, (String) c));
        List<NamedXContentRegistry.Entry> entries = map.entrySet().stream()
                .map(entry -> new NamedXContentRegistry.Entry(Aggregation.class, new ParseField(entry.getKey()), entry.getValue()))
                .collect(Collectors.toList());
        return entries;
    }

    private SearchResponse getSearchResponseFromJson(String jsonResponse){
        try {
            NamedXContentRegistry registry = new NamedXContentRegistry(getDefaultNamedXContents());
            XContentParser parser = JsonXContent.jsonXContent.createParser(registry, DeprecationHandler.IGNORE_DEPRECATIONS, jsonResponse);
            return SearchResponse.fromXContent(parser);
        }catch (Exception e){
            log.error("Es8查询结果转换异常：", e);
        }
        return null;
    }

    @Override
    public Map<String, Object> getDataById(final String index, final String id, final String[] includes, final String[] excludes) {
        final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(new IdsQueryBuilder().addIds(id));
        if (!ArrayUtil.isEmpty(includes) || !ArrayUtil.isEmpty(excludes)) {
            final FetchSourceContext fetchSourceContext =
                    new FetchSourceContext(true, includes, excludes);
            searchSourceBuilder.fetchSource(fetchSourceContext);
        }
        SearchResponse response = request(searchSourceBuilder, index, null, null);
        //获取到结果
        final SearchHit[] hits = response.getHits().getHits();
        if (hits == null || hits.length == 0) {
            return new HashMap<>(0);
        }
        final Map<String, Object> sourceAsMap = hits[0].getSourceAsMap();
        sourceAsMap.entrySet().removeIf(entry -> entry.getValue() == null);
        return sourceAsMap;
    }

    @Override
    public JSONObject getBucketsAggregationPageResult(final String index, final BoolQueryBuilder boolQueryBuilder, final AggregationBuilder aggregationBuilder) {
        // 初始化查询请求对象
        final SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
        if (boolQueryBuilder != null) {
            searchBuilder.query(boolQueryBuilder);
        }
        // 设置size，无需返回hits的搜索结果
        searchBuilder.size(0);
        searchBuilder.aggregation(aggregationBuilder);
        searchBuilder.trackTotalHits(true);
        final SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchBuilder);
        log.info("聚合查询参数{}", searchBuilder);
        // 获取结果
        return aggRequest(searchBuilder, index, null, null);
    }

    @Override
    public Long countRequest(SearchSourceBuilder sourceBuilder, String index) {
        SearchResponse searchResponse = request(sourceBuilder, EsIndexEnum.PATENT_VECTOR.getEsIndex(), null, null);
        return Objects.requireNonNull(searchResponse.getHits().getTotalHits()).value;
    }
}
