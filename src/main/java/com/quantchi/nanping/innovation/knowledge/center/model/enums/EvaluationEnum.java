package com.quantchi.nanping.innovation.knowledge.center.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/15 上午11:00
 */
@Getter
public enum EvaluationEnum {

    ONE("A", 80.0, "卓越"),
    TWO("B", 60.0, "优秀"),
    THREE("C", 40.0, "良好"),
    FOUR("D", 20.0, "普通"),
    FIVE("E", 0.0, "入门"),
            ;

    private final String level;

    private final Double score;

    private final String desc;

    EvaluationEnum(final String level, final Double score, final String desc) {
        this.level = level;
        this.score = score;
        this.desc = desc;
    }

    /**
     * 根据百分比获取对应的评价枚举。
     * @param percentage 百分比
     * @return 对应的评价枚举
     */
    public static EvaluationEnum getEvaluationByPercentage(final Integer percentage) {
        if (percentage == null) {
            return EvaluationEnum.FIVE;
        }
        // 从高到低遍历枚举值
        for (final EvaluationEnum evaluation : EvaluationEnum.values()) {
            if (percentage >= evaluation.getScore()) {
                return evaluation;
            }
        }
        // 如果所有的评价都不符合，返回最低的评价
        return EvaluationEnum.FIVE;
    }

    public static EvaluationEnum getEvaluationByPercentage(final Double percentage) {
        if (percentage == null) {
            return EvaluationEnum.FIVE;
        }
        // 从高到低遍历枚举值
        for (final EvaluationEnum evaluation : EvaluationEnum.values()) {
            if (percentage >= evaluation.getScore()) {
                return evaluation;
            }
        }
        // 如果所有的评价都不符合，返回最低的评价
        return EvaluationEnum.FIVE;
    }
}
