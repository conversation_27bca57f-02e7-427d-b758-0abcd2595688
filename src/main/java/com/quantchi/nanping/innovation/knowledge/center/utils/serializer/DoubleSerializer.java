package com.quantchi.nanping.innovation.knowledge.center.utils.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2024/7/30 下午5:08
 */
public class DoubleSerializer extends JsonSerializer<Double> {

    public DoubleSerializer() {
    }

    @Override
    public void serialize(final Double value, final JsonGenerator gen, final SerializerProvider serializers) throws IOException {
        if (value != null) {
            BigDecimal number = BigDecimal.valueOf(value).setScale(2, RoundingMode.HALF_UP);
            gen.writeNumber(number.doubleValue());
        } else {
            gen.writeNumber(value);
        }
    }
}
