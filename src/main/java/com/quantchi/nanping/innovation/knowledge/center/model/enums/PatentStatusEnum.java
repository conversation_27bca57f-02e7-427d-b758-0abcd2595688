package com.quantchi.nanping.innovation.knowledge.center.model.enums;

import io.swagger.annotations.ApiModel;
import lombok.Getter;

@ApiModel("专利状态枚举")
@Getter
public enum PatentStatusEnum {

    OTHER(1, "其他"),
    VALID(2, "有效"),
    ;

    private final Integer id;

    private final String name;

    PatentStatusEnum(final Integer id, final String name) {
        this.id = id;
        this.name = name;
    }

    public static String getNameByKey(final Integer id) {
        for (final PatentStatusEnum value : PatentStatusEnum.values()) {
            if (value.getId().equals(id)) {
                return value.getName();
            }
        }
        return null;
    }

}
