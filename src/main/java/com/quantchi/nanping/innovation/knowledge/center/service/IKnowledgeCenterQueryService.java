package com.quantchi.nanping.innovation.knowledge.center.service;

import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.model.MultidimensionalQuery;

/**
 * 知识中心查询服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-18
 */
public interface IKnowledgeCenterQueryService {

    /**
     * 导航菜单筛选+关键字查询
     *
     * @param mQuery 多维查询参数
     * @param isService 是否为服务侧
     * @param nodePath 节点路径
     * @return 查询结果
     */
    EsPageResult queryByTermsAndKey(MultidimensionalQuery mQuery, boolean isService, String nodePath);
}
