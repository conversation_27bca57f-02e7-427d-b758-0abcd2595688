package com.quantchi.nanping.innovation.knowledge.center.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("patent_value_unit")
@ApiModel(value = "PatentValueUnit对象", description = "")
public class PatentValueUnit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统当前使用汇率
     */
    public static final Integer CURRENT_RATE_ID = 0;

    @ApiModelProperty("主键id")
    @TableId
    private String unitId;

    @ApiModelProperty("美元兑人民币汇率")
    private BigDecimal exchangeRate;
}
