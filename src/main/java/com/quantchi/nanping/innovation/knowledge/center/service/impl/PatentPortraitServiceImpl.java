package com.quantchi.nanping.innovation.knowledge.center.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.model.CompanyCollection;
import com.quantchi.nanping.innovation.company.service.ICompanyCollectionService;
import com.quantchi.nanping.innovation.company.service.IModelChatService;
import com.quantchi.nanping.innovation.component.LocalModelComponent;
import com.quantchi.nanping.innovation.knowledge.center.dao.IndustryIpcMapper;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.*;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentDimension;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentTechAnalysis;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentValue;
import com.quantchi.nanping.innovation.knowledge.center.service.*;
import com.quantchi.nanping.innovation.model.bo.CompanyPageBo;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import com.zhipu.oapi.service.v4.model.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatentPortraitServiceImpl implements PatentPortraitService {

    /**
     * 缓存前缀
     */
    private static final String PATENT_PORTRAIT_PREFIX = "patent_portrait:";

    private final static String CURRENT_REDIS_VALUE = "ON";

    @Autowired
    private IndustryIpcMapper industryIpcMapper;

    @Autowired
    private IModelChatService chatService;

    @Autowired
    private IPatentTechAnalysisService patentTechAnalysisService;

    @Autowired
    private IEs8Service es8Service;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private StringRedisCache redisCache;

    @Autowired
    private ICompanyCollectionService collectionService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private IPatentValueService patentValueService;

    @Autowired
    private IPatentDimensionService patentDimensionService;

    @Autowired
    private LocalModelComponent localModelComponent;

    @Value("${local.model.chatApi}")
    private String chatApi;

    @Value("${local.model.keyTech.apiKey}")
    private String keyTechApiKey;

    @Override
    public Map<String, Object> patentOverview(String id, boolean showCollected) throws IOException {
        final String[] excludes = {"ti_vector", "ab_vector"};
        Map<String, Object> source = es8Service.getDataById("nanping_innovation_patent_portrait", id, null, excludes);
        if (source != null) {
            redisCache.put(PATENT_PORTRAIT_PREFIX + id, JSONObject.toJSONString(source), 10, TimeUnit.MINUTES);
        }
        // 兼容标题字段
        if (source.get("title_cn") == null){
            source.put("title_cn", source.get("title"));
        }
        // 申请人(过滤不在库里的企业)
        List<Map<String, Object>> applicants = (List<Map<String, Object>>) source.get("applicants");
        if (!CollectionUtils.isEmpty(applicants)) {
            List<String> companyIds = new ArrayList<>();
            applicants.forEach(a -> {
                if (a.containsKey("id") && StringUtils.isNotEmpty((String) a.get("id"))) {
                    companyIds.add((String) a.get("id"));
                }
            });
            List<String> existedCompanyIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(companyIds)) {
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                boolQueryBuilder.filter(QueryBuilders.idsQuery().addIds(companyIds.toArray(new String[0])));
                SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, companyIds.size(), null, null, null);
                SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.COMPANY.getEsIndex());
                EsPageResult companyPage = ElasticsearchBuilder.buildPageResult(searchResponse);
                List<Map<String, Object>> companyList = companyPage.getList();
                if (!CollectionUtils.isEmpty(companyList)) {
                    companyList.forEach(c -> existedCompanyIds.add((String) c.get("id")));
                }
            }
            applicants.forEach(a -> {
                if (!existedCompanyIds.contains(a.get("id"))) {
                    a.put("id", null);
                }
            });
        }
        //主分类号 树
        if (source.get("main_ipc") != null) {
            String mainIpc = source.get("main_ipc").toString();
            source.put("main_ipc_tree", getClassNumberTree(mainIpc));
        }

        //ipc分类号树
        List<String> ipc = (List<String>) source.get("ipc");
        List<PatentPortraitClassNumberTreeBO> ipcTree = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ipc)) {
            for (String item : ipc) {
                PatentPortraitClassNumberTreeBO treeBO = new PatentPortraitClassNumberTreeBO();
                treeBO.setClassNumber(item);
                treeBO.setTree(getClassNumberTree(item));
                ipcTree.add(treeBO);
            }
        }
        source.put("ipc_tree", ipcTree);

        //cpc分类号树
        List<String> cpc = (List<String>) source.get("cpc");
        List<PatentPortraitClassNumberTreeBO> cpcTree = new ArrayList<>();
        if (!CollectionUtils.isEmpty(cpc)) {
            for (String item : cpc) {
                PatentPortraitClassNumberTreeBO treeBO = new PatentPortraitClassNumberTreeBO();
                treeBO.setClassNumber(item);
                treeBO.setTree(getClassNumberTree(item));
                cpcTree.add(treeBO);
            }
        }
        source.put("cpc_tree", cpcTree);

        //国民经济行业分类
        List<String> necList = (List<String>) source.get("nec");
        if (!CollectionUtils.isEmpty(necList)) {
            List<PatentPortraitIndustryClassBO> necClassList = industryIpcMapper.getNameByNec(necList);
            sortByList(necClassList, necList);
            source.put("nec_class_list", necClassList);
            //国民经济行业（主）
            List<PatentPortraitIndustryClassBO> necClassMainList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(necClassList)) {
                necClassMainList.add(necClassList.get(0));
            }
            source.put("nec_class_main_list", necClassMainList);
        } else {
            source.put("nec_class_list", null);
            source.put("nec_class_main_list", null);
        }

        //新兴产业分类
        List<String> secList = (List<String>) source.get("sec");
        if (!CollectionUtils.isEmpty(secList)) {
            List<PatentPortraitIndustryClassBO> secClassList = industryIpcMapper.getNameBySec(secList);
            sortByList(secClassList, secList);
            source.put("sec_class_list", secClassList);
            //新兴产业（主）
            List<PatentPortraitIndustryClassBO> secClassMainList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(secClassList)) {
                secClassMainList.add(secClassList.get(0));
            }
            source.put("sec_class_main_list", secClassMainList);
        } else {
            source.put("sec_class_list", null);
            source.put("sec_class_main_list", null);
        }

        //同族专利公开号
        List<String> simpleFamily = (List<String>) source.get("simple_family");
        source.put("simple_family_country", getFamilyPatentForCountry(simpleFamily));

        //拓展同族专利公开号
        List<String> completeFamily = (List<String>) source.get("complete_family");
        source.put("complete_family_country", getFamilyPatentForCountry(completeFamily));

        if (showCollected && StpUtil.isLogin()) {
            // 设置收藏标志
            List<CompanyCollection> collections = collectionService.listByType(sysLoginService.findCollectEntityId(), EsIndexEnum.PATENT_VECTOR.getType());
            List<String> collectedIds = collections.stream().map(CompanyCollection::getCollectId).collect(Collectors.toList());
            if (collectedIds.contains(source.get("id"))) {
                source.put("collected", true);
            }
        }
        // 判断是否展示专利价值
        source.put("displayValue", displayValue(id));
        return source;
    }

    @Override
    public boolean displayValue(String id) {
        // 判断是否展示专利价值
        PatentValue value = patentValueService.getById(id);
        PatentDimension dimension = patentDimensionService.getById(id);
        return value != null && dimension != null;
    }

    private void sortByList(List<PatentPortraitIndustryClassBO> classBOList, List<String> sortList) {
        Collections.sort(classBOList, new Comparator<PatentPortraitIndustryClassBO>() {
            @Override
            public int compare(PatentPortraitIndustryClassBO o1, PatentPortraitIndustryClassBO o2) {
                int o1Num = sortList.indexOf(o1.getId());
                int o2Num = sortList.indexOf(o2.getId());
                if (o1Num - o2Num <= 0) {
                    return -1;
                } else {
                    return 1;
                }
            }
        });
    }

    /**
     * 获取分类号树
     *
     * @return
     */
    private PatentPortraitIndustryIpcBO getClassNumberTree(String classNumber) {
        PatentPortraitIndustryIpcBO tree = new PatentPortraitIndustryIpcBO();
        PatentPortraitIndustryIpcBO ipcBO = industryIpcMapper.getIpcByCode(classNumber);
        if (ipcBO != null) {
            //分类号必须在库里
            List<PatentPortraitIndustryIpcBO> treeNode = new ArrayList<>();
            treeNode.add(ipcBO);
            String parentId = ipcBO.getParentId();
            while (!StringUtils.isEmpty(parentId)) {
                PatentPortraitIndustryIpcBO mainIpcParentBO = industryIpcMapper.getIpcByParentId(parentId);
                treeNode.add(mainIpcParentBO);
                parentId = mainIpcParentBO.getParentId();
            }
            // 处理ipcTreeNode 组树
            Map<String, PatentPortraitIndustryIpcBO> treeNodeMap = new HashMap<>();
            for (PatentPortraitIndustryIpcBO bo : treeNode) {
                treeNodeMap.put(bo.getId(), bo);
            }
            for (PatentPortraitIndustryIpcBO bo : treeNode) {
                if (StringUtils.isEmpty(bo.getParentId())) {
                    tree = bo;
                } else {
                    PatentPortraitIndustryIpcBO parent = treeNodeMap.get(bo.getParentId());
                    parent.getChildren().add(bo);
                }
            }
        }

        return tree;
    }

//    private List<PatentPortraitFamilyPatentBO> getFamilyPatentForCountry(List<String> familyPatent) {
//        List<PatentPortraitFamilyPatentBO> familyPatentList = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(familyPatent)) {
//            //收集所有国别类型
//            List<String> countryEncoding = new ArrayList<>();
//            for (String item : familyPatent) {
//                String subString = item.substring(0, 2);
//                if (!countryEncoding.contains(subString)) {
//                    countryEncoding.add(subString);
//                }
//            }
//            List<PatentPortraitCountryEncodingBO> countryEncodingList = countryEncodingMapper.getCountryByEncoding(countryEncoding);
//            //根据查询到的国别个数创建数据集
//            for (PatentPortraitCountryEncodingBO bo : countryEncodingList) {
//                PatentPortraitFamilyPatentBO familyBO = new PatentPortraitFamilyPatentBO();
//                familyBO.setCountry(bo.getCountry());
//                familyBO.setEncoding(bo.getEncoding());
//                familyPatentList.add(familyBO);
//            }
//            Set<String> set = new HashSet<>();//放入数据集的专利
//            //将专利分类放入数据集
//            for (String item : familyPatent) {
//                for (PatentPortraitFamilyPatentBO bo : familyPatentList) {
//                    if (item.substring(0, 2).equals(bo.getEncoding())) {
//                        bo.getList().add(item);
//                        set.add(item);
//                    }
//                }
//            }
//            if (set.size() < familyPatent.size()) {
//                //说明存在找不到国别的专利 找出来放在其他分类
//                PatentPortraitFamilyPatentBO otherBO = new PatentPortraitFamilyPatentBO();
//                otherBO.setCountry("其他");
//                List<String> otherList = new ArrayList<>();
//                for (String item : familyPatent) {
//                    if (!set.contains(item)) {
//                        otherList.add(item);
//                    }
//                }
//                otherBO.setList(otherList);
//                familyPatentList.add(otherBO);
//            }
//        }
//
//        return familyPatentList;
//    }

    private List<PatentPortraitFamilyPatentBO> getFamilyPatentForCountry(List<String> familyPatent) throws IOException {
        List<PatentPortraitFamilyPatentBO> familyPatentList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(familyPatent)) {
            // 初始化布尔查询
            BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.must(QueryBuilders.termsQuery("public_code", familyPatent));
            // 创建聚合构建器
            TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders
                    .terms("by_public_country") // 聚合名称
                    .field("public_country"); // 替换为你要聚合的字段名
            // 创建top_hits聚合构建器
            TopHitsAggregationBuilder topHitsAggregationBuilder = AggregationBuilders
                    .topHits("top_hits")
                    .size(100); // 指定每个桶返回的顶部文档数量
            // 将top_hits聚合构建器添加到terms聚合构建器
            termsAggregationBuilder.subAggregation(topHitsAggregationBuilder);
            JSONObject result = es8Service.getBucketsAggregationPageResult("nanping_innovation_patent_portrait", filterBuilder, termsAggregationBuilder);
            if (result == null) {
                return familyPatentList;
            }
            JSONArray groupArray = result.getJSONObject("aggregations").getJSONObject("by_public_country").getJSONArray("buckets");
            for (int i = 0; i < groupArray.size(); i++) {
                JSONObject group = groupArray.getJSONObject(i);
                String key = group.getString("key");
                Integer docCount = group.getInteger("doc_count");
                PatentPortraitFamilyPatentBO bo = new PatentPortraitFamilyPatentBO();
                bo.setCountry(key);
                bo.setNum(docCount);
                JSONArray subGroupArray = group.getJSONObject("top_hits").getJSONObject("hits").getJSONArray("hits");
                for (int j = 0; j < subGroupArray.size(); j++) {
                    JSONObject subGroup = subGroupArray.getJSONObject(j);
                    bo.getList().add(subGroup.getJSONObject("_source").getString("public_code"));
                }
                familyPatentList.add(bo);
            }
        }
        return familyPatentList;
    }


    @Override
    public Map<String, Object> quoteChartOrList(String id, Integer type) throws IOException {
        Map<String, Object> source = null;
        String existedSource = redisCache.get(PATENT_PORTRAIT_PREFIX + id);
        if (StringUtils.isEmpty(existedSource)) {
            final String[] includes = {"id", "public_code", "ct", "ctfw", "ct_times", "ctfw_times", "title_cn", "applicants",
                    "public_date", "apply_code", "simple_family_num", "main_ipc"};//查询字段
            source = es8Service.getDataById("nanping_innovation_patent_portrait", id, includes, new String[]{});
        } else {
            source = JSONObject.parseObject(existedSource, Map.class);
        }
        List<String> ctList = (List<String>) source.get("ct");//引证专利
        List<String> ctfwList = (List<String>) source.get("ctfw");//被引证专利
        if (type == 1) {
            //引证分析图
            source.put("ct_children", getCtOrCtfwDetail(type, 1, ctList));
            source.put("ctfw_children", getCtOrCtfwDetail(type, 2, ctfwList));

            return source;
        } else {
            //引证列表
            Map<String, Object> result = new HashMap<>();
            result.put("ct_list", getCtOrCtfwDetail(type, 1, ctList));
            result.put("ctfw_list", getCtOrCtfwDetail(type, 2, ctfwList));

            return result;
        }

    }

    private List<Map<String, Object>> getCtOrCtfwDetail(Integer type, Integer searchType, List<String> list) throws IOException {
        List<Map<String, Object>> children = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            SearchRequest searchRequest = new SearchRequest("nanping_innovation_patent_portrait");
            SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
            if (type == 1) {
                //引证分析图
                if (searchType == 1) {
                    //引证专利
                    searchBuilder.fetchSource(new String[]{"id", "public_code", "ct_times", "title_cn", "applicants",
                            "public_date", "apply_code", "simple_family_num", "main_ipc"}, null);
                } else if (searchType == 2) {
                    //被引证专利
                    searchBuilder.fetchSource(new String[]{"id", "public_code", "ctfw_times", "title_cn", "applicants",
                            "public_date", "apply_code", "simple_family_num", "main_ipc"}, null);
                }
            } else if (type == 2) {
                //引证列表
                searchBuilder.fetchSource(new String[]{"id", "public_code", "title", "title_cn", "applicants", "public_date",
                        "apply_code", "apply_date", "ipc"}, null);
            }
            // 初始化布尔查询
            BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.must(QueryBuilders.termsQuery("public_code", list));
            searchBuilder.size(10000);
            searchBuilder.query(filterBuilder);
            searchRequest.source(searchBuilder);
            SearchResponse searchResponse = es8Service.request(searchBuilder, "nanping_innovation_patent_portrait", null, null);
            SearchHit[] hits = searchResponse.getHits().getHits();
            Map<String, Map<String, Object>> hitMap = new HashMap<>();
            for (SearchHit hit : hits) {
                Map<String, Object> childSource = hit.getSourceAsMap();
                if (type == 1) {
                    //分析图需要把不在我们库的数据补全
                    hitMap.put(childSource.get("public_code").toString(), childSource);
                } else {
                    //列表，我们库有几条返回几条
                    if (childSource.get("title_cn") == null) {
                        childSource.put("title_cn", childSource.get("title"));
                    }
                    children.add(childSource);
                }

            }
            if (type == 1) {
                //分析图数据补全
                for (String item : list) {
                    if (hitMap.get(item) != null) {
                        children.add(hitMap.get(item));
                    } else {
                        //不在我们库，没法给详细信息
                        Map<String, Object> noDataMap = new HashMap<>();
                        noDataMap.put("public_code", item);
                        children.add(noDataMap);
                    }
                }
            }
        }

        return children;
    }

    @Override
    public Map<String, Object> cognateMap(String id, Integer type, List<String> country) throws IOException {
        Map<String, Object> source = null;
        String existedSource = redisCache.get(PATENT_PORTRAIT_PREFIX + id);
        if (StringUtils.isEmpty(existedSource)) {
            final String[] includes = {"id", "simple_family", "simple_family_num", "complete_family", "complete_family_num"};
            source = es8Service.getDataById("nanping_innovation_patent_portrait", id, includes, new String[]{});
        } else {
            source = JSONObject.parseObject(existedSource, Map.class);
        }
        List<String> simpleFamily = (List<String>) source.get("simple_family");//简单同族
        List<String> completeFamily = (List<String>) source.get("complete_family");//扩展同族
        if (simpleFamily != null) {
            simpleFamily.removeIf(f -> !f.startsWith("CN"));
        }
        if (completeFamily != null) {
            completeFamily.removeIf(f -> !f.startsWith("CN"));
        }
        if (CollectionUtils.isEmpty(simpleFamily)) {
            source.put("simple_family", null);
            source.put("simple_family_num", 0);
        } else {
            source.put("simple_family", simpleFamily);
            source.put("simple_family_num", simpleFamily.size());
        }
        if (CollectionUtils.isEmpty(completeFamily)) {
            source.put("complete_family", null);
            source.put("complete_family_num", 0);
        } else {
            source.put("complete_family", completeFamily);
            source.put("complete_family_num", completeFamily.size());
        }

        //计算国家类别同族专利数
        //数据筛选传参影响整个页面数据，包括同族个数、地图、列表
        //地图和地区传参无关
        Set<String> family = new HashSet<>();
        if (type == 1) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        } else if (type == 2) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
            source.remove("complete_family");
            source.remove("complete_family_num");
        } else if (type == 3) {
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
            source.remove("simple_family");
            source.remove("simple_family_num");
        }
        List<PatentPortraitFamilyPatentBO> familyNumForCountry = getFamilyPatentForCountry(new ArrayList<>(family));
        for (PatentPortraitFamilyPatentBO bo : familyNumForCountry) {
            bo.setNum(bo.getList().size());
        }
        source.put("family_num_for_country", familyNumForCountry);

        return source;
    }

    @Override
    public List<Map<String, Object>> cognateList(String id, Integer type, List<String> country) throws IOException {
        Map<String, Object> source = null;
        String existedSource = redisCache.get(PATENT_PORTRAIT_PREFIX + id);
        if (StringUtils.isEmpty(existedSource)) {
            final String[] includes = {"id", "simple_family", "simple_family_num", "complete_family", "complete_family_num"};
            source = es8Service.getDataById("nanping_innovation_patent_portrait", id, includes, new String[]{});
        } else {
            source = JSONObject.parseObject(existedSource, Map.class);
        }
        List<String> simpleFamily = (List<String>) source.get("simple_family");//简单同族
        List<String> completeFamily = (List<String>) source.get("complete_family");//扩展同族

        //数据筛选传参影响整个页面数据，包括同族个数、地图、列表
        Set<String> family = new HashSet<>();
        if (type == 1) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        } else if (type == 2) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
        } else if (type == 3) {
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        }

        //地区传参只影响列表
        List<String> familyForList = null;
        if (!CollectionUtils.isEmpty(country)) {
            //过滤掉非地区传参的专利
            familyForList = filterPatentByRegion(new ArrayList<>(family), country);
        } else {
            familyForList = new ArrayList<>(family);
        }

        //同族列表
        List<Map<String, Object>> familyList = getFamilyPatentDetailList(familyForList);
        for (Map<String, Object> patent : familyList) {
            String publicCode = patent.get("public_code").toString();
            List<String> familyType = new ArrayList<>();
            if (simpleFamily != null && simpleFamily.contains(publicCode)) {
                familyType.add("简单");
            }
            if (completeFamily != null && completeFamily.contains(publicCode)) {
                familyType.add("扩展");
            }
            patent.put("family_type", familyType);
        }

        return familyList;
    }

//    private List<String> filterPatentByRegion(List<String> patent, List<PatentPortraitCountryEncodingBO> encodingList) {
//        List<String> result = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(patent)) {
//            if (CollectionUtils.isEmpty(encodingList)) {
//                //有可能库里没有这些地区
//                return new ArrayList<>();
//            } else {
//                for (PatentPortraitCountryEncodingBO bo : encodingList) {
//                    for (String item : patent) {
//                        if (item.substring(0, 2).equals(bo.getEncoding())) {
//                            result.add(item);
//                        }
//                    }
//                }
//            }
//        }
//        return result;
//    }

    private List<String> filterPatentByRegion(List<String> patent, List<String> country) throws IOException {
        List<String> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(patent)) {
            //查询专利公开国家字段符合传参country的专利
            SearchRequest searchRequest = new SearchRequest("nanping_innovation_patent_portrait");
            SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
            searchBuilder.fetchSource(new String[]{"id", "public_code"}, null);
            // 初始化布尔查询
            BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.must(QueryBuilders.termsQuery("public_country", country));
            filterBuilder.must(QueryBuilders.termsQuery("public_code", patent));
            searchBuilder.query(filterBuilder);
            searchRequest.source(searchBuilder);
            SearchResponse searchResponse = es8Service.request(searchBuilder, "nanping_innovation_patent_portrait", null, null);
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                Map<String, Object> source = hit.getSourceAsMap();
                result.add(source.get("public_code").toString());
            }
        }
        return result;
    }


    private List<Map<String, Object>> getFamilyPatentDetailList(List<String> patent) throws IOException {
        List<Map<String, Object>> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(patent)) {
            SearchRequest searchRequest = new SearchRequest("nanping_innovation_patent_portrait");
            SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
            searchBuilder.fetchSource(new String[]{"id", "public_code", "title_cn", "priority_code",
                    "public_date", "apply_code", "apply_date", "public_country", "patent_type", "status"}, null);
            // 初始化布尔查询
            BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.must(QueryBuilders.termsQuery("public_code", patent));
            searchBuilder.size(10000);
            searchBuilder.query(filterBuilder);
            searchRequest.source(searchBuilder);
            SearchResponse searchResponse = es8Service.request(searchBuilder, "nanping_innovation_patent_portrait", null, null);
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                Map<String, Object> source = hit.getSourceAsMap();
                result.add(source);
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> cognateTimeline(String id, Integer type) throws IOException {
        Map<String, Object> source = null;
        String existedSource = redisCache.get(PATENT_PORTRAIT_PREFIX + id);
        if (StringUtils.isEmpty(existedSource)) {
            final String[] includes = {"id", "public_code", "simple_family", "complete_family"};
            source = es8Service.getDataById("nanping_innovation_patent_portrait", id, includes, new String[]{});
        } else {
            source = JSONObject.parseObject(existedSource, Map.class);
        }
        if (source == null || source.isEmpty()) {
            return new HashMap<>(0);
        }
        List<String> simpleFamily = (List<String>) source.get("simple_family");//简单同族
        List<String> completeFamily = (List<String>) source.get("complete_family");//扩展同族
        String publicCode = source.get("public_code").toString();//当前专利
        Set<String> family = new HashSet<>();
        if (type == 1) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        } else if (type == 2) {
            family.addAll(simpleFamily == null ? new ArrayList<>() : simpleFamily);
        } else if (type == 3) {
            family.addAll(completeFamily == null ? new ArrayList<>() : completeFamily);
        }
        family.add(publicCode);
        Map<String, Object> familyTimeline = getFamilyTimeline(new ArrayList<>(family), publicCode);
        return familyTimeline;
    }

    @Override
    public PatentTechAnalysis techAnalysis(String id) {
        Map<String, Object> source = null;
        String existedSource = redisCache.get(PATENT_PORTRAIT_PREFIX + id);
        if (StringUtils.isEmpty(existedSource)) {
            final String[] includes = {"title", "title_cn", "abstract", "abstract_cn"};
            source = es8Service.getDataById("nanping_innovation_patent_portrait", id, includes, new String[]{});
        } else {
            source = JSONObject.parseObject(existedSource, Map.class);
        }
        //查询字段
        if (source == null || source.isEmpty()) {
            return null;
        }
        // 判断是否已存在
        PatentTechAnalysis existed = patentTechAnalysisService.getById(id);
        if (existed != null) {
            existed.setTechnology(JSONObject.parseObject(existed.getTechnologies(), List.class));
            return existed;
        }
        // 不允许连续输入
//        List<ChatMessage> messages = new ArrayList<>();
//        ChatMessage currentMessage = new ChatMessage("user", null);
        // 你是专利检索助手，输出以下内容的专利检索关键词或延伸关键词(keywords),申请人或机构名称(applicants，必须是输入中连续的文本，如果是简称转换成完整标准名称),专利名称(names，必须是必须是输入中存在的连续文本)和涉及的技术点名称（techPoints，仅输出最主要的3个）。输出要求：以JSON形式，示例：{"keywords":["关键词1","关键词2"],"applicants":["申请人1","申请人2"],"names":["专利名称1"],"techPoints":["技术点名称1"]},若没有具体信息，对应填充null值。
//        StringBuilder currentMessageContent = new StringBuilder("你是一位产业专利分析专家，根据专利内容分析总结这篇专利研究了领域场景下的什么技术问题，使用的关键技术和达到的效果。\n" +
//                "                    ##要求##\n" +
//                "                    1、研究的问题明确给出某个领域的某项细粒度任务问题，总结并凝练为15字以内，使用专业术语描述；\n" +
//                "                    2、关键技术仅总结最重要的关键技术和简要描述，要求简洁清楚，语义完整；\n" +
//                "                    3、关键技术数量控制在1到3个，多个技术使用json数组输出,json key为 name：技术名称、desc：技术简要描述。\n" +
//                "                    3、达到的效果指专利方法能够达到的效果，比如提高效率等，字符串格式。\n" +
//                "                    4、以json格式输出，json key为problem:解决的问题、technology:采用的关键技术、effect:达到的效果；\n" +
//                "                    ##专利内容##\n");

        StringBuilder currentMessageContent = new StringBuilder();
        currentMessageContent.append("                    标题：").append(source.get("title_cn") == null ? source.get("title_cn") : source.get("title")).append("\n");
        if (source.containsKey("abstract_cn") && StringUtils.isNotEmpty((String) source.get("abstract_cn"))) {
            currentMessageContent.append("                    摘要：").append(source.get("abstract_cn")).append("\n");
        }else{
            currentMessageContent.append("                    摘要：").append(source.get("abstract")).append("\n");
        }
//        currentMessage.setContent(currentMessageContent.toString());
//        messages.add(currentMessage);
//        String analysis = chatService.send(null, id, messages);

        JSONObject requestParam = localModelComponent.buildChatRequestParam(currentMessageContent.toString(), "blocking");
        String analysis = localModelComponent.requestChat(chatApi, keyTechApiKey, requestParam, null);

        log.error("analysis:{}", analysis);
        if (!analysis.contains("{") || !analysis.contains("}")){
            return new PatentTechAnalysis();
        }
        PatentTechAnalysis analysisObj = JSONObject.parseObject(analysis.substring(analysis.indexOf("{"), analysis.lastIndexOf("}") + 1),
                PatentTechAnalysis.class);
        analysisObj.setId(id);
        analysisObj.setTitle((String) source.get("title_cn"));
        analysisObj.setAbstractContent((String) source.get("abstract_cn"));
        analysisObj.setTechnologies(JSONObject.toJSONString(analysisObj.getTechnology()));

        String redisKey = "patent_tech_analysis" + id;
        if (!redisCache.lock(redisKey, CURRENT_REDIS_VALUE)) {
            return analysisObj;
        }

        patentTechAnalysisService.save(analysisObj);

        redisCache.unLock(redisKey, CURRENT_REDIS_VALUE);

        return analysisObj;
    }

    private Map<String, Object> getFamilyTimeline(List<String> patent, String publicCode) {
        Map<String, Object> result = new HashMap<>();
        List<PatentPortraitFamilyTimelineBO> dataList = new ArrayList<>();//数据
        List<String> regionOrdinate = new ArrayList<>();//纵坐标
        if (!CollectionUtils.isEmpty(patent)) {
            SearchRequest searchRequest = new SearchRequest("nanping_innovation_patent_portrait");
            SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
            searchBuilder.fetchSource(new String[]{"id", "public_code", "title_cn",
                    "public_date", "public_country"}, null);
            // 初始化布尔查询
            BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
            filterBuilder.must(QueryBuilders.termsQuery("public_code", patent));
            searchBuilder.query(filterBuilder);
            // 创建日期直方图聚合构建器，按年份聚合
            DateHistogramAggregationBuilder yearAggregationBuilder = AggregationBuilders
                    .dateHistogram("by_year")
                    .field("public_date") // 替换为你的日期字段名
                    .calendarInterval(DateHistogramInterval.YEAR); // 按年份间隔

            // 创建地区terms聚合构建器
            TermsAggregationBuilder regionAggregationBuilder = AggregationBuilders
                    .terms("by_region")
                    .field("public_country"); // 替换为你的地区字段名
            // 将地区terms聚合构建器添加到年份直方图聚合构建器
            yearAggregationBuilder.subAggregation(regionAggregationBuilder);
            // 创建top_hits聚合构建器，为每个聚合桶返回相关文档
            TopHitsAggregationBuilder topHitsAggregationBuilder = AggregationBuilders
                    .topHits("top_hits")
                    .size(100); // 指定每个聚合桶返回的文档数量
            // 将top_hits聚合构建器添加到地区直方图聚合构建器
            regionAggregationBuilder.subAggregation(topHitsAggregationBuilder);
            // 将年份直方图聚合构建器添加到搜索源构建器
            searchBuilder.aggregation(yearAggregationBuilder);
            // 将搜索源构建器设置到搜索请求
            searchRequest.source(searchBuilder);
            //SearchResponse searchResponse = es8Service.request(searchBuilder, "nanping_innovation_patent_portrait", null, null);;
            JSONObject searchResult = es8Service.getBucketsAggregationPageResult("nanping_innovation_patent_portrait", filterBuilder, yearAggregationBuilder);
            if (searchResult == null) {
                return result;
            }
//            Long totalCount = searchResult.getJSONObject("hits").getJSONObject("total").getLong("value");
//            Long displayCount = 0L;
//            List<CommonIndexVO> aggResult = new ArrayList<>();
//            Set<String> aggValueSet = new HashSet<>();
            JSONArray yearGroupArray = searchResult.getJSONObject("aggregations").getJSONObject("by_year").getJSONArray("buckets");
            for (int i = 0; i < yearGroupArray.size(); i++) {
                JSONObject yearGroup = yearGroupArray.getJSONObject(i);
                String year = yearGroup.getString("key_as_string").substring(0, 4);
//                Integer docCount = yearGroup.getInteger("doc_count");
                PatentPortraitFamilyTimelineBO timelineBO = new PatentPortraitFamilyTimelineBO();
                timelineBO.setYear(year);
                JSONArray regionGroupArray = yearGroup.getJSONObject("by_region").getJSONArray("buckets");
                List<PatentPortraitFamilyTimelineDataBO> list = new ArrayList<>();
                if (!CollectionUtils.isEmpty(regionGroupArray)) {
                    for (int j = 0; j < regionGroupArray.size(); j++) {
                        JSONObject regionGroup = regionGroupArray.getJSONObject(j);
                        String region = regionGroup.getString("key");
                        PatentPortraitFamilyTimelineDataBO dataBO = new PatentPortraitFamilyTimelineDataBO();
                        dataBO.setLabel(region);
                        if (!regionOrdinate.contains(region)) {
                            regionOrdinate.add(region);
                        }
                        List<PatentPortraitFamilyTimelineDetailBO> value = new ArrayList<>();
                        JSONArray topArray = regionGroup.getJSONObject("top_hits").getJSONObject("hits").getJSONArray("hits");
                        if (!CollectionUtils.isEmpty(topArray)) {

                        }
                        for (int k = 0; k < topArray.size(); k++) {
                            JSONObject topObj = topArray.getJSONObject(k);
                            JSONObject hit = topObj.getJSONObject("_source");
                            PatentPortraitFamilyTimelineDetailBO detailBO = new PatentPortraitFamilyTimelineDetailBO();
                            detailBO.setTitle(hit.get("title_cn") == null ? "" : hit.getString("title_cn"));
                            detailBO.setPublicCode(hit.get("public_code") == null ? "" : hit.getString("public_code"));
                            detailBO.setPublicDate(hit.get("public_date") == null ? "" : hit.getString("public_date"));
                            if (hit.get("public_code") != null && hit.get("public_code").equals(publicCode)) {
                                detailBO.setIsCurrent(1);
                            } else {
                                detailBO.setIsCurrent(0);
                            }
                            value.add(detailBO);
                        }
                        dataBO.setValue(value);
                        list.add(dataBO);
                    }
                    timelineBO.setList(list);
                    dataList.add(timelineBO);
                }


            }
        }
        result.put("data", dataList);
        result.put("ordinate", regionOrdinate);
        return result;
    }
}
