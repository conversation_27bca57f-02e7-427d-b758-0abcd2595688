package com.quantchi.nanping.innovation.knowledge.center.model.enums;

import io.swagger.annotations.ApiModel;
import lombok.Getter;

@ApiModel("专利类型枚举")
@Getter
public enum PatentTypeEnum {

    INVENTOR(4, "发明授权"),
    APPLICATION(3, "发明申请"),
    PRACTICAL(2, "实用新型"),
    DESIGN(1, "外观设计"),
    ;

    private final Integer id;

    private final String name;

    PatentTypeEnum(final Integer id, final String name) {
        this.id = id;
        this.name = name;
    }

    public static String getNameByKey(final Integer id) {
        for (final PatentTypeEnum value : PatentTypeEnum.values()) {
            if (value.getId().equals(id)) {
                return value.getName();
            }
        }
        return null;
    }

}
