package com.quantchi.nanping.innovation.knowledge.center.utils.export;

import cn.hutool.core.collection.CollUtil;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.*;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentTechAnalysis;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.PatentValueDimensionEvaluationVO;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.PatentValueOverallEvaluationMarketValueVO;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.PatentValueOverallEvaluationResultCompareVO;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.PatentValueOverallEvaluationVO;
import com.quantchi.nanping.innovation.knowledge.center.service.IPatentDimensionService;
import com.quantchi.nanping.innovation.knowledge.center.service.PatentPortraitService;
import com.quantchi.nanping.innovation.knowledge.center.service.impl.PatentEsInfoService;
import com.quantchi.tianying.model.EsPageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 专利模板填充
 *
 * <AUTHOR>
 * @date 2024/10/31 15:33
 */
@Component
public class PatentExportUtil {

    @Autowired
    private PatentPortraitService patentPortraitService;

    @Autowired
    private IPatentDimensionService patentDimensionService;

    @Autowired
    private PatentEsInfoService patentEsInfoService;

    /**
     * 设置专利概览内容和技术分析内容
     *
     * @param id
     * @param dataMap
     * @throws IOException
     */
    public void setPatentOverview(final String id, final Map<String, Object> dataMap) throws IOException {
        final Map<String, Object> patentOverview = patentPortraitService.patentOverview(id, false);
        // 首页
        dataMap.put("patentName", patentOverview.get("title_cn"));
        dataMap.put("public_code", patentOverview.get("public_code"));

        // 基本信息
        final Object applicants = patentOverview.get("applicants");
        final List<String> applicantsList = new ArrayList<>();
        if (applicants instanceof List) {
            ((List) applicants).forEach(item -> {
                if (item instanceof Map) {
                    applicantsList.add((String) ((Map) item).get("name"));
                }
            });
        }
        final Object applicantsCountry = patentOverview.get("applicants_country");
        final List<String> applicantsCountryList = new ArrayList<>();
        if (applicantsCountry instanceof List) {
            ((List) applicantsCountry).forEach(item -> {
                if (item instanceof String) {
                    applicantsCountryList.add((String) item);
                }
            });
        }
        final Object patentees = patentOverview.get("patentees");
        final List<String> patenteesList = new ArrayList<>();
        if (patentees instanceof List) {
            ((List) patentees).forEach(item -> {
                if (item instanceof Map) {
                    patenteesList.add((String) ((Map) item).get("name"));
                }
            });
        }
        final Object inventors = patentOverview.get("inventors");
        final List<String> inventorsList = new ArrayList<>();
        if (inventors instanceof List) {
            ((List) inventors).forEach(item -> {
                if (item instanceof Map) {
                    inventorsList.add((String) ((Map) item).get("name"));
                }
            });
        }
        final TableRenderData basicInfoTable = Tables.of(new String[][]{
                new String[]{"申请号", getObject(patentOverview.get("apply_code")), "申请日", getObject(patentOverview.get("apply_date"))},
                new String[]{"公开（公告）号", getObject(patentOverview.get("public_code")), "公开（公告）日", getObject(patentOverview.get("public_date"))},
                new String[]{"专利类型", getObject(patentOverview.get("patent_type")), "当前状态", getObject(patentOverview.get("status"))},
                new String[]{"申请人", String.join(",", applicantsList), "申请人国家/地区", String.join(",", applicantsCountryList)},
                new String[]{"申请人地址", getObject(patentOverview.get("address")), "预估到期日", getObject(patentOverview.get("estimated_maturity_date"))},
                new String[]{"当前权利人", String.join(",", patenteesList), "发明人（原始）", String.join(",", inventorsList)},
                new String[]{"权利要求数量", getObject(patentOverview.get("claims_num")), "文献页数", getObject(patentOverview.get("page"))}
        }).border(BorderStyle.DEFAULT).create();
        dataMap.put("basicInfo", basicInfoTable);

        // 专利摘要
        dataMap.put("abstract_cn", patentOverview.get("abstract_cn"));
        dataMap.put("abstract_en", patentOverview.get("abstract_en"));

        // 1.3 分类信息
        // 主ipc分类
        dataMap.put("main_ipc", getObject(patentOverview.get("main_ipc")));
        final Object mainIpcTreeObject = patentOverview.get("main_ipc_tree");
        if (mainIpcTreeObject instanceof PatentPortraitIndustryIpcBO) {
            PatentPortraitIndustryIpcBO mainIpcTree = (PatentPortraitIndustryIpcBO) mainIpcTreeObject;
            dataMap.put("main_ipc_table", getTableRenderDataFromIpcTree(mainIpcTree));
        }

        // ipc分类号
        final Object ipcTreeObject = patentOverview.get("ipc_tree");
        if (ipcTreeObject instanceof List) {
            final List<String> ipcList = new ArrayList<>();
            final List<Map<String, Object>> ipcTableList = new ArrayList<>();
            ((List) ipcTreeObject).forEach(item -> {
                if (item instanceof PatentPortraitClassNumberTreeBO) {
                    final PatentPortraitClassNumberTreeBO classNumberTreeBO = (PatentPortraitClassNumberTreeBO) item;
                    ipcList.add(classNumberTreeBO.getClassNumber());
                    final PatentPortraitIndustryIpcBO tree = classNumberTreeBO.getTree();
                    ipcTableList.add(new HashMap<String, Object>() {{
                        put("ipc_table", getTableRenderDataFromIpcTree(tree));
                        put("ipc_content", "");
                    }});
                }
            });
            if (!CollectionUtils.isEmpty(ipcList)) {
                final String ipcListString = String.join(", ", ipcList);
                dataMap.put("ipcListString", ipcListString);
                dataMap.put("ipcList", ipcTableList);
            }
        }
        // cpc分类号
        final Object cpcTreeObject = patentOverview.get("cpc_tree");
        if (cpcTreeObject instanceof List) {
            final List<String> cpcList = new ArrayList<>();
            final List<Map<String, Object>> cpcTableList = new ArrayList<>();
            ((List) cpcTreeObject).forEach(item -> {
                if (item instanceof PatentPortraitClassNumberTreeBO) {
                    final PatentPortraitClassNumberTreeBO classNumberTreeBO = (PatentPortraitClassNumberTreeBO) item;
                    cpcList.add(classNumberTreeBO.getClassNumber());
                    final PatentPortraitIndustryIpcBO tree = classNumberTreeBO.getTree();
                    cpcTableList.add(new HashMap<String, Object>() {{
                        put("cpc_table", getTableRenderDataFromIpcTree(tree));
                        put("cpc_content", "");
                    }});
                }
            });
            if (!CollectionUtils.isEmpty(cpcList)) {
                final String ipcListString = String.join(", ", cpcList);
                dataMap.put("cpcListString", ipcListString);
                dataMap.put("cpcList", cpcTableList);
            }
        }

        // 行业分类
        final TableRenderData industryTable = Tables.of(new String[][]{
                new String[]{"国民经济行业分类", getIdNameString(patentOverview.get("nec_class_list"))},
                new String[]{"国民经济行业(主)", getIdNameString(patentOverview.get("nec_class_main_list"))},
                new String[]{"新兴产业分类", getIdNameString(patentOverview.get("sec_class_list"))},
                new String[]{"新兴产业(主)", getIdNameString(patentOverview.get("sec_class_main_list"))}
        }).border(BorderStyle.DEFAULT).create();
        dataMap.put("industry_table", industryTable);

        // 同族信息 instance_doc_patent-ec82453775b2e8cf8a20982a0fa63690这个专利有多种同族专利
        final List<List<String>> familyList = new ArrayList<>();
        final Object simpleFamilyCountry = patentOverview.get("simple_family_country");
        final AtomicInteger simpleIndex = new AtomicInteger(0);
        if (simpleFamilyCountry instanceof List) {
            ((List) simpleFamilyCountry).forEach(item -> {
                if (item instanceof PatentPortraitFamilyPatentBO) {
                    final PatentPortraitFamilyPatentBO familyPatentBO = (PatentPortraitFamilyPatentBO) item;
                    final List<String> list = new ArrayList<>();
                    if (simpleIndex.get() == 0) {
                        list.add("同族专利公开号");
                    } else {
                        list.add("");
                    }
                    list.add(familyPatentBO.getCountry());
                    list.add(String.join(",", familyPatentBO.getList()));
                    familyList.add(list);
                    simpleIndex.incrementAndGet();
                }
            });
        }
        final Object completeFamilyCountry = patentOverview.get("complete_family_country");
        final AtomicInteger completeIndex = new AtomicInteger(0);
        if (completeFamilyCountry instanceof List) {
            ((List) completeFamilyCountry).forEach(item -> {
                if (item instanceof PatentPortraitFamilyPatentBO) {
                    final PatentPortraitFamilyPatentBO familyPatentBO = (PatentPortraitFamilyPatentBO) item;
                    final List<String> list = new ArrayList<>();
                    if (completeIndex.get() == 0) {
                        list.add("扩展专利公开号");
                    } else {
                        list.add("");
                    }
                    list.add(familyPatentBO.getCountry());
                    list.add(String.join(",", familyPatentBO.getList()));
                    familyList.add(list);
                    completeIndex.incrementAndGet();
                }
            });
        }
        final String[][] familyTableArray = new String[familyList.size()][];
        for (int i = 0; i < familyList.size(); i++) {
            familyTableArray[i] = familyList.get(i).toArray(new String[0]);
        }
        final TableRenderData familyTable = Tables.of(familyTableArray)
                .border(BorderStyle.DEFAULT).create();
        // 合并单元格，把第0列的第0行到第simpleIndex.get() - 1行合并
        final MergeCellRule.MergeCellRuleBuilder builder = MergeCellRule.builder();
        if (simpleIndex.get() > 1) {
            builder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(simpleIndex.get() - 1, 0));
        }
        if (completeIndex.get() > 1) {
            builder.map(MergeCellRule.Grid.of(simpleIndex.get(), 0), MergeCellRule.Grid.of(simpleIndex.get() + completeIndex.get() - 1, 0));
        }
        final MergeCellRule rule = builder.build();
        familyTable.setMergeRule(rule);
        dataMap.put("familyTable", familyTable);

        // 代理信息
        final TableRenderData agentTable = Tables.of(new String[][]{
                new String[]{"代理机构", getObject(patentOverview.get("agency"))},
                new String[]{"代理人", getObject(patentOverview.get("agent"))}
        }).border(BorderStyle.DEFAULT).create();
        dataMap.put("agentTable", agentTable);

        // 首项权利要求
        dataMap.put("first_claim", getObject(patentOverview.get("first_claim")));

        // 2 技术分析
        PatentTechAnalysis techAnalysis = patentPortraitService.techAnalysis(id);
        // 解决问题
        dataMap.put("problem", techAnalysis.getProblem());
        // 关键技术
        final List<Map<String, Object>> tableList = new ArrayList<>();
        for (Map<String, String> techItem : techAnalysis.getTechnology()) {
            tableList.add(new HashMap<String, Object>() {{
                put("technologyName", techItem.get("name"));
                put("technologyDesc", techItem.get("desc"));
            }});
        }
        dataMap.put("technologyList", tableList);
        // 技术效果
        dataMap.put("effect", techAnalysis.getEffect());
    }

    private String getIdNameString(final Object object) {
        final List<String> necClassStringList = new ArrayList<>();
        if (object instanceof List) {
            ((List) object).forEach(item -> {
                if (item instanceof Map) {
                    necClassStringList.add(((Map) item).get("id") + " "
                            + ((Map) item).get("name"));
                } else if (item instanceof PatentPortraitIndustryClassBO) {
                    necClassStringList.add(((PatentPortraitIndustryClassBO) item).getId() + " "
                            + ((PatentPortraitIndustryClassBO) item).getName());
                }
            });
        }
        return String.join(";", necClassStringList);
    }

    private TableRenderData getTableRenderDataFromIpcTree(PatentPortraitIndustryIpcBO ipcBO) {
        final List<List<String>> mainIpcTableList = new ArrayList<>();
        while (CollUtil.isNotEmpty(ipcBO.getChildren())) {
            final List<PatentPortraitIndustryIpcBO> children = ipcBO.getChildren();
            ipcBO = children.get(0);
            List<String> row = new ArrayList<>(2);
            row.add(ipcBO.getCode());
            row.add(ipcBO.getName());
            mainIpcTableList.add(row);
        }
        final String[][] mainIpcTableArray = new String[mainIpcTableList.size()][];
        for (int i = 0; i < mainIpcTableList.size(); i++) {
            mainIpcTableArray[i] = mainIpcTableList.get(i).toArray(new String[0]);
        }
        return Tables.of(mainIpcTableArray)
                .border(BorderStyle.DEFAULT).create();
    }

    /**
     * 设置价值评价内容
     *
     * @param id
     * @param dataMap
     */
    public void setValueEvaluation(final String id, final Map<String, Object> dataMap) throws IOException {
        //总体评价
        PatentValueOverallEvaluationVO evaluationVO = patentDimensionService.evaluationResult(id);
        dataMap.put("overall_evaluation", evaluationVO.getSummarize()
                .replaceAll("<span style=\"color:#156fff;\">", "")
                .replaceAll("</span>", ""));
        //整体评价结果
        // 突出显示整体评价
        dataMap.put("evaluate_result", getObject(evaluationVO.getResultBO().getEvaluationResult()));
        dataMap.put("evaluate_exceed", evaluationVO.getResultBO().getRatio() + "%");
        StringBuilder wholeEvaluation = new StringBuilder("");
        TableRenderData wholeEvaluationTable = new TableRenderData();
        if (evaluationVO.getResultBO() != null) {
            wholeEvaluation.append("经综合评价，该专利价值评价相同IPC分类技术集群中超过")
                    .append(evaluationVO.getResultBO().getRatio())
                    .append("%")
                    .append("的其他专利，综合价值表现")
                    .append(evaluationVO.getResultBO().getEvaluationResult());

            wholeEvaluationTable = Tables.of(new String[][]{
                    new String[]{"评价结果", getObject(evaluationVO.getResultBO().getEvaluationResult()),
                            "IPC分类排名", "超过" + evaluationVO.getResultBO().getRatio() + "%的专利。"}
            }).border(BorderStyle.DEFAULT).create();
        }
        dataMap.put("whole_evaluation", wholeEvaluation);
        dataMap.put("whole_evaluation_table", wholeEvaluationTable);
        //评价结果对比
        PatentValueOverallEvaluationResultCompareVO compareVO = patentDimensionService.evaluationResultCompare(id);
        StringBuilder evaluationCompare = new StringBuilder("");
        evaluationCompare.append("相较专利所处技术领域集群中位数价值表现，")
                .append(compareVO.getBest())
                .append("维度表现最好，")
                .append(compareVO.getWorst())
                .append("维度表现最差，合计")
                .append(compareVO.getCount())
                .append("项维度低于集群中位数。");
        dataMap.put("evaluation_compare", evaluationCompare);
        RowRenderData evaluationCompareTableRow0 = Rows.of("细分维度", "本专利分值", evaluationVO.getResultBO().getIpcLevel3Code() + "技术集群中位数分值", "差值")
                .textColor("FFFFFF")
                .bgColor("4472C4").center().create();
        Map<String, String> map = new HashMap<>();
        for (CommonDataForCountBO bo : compareVO.getPatentMedianValueList()) {
            map.put(bo.getName(), bo.getData());
        }
        final List<RowRenderData> evaluationCompareTableDataList = new ArrayList<>();
        evaluationCompareTableDataList.add(evaluationCompareTableRow0);
        compareVO.getPatentValueList().forEach(item -> {
            evaluationCompareTableDataList.add(Rows.of(
                    getObject(item.getName()),
                    getObject(item.getData()),
                    getObject(map.get(item.getName())),
                    getObject((!StringUtils.isEmpty(item.getData()) && !StringUtils.isEmpty(map.get(item.getName()))) ?
                            Double.parseDouble(item.getData()) - Double.parseDouble(map.get(item.getName())) : "-")
            ).create());
        });
        dataMap.put("evaluation_compare_table", Tables.create(evaluationCompareTableDataList.toArray(new RowRenderData[0])));
        //预估市场价值
        PatentValueOverallEvaluationMarketValueVO marketValueVO = patentDimensionService.evaluationMarketValue(id);
        // 突出显示市场价值
        dataMap.put("market_value", getObject(marketValueVO.getMarketValue()));
        dataMap.put("value_exceed", getObject(marketValueVO.getIpcLevel3CodeRatio() + "%"));
        StringBuilder estimatedEvaluation = new StringBuilder("");
        estimatedEvaluation.append("经测算，该专利预估市场价值为")
                .append(marketValueVO.getMarketValue())
                .append("万元，")
                .append("相同IPC分类技术集群中超过")
                .append(marketValueVO.getIpcLevel3CodeRatio())
                .append("%的其他专利");
        dataMap.put("estimated_evaluation", estimatedEvaluation);
        TableRenderData estimatedEvaluationTable = Tables.of(new String[][]{
                new String[]{"预估专利市场价值：", getObject(marketValueVO.getMarketValue() + "万元"),
                        "较技术集群平均价值：",
                        getObject((marketValueVO.getCompareAvgValue().contains("-") ? "低" : "高")
                                + marketValueVO.getCompareAvgValue().replace("-", "") + "%"),
                        "同IPC分类排名：",
                        getObject("超过" + marketValueVO.getIpcLevel3CodeRatio() + "%的专利")}
        }).border(BorderStyle.DEFAULT).create();
        dataMap.put("estimated_evaluation_table", estimatedEvaluationTable);
        //维度评价
        PatentValueDimensionEvaluationVO dimensionEvaluationVO = patentDimensionService.evaluationDimension(id);
        StringBuilder dimensionEvaluation = new StringBuilder("");
        dimensionEvaluation.append("通过技术价值、通用价值、法律价值、战略价值、市场价值等五个维度评价该专利，其中")
                .append(dimensionEvaluationVO.getBest())
                .append("在同技术集群中表现最好，")
                .append(dimensionEvaluationVO.getWorst())
                .append("在同技术集群中表现最差，共发现该专利")
                .append(dimensionEvaluationVO.getCount())
                .append("个亮点指标。");
        dataMap.put("dimension_evaluation", dimensionEvaluation);
        if (!CollectionUtils.isEmpty(dimensionEvaluationVO.getList())) {
            RowRenderData dimensionEvaluationTableRow0 = Rows.of("细分维度", "评价结果", "IPC分类排名", "价值亮点")
                    .textColor("FFFFFF")
                    .bgColor("4472C4").center().create();
            Map<String, String> ratioMap = new HashMap<>();
            ratioMap.put("入门", "(超过0%-20%)");
            ratioMap.put("一般", "(超过20%-35%)");
            ratioMap.put("良好", "(超过35%-50%)");
            ratioMap.put("优秀", "(超过50%-70%)");
            ratioMap.put("卓越", "(超过70%-100%)");
            final List<RowRenderData> dimensionEvaluationTableDataList = new ArrayList<>();
            dimensionEvaluationTableDataList.add(dimensionEvaluationTableRow0);
            dimensionEvaluationVO.getList().forEach(item -> {
                dimensionEvaluationTableDataList.add(Rows.of(
                        getObject(item.getName()),
                        getObject(item.getEvaluationResult() + ratioMap.get(item.getEvaluationResult())),
                        getObject("超过" + item.getRatio() + "%的专利"),
                        getObject(item.getValueDesc())
                ).create());
            });
            dataMap.put("dimension_evaluation_table", Tables.create(dimensionEvaluationTableDataList.toArray(new RowRenderData[0])));
        }

    }

    /**
     * 设置同族专利内容
     *
     * @param id
     * @param dataMap
     * @throws IOException
     */
    public void setCognatePatent(final String id, final Map<String, Object> dataMap) throws IOException {
        final List<Map<String, Object>> simplePatentList = patentPortraitService.cognateList(id, 2, null);
        dataMap.put("simpleFamilyNum", simplePatentList.size());
        if (CollUtil.isNotEmpty(simplePatentList)) {
            RowRenderData row0 = Rows.of("序号", "公开(公告)号", "标题(中文)", "优先权号", "公开(公告日)", "申请号", "申请日", "国别或地区", "同族类别")
                    .textColor("FFFFFF")
                    .bgColor("4472C4").center().create();
            final List<RowRenderData> rowDataList = new ArrayList<>();
            rowDataList.add(row0);
            final AtomicInteger index = new AtomicInteger(0);
            simplePatentList.forEach(item -> {
                rowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                        getObject(item.get("public_code")),
                        getObject(item.get("title_cn")),
                        getObject(item.get("priority_code")),
                        getObject(item.get("public_date")),
                        getObject(item.get("apply_code")),
                        getObject(item.get("apply_date")),
                        getObject(item.get("public_country")),
                        getObject(item.get("family_type"))
                ).create());
            });
            dataMap.put("simpleFamilyTable", Tables.create(rowDataList.toArray(new RowRenderData[0])));
        }

        final List<Map<String, Object>> expandPatentList = patentPortraitService.cognateList(id, 3, null);
        dataMap.put("expandFamilyNum", expandPatentList.size());
        if (CollUtil.isNotEmpty(expandPatentList)) {
            RowRenderData row0 = Rows.of("序号", "公开(公告)号", "标题(中文)", "优先权号", "公开(公告日)", "申请号", "申请日", "国别或地区", "同族类别")
                    .textColor("FFFFFF")
                    .bgColor("4472C4").center().create();
            final List<RowRenderData> rowDataList = new ArrayList<>();
            rowDataList.add(row0);
            final AtomicInteger index = new AtomicInteger(0);
            expandPatentList.forEach(item -> {
                rowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                        getObject(item.get("public_code")),
                        getObject(item.get("title_cn")),
                        getObject(item.get("priority_code")),
                        getObject(item.get("public_date")),
                        getObject(item.get("apply_code")),
                        getObject(item.get("apply_date")),
                        getObject(item.get("public_country")),
                        getObject(item.get("family_type"))
                ).create());
            });
            dataMap.put("expandFamilyTable", Tables.create(rowDataList.toArray(new RowRenderData[0])));
        }

    }

    /**
     * 设置引证专利内容
     *
     * @param id
     * @param dataMap
     * @throws IOException
     */
    public void setCitedPatent(final String id, final Map<String, Object> dataMap) throws IOException {
        final Map<String, Object> quoteList = patentPortraitService.quoteChartOrList(id, 2);
        final Object ctList = quoteList.get("ct_list");
        final Object ctfwList = quoteList.get("ctfw_list");
        setCitedOrCitingPatent(ctList, dataMap, "citingPatentNum", "citingPatenTable");
        setCitedOrCitingPatent(ctfwList, dataMap, "citedPatentNum", "citedPatentTable");
    }

    private void setCitedOrCitingPatent(final Object ctList, final Map<String, Object> dataMap,
                                               final String numField, final String tableField) {
        if (ctList instanceof List) {
            final List<Map<String, Object>> ctMapList = (List<Map<String, Object>>) ctList;
            dataMap.put(numField, ctMapList.size());
            if (CollUtil.isNotEmpty(ctMapList)) {
                RowRenderData row0 = Rows.of("序号", "公开(公告)号", "标题", "申请人", "公开(公告日)", "申请号", "申请日", "IPC分类号")
                        .textColor("FFFFFF")
                        .bgColor("4472C4").center().create();
                final List<RowRenderData> rowDataList = new ArrayList<>();
                rowDataList.add(row0);
                final AtomicInteger index = new AtomicInteger(0);
                ctMapList.forEach(item -> {
                    rowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                            getObject(item.get("public_code")),
                            getObject(item.get("title")),
                            getObject(item.get("applicants")),
                            getObject(item.get("public_date")),
                            getObject(item.get("apply_code")),
                            getObject(item.get("apply_date")),
                            getObject(item.get("ipc"))
                    ).create());
                });
                dataMap.put(tableField, Tables.create(rowDataList.toArray(new RowRenderData[0])));
            }
        }
    }

    /**
     * 设置相似专利内容
     *
     * @param id
     * @param dataMap
     */
    public void setSimilarPatent(final String id, final Map<String, Object> dataMap) {
        final EsPageResult esPageResult = patentEsInfoService.querySimilarPatent(id, 1, 10);
        List<Map<String, Object>> similarPatentList = esPageResult.getList();
        if (CollUtil.isNotEmpty(similarPatentList)) {
            RowRenderData row0 = Rows.of("序号", "公开(公告)号", "标题(中文)", "申请人", "公开(公告日)", "申请号", "申请日", "同族类别")
                    .textColor("FFFFFF")
                    .bgColor("4472C4").center().create();
            final List<RowRenderData> rowDataList = new ArrayList<>();
            rowDataList.add(row0);
            final AtomicInteger index = new AtomicInteger(0);
            similarPatentList.forEach(item -> {
                rowDataList.add(Rows.of(String.valueOf(index.incrementAndGet()),
                        getObject(item.get("public_code")),
                        getObject(item.get("title")),
                        getObject(item.get("applicants")),
                        getObject(item.get("public_date")),
                        getObject(item.get("apply_code")),
                        getObject(item.get("apply_date")),
                        getObject(item.get("homoclan_category"))
                ).create());
            });
            dataMap.put("similarPatentTable", Tables.create(rowDataList.toArray(new RowRenderData[0])));
        }
    }

    public static String getObject(final Object object) {
        if (object instanceof String) {
            return object.toString();
        } else if (object instanceof List) {
            final List<String> itemList = new ArrayList<>();
            ((List) object).forEach(item -> {
                if (item instanceof String) {
                    itemList.add((String) item);
                } else if (item instanceof Map) {
                    itemList.add((String) ((Map) item).get("name"));
                }
            });
            return String.join(";", itemList);
        } else if (object != null) {
            return String.valueOf(object);
        }
        return "--";
    }
}
