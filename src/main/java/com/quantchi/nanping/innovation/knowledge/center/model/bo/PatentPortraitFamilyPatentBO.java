package com.quantchi.nanping.innovation.knowledge.center.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PatentPortraitFamilyPatentBO {
    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "国家编码")
    private String encoding;

    @ApiModelProperty(value = "数量")
    private Integer num;

    @ApiModelProperty(value = "专利公开号")
    private List<String> list = new ArrayList<>();
}
