package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/24 下午2:59
 */
@ApiModel("专利价值评分展示类")
@Data
public class PatentValueEvaluationVO {

    @ApiModelProperty("综合价值")
    private Integer value;

    @ApiModelProperty("技术价值评分")
    private Integer technicalValue;

    @ApiModelProperty("通用价值")
    private Integer generalValue;

    @ApiModelProperty("法律价值")
    private Integer legalValue;

    @ApiModelProperty("战略价值")
    private Integer strategicValue;

    @ApiModelProperty("市场价值")
    private Integer marketValue;

    @ApiModelProperty("技术价值评分描述")
    private String technicalValueDesc;

    @ApiModelProperty("通用价值描述")
    private String generalValueDesc;

    @ApiModelProperty("法律价值描述")
    private String legalValueDesc;

    @ApiModelProperty("战略价值描述")
    private String strategicValueDesc;

    @ApiModelProperty("市场价值描述")
    private String marketValueDesc;

    private Integer count;

}
