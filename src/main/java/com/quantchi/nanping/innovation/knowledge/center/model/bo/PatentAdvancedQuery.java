package com.quantchi.nanping.innovation.knowledge.center.model.bo;

import com.quantchi.nanping.innovation.model.bo.PageBO;
import com.quantchi.tianying.model.AdvancedSearchQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/21 16:37
 */
@Data
@ApiModel("专利高级查询条件")
public class PatentAdvancedQuery extends PageBO {

    @ApiModelProperty("聚合字段,用于聚合统计")
    private String aggField;

    @ApiModelProperty("聚合字段值，用于分页查询")
    private Map<String, List<String>> aggFieldValueMap;

    @ApiModelProperty("条件组列表")
    private List<SearchCondition> conditionList;
}
