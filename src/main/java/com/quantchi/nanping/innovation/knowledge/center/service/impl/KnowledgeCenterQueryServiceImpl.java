package com.quantchi.nanping.innovation.knowledge.center.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.quantchi.nanping.innovation.company.model.CompanyCollection;
import com.quantchi.nanping.innovation.company.service.ICompanyCollectionService;
import com.quantchi.nanping.innovation.knowledge.center.config.EsBoostProperties;
import com.quantchi.nanping.innovation.knowledge.center.config.FieldBoostProperty;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.knowledge.center.service.IKnowledgeCenterQueryService;
import com.quantchi.nanping.innovation.knowledge.center.utils.ElasticsearchBuilder;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.ISysUserChainScopeService;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.config.property.CustomIndexNavSetting;
import com.quantchi.tianying.config.property.NavigationSettings;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.model.MultidimensionalQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 知识中心查询服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-18
 */
@Slf4j
@Service
public class KnowledgeCenterQueryServiceImpl implements IKnowledgeCenterQueryService {

    @Resource
    private EsBoostProperties esBoostProperties;

    @Resource
    private NavigationSettings navigationSettings;

    @Autowired
    private ElasticsearchBuilder elasticsearchBuilder;

    @Autowired
    private ISysUserChainScopeService chainScopeService;

    @Autowired
    private IEs8Service es8Service;

    @Autowired
    private ICompanyCollectionService collectionService;

    @Autowired
    private SysLoginService sysLoginService;

    @Override
    public EsPageResult queryByTermsAndKey(MultidimensionalQuery mQuery, boolean isService, String nodePath) {
        EsAlterUtil.checkPageRange(mQuery.getPageNum(), mQuery.getPageSize(), null);
        if (StringUtils.isEmpty(mQuery.getIndex())) {
            throw new IllegalArgumentException("缺少必要的查询条件");
        }
        
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        mQuery.setIndex(mQuery.getIndex());
        if (EsIndexEnum.PATENT.getEsIndex().equals(mQuery.getIndex())){
            mQuery.setIndex(EsIndexEnum.PATENT_VECTOR.getEsIndex());
        }
        
        // 设置字段匹配得分比重
        if (StringUtils.isNotBlank(mQuery.getKeyword())) {
            List<FieldBoostProperty> boostList = esBoostProperties.getFieldBoostListByIndex(mQuery.getIndex());
            ElasticsearchBuilder.keywordQueryWithOperator(boolQuery, mQuery.getKeyword(),
                    Operator.AND, boostList);
        }
        
        // 设置查询条件
        if (mQuery.getTermQueries() != null && mQuery.getTermQueries().size() > 0) {
            List<CustomIndexNavSetting> indexNavigationSettings = navigationSettings.getIndexNavSettingMap().get(mQuery.getIndex());
            Map<String, CustomIndexNavSetting> filedSettingMap = indexNavigationSettings.stream()
                    .collect(Collectors.toMap(CustomIndexNavSetting::getField, Function.identity()));
            for (Map.Entry<String, List<String>> entry : mQuery.getTermQueries().entrySet()) {
                CustomIndexNavSetting fieldSetting = filedSettingMap.get(entry.getKey());
                // 获取字段值范围
                List<String> rangeFields = navigationSettings.getRangeFields(fieldSetting, entry.getValue(), null);
                //获取字段值别名
                Object[] aliasFields = navigationSettings.getAliasFields(Arrays.asList(fieldSetting), entry.getValue());
                if (CollectionUtils.isNotEmpty(rangeFields)) {
                    ElasticsearchBuilder.addFilterCondition(boolQuery, entry.getKey(), rangeFields.toArray());
                } else if (null != aliasFields && aliasFields.length != 0) {
                    ElasticsearchBuilder.addFilterCondition(boolQuery, entry.getKey(), aliasFields);
                } else {
                    if (!EsIndexEnum.POLICY.getEsIndex().equals(mQuery.getIndex())) {
                        ElasticsearchBuilder.addFilterCondition(boolQuery, entry.getKey(), entry.getValue().toArray(new String[entry.getValue().size()]));
                    }
                }
                log.info("综合查询参数:{}", boolQuery);
            }
        }

        if (EsIndexEnum.POLICY.getEsIndex().equals(mQuery.getIndex())) {
            if (isService) {
                boolQuery.filter(QueryBuilders.termQuery("province", CommonConstant.DIVISION_FUJIAN.getName()));
            }
            boolQuery.filter(QueryBuilders.termQuery("is_valid", 1));
        }

        String sort = null;
        EsIndexEnum esIndexEnum = EsIndexEnum.getEsIndexByIndex(mQuery.getIndex());
        if (StringUtils.isBlank(mQuery.getKeyword())) {
            if (null != esIndexEnum) {
                String sortField = esIndexEnum.getSort();
                if (StringUtils.isNotBlank(sortField)) {
                    sort = sortField;
                }
            }
        }
        
        List<FunctionScoreQueryBuilder.FilterFunctionBuilder> customSortRules = EsAlterUtil.buildCustomSortRule(mQuery.getIndex(), null);
        if (StringUtils.isNotEmpty(nodePath)) {
            String[] nodeIds = nodePath.split("\\|");
            if (nodeIds.length == 1) {
                BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                shouldQuery.should(QueryBuilders.termQuery("chain_node.id", nodeIds[0]));
                shouldQuery.should(QueryBuilders.termQuery("chain_node.id.keyword", nodeIds[0]));
                shouldQuery.minimumShouldMatch(1);
                boolQuery.filter(shouldQuery);
            } else {
                BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                shouldQuery.should(QueryBuilders.termQuery("chain_node.id", nodeIds[nodeIds.length - 1]));
                shouldQuery.should(QueryBuilders.termQuery("chain_node.id.keyword", nodeIds[nodeIds.length - 1]));
                shouldQuery.minimumShouldMatch(1);
                boolQuery.filter(shouldQuery);
            }
        }
        
        // 产业链范围控制
        Set<String> permittedChainIds = chainScopeService.getCurrentUserPermittedChainIds();
        if (CollectionUtils.isNotEmpty(permittedChainIds) && !EsIndexEnum.EXPERT.getEsIndex().equals(mQuery.getIndex())) {
            boolQuery.filter(QueryBuilders.termsQuery("chain.id", permittedChainIds));
        }
        
        // 开启向量搜索
        QueryBuilder finalQuery = boolQuery;
        // 获取结果
        SearchResponse searchResponse = null;
        if (EsIndexEnum.PATENT_VECTOR.getEsIndex().equals(mQuery.getIndex())){
            SearchSourceBuilder  searchBuilder = EsAlterUtil.buildSearchSource(finalQuery, mQuery.getPageNum(), mQuery.getPageSize(), null, new String[]{"name_vector","ab_vector","ti_vector"}, sort);
            searchResponse = es8Service.request(searchBuilder, EsIndexEnum.PATENT_VECTOR.getEsIndex(), null, null);
        }else{
            searchResponse = elasticsearchBuilder.pageByFields(mQuery.getIndex(), mQuery.getKeyword(), finalQuery,
                    mQuery.getPageNum(), mQuery.getPageSize(), sort, customSortRules);
        }
        
        EsPageResult resultMap = com.quantchi.tianying.utils.ElasticsearchBuilder.buildPageResult(searchResponse);
        if (EsIndexEnum.PATENT_VECTOR.getEsIndex().equals(mQuery.getIndex())){
            for (Map<String, Object> record : resultMap.getList()) {
                if (StringUtils.isNotEmpty((String)record.get("abstract_cn"))){
                    record.put("abstract", record.get("abstract_cn"));
                }
                if (StringUtils.isNotEmpty((String)record.get("title_cn"))){
                    record.put("name", record.get("title_cn"));
                }
            }
        }
        
        if (isService && StpUtil.isLogin()) {
            // 设置收藏标志
            List<CompanyCollection> collections = collectionService.listByType(sysLoginService.findCollectEntityId(), esIndexEnum.getType());
            List<String> collectedIds = collections.stream().map(CompanyCollection::getCollectId).collect(Collectors.toList());
            resultMap.getList().forEach(r -> {
                if (collectedIds.contains((String) r.get("id"))) {
                    r.put("collected", true);
                }
            });
        }
        
        return resultMap;
    }
}
