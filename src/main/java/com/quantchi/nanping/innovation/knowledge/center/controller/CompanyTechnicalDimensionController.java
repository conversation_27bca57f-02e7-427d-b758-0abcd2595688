package com.quantchi.nanping.innovation.knowledge.center.controller;

import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.CompanyTechnicalInfo;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.PatentValueAnalysis;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.StInnovationLevel;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.StStrengthVO;
import com.quantchi.nanping.innovation.knowledge.center.service.ICompanyTechnicalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/30 16:23
 */
@Slf4j
@RestController
@RequestMapping("/api/company/technical")
@Api(tags = "技术洞察接口")
@RequiredArgsConstructor
public class CompanyTechnicalDimensionController {

    @Autowired
    private ICompanyTechnicalService technicalService;

    @GetMapping("/stStrength")
    @ApiOperation("企业科创实力")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String")
    })
    public Result<StStrengthVO> getStStrengthVO(@RequestParam final String id, String nodeId) {
        StStrengthVO vo = new StStrengthVO();
        StInnovationLevel innovationLevel = technicalService.getOverviewById(id, nodeId);
        PatentValueAnalysis patentValueAnalysis = new PatentValueAnalysis();
        patentValueAnalysis.setAveragePatentValueUnit("万元");
        patentValueAnalysis.setTotalPatentValueUnit("万元");
        patentValueAnalysis = technicalService.getPatentAnalysisById(id);
        vo.setInnovationLevel(innovationLevel);
        vo.setTechnicalQualityAnalysis(patentValueAnalysis);
        return ResultConvert.success(vo);
    }

    @GetMapping("/all")
    public Result<Map<String, Object>> getAll() {
        return ResultConvert.success(technicalService.getTotalCompanyValue());
    }

    private List<CompanyTechnicalDimension> buildDimensions(int oneScore, int twoScore, int threeScore, int fourScore, int fiveScore) {
        List<CompanyTechnicalDimension> dimensionDetails = new ArrayList<>();
        final CompanyTechnicalDimension one = new CompanyTechnicalDimension();
        one.setDimensionName("研发规模");
        one.setEvaluationScore(oneScore);
        dimensionDetails.add(one);
        final CompanyTechnicalDimension two = new CompanyTechnicalDimension();
        two.setDimensionName("研发可持续性");
        two.setEvaluationScore(twoScore);
        dimensionDetails.add(two);
        final CompanyTechnicalDimension three = new CompanyTechnicalDimension();
        three.setDimensionName("技术影响力");
        three.setEvaluationScore(threeScore);
        dimensionDetails.add(three);
        final CompanyTechnicalDimension four = new CompanyTechnicalDimension();
        four.setDimensionName("技术布局");
        four.setEvaluationScore(fourScore);
        dimensionDetails.add(four);
        final CompanyTechnicalDimension five = new CompanyTechnicalDimension();
        five.setDimensionName("技术质量");
        five.setEvaluationScore(fiveScore);
        dimensionDetails.add(five);
        return dimensionDetails;
    }


}
