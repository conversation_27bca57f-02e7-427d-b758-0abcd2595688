package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentValueDimensionEvaluationBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PatentValueDimensionEvaluationVO {

    @ApiModelProperty(value = "IPC分类技术集群")
    private String ipcLevel3Code;

    @ApiModelProperty(value = "表现最好")
    private String best;

    @ApiModelProperty(value = "表现最差")
    private String worst;

    @ApiModelProperty(value = "两点个数")
    private Integer count;

    @ApiModelProperty(value = "数据")
    private List<PatentValueDimensionEvaluationBO> list;
}
