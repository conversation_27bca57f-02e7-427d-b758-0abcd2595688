package com.quantchi.nanping.innovation.knowledge.center.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.model.vo.CommonIndexVO;
import com.quantchi.nanping.innovation.utils.BigDecimalUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.range.ParsedRange;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.range.RangeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 聚合统计工具
 *
 * <AUTHOR>
 * @date 2024/5/29 9:31
 */
@Slf4j
public class EsAggUtil {

    /**
     * 聚合统计数量
     */
    private static final Integer DEFAULT_BUCKET_SIZE = 10;

    /**
     * 其他组别
     */
    private static final String OTHER_GROUP = "其他";

    /**
     * 按日期聚合查询
     *
     * @param es8Service
     * @param index
     * @param dateField
     * @param boolQueryBuilder
     * @return
     */
    public static List<CommonIndexVO> getDateAggregationResult(IEs8Service es8Service, String index,
                                                               String dateField, String dateFormat, DateHistogramInterval interval,
                                                               BoolQueryBuilder boolQueryBuilder) {
        DateHistogramAggregationBuilder dateAggregationBuilder = AggregationBuilders.dateHistogram(dateField).field(dateField)
                .format(dateFormat).calendarInterval(DateHistogramInterval.YEAR).order(BucketOrder.count(false));
        if (StringUtils.isNotEmpty(dateFormat)) {
            dateAggregationBuilder.format(dateFormat);
        }
        if (interval != null) {
            dateAggregationBuilder.calendarInterval(interval);
        }
        List<CommonIndexVO> aggResult = new ArrayList<>();
        JSONObject result = es8Service.getBucketsAggregationPageResult(index, boolQueryBuilder, dateAggregationBuilder);
        if (result == null){
            return aggResult;
        }
        Long totalCount = result.getJSONObject("hits").getJSONObject("total").getLong("value");
        JSONArray groupArray = result.getJSONObject("aggregations").getJSONObject(dateField).getJSONArray("buckets");
        for (int i = 0; i < groupArray.size(); i++) {
            JSONObject group = groupArray.getJSONObject(i);
            String key = group.getString("key_as_string");
            Integer docCount = group.getInteger("doc_count");
            aggResult.add(new CommonIndexVO(key, getProportion(Long.valueOf(docCount), totalCount)));
            if (aggResult.size() >= DEFAULT_BUCKET_SIZE) {
                break;
            }
        }
        return aggResult;
    }

    /**
     * 按数值范围进行聚合
     *
     * @param es8Service
     * @param index
     * @param rangeField
     * @param range
     * @param boolQueryBuilder
     * @return
     */
    public static List<CommonIndexVO> getRangeAggregationResult(IEs8Service es8Service, String index,
                                                                String rangeField, List<Object> range, BoolQueryBuilder boolQueryBuilder) {
        RangeAggregationBuilder aggregationBuilder = AggregationBuilders.range(rangeField).field(rangeField);
        for (int i = 0; i < range.size(); i++) {
            if (i + 1 < range.size()) {
                aggregationBuilder.addRange(Double.valueOf(String.valueOf(range.get(i))),
                        Double.valueOf(String.valueOf(range.get(i + 1))));
            } else {
                aggregationBuilder.addUnboundedFrom(Double.valueOf(String.valueOf(range.get(i))));
            }
        }
        List<CommonIndexVO> aggResult = new ArrayList<>();
        JSONObject result = es8Service.getBucketsAggregationPageResult(index, boolQueryBuilder, aggregationBuilder);
        if (result == null){
            return aggResult;
        }
        Long totalCount = result.getJSONObject("hits").getJSONObject("total").getLong("value");
        JSONArray groupArray = result.getJSONObject("aggregations").getJSONObject(rangeField).getJSONArray("buckets");
        for (int i = 0; i < groupArray.size(); i++) {
            JSONObject group = groupArray.getJSONObject(i);
            Integer docCount = group.getInteger("doc_count");
            String key = convertDoubleStringToString(group.getString("from")) + "-"
                    + (StringUtils.isEmpty(group.getString("to")) ? "*" : convertDoubleStringToString(String.valueOf((group.getDouble("to") - 1))));
            aggResult.add(new CommonIndexVO(key, getProportion(Long.valueOf(docCount), totalCount)));
        }
        return aggResult;
    }

    /**
     * 按照非文本/日期类型字段进行聚合统计
     *
     * @param es8Service
     * @param index
     * @param termField
     * @param termFormat
     * @param range
     * @param boolQueryBuilder
     * @return
     */
    public static List<CommonIndexVO> getTermAggregationResult(IEs8Service es8Service, String index,
                                                               String termField, String termFormat, List<Object> range,
                                                               BoolQueryBuilder boolQueryBuilder) {
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(termField).field(termField);
        if (CollectionUtils.isNotEmpty(range)) {
            termsAggregationBuilder.includeExclude(new IncludeExclude(range.toArray(new String[0]), null));
        } else if (StringUtils.isNotEmpty(termFormat)) {
            termsAggregationBuilder.format(termFormat);
        }
        termsAggregationBuilder.order(BucketOrder.count(false)).size(DEFAULT_BUCKET_SIZE);
        List<CommonIndexVO> aggResult = new ArrayList<>();
        JSONObject result = es8Service.getBucketsAggregationPageResult(index, boolQueryBuilder, termsAggregationBuilder);
        if (result == null){
            return aggResult;
        }
        Long totalCount = result.getJSONObject("hits").getJSONObject("total").getLong("value");
        Long displayCount = 0L;
        Set<String> aggValueSet = new HashSet<>();
        JSONArray groupArray = result.getJSONObject("aggregations").getJSONObject(termField).getJSONArray("buckets");
        for (int i = 0; i < groupArray.size(); i++) {
            JSONObject group = groupArray.getJSONObject(i);
            String key = group.getString("key");
            Integer docCount = group.getInteger("doc_count");
            aggValueSet.add(key);
            displayCount += docCount;
            aggResult.add(new CommonIndexVO(key, getProportion(Long.valueOf(docCount), totalCount)));
        }
        if (CollectionUtils.isNotEmpty(range)) {
            // 补充组别为0
            for (Object emptyGroup : range) {
                if (aggValueSet.contains(emptyGroup) || OTHER_GROUP.equals(emptyGroup)) {
                    continue;
                }
                aggResult.add(new CommonIndexVO(String.valueOf(emptyGroup), "0.00"));
            }
            // 补充其他字段
            if (range.contains(OTHER_GROUP)) {
                aggResult.add(new CommonIndexVO(OTHER_GROUP, getProportion(totalCount - displayCount, totalCount)));
            }
        }
        return aggResult;
    }

    /**
     * 去除double字符串末尾的0
     *
     * @param val
     * @return
     */
    private static String convertDoubleStringToString(String val) {
        BigDecimal bd = new BigDecimal(val);
        return bd.stripTrailingZeros().toPlainString();
    }

    private static String getProportion(Long current, Long total) {
        BigDecimal proportion = BigDecimalUtil.divide(current * 100, total, 2, RoundingMode.HALF_UP);
        if (new BigDecimal(0.01).compareTo(proportion) >= 0 && current > 0) {
            return "0.01";
        }
        return proportion.toString();
    }

}
