package com.quantchi.nanping.innovation.knowledge.center.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentDimension;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
@Mapper
public interface PatentDimensionMapper extends BaseMapper<PatentDimension> {

    /**
     * 按小类统计某个类型分数的中位数
     *
     * @param subClass
     * @param field
     * @return
     */
    @Select("select percentile_cont(0.5) within group (order by ${field}) from patent_dimension where main_ipc_category = #{subClass} and patent_status in ('有效','审中')")
    Integer getMedianValueBySubClass(@Param("subClass") String subClass, @Param("field") String field);

    /**
     * 统计小类下该类型分数小于等于value的数量
     *
     * @param subClass
     * @param field
     * @param value
     * @return
     */
    @Select("select count(*) from patent_dimension where main_ipc_category = #{subClass} and ${field} <= #{value} and patent_status in ('有效','审中')")
    Long countLteBySubClassAndField(@Param("subClass") String subClass, @Param("field") String field, @Param("value") Integer value);
}
