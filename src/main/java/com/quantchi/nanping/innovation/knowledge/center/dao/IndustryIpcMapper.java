package com.quantchi.nanping.innovation.knowledge.center.dao;

import com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentPortraitIndustryClassBO;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentPortraitIndustryIpcBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IndustryIpcMapper {

    PatentPortraitIndustryIpcBO getIpcByCode(@Param("code") String code);

    PatentPortraitIndustryIpcBO getIpcByParentId(@Param("parentId") String parentId);

    /**
     * 根据nec编号获取名称
     * @param list
     * @return
     */
    List<PatentPortraitIndustryClassBO> getNameByNec(@Param("list") List<String> list);

    /**
     * 根据sec编号获取名称
     * @param list
     * @return
     */
    List<PatentPortraitIndustryClassBO> getNameBySec(@Param("list") List<String> list);
}
