package com.quantchi.nanping.innovation.knowledge.center.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PatentPortraitFamilyTimelineDetailBO {
    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "公开公告日")
    private String publicDate;

    @ApiModelProperty(value = "公开公告号")
    private String publicCode;

    @ApiModelProperty(value = "是否当前专利 1是0否")
    private Integer isCurrent;
}
