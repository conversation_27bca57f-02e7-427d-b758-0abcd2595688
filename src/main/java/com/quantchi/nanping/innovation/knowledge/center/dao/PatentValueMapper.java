package com.quantchi.nanping.innovation.knowledge.center.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/10 10:28
 */
@Mapper
public interface PatentValueMapper extends BaseMapper<PatentValue> {

    /**
     * 取同一主分类号下经济价值平均值
     *
     * @param mainIpc
     * @return
     */
    @Select("select AVG(value) from patent_value where main_ipc = #{mainIpc} and status in ('有效','审中')")
    BigDecimal getAvgValueByMainIpc(@Param("mainIpc") String mainIpc);
}
