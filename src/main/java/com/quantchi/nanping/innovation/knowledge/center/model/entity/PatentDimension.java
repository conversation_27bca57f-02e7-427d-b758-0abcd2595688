package com.quantchi.nanping.innovation.knowledge.center.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("patent_dimension")
@ApiModel(value = "PatentDimension对象", description = "")
public class PatentDimension implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("专利ID")
    private String id;

    @ApiModelProperty("主分类号")
    private String mainIpc;

    @ApiModelProperty("小类")
    private String mainIpcCategory;

    @ApiModelProperty("被引次数")
    private Integer ctfwTimes;

    @ApiModelProperty("年均被引次数")
    private Object ctfwTimesYearly;

    @ApiModelProperty("家族被引次数")
    private Integer fctfwTimes;

    @ApiModelProperty("家族年均被引次数")
    private Object fctfwTimesYearly;

    @ApiModelProperty("申请人数量")
    private Integer applicantsNum;

    @ApiModelProperty("发明人数量")
    private Integer inventorsNum;

    @ApiModelProperty("专利自引数量")
    private Integer citeSelfTimes;

    @ApiModelProperty("同族专利平均月份")
    @Deprecated
    private Object familyAgeAvg;

    @ApiModelProperty("保护范围")
    private Integer protectionScope;

    @ApiModelProperty("全文页数")
    private Integer page;

    @ApiModelProperty("技术大组覆盖数量")
    private Integer ipcGroupNum;

    @ApiModelProperty("权利要求数量")
    private Integer claimsNum;

    @ApiModelProperty("专利类型")
    private Integer patentType;

    @ApiModelProperty("复审无效次数")
    private Integer reexamineTimes;

    @ApiModelProperty("有效状态")
    @Deprecated
    private Integer status;

    @ApiModelProperty("专利剩余年限")
    private Integer remainLife;

    @ApiModelProperty("申请人类型")
    @Deprecated
    private Integer applicantType;

    @ApiModelProperty("简单同族专利数量")
    private Integer simpleFamilyNum;

    @ApiModelProperty("扩展同族专利数量")
    private Integer completeFamilyNum;

    @ApiModelProperty("专利转让次数")
    private Integer assignTimes;

    @ApiModelProperty("专利许可次数")
    private Integer licenceTimes;

    @ApiModelProperty("专利质押次数")
    private Integer pledgeTimes;

    @ApiModelProperty("专利布局国家数量")
    private Integer sfCountryNum;

    @ApiModelProperty("专利五局布局数量，世界五国知识产权局：中国、欧洲、日本、韩国、美国")
    private Integer sfMainCountryNum;

    @ApiModelProperty("覆盖国家最近一年的GDP均值")
    private Object sfCountryGdp;

    @ApiModelProperty("技术价值评分")
    private Integer technicalValue;

    @ApiModelProperty("通用价值")
    private Integer generalValue;

    @ApiModelProperty("法律价值")
    private Integer legalValue;

    @ApiModelProperty("战略价值")
    private Integer strategicValue;

    @ApiModelProperty("市场价值")
    private Integer marketValue;

    @ApiModelProperty("综合价值")
    private Integer value;

    @ApiModelProperty("专利状态")
    private String patentStatus;

    public static final String ID = "id";

    public static final String CTFW_TIMES = "ctfw_times";

    public static final String CTFW_TIMES_YEARLY = "ctfw_times_yearly";

    public static final String FCTFW_TIMES = "fctfw_times";

    public static final String FCTFW_TIMES_YEARLY = "fctfw_times_yearly";

    public static final String APPLICANTS_NUM = "applicants_num";

    public static final String INVENTORS_NUM = "inventors_num";

    public static final String CITE_SELF_TIMES = "cite_self_times";

    public static final String FAMILY_AGE_AVG = "family_age_avg";

    public static final String PROTECTION_SCOPE = "protection_scope";

    public static final String PAGE = "page";

    public static final String IPC_GROUP_NUM = "ipc_group_num";

    public static final String CLAIMS_NUM = "claims_num";

    public static final String PATENT_TYPE = "patent_type";

    public static final String REEXAMINE_TIMES = "reexamine_times";

    public static final String STATUS = "status";

    public static final String REMAIN_LIFE = "remain_life";

    public static final String APPLICANT_TYPE = "applicant_type";

    public static final String SIMPLE_FAMILY_NUM = "simple_family_num";

    public static final String COMPLETE_FAMILY_NUM = "complete_family_num";

    public static final String ASSIGN_TIMES = "assign_times";

    public static final String LICENCE_TIMES = "licence_times";

    public static final String PLEDGE_TIMES = "pledge_times";

    public static final String SF_COUNTRY_NUM = "sf_country_num";

    public static final String SF_MAIN_COUNTRY_NUM = "sf_main_country_num";

    public static final String SF_COUNTRY_GDP = "sf_country_gdp";

    public static final String TECHNICAL_VALUE = "technical_value";

    public static final String GENERAL_VALUE = "general_value";

    public static final String LEGAL_VALUE = "legal_value";

    public static final String STRATEGIC_VALUE = "strategic_value";

    public static final String MARKET_VALUE = "market_value";

    public static final String VALUE = "value";

    public static final String CREATE_TIME = "create_time";
}
