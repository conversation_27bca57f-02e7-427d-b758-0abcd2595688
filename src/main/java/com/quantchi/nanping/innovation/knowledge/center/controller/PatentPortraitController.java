package com.quantchi.nanping.innovation.knowledge.center.controller;

import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentTechAnalysis;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.*;
import com.quantchi.nanping.innovation.knowledge.center.service.IPatentDimensionService;
import com.quantchi.nanping.innovation.knowledge.center.service.PatentPortraitService;
import com.quantchi.nanping.innovation.knowledge.center.service.impl.PatentEsInfoService;
import com.quantchi.tianying.model.EsPageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/patent/portrait")
@Api(tags = "专利画像接口")
public class PatentPortraitController {

    @Autowired
    private PatentPortraitService patentPortraitService;

    @Autowired
    private IPatentDimensionService patentDimensionService;

    @Autowired
    private PatentEsInfoService patentEsInfoService;

    @GetMapping("/patentOverview")
    @ApiOperation("专利概览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String")
    })
    public Result<Map<String, Object>> patentOverview(@RequestParam String id) throws IOException {
        return ResultConvert.success(patentPortraitService.patentOverview(id, true));
    }

    @GetMapping("/techAnalysis")
    @ApiOperation("专利技术分析")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String")
    })
    public Result<PatentTechAnalysis> techAnalysis(@RequestParam String id) throws IOException {
        return ResultConvert.success(patentPortraitService.techAnalysis(id));
    }

    @GetMapping("/quoteChartOrList")
    @ApiOperation("引证专利-引证分析图/列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "类型 1.引证分析图 2.引证列表", required = true, dataType = "Integer"),
    })
    public Result<Map<String, Object>> quoteChartOrList(@RequestParam String id, @RequestParam Integer type) throws IOException {
        return ResultConvert.success(patentPortraitService.quoteChartOrList(id, type));
    }

    @GetMapping("/valueEvaluation")
    @ApiOperation("专利价值评价")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "id", value = "链英文名", required = false, dataType = "String")
    })
    public Result<PatentValueEvaluationVO> patentValueEvaluation(
            @RequestParam final String id,
            @RequestParam(required = false, defaultValue = "ningbo_new_energy_vehicle") final String chainEnName) {
        return ResultConvert.success(patentDimensionService.patentValueEvaluation(id, chainEnName));
    }

    @GetMapping("/similarPatent")
    @ApiOperation("相似专利")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, dataType = "Integer")
    })
    public Result<EsPageResult> querySimilarPatent(
            @RequestParam final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        return ResultConvert.success(patentEsInfoService.querySimilarPatent(id, pageNum, pageSize));
    }

    @GetMapping("/cognateMap")
    @ApiOperation("同族专利-同族地图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "数据筛选 1.全部 2.简单同族 3.扩展同族", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "country", value = "国家列表", required = true, dataType = "ArrayList")
    })
    public Result<Map<String, Object>> cognateMap(@RequestParam String id, @RequestParam Integer type, @RequestParam List<String> country) throws IOException{
        return ResultConvert.success(patentPortraitService.cognateMap(id, type, country));
    }

    @GetMapping("/cognateList")
    @ApiOperation("同族专利-同族列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "数据筛选 1.全部 2.简单同族 3.扩展同族", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "country", value = "国家列表", required = true, dataType = "ArrayList")
    })
    public Result<List<Map<String, Object>>> cognateList(@RequestParam String id, @RequestParam Integer type, @RequestParam List<String> country) throws IOException{
        return ResultConvert.success(patentPortraitService.cognateList(id, type, country));
    }

    @GetMapping("/cognateTimeline")
    @ApiOperation("同族专利-同族时间轴")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "数据筛选 1.全部 2.简单同族 3.扩展同族", required = true, dataType = "Integer")
    })
    public Result<Map<String, Object>> cognateTimeline(@RequestParam String id, @RequestParam Integer type) throws IOException {
        return ResultConvert.success(patentPortraitService.cognateTimeline(id, type));
    }

    @GetMapping("/patentValue/overallEvaluation/result")
    @ApiOperation("专利价值评价-总体评价-整体评价结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String")
    })
    public Result<PatentValueOverallEvaluationVO> evaluationResult(@RequestParam final String id) {
        return ResultConvert.success(patentDimensionService.evaluationResult(id));
    }

    @GetMapping("/patentValue/overallEvaluation/resultCompare")
    @ApiOperation("专利价值评价-总体评价-评价结果对比")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String")
    })
    public Result<PatentValueOverallEvaluationResultCompareVO> evaluationResultCompare(@RequestParam final String id) throws IOException {
        return ResultConvert.success(patentDimensionService.evaluationResultCompare(id));
    }

    @GetMapping("/patentValue/overallEvaluation/marketValue")
    @ApiOperation("专利价值评价-总体评价-预估市场价值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String")
    })
    public Result<PatentValueOverallEvaluationMarketValueVO> evaluationMarketValue(@RequestParam final String id) {
        return ResultConvert.success(patentDimensionService.evaluationMarketValue(id));
    }

    @GetMapping("/patentValue/overallEvaluation/dimension")
    @ApiOperation("专利价值评价-总体评价-维度评价")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "专利id", required = true, dataType = "String"),
    })
    public Result<PatentValueDimensionEvaluationVO> evaluationDimension(@RequestParam final String id) {
        return ResultConvert.success(patentDimensionService.evaluationDimension(id));
    }
}
