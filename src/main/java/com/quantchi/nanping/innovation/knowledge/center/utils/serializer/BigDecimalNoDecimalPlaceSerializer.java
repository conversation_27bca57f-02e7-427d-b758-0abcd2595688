package com.quantchi.nanping.innovation.knowledge.center.utils.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;


public class BigDecimalNoDecimalPlaceSerializer extends JsonSerializer<BigDecimal> {

    public BigDecimalNoDecimalPlaceSerializer() {
    }

    @Override
    public void serialize(final BigDecimal value, final JsonGenerator gen, final SerializerProvider serializers) throws IOException {
        if (value != null) {
            final BigDecimal number = value.setScale(0, RoundingMode.HALF_UP);
            gen.writeNumber(number);
        } else {
            gen.writeNumber(value);
        }

    }
}
