package com.quantchi.nanping.innovation.knowledge.center.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.knowledge.center.dao.IndustryIpcMapper;
import com.quantchi.nanping.innovation.knowledge.center.dao.PatentDimensionMapper;
import com.quantchi.nanping.innovation.knowledge.center.dao.PatentValueUnitMapper;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.CommonDataForCountBO;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentPortraitIndustryIpcBO;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentValueDimensionEvaluationBO;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentValueOverallEvaluationResultBO;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentDimension;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentValue;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentValueUnit;
import com.quantchi.nanping.innovation.knowledge.center.model.enums.ApplicantTypeEnum;
import com.quantchi.nanping.innovation.knowledge.center.model.enums.PatentStatusEnum;
import com.quantchi.nanping.innovation.knowledge.center.model.enums.PatentTypeEnum;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.*;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.knowledge.center.service.IPatentDimensionService;
import com.quantchi.nanping.innovation.knowledge.center.service.IPatentValueService;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.quantchi.nanping.innovation.config.MyBatisPlusConfig.DYNAMIC_TABLE_NAME_THREAD_LOCAL;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Service
public class PatentDimensionServiceImpl extends ServiceImpl<PatentDimensionMapper, PatentDimension> implements IPatentDimensionService {

    /**
     * 用于计算的专利状态范围
     */
    private static final List<String> VALID_STATUS = Arrays.asList("有效", "审中");

    @Autowired
    private IndustryIpcMapper industryIpcMapper;

    @Autowired
    private IEs8Service es8Service;

    @Autowired
    private StringRedisCache redisCache;

    @Autowired
    private IPatentValueService patentValueService;

    @Autowired
    private PatentDimensionMapper dimensionMapper;

    @Override
    public PatentValueEvaluationVO patentValueEvaluation(
            final String id, final String chainEnName) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainEnName);
        final PatentDimension patent = this.getById(id);
        return convert2PatentValueEvaluationVO(patent);
    }

    private PatentValueEvaluationVO convert2PatentValueEvaluationVO(PatentDimension dimension) {
        final PatentValueEvaluationVO vo = new PatentValueEvaluationVO();
        if (dimension == null) {
            return vo;
        }
        BeanUtils.copyProperties(dimension, vo);
        // 设置各项分数的描述
        final StringBuilder technicalValueDesc = new StringBuilder(128);
        final StringBuilder generalValueDesc = new StringBuilder(128);
        final StringBuilder legalValueDesc = new StringBuilder(128);
        final StringBuilder strategicValueDesc = new StringBuilder(128);
        final StringBuilder marketValueDesc = new StringBuilder(128);
        int count = 0;
        // 技术价值描述
        final Integer ctfwTimes = dimension.getCtfwTimes();
        if (ctfwTimes != null && ctfwTimes > 0) {
            technicalValueDesc.append("专利被引数量：").append(ctfwTimes).append("\n");
            count++;
        } else {
            //technicalValueDesc.append("无专利被引情况\n");
        }
        final Integer fctfwTimes = dimension.getFctfwTimes();
        if (fctfwTimes != null && fctfwTimes > 0) {
            technicalValueDesc.append("家族专利被引数量：").append(fctfwTimes).append("\n");
            count++;
        } else {
            //technicalValueDesc.append("无家族专利被引情况\n");
        }
        final Integer applicantsNum = dimension.getApplicantsNum();
        if (applicantsNum != null && applicantsNum > 0) {
            technicalValueDesc.append("申请人数量：").append(applicantsNum).append("\n");
            count++;
        } else {
            //technicalValueDesc.append("无申请人数量\n");
        }
        final Integer inventorsNum = dimension.getInventorsNum();
        if (inventorsNum != null && inventorsNum > 0) {
            technicalValueDesc.append("发明人数量：").append(inventorsNum).append("\n");
            count++;
        } else {
            //technicalValueDesc.append("无发明人数量\n");
        }
        final Integer citeSelfTimes = dimension.getCiteSelfTimes();
        if (citeSelfTimes != null && citeSelfTimes > 0) {
            technicalValueDesc.append("专利自引数量：").append(citeSelfTimes);
            count++;
        } else {
            //technicalValueDesc.append("无专利自引情况");
        }

        // 通用价值描述
        final Integer protectionScope = dimension.getProtectionScope();
        if (protectionScope != null && protectionScope > 0) {
            generalValueDesc.append("保护范围：").append(protectionScope).append("\n");
            count++;
        } else {
            //generalValueDesc.append("无保护范围\n");
        }
        final Integer page = dimension.getPage();
        if (page != null && page > 0) {
            generalValueDesc.append("PDF全文页数：").append(page).append("\n");
            count++;
        } else {
            //generalValueDesc.append("无PDF全文\n");
        }
        final Integer ipcGroupNum = dimension.getIpcGroupNum();
        if (ipcGroupNum != null && ipcGroupNum > 0) {
            generalValueDesc.append("专利IPC覆盖数量：").append(ipcGroupNum).append("\n");
            count++;
        } else {
            //generalValueDesc.append("无专利IPC覆盖数量\n");
        }
        final Integer claimsNum = dimension.getClaimsNum();
        if (claimsNum != null && claimsNum > 0) {
            generalValueDesc.append("权利要求数量：").append(claimsNum);
            count++;
        } else {
            //generalValueDesc.append("无权利要求数量");
        }

        // 法律价值描述
        final Integer patentType = dimension.getPatentType();
        if (patentType != null && patentType > 0 && patentType < 5) {
            legalValueDesc.append("专利类型：").append(PatentTypeEnum.getNameByKey(patentType)).append("\n");
            count++;
        } else {
            //legalValueDesc.append("无专利类型\n");
        }
        final Integer status = dimension.getStatus();
        if (status != null && status > 0 && status < 3) {
            legalValueDesc.append("专利状态：").append(PatentStatusEnum.getNameByKey(status));
            count++;
        } else {
            //legalValueDesc.append("无专利状态数据");
        }

        // 战略价值
        final Integer applicantType = dimension.getApplicantType();
        if (applicantType != null && applicantType > 0 && applicantType < 5) {
            strategicValueDesc.append("申请人类型：").append(ApplicantTypeEnum.getNameByKey(applicantType)).append("\n");
            count++;
        } else {
            //strategicValueDesc.append("无申请人类型\n");
        }
        final Integer simpleFamilyNum = dimension.getSimpleFamilyNum();
        if (simpleFamilyNum != null && simpleFamilyNum > 0) {
            strategicValueDesc.append("简单同族专利数量：").append(simpleFamilyNum).append("\n");
            count++;
        } else {
            //strategicValueDesc.append("无简单同族专利数量\n");
        }
        final Integer completeFamilyNum = dimension.getCompleteFamilyNum();
        if (completeFamilyNum != null && completeFamilyNum > 0) {
            strategicValueDesc.append("扩展同族专利数量：").append(completeFamilyNum).append("\n");
            count++;
        } else {
            //strategicValueDesc.append("无扩展同族专利数量\n");
        }
        final Integer assignTimes = dimension.getAssignTimes();
        if (assignTimes != null && assignTimes > 0) {
            strategicValueDesc.append("专利转移次数：").append(assignTimes).append("\n");
            count++;
        } else {
            //strategicValueDesc.append("无专利转移次数\n");
        }
        final Integer licenceTimes = dimension.getLicenceTimes();
        if (licenceTimes != null && licenceTimes > 0) {
            strategicValueDesc.append("专利许可次数：").append(licenceTimes).append("\n");
            count++;
        } else {
            //strategicValueDesc.append("无专利许可次数\n");
        }
        final Integer pledgeTimes = dimension.getPledgeTimes();
        if (pledgeTimes != null && pledgeTimes > 0) {
            strategicValueDesc.append("专利质押次数：").append(pledgeTimes);
            count++;
        } else {
            //strategicValueDesc.append("无专利质押次数");
        }

        // 市场价值
        // 简单同族专利覆盖国家数量
        final Integer sfCountryNum = dimension.getSfCountryNum();
        if (sfCountryNum != null && sfCountryNum > 0) {
            marketValueDesc.append("简单同族专利覆盖国家数量：").append(sfCountryNum).append("\n");
            count++;
        } else {
            //marketValueDesc.append("无简单同族专利覆盖国家数量\n");
        }
        // 五局专利覆盖 simple_family加工：计算五局国家数量
        final Integer sfMainCountryNum = dimension.getSfMainCountryNum();
        if (sfMainCountryNum != null && sfMainCountryNum > 0) {
            marketValueDesc.append("五局专利覆盖国家数量：").append(sfMainCountryNum);
            count++;
        } else {
            //marketValueDesc.append("无五局专利覆盖国家数量");
        }

        vo.setTechnicalValueDesc(technicalValueDesc.toString());
        vo.setGeneralValueDesc(generalValueDesc.toString());
        vo.setLegalValueDesc(legalValueDesc.toString());
        vo.setStrategicValueDesc(strategicValueDesc.toString());
        vo.setMarketValueDesc(marketValueDesc.toString());
        vo.setCount(count);
        return vo;
    }

    private Map<String, Object> findPatentById(String id, String[] includes) {
        Map<String, Object> source = null;
        String existedSource = redisCache.get("patent_portrait:" + id);
        if (StringUtils.isEmpty(existedSource)) {
            source = es8Service.getDataById("nanping_innovation_patent_portrait", id, includes, new String[]{});
        } else {
            source = JSONObject.parseObject(existedSource, Map.class);
        }
        return source;
    }

    @Override
    public PatentValueOverallEvaluationVO evaluationResult(String id) {
        PatentValueOverallEvaluationVO result = new PatentValueOverallEvaluationVO();
        final String[] includes = {"id", "title", "title_cn", "public_code", "abstract", "abstract_cn", "main_ipc", "value"};//查询字段
        Map<String, Object> source = findPatentById(id, includes);
        String mainIpc = (String) source.get("main_ipc");//ipc主分类号

        PatentDimension patentDimension = this.getById(id);
        Integer value = patentDimension.getValue();

        final List<CompletableFuture<Void>> futureList = new ArrayList<>(2);
        futureList.add(CompletableFuture.runAsync(() -> {
            //概述
            String mainIpcName = "";
            if (!StringUtils.isEmpty(mainIpc)) {
                PatentPortraitIndustryIpcBO bo = industryIpcMapper.getIpcByCode(mainIpc);
                if (bo != null) {
                    mainIpcName = bo.getName();
                }
            }

            String summarize = "该专利" +
                    "<span style=\"color:#156fff;\">" +
                    source.get("title") +
                    "（" +
                    source.get("public_code") +
                    "）" +
                    "</span>" +
                    (mainIpc == null ? "" : "隶属IPC主分类号" +
                            "<span style=\"color:#156fff;\">" +
                            mainIpc +
                            "</span>" +
                            "，为" +
                            "<span style=\"color:#156fff;\">" +
                            mainIpcName +
                            "</span>") +
                    "，" +
                    "<span style=\"color:#156fff;\">" +
                    source.get("abstract_cn") +
                    "</span>";
            result.setSummarize(summarize);
        }));
        futureList.add(CompletableFuture.runAsync(() -> {
            //整体评价结果
            //确定ipc level=3的code
            String ipcLevel3Code = patentDimension.getMainIpcCategory();
            if (!StringUtils.isEmpty(ipcLevel3Code)) {
                PatentValueOverallEvaluationResultBO resultBO = new PatentValueOverallEvaluationResultBO();
                resultBO.setIpcLevel3Code(ipcLevel3Code);
                if (value == null) {
                    resultBO.setRatio("0.0");
                    resultBO.setEvaluationResult("入门");
                } else {
                    final BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                    boolQueryBuilder.must(QueryBuilders.wildcardQuery("main_ipc", ipcLevel3Code + "*"));
                    //同三级ipc的专利数
                    long total = countBySubClass(patentDimension.getMainIpcCategory());
                    //boolQueryBuilder.must(QueryBuilders.rangeQuery("value").lte(value));
                    //同三级ipc且value小于等于当前专利的专利数
                    long ltTotal = countLteBySubClass(patentDimension.getMainIpcCategory(), value);
                    if (total != 0) {
                        BigDecimal ltValueRatio = new BigDecimal(ltTotal).divide(new BigDecimal(total), 3, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP);
                        resultBO.setRatio(ltValueRatio.toString());
                        if (ltValueRatio.compareTo(new BigDecimal(0)) >= 0 && ltValueRatio.compareTo(new BigDecimal(30)) < 0) {
                            //0-30入门
                            resultBO.setEvaluationResult("入门");
                        } else if (ltValueRatio.compareTo(new BigDecimal(30)) >= 0 && ltValueRatio.compareTo(new BigDecimal(50)) < 0) {
                            //30-50一般
                            resultBO.setEvaluationResult("一般");
                        } else if (ltValueRatio.compareTo(new BigDecimal(50)) >= 0 && ltValueRatio.compareTo(new BigDecimal(65)) < 0) {
                            //50-65良好
                            resultBO.setEvaluationResult("良好");
                        } else if (ltValueRatio.compareTo(new BigDecimal(65)) >= 0 && ltValueRatio.compareTo(new BigDecimal(80)) < 0) {
                            //65-80优秀
                            resultBO.setEvaluationResult("优秀");
                        } else if (ltValueRatio.compareTo(new BigDecimal(80)) >= 0 && ltValueRatio.compareTo(new BigDecimal(100)) <= 0) {
                            //65-80优秀
                            resultBO.setEvaluationResult("卓越");
                        }
                    }
                }

                result.setResultBO(resultBO);
            }
        }));
        final CompletableFuture<Void> AllFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[2]));
        try {
            AllFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }

        return result;
    }

    /**
     * 按小类统计数量
     *
     * @param subClass
     * @return
     */
    private Long countBySubClass(String subClass) {
        return this.count(Wrappers.lambdaQuery(PatentDimension.class).eq(PatentDimension::getMainIpcCategory, subClass)
                .in(PatentDimension::getPatentStatus, VALID_STATUS));
    }

    /**
     * 统计同小类下小于等于value的数量
     *
     * @param subClass
     * @param value
     * @return
     */
    private Long countLteBySubClass(String subClass, Integer value) {
        return this.count(Wrappers.lambdaQuery(PatentDimension.class).eq(PatentDimension::getMainIpcCategory, subClass)
                .le(PatentDimension::getValue, value)
                .in(PatentDimension::getPatentStatus, VALID_STATUS));
    }

    private String level3CodeByMainIpc(String mainIpc) {
        String ipcLevel3Code = null;
        if (!StringUtils.isEmpty(mainIpc)) {
            PatentPortraitIndustryIpcBO bo = industryIpcMapper.getIpcByCode(mainIpc);
            if (bo != null) {
                String parentId = bo.getParentId();
                while (true) {
                    PatentPortraitIndustryIpcBO mainIpcParentBO = industryIpcMapper.getIpcByParentId(parentId);
                    if (mainIpcParentBO != null) {
                        if (mainIpcParentBO.getLevel() == 3) {
                            ipcLevel3Code = mainIpcParentBO.getCode();
                            break;
                        } else {
                            parentId = mainIpcParentBO.getParentId();
                        }
                    } else {
                        break;
                    }
                }
            }
        }

        return ipcLevel3Code;
    }

    @Override
    public PatentValueOverallEvaluationResultCompareVO evaluationResultCompare(String id) throws IOException {
        PatentValueOverallEvaluationResultCompareVO result = new PatentValueOverallEvaluationResultCompareVO();
        PatentDimension patentDimension = this.getById(id);
        List<CommonDataForCountBO> patentValueList = new ArrayList<>();
        CommonDataForCountBO technical = new CommonDataForCountBO();
        technical.setName("技术价值");
        technical.setData(String.valueOf(patentDimension.getTechnicalValue()));
        patentValueList.add(technical);
        CommonDataForCountBO market = new CommonDataForCountBO();
        market.setName("市场价值");
        market.setData(String.valueOf(patentDimension.getMarketValue()));
        patentValueList.add(market);
        CommonDataForCountBO legal = new CommonDataForCountBO();
        legal.setName("法律价值");
        legal.setData(String.valueOf(patentDimension.getLegalValue()));
        patentValueList.add(legal);
        CommonDataForCountBO strategic = new CommonDataForCountBO();
        strategic.setName("战略价值");
        strategic.setData(String.valueOf(patentDimension.getStrategicValue()));
        patentValueList.add(strategic);
        CommonDataForCountBO general = new CommonDataForCountBO();
        general.setName("通用价值");
        general.setData(String.valueOf(patentDimension.getGeneralValue()));
        patentValueList.add(general);

        result.setPatentValueList(patentValueList);

        //表现最好与最差
        Map<String, Integer> map = new HashMap<>();
        for (CommonDataForCountBO bo : patentValueList) {
            map.put(bo.getName(), StringUtils.isEmpty(bo.getData()) ? null : Integer.parseInt(bo.getData()));
        }

        //计算各价值维度中位数(近似值)
        String ipcLevel3Code = patentDimension.getMainIpcCategory();
        if (!StringUtils.isEmpty(ipcLevel3Code)) {
            List<CommonDataForCountBO> patentMedianValueList = new ArrayList<>();
            CommonDataForCountBO medianTechnical = new CommonDataForCountBO();
            medianTechnical.setName("技术价值");
            medianTechnical.setData(String.valueOf(dimensionMapper.getMedianValueBySubClass(patentDimension.getMainIpcCategory(), PatentDimension.TECHNICAL_VALUE)));
            patentMedianValueList.add(medianTechnical);
            CommonDataForCountBO medianMarket = new CommonDataForCountBO();
            medianMarket.setName("市场价值");
            medianMarket.setData(String.valueOf(dimensionMapper.getMedianValueBySubClass(patentDimension.getMainIpcCategory(), PatentDimension.MARKET_VALUE)));
            patentMedianValueList.add(medianMarket);
            CommonDataForCountBO medianLegal = new CommonDataForCountBO();
            medianLegal.setName("法律价值");
            medianLegal.setData(String.valueOf(dimensionMapper.getMedianValueBySubClass(patentDimension.getMainIpcCategory(), PatentDimension.LEGAL_VALUE)));
            patentMedianValueList.add(medianLegal);
            CommonDataForCountBO medianStrategic = new CommonDataForCountBO();
            medianStrategic.setName("战略价值");
            medianStrategic.setData(String.valueOf(dimensionMapper.getMedianValueBySubClass(patentDimension.getMainIpcCategory(), PatentDimension.STRATEGIC_VALUE)));
            patentMedianValueList.add(medianStrategic);
            CommonDataForCountBO medianGeneral = new CommonDataForCountBO();
            medianGeneral.setName("通用价值");
            medianGeneral.setData(String.valueOf(dimensionMapper.getMedianValueBySubClass(patentDimension.getMainIpcCategory(), PatentDimension.GENERAL_VALUE)));
            patentMedianValueList.add(medianGeneral);
            result.setPatentMedianValueList(patentMedianValueList);

            //低于集群中位数项数
            int count = 0;
            BigDecimal performanceMax = null, performanceMin = null;
            for (CommonDataForCountBO bo : patentMedianValueList) {
                Integer value = map.get(bo.getName());
                if (value != null) {
                    if (value.doubleValue() < Double.parseDouble(bo.getData())) {
                        count++;
                    }
                    BigDecimal distance = new BigDecimal(value - Integer.parseInt(bo.getData())).divide(new BigDecimal(bo.getData()), 2, RoundingMode.HALF_UP);
                    if (performanceMax == null || distance.compareTo(performanceMax) > 0){
                        performanceMax = distance;
                        result.setBest(bo.getName());
                    }
                    if (performanceMin == null){
                        performanceMin = distance;
                        result.setWorst(bo.getName());
                    }
                    if (distance.compareTo(performanceMin) < 0){
                        performanceMin = distance;
                        if (performanceMin.compareTo(performanceMax) < 0){
                            result.setWorst(bo.getName());
                        }
                    }
                }
            }
            result.setCount(count);
        }

        return result;
    }

    @Override
    public PatentValueOverallEvaluationMarketValueVO evaluationMarketValue(String id) {
        PatentValueOverallEvaluationMarketValueVO result = new PatentValueOverallEvaluationMarketValueVO();
        PatentValue patentValue = patentValueService.getById(id);
        PatentValueUnit unit = patentValueService.getCurrentUnit();
        String mainIpc = patentValue.getMainIpc();
        BigDecimal estimateValue = patentValue.getValue() == null ? new BigDecimal(0.0) : patentValue.getValue().multiply(unit.getExchangeRate());
        result.setMainIpc(mainIpc);
        DecimalFormat df = new DecimalFormat("0.00");
        result.setMarketValue(df.format(estimateValue));

        String ipcLevel3Code = patentValue.getMainIpcCategory();
        if (!StringUtils.isEmpty(ipcLevel3Code)) {
            result.setIpcLevel3Code(ipcLevel3Code);
            final List<CompletableFuture<Void>> futureList = new ArrayList<>(3);
            futureList.add(CompletableFuture.runAsync(() -> {
                //同三级ipc的专利数
                long level3Total = patentValueService.countBySubClass(ipcLevel3Code);
                //同三级ipc且estimate_value小于等于当前专利的专利数
                long level3LtTotal = patentValueService.countLteBySubClass(ipcLevel3Code, patentValue.getValue());
                if (level3Total != 0) {
                    BigDecimal level3LtRatio = new BigDecimal(level3LtTotal).divide(new BigDecimal(level3Total), 3, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP);
                    result.setIpcLevel3CodeRatio(level3LtRatio.toString());

                }
            }));
            futureList.add(CompletableFuture.runAsync(() -> {
                //同main_ipc的专利数
                long ipcTotal = patentValueService.countByMainIpc(patentValue.getMainIpc());
                //同main_ipc且estimate_value小于等于当前专利的专利数
                long ipcLtTotal = patentValueService.countLteByMainIpc(patentValue.getMainIpc(), patentValue.getValue());
                if (ipcTotal != 0) {
                    BigDecimal ipcLtRatio = new BigDecimal(ipcLtTotal).divide(new BigDecimal(ipcTotal), 3, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP);
                    result.setMainIpcRatio(ipcLtRatio.toString());

                }
            }));
            futureList.add(CompletableFuture.runAsync(() -> {
                BigDecimal avgValue = patentValueService.getAvgValueByMainIpc(patentValue.getMainIpc());
                if (avgValue.compareTo(new BigDecimal(0)) != 0) {
                    BigDecimal compareAvgValue = (patentValue.getValue().subtract(avgValue))
                            .divide(avgValue, 3, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100))
                            .setScale(1, RoundingMode.HALF_UP);
                    result.setCompareAvgValue(compareAvgValue.toString());
                }
            }));
            final CompletableFuture<Void> AllFutures =
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[3]));
            try {
                AllFutures.get();
            } catch (final Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        return result;
    }

    @Override
    public PatentValueDimensionEvaluationVO evaluationDimension(String id) {
        PatentValueDimensionEvaluationVO result = new PatentValueDimensionEvaluationVO();
        PatentDimension patentDimension = this.getById(id);
        String ipcLevel3Code = patentDimension.getMainIpcCategory();
        Integer technicalValue = patentDimension.getTechnicalValue();
        Integer generalValue = patentDimension.getGeneralValue();
        Integer marketValue = patentDimension.getMarketValue();
        Integer strategicValue = patentDimension.getStrategicValue();
        Integer legalValue = patentDimension.getLegalValue();

        //获取价值两点
        PatentValueEvaluationVO evaluationVO = convert2PatentValueEvaluationVO(patentDimension);
        result.setCount(evaluationVO.getCount());

        if (!StringUtils.isEmpty(ipcLevel3Code)) {
            result.setIpcLevel3Code(ipcLevel3Code);
            List<PatentValueDimensionEvaluationBO> list = new ArrayList<>();
            //同三级ipc的专利数
            final BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("main_ipc", ipcLevel3Code + "*"));
            long total = countBySubClass(ipcLevel3Code);
            final List<CompletableFuture<Void>> futureList = new ArrayList<>(5);
            Map<String, PatentValueDimensionEvaluationBO> map = new HashMap<>();
            futureList.add(CompletableFuture.runAsync(() -> {
                CommonDataForCountBO data = ipcLevel3RankForValue(ipcLevel3Code, "technical_value", technicalValue, total);
                PatentValueDimensionEvaluationBO evaluationBO = new PatentValueDimensionEvaluationBO();
                evaluationBO.setName("技术价值");
                evaluationBO.setEvaluationResult(data.getName());
                evaluationBO.setIpcLevel3Code(ipcLevel3Code);
                evaluationBO.setRatio(data.getData());
                evaluationBO.setValueDesc(evaluationVO.getTechnicalValueDesc());
                map.put("技术价值", evaluationBO);
            }));
            futureList.add(CompletableFuture.runAsync(() -> {
                CommonDataForCountBO data = ipcLevel3RankForValue(ipcLevel3Code, "general_value", generalValue, total);
                PatentValueDimensionEvaluationBO evaluationBO = new PatentValueDimensionEvaluationBO();
                evaluationBO.setName("通用价值");
                evaluationBO.setEvaluationResult(data.getName());
                evaluationBO.setIpcLevel3Code(ipcLevel3Code);
                evaluationBO.setRatio(data.getData());
                evaluationBO.setValueDesc(evaluationVO.getGeneralValueDesc());
                map.put("通用价值", evaluationBO);
            }));
            futureList.add(CompletableFuture.runAsync(() -> {
                CommonDataForCountBO data = ipcLevel3RankForValue(ipcLevel3Code, "market_value", marketValue, total);
                PatentValueDimensionEvaluationBO evaluationBO = new PatentValueDimensionEvaluationBO();
                evaluationBO.setName("市场价值");
                evaluationBO.setEvaluationResult(data.getName());
                evaluationBO.setIpcLevel3Code(ipcLevel3Code);
                evaluationBO.setRatio(data.getData());
                evaluationBO.setValueDesc(evaluationVO.getMarketValueDesc());
                map.put("市场价值", evaluationBO);
            }));
            futureList.add(CompletableFuture.runAsync(() -> {
                CommonDataForCountBO data = ipcLevel3RankForValue(ipcLevel3Code, "strategic_value", strategicValue, total);
                PatentValueDimensionEvaluationBO evaluationBO = new PatentValueDimensionEvaluationBO();
                evaluationBO.setName("战略价值");
                evaluationBO.setEvaluationResult(data.getName());
                evaluationBO.setIpcLevel3Code(ipcLevel3Code);
                evaluationBO.setRatio(data.getData());
                evaluationBO.setValueDesc(evaluationVO.getStrategicValueDesc());
                map.put("战略价值", evaluationBO);
            }));
            futureList.add(CompletableFuture.runAsync(() -> {
                CommonDataForCountBO data = ipcLevel3RankForValue(ipcLevel3Code, "legal_value", legalValue, total);
                PatentValueDimensionEvaluationBO evaluationBO = new PatentValueDimensionEvaluationBO();
                evaluationBO.setName("法律价值");
                evaluationBO.setEvaluationResult(data.getName());
                evaluationBO.setIpcLevel3Code(ipcLevel3Code);
                evaluationBO.setRatio(data.getData());
                evaluationBO.setValueDesc(evaluationVO.getLegalValueDesc());
                map.put("法律价值", evaluationBO);
            }));

            final CompletableFuture<Void> AllFutures =
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[5]));
            try {
                AllFutures.get();
            } catch (final Exception e) {
                log.error(e.getMessage(), e);
            }
            list.add(map.get("技术价值"));
            list.add(map.get("通用价值"));
            list.add(map.get("市场价值"));
            list.add(map.get("战略价值"));
            list.add(map.get("法律价值"));
            result.setList(list);
            double best = -1.0;
            double worst = 101.0;
            for (PatentValueDimensionEvaluationBO bo : list) {
                if (Double.parseDouble(bo.getRatio()) > best) {
                    result.setBest(bo.getName());
                    best = Double.parseDouble(bo.getRatio());
                }
                if (Double.parseDouble(bo.getRatio()) < worst) {
                    result.setWorst(bo.getName());
                    worst = Double.parseDouble(bo.getRatio());
                }
            }
        }

        return result;
    }

    private CommonDataForCountBO ipcLevel3RankForValue(String ipcLevel3Code, String valueField, Integer value, long total) {
        CommonDataForCountBO result = new CommonDataForCountBO();
        if (value == null) {
            result.setData("0.0");
            result.setName("入门");
            return result;
        }
        //同三级ipc且value小于等于当前专利的专利数
        final BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.wildcardQuery("main_ipc", ipcLevel3Code + "*"));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(valueField).lte(value));
        long ltTotal = dimensionMapper.countLteBySubClassAndField(ipcLevel3Code, valueField, value);
        if (total != 0) {
            BigDecimal ltValueRatio = new BigDecimal(ltTotal).divide(new BigDecimal(total), 3, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP);
            result.setData(ltValueRatio.toString());
            if (ltValueRatio.compareTo(new BigDecimal(0)) >= 0 && ltValueRatio.compareTo(new BigDecimal(30)) < 0) {
                //0-30入门
                result.setName("入门");
            } else if (ltValueRatio.compareTo(new BigDecimal(30)) >= 0 && ltValueRatio.compareTo(new BigDecimal(50)) < 0) {
                //30-50一般
                result.setName("一般");
            } else if (ltValueRatio.compareTo(new BigDecimal(50)) >= 0 && ltValueRatio.compareTo(new BigDecimal(65)) < 0) {
                //50-65良好
                result.setName("良好");
            } else if (ltValueRatio.compareTo(new BigDecimal(65)) >= 0 && ltValueRatio.compareTo(new BigDecimal(80)) < 0) {
                //65-80优秀
                result.setName("优秀");
            } else if (ltValueRatio.compareTo(new BigDecimal(80)) >= 0 && ltValueRatio.compareTo(new BigDecimal(100)) <= 0) {
                //65-80优秀
                result.setName("卓越");
            }
        }

        return result;
    }

}
