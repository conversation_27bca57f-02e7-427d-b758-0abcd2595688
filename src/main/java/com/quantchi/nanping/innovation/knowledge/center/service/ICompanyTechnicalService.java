package com.quantchi.nanping.innovation.knowledge.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.CompanyPatentRelation;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.PatentValueAnalysis;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.StInnovationLevel;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/19 11:06
 */
public interface ICompanyTechnicalService extends IService<CompanyPatentRelation> {

    /**
     * 获得企业总体科创评价
     *
     * @param companyId
     * @return
     */
    StInnovationLevel getOverviewById(String companyId, String nodeId);

    /**
     * 获得企业专利价值分析
     *
     * @param companyId
     * @return
     */
    PatentValueAnalysis getPatentAnalysisById(String companyId);

    /**
     * 获取全部企业总的专利价值
     *
     * @return
     */
    Map<String, Object> getTotalCompanyValue();

    /**
     * 是否展示科创实力评价
     *
     * @param companyId
     * @return
     */
    boolean dsiplayTechnicalEvaluation(String companyId);
}
