package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/14 下午2:21
 */
@Data
@ApiModel("科创实力")
public class StStrengthVO {

    public StStrengthVO() {
        innovationLevel = new StInnovationLevel();
        researchScaleAnalysis = new ResearchScaleAnalysis();
        technicalQualityAnalysis = new PatentValueAnalysis();
        technicalAnalysisReport = new TechnicalAnalysisReport();
    }

    @ApiModelProperty("科创能级评价")
    private StInnovationLevel innovationLevel;

    @ApiModelProperty("研发规模分析")
    private ResearchScaleAnalysis researchScaleAnalysis;

    @ApiModelProperty("技术质量分析")
    private PatentValueAnalysis technicalQualityAnalysis;

    @ApiModelProperty("技术布局分析")
    private TechnicalAnalysisReport technicalAnalysisReport;

}
