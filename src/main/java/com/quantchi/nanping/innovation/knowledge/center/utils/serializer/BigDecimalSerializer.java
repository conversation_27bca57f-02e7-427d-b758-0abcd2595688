package com.quantchi.nanping.innovation.knowledge.center.utils.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/30 下午5:08
 */
public class BigDecimalSerializer extends JsonSerializer<BigDecimal> {

    public BigDecimalSerializer() {
    }

    @Override
    public void serialize(final BigDecimal value, final JsonGenerator gen, final SerializerProvider serializers) throws IOException {
        if (value != null) {
            BigDecimal number = value.setScale(2, BigDecimal.ROUND_HALF_UP);
            gen.writeNumber(number);
        } else {
            gen.writeNumber(value);
        }

    }
}
