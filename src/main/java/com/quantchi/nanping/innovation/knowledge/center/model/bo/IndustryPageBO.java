package com.quantchi.nanping.innovation.knowledge.center.model.bo;

import com.quantchi.nanping.innovation.model.bo.PageBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/17 10:33
 */
@ApiModel(value = "看产业查询参数")
@Data
public class IndustryPageBO extends PageBO {

    @ApiModelProperty("用户实体id")
    private String entityId;

    @ApiModelProperty("用户类型")
    private Integer userType;

    @ApiModelProperty("索引名称")
    private String index;

    @ApiModelProperty("关键字搜索")
    private String keyword;
}
