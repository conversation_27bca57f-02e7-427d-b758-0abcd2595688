package com.quantchi.nanping.innovation.knowledge.center.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.google.j2objc.annotations.AutoreleasePool;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.common.core.exception.base.BusinessException;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.model.CompanyCollection;
import com.quantchi.nanping.innovation.company.service.ICompanyCollectionService;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentAdvancedQuery;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.SearchCondition;
import com.quantchi.nanping.innovation.knowledge.center.model.enums.PatentAggregationEnum;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.knowledge.center.utils.ElasticsearchBuilder;
import com.quantchi.nanping.innovation.knowledge.center.utils.EsAggUtil;
import com.quantchi.nanping.innovation.model.bo.CompanyPageBo;
import com.quantchi.nanping.innovation.model.bo.CompanyPatentPageBo;
import com.quantchi.nanping.innovation.model.enums.BusinessType;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.vo.CommonIndexVO;
import com.quantchi.nanping.innovation.service.IWordVectorService;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.SpringUtils;
import com.quantchi.tianying.config.property.CustomIndexNavSetting;
import com.quantchi.tianying.config.property.NavigationSettings;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.ActualFieldAndType;
import com.quantchi.tianying.model.EsPageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.cluster.metadata.MappingMetadata;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/21 17:45
 */
@Api(tags = "找技术")
@Slf4j
@RestController
@RequestMapping("/patent")
@Metrics
@Validated
public class PatentSearchController {

    @Resource
    private NavigationSettings navigationSettings;

    @Autowired
    private IEs8Service es8Service;

    @Autowired
    private ICompanyCollectionService collectionService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private PatentService patentService;

    /**
     * 需要手动高亮的字段
     */
    private static final List<String> CUSTOM_HIGHLIGHT_FIELDS = Arrays.asList("apply_code", "public_code", "ipc", "cpc", "patentee", "applicants.name", "inventors.name");

    /**
     * 范围字符
     */
    private static final String RANGE_SPILT = "-";

    /**
     * 不设限的范围边界
     */
    private static final String UNLIMITED_BOUND = "*";

    /**
     * 中文标点符号正则
     */
    private static final Pattern CHINESE_SYMBOL_PATTERN = Pattern.compile("[\\pP\\p{Punct}]");

    /**
     * 获取对应索引条目的导航栏设置
     *
     * @param index
     * @return
     */
    @ApiOperation("获取检索字段配置")
    @GetMapping("/getNavSetting")
    @AntiReptile
    public Result getNavSetting(@NonNull String index, String termQuery) {
        List<CustomIndexNavSetting> customIndexNavSettings = navigationSettings.getIndexNavSettingMap().get(index);
        if (StringUtils.isNotBlank(termQuery)) {
            // 只查询单个field
            customIndexNavSettings = customIndexNavSettings.stream().filter(item ->
                    termQuery.equals(item.getField())).collect(Collectors.toList());
            return ResultConvert.success(customIndexNavSettings);
        }
        // 级联参数根据父级的scope参数构建children
        customIndexNavSettings.forEach(customIndex -> {
            final List<CustomIndexNavSetting> children = customIndex.getChildren();
            if (children == null) {
                return;
            }
            children.forEach(ch -> {
                final List<CustomIndexNavSetting> settings = new LinkedList<>();
                final List<String> scope = ch.getScope();
                if (scope == null) {
                    return;
                }
                // 通过scope构建子级的list
                scope.forEach(sc -> {
                    final CustomIndexNavSetting customIndexNavSetting = new CustomIndexNavSetting();
                    // 使用名称作为field
                    customIndexNavSetting.setField(sc);
                    customIndexNavSetting.setFieldName(sc);
                    settings.add(customIndexNavSetting);
                });
                ch.setChildren(settings);
                ch.setField(ch.getFieldName());
                ch.setScope(null);
            });
        });
        return ResultConvert.success(customIndexNavSettings);
    }

    @ApiOperation("获取聚合字段配置")
    @GetMapping("/getAggSetting")
    @AntiReptile
    public Result<Map<String, String>> getAggregationSetting() {
        Map<String, String> settingMap = new LinkedHashMap<>();
        for (PatentAggregationEnum pEnum : PatentAggregationEnum.values()) {
            settingMap.put(pEnum.getField(), pEnum.getFieldName());
        }
        return ResultConvert.success(settingMap);
    }

    @ApiOperation("获取聚合统计")
    @PostMapping("/aggregation")
    @AntiReptile
    @Log(title = "专利聚合统计", businessType = BusinessType.OTHER)
    public Result<List<CommonIndexVO>> getAggregationResult(@RequestBody @NonNull PatentAdvancedQuery query) {
        // 字段校验
        if (StringUtils.isEmpty(query.getAggField())) {
            throw new BusinessException("聚合字段选择不能为空");
        }
        PatentAggregationEnum aggEnum = PatentAggregationEnum.findByField(query.getAggField());
        if (aggEnum == null) {
            throw new BusinessException("聚合字段不存在，请重新选择");
        }
        // 获取配置
        String index = EsIndexEnum.PATENT_VECTOR.getEsIndex();
        // 构建查询条件
        BoolQueryBuilder complexQuery = buildComplexAdvancedQuery(query.getConditionList());
        complexQuery.mustNot(QueryBuilders.termQuery("patent_type", "外观设计"));
        complexQuery.filter(QueryBuilders.existsQuery("chain.id"));
        // 加上聚合字段，进行聚合统计
        PatentAggregationEnum aggSetting = PatentAggregationEnum.findByField(query.getAggField());
        List<CommonIndexVO> aggResult = null;
        if (aggSetting.getFieldClazz().equals(Date.class)) {
            // 日期聚合
            aggResult = EsAggUtil.getDateAggregationResult(es8Service, index,
                    query.getAggField(), aggSetting.getFieldFormat(), DateHistogramInterval.YEAR, complexQuery);
        } else if (aggSetting.isInterval() && CollectionUtils.isNotEmpty(aggSetting.getFieldRange())) {
            // 范围聚合
            aggResult = EsAggUtil.getRangeAggregationResult(es8Service, index,
                    query.getAggField(), aggSetting.getFieldRange(), complexQuery);
        } else {
            // 字段聚合
            aggResult = EsAggUtil.getTermAggregationResult(es8Service, index,
                    query.getAggField(), aggSetting.getFieldFormat(), aggSetting.getFieldRange(), complexQuery);
        }
        return ResultConvert.success(aggResult);
    }

    @ApiOperation("分页查询")
    @PostMapping("/filtering")
    @AntiReptile
    @Log(title = "专利检索", businessType = BusinessType.OTHER)
    public Result<EsPageResult> getPageResult(@RequestBody @NonNull PatentAdvancedQuery query) {
        // 参数校验
        EsAlterUtil.checkPageRange(query.getPageNum(), query.getPageSize(), null);
        // 获取配置
        String index = EsIndexEnum.PATENT_VECTOR.getEsIndex();
        // 构建查询
        if (query.getConditionList() == null) {
            query.setConditionList(new ArrayList<>(0));
        }
        // 补充聚合字段筛选
        BoolQueryBuilder groupQueryBuilder = QueryBuilders.boolQuery();
        if (query.getAggFieldValueMap() != null && query.getAggFieldValueMap().size() > 0) {
            for (Map.Entry<String, List<String>> aggFiledValues : query.getAggFieldValueMap().entrySet()) {
                groupQueryBuilder.filter(buildAggregationFieldQuery(aggFiledValues.getKey(), aggFiledValues.getValue()));
            }
        }
        groupQueryBuilder.mustNot(QueryBuilders.termQuery("patent_type", "外观设计"));
        groupQueryBuilder.filter(QueryBuilders.existsQuery("chain.id"));
        QueryBuilder finalQuery = null;
        boolean vectorSearch = false;
        String vectorColumn = null;
        List<String> texts = new ArrayList<>();
        // 如果是标题或者摘要，开启变量搜索
        if (CollectionUtils.isNotEmpty(query.getConditionList())) {
//            vectorSearch = true;
//            SearchCondition searchCondition = query.getConditionList().get(0);
//            if ("name".equalsIgnoreCase(searchCondition.getField())) {
//                texts.addAll(searchCondition.getValueList());
//                vectorColumn = "ti_vector";
//            }
//            if ("abstract".equalsIgnoreCase(searchCondition.getField())) {
//                texts.addAll(searchCondition.getValueList());
//                vectorColumn = "ab_vector";
//            }
            finalQuery = groupQueryBuilder.filter(buildComplexAdvancedQuery(query.getConditionList()));
        } else {
            finalQuery = groupQueryBuilder;
        }
        // 构建分页查询
        SearchSourceBuilder searchSource = EsAlterUtil.buildSearchSource(finalQuery, query.getPageNum(), query.getPageSize(),
                null, new String[]{"ti_vector", "ab_vector", "tiab_vector"}, null);
        //定义高亮查询
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        //设置需要高亮的字段
        if (CollectionUtils.isNotEmpty(query.getConditionList())) {
            highlightBuilder.field(query.getConditionList().get(0).getField())
                    // 设置前缀、后缀
                    .preTags("<span style=\"color:rgb(30, 132, 246)\">")
                    .postTags("</span>");
            searchSource.highlighter(highlightBuilder);
        }
        searchSource.sort("_score", SortOrder.DESC);
        searchSource.sort("public_date", SortOrder.DESC);
        // 获取结果
        SearchResponse searchResponse = es8Service.request(searchSource, index, vectorColumn, CollectionUtils.isNotEmpty(texts) ? texts.get(0) : null);
//        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSource, index);
        // 收集需要手动高亮的字段，keyword类型
        Map<String, String> keywordHighLightMap = new HashMap<>();
        for (SearchCondition condition : query.getConditionList()) {
            if (CUSTOM_HIGHLIGHT_FIELDS.contains(condition.getField())
                    || (vectorSearch && Arrays.asList("name", "abstract_cn").contains(condition.getField()))) {
                keywordHighLightMap.put(condition.getField(), condition.getValueList().get(0));
            }
        }
        EsPageResult esPageResult = ElasticsearchBuilder.buildPageResultWithHighlight(searchResponse, keywordHighLightMap);
        for (Map<String, Object> record : esPageResult.getList()) {
            if (StringUtils.isNotEmpty((String)record.get("abstract_cn"))){
                record.put("abstract", record.get("abstract_cn"));
            }
        }
        if (StpUtil.isLogin()) {
            // 设置收藏标志
            List<CompanyCollection> collections = collectionService.listByType(sysLoginService.findCollectEntityId(), EsIndexEnum.PATENT_VECTOR.getType());
            List<String> collectedIds = collections.stream().map(CompanyCollection::getCollectId).collect(Collectors.toList());
            esPageResult.getList().forEach(r -> {
                if (collectedIds.contains((String) r.get("id"))) {
                    r.put("collected", true);
                }
            });
        }
        return ResultConvert.success(esPageResult);
    }

    @ApiOperation("分页查询")
    @PostMapping("/byCompanyId")
    @AntiReptile
    @Log(title = "企业画像-企业专利检索", businessType = BusinessType.OTHER)
    public Result<Map<String, Object>> pageByCompanyId(@RequestBody @NonNull CompanyPatentPageBo patentPageBo) {
        if (StringUtils.isEmpty(patentPageBo.getCompanyId()) || patentPageBo.getPageNum() == null
                || patentPageBo.getPageSize() == null){
            return ResultConvert.error(500, "缺少参数");
        }
        if (patentPageBo.getPageSize() > 100){
            patentPageBo.setPageSize(100);
        }
        return ResultConvert.success(patentService.pageByCompanyId(patentPageBo.getCompanyId(), patentPageBo.getPageNum(), patentPageBo.getPageSize()));
    }

    /**
     * 判断是否是中文标点符号
     *
     * @param str
     * @return
     */
    public static boolean isChinesePunctuation(String str) {
        Matcher matcher = CHINESE_SYMBOL_PATTERN.matcher(str);
        return matcher.find();
    }


    /**
     * 根据聚合分组筛选构建查询条件
     *
     * @param aggField
     * @param aggFieldValues
     * @return
     */
    private QueryBuilder buildAggregationFieldQuery(String aggField, List<String> aggFieldValues) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        PatentAggregationEnum aggEnum = PatentAggregationEnum.findByField(aggField);
        if (aggEnum == null) {
            throw new BusinessException("聚合字段不存在，请重新选择");
        }
        if (CollectionUtils.isEmpty(aggFieldValues)) {
            throw new BusinessException("请选择聚合分组");
        }
        if (aggEnum.getFieldClazz().equals(Date.class)) {
            for (String selectedGroup : aggFieldValues) {
                String rangeSuffix = "||/" + aggEnum.getFieldFormat().charAt(aggEnum.getFieldFormat().length() - 1);
                queryBuilder.should(QueryBuilders.rangeQuery(aggEnum.getField())
                        .lte(selectedGroup + rangeSuffix).gte(selectedGroup + rangeSuffix).format(aggEnum.getFieldFormat()));
            }
        } else if (aggEnum.isInterval() && CollectionUtils.isNotEmpty(aggEnum.getFieldRange())) {
            for (String selectedGroup : aggFieldValues) {
                String[] range = selectedGroup.split(RANGE_SPILT);
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(aggEnum.getField());
                if (!UNLIMITED_BOUND.equals(range[0])) {
                    rangeQueryBuilder.gte(range[0]);
                }
                if (!UNLIMITED_BOUND.equals(range[1])) {
                    rangeQueryBuilder.lte(range[1]);
                }
                queryBuilder.should(rangeQueryBuilder);
            }
        } else {
            for (String selectedGroup : aggFieldValues) {
                if ("其他".equals(selectedGroup) && CollectionUtils.isNotEmpty(aggEnum.getFieldRange())) {
                    queryBuilder.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.termsQuery(aggEnum.getField(), aggEnum.getFieldRange())));
                } else if (StringUtils.isEmpty(aggEnum.getFieldFormat())) {
                    queryBuilder.should(QueryBuilders.termQuery(aggEnum.getField(), selectedGroup));
                } else {
                    queryBuilder.should(QueryBuilders.matchPhraseQuery(aggEnum.getField(), selectedGroup));
                }
            }
        }
        queryBuilder.minimumShouldMatch(1);
        return queryBuilder;
    }

    /**
     * 构建搜素字段查询
     *
     * @param conditionList
     * @return
     */
    private BoolQueryBuilder buildComplexAdvancedQuery(List<SearchCondition> conditionList) {
        BoolQueryBuilder complexQuery = QueryBuilders.boolQuery();
        if (CollectionUtils.isEmpty(conditionList)) {
            return complexQuery;
        }
        for (int i = 0; i < conditionList.size(); i++) {
            final SearchCondition currentCondition = conditionList.get(i);
            // 判断连接符
            if (i == 0) {
                // 根据条件判断使用should还是must
                if (conditionList.size() > 1) {
                    final SearchCondition secondCondition = conditionList.get(1);
                    if (Operator.OR.toString().equals(secondCondition.getOperator())) {
                        complexQuery = complexQuery.should(buildAdvancedQuery(currentCondition));
                    } else {
                        complexQuery = complexQuery.must(buildAdvancedQuery(currentCondition));
                    }
                } else {
                    complexQuery = complexQuery.must(buildAdvancedQuery(currentCondition));
                }
            } else {
                if (null != currentCondition.getOperator() && Operator.OR.toString().equals(currentCondition.getOperator())) {
                    complexQuery = complexQuery.should(buildAdvancedQuery(currentCondition));
                } else {
                    complexQuery = complexQuery.must(buildAdvancedQuery(currentCondition));
                }
            }
        }
        return complexQuery;
    }

    /**
     * 高级搜索条件参数构建
     *
     * @param searchCondition
     * @return
     */
    private BoolQueryBuilder buildAdvancedQuery(SearchCondition searchCondition) {
        final String operator = searchCondition.getOperator();
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (operator.equals(Operator.OR.toString())) {
            boolQueryBuilder.should(buildBoolQueryForValue(searchCondition));
        } else {
            if (Objects.equals(operator, "NOT")) {
                boolQueryBuilder.mustNot(buildBoolQueryForValue(searchCondition));
            } else {
                boolQueryBuilder.must(buildBoolQueryForValue(searchCondition));
            }
        }
        return boolQueryBuilder;
    }

    /**
     * 按字段构建不同类型查询
     *
     * @param searchCondition
     * @return
     */
    private QueryBuilder buildBoolQueryForValue(final SearchCondition searchCondition) {
        if (CollectionUtils.isEmpty(searchCondition.getValueList())) {
            return QueryBuilders.boolQuery();
        }
        final String field = searchCondition.getField();
        final Integer customType = searchCondition.getCustomType();
        final List<String> valueList = searchCondition.getValueList();
        // 日期类型
        if (customType == 1) {
            final RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(field);
            if (StringUtils.isNotEmpty(valueList.get(0))) {
                rangeQueryBuilder.gte(valueList.get(0));
            }
            if (valueList.size() > 1 && StringUtils.isNotEmpty(valueList.get(1))) {
                rangeQueryBuilder.lte(valueList.get(1));
            }
            return rangeQueryBuilder;
        }
        if (customType == 3) {
            final BoolQueryBuilder boolQuery = new BoolQueryBuilder();
            // TODO 级联查询范围
            return boolQuery;
        }
        if (customType == 0) {
            return QueryBuilders.termsQuery(field, valueList);
        }
        final ActualFieldAndType keywordFieldTypeByMapping = getFieldTypeByMapping(EsIndexEnum.PATENT_VECTOR.getEsIndex(), field);
        final String type = keywordFieldTypeByMapping.getType();
        if (Objects.equals("text", type)) {
            // 如果是text类型，那么使用matchQuery，keyword类型使用wildcardQuery
            List<Term> terms = HanLP.segment(valueList.get(0));
            BoolQueryBuilder multiQueryBuilder = QueryBuilders.boolQuery();
            for (Term term : terms) {
                if (isChinesePunctuation(term.word)){
                    continue;
                }
                multiQueryBuilder.filter(QueryBuilders.matchPhraseQuery(field, term.word));
            }
            return multiQueryBuilder;
        } else {
            return QueryBuilders.wildcardQuery(field, "*" + valueList.get(0) + "*");
        }
    }

    private ActualFieldAndType getFieldTypeByMapping(final String index, final String field) {
        final ActualFieldAndType actualFieldAndType = new ActualFieldAndType();
        actualFieldAndType.setField(field);
        final Map<String, MappingMetadata> mappings = ElasticsearchHelper.getMappings();
        final MappingMetadata mappingMetaData = mappings.get(index);
        if (mappingMetaData != null) {
            final Map<String, Object> sourceAsMap = mappingMetaData.getSourceAsMap();
            Map<String, Object> indexMapping = (HashMap) sourceAsMap.get("properties");
            String[] fieldParts = field.split("\\.");
            for (int i = 0; i < fieldParts.length; i++) {
                String fieldPart = fieldParts[i];
                if (!indexMapping.containsKey(fieldPart)) {
                    throw new BusinessException("此字段" + field + "在这个索引" + index + "中不存在！");
                }
                Map<String, Object> fieldMap = (Map<String, Object>) indexMapping.get(fieldPart);
                if (i + 1 >= fieldParts.length) {
                    actualFieldAndType.setType((String) fieldMap.get("type"));
                    break;
                }
                if (fieldMap.containsKey("fields")) {
                    indexMapping = (Map<String, Object>) fieldMap.get("fields");
                } else {
                    indexMapping = (Map<String, Object>) fieldMap.get("properties");
                }

            }
        } else {
            actualFieldAndType.setType(StringUtils.EMPTY);
        }
        return actualFieldAndType;
    }
}
