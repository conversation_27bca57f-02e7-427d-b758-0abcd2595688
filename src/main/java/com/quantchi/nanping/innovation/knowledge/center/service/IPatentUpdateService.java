package com.quantchi.nanping.innovation.knowledge.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentUpdateRecord;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:48
 */
public interface IPatentUpdateService extends IService<PatentUpdateRecord> {

    /**
     * 按照专利id获取
     *
     * @param id
     * @return
     */
    PatentUpdateRecord getById(String id);
}
