package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/20 下午5:45
 */
@Data
@ApiModel(description = "专利价值分布")
@NoArgsConstructor
@AllArgsConstructor
public class PatentValueDistribution {
    @ApiModelProperty(value = "0-10万的专利数量")
    private Long range0To10Million;

    @ApiModelProperty(value = "10-50万的专利数量")
    private Long range10To50Million;

    @ApiModelProperty(value = "50-100万的专利数量")
    private Long range50To100Million;

    @ApiModelProperty(value = "100-500万的专利数量")
    private Long range100To500Million;

    @ApiModelProperty(value = "500万以上的专利数量")
    private Long rangeAbove500Million;
}
