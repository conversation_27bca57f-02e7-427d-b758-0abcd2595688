package com.quantchi.nanping.innovation.knowledge.center.utils;

import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.SearchHit;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * es查询结果处理工具
 *
 * <AUTHOR>
 * @date 2022/4/19 15:11
 */
public class EsResultUtil {

    public static List getListFromSearchResponse(SearchResponse searchResponse) {
        List list = new ArrayList();
        for (SearchHit hit : searchResponse.getHits()) {
            list.add(hit.getSourceAsMap());
        }
        return list;
    }

    public static Map<String, Object> getFirstFromSearchResponse(SearchResponse searchResponse) {
        SearchHit[] hits = searchResponse.getHits().getHits();
        if (hits == null || hits.length == 0) {
            return new HashMap<>(0);
        }
        return hits[0].getSourceAsMap();
    }
}
