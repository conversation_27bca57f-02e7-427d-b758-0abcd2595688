package com.quantchi.nanping.innovation.knowledge.center.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * es权重配置
 * <AUTHOR>
 * @date 2022/4/20 14:15
 */
@Data
public class EsBoostProperties {

    List<FieldBoostProperty> boostPropertyList;

    public List<String> getFieldListByIndex(String index){
        List<FieldBoostProperty> subFields = new ArrayList<>();
        for (FieldBoostProperty fieldBoostProperty: boostPropertyList){
            if (fieldBoostProperty.getField().equals(index)){
                subFields = fieldBoostProperty.getFieldBoostPropertyList();
            }
        }
        return subFields.stream().map(FieldBoostProperty::getField).collect(Collectors.toList());
    }

    public List<FieldBoostProperty> getFieldBoostListByIndex(String index){
        List<FieldBoostProperty> subFields = new ArrayList<>();
        for (FieldBoostProperty fieldBoostProperty: boostPropertyList){
            if (fieldBoostProperty.getField().equals(index)){
                subFields = fieldBoostProperty.getFieldBoostPropertyList();
            }
        }
        return subFields;
    }

}
