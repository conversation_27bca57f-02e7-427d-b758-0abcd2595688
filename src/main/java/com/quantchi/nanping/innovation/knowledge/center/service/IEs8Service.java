package com.quantchi.nanping.innovation.knowledge.center.service;

import com.alibaba.fastjson.JSONObject;
import com.quantchi.tianying.model.AggregationPageResult;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/8 9:38
 */
public interface IEs8Service {

    /**
     * 查询
     *
     * @param sourceBuilder
     * @param index
     * @param vectorColumn
     * @param keyword
     * @return
     */
    SearchResponse request(SearchSourceBuilder sourceBuilder, String index, String vectorColumn, String keyword);

    /**
     * 获取
     *
     * @param index
     * @param id
     * @param includes
     * @param excludes
     * @return
     */
    Map<String, Object> getDataById(String index, String id, String[] includes, String[] excludes);

    /**
     * 聚合统计
     *
     * @param index
     * @param boolQueryBuilder
     * @param aggregationBuilder
     * @return
     */
    JSONObject getBucketsAggregationPageResult(String index, BoolQueryBuilder boolQueryBuilder, AggregationBuilder aggregationBuilder);

    /**
     * 计数
     *
     * @param sourceBuilder
     * @param index
     * @return
     */
    Long countRequest(SearchSourceBuilder sourceBuilder, String index);

}
