package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import com.quantchi.nanping.innovation.knowledge.center.model.entity.CompanyTechnicalInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/14 上午11:12
 */
@Data
@ApiModel("科创能级评价")
@NoArgsConstructor
@AllArgsConstructor
public class StInnovationLevel {

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "科创评分")
    private Integer evaluationScore;

    @ApiModelProperty(value = "综合评价")
    private String overallEvaluation;

    @ApiModelProperty(value = "细分维度分析")
    private List<CompanyTechnicalDimension> dimensionDetails;

    @ApiModelProperty(value = "排名信息")
    private CompanyTechnicalInfo ranking;

}

