package com.quantchi.nanping.innovation.knowledge.center.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.knowledge.center.dao.PatentUpdateRecordMapper;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentUpdateRecord;
import com.quantchi.nanping.innovation.knowledge.center.service.IPatentUpdateService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:48
 */
@Service
public class PatentUpdateServiceImpl extends ServiceImpl<PatentUpdateRecordMapper, PatentUpdateRecord> implements IPatentUpdateService {
    @Override
    public PatentUpdateRecord getById(String id) {
        PatentUpdateRecord record = this.getOne(Wrappers.lambdaQuery(PatentUpdateRecord.class)
                .eq(PatentUpdateRecord::getPatentId, id)
                .orderByDesc(PatentUpdateRecord::getUpdateTime)
                .last("limit 1"));
        if (record == null){
            record = new PatentUpdateRecord();
            record.setPatentId(id);
            record.setUpdateStatus(-1);
        }
        return record;
    }
}
