package com.quantchi.nanping.innovation.knowledge.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.PatentDimension;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.*;

import java.io.IOException;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
public interface IPatentDimensionService extends IService<PatentDimension> {

    PatentValueEvaluationVO patentValueEvaluation(
            String id, String chainEnName);

    /**
     * 专利价值评价-总体评价-整体评价结果
     * @param id
     * @return
     */
    PatentValueOverallEvaluationVO evaluationResult(String id);

    /**
     * 专利价值评价-总体评价-评价结果对比
     * @param id
     * @return
     */
    PatentValueOverallEvaluationResultCompareVO evaluationResultCompare(String id) throws IOException;

    /**
     * 专利价值评价-总体评价-预估市场价值
     * @param id
     * @return
     */
    PatentValueOverallEvaluationMarketValueVO evaluationMarketValue(String id);

    /**
     * 专利价值评价-总体评价-维度评价
     * @param id
     * @return
     */
    PatentValueDimensionEvaluationVO evaluationDimension(String id);
}
