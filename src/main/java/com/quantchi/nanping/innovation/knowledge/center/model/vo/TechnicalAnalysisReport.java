package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(description = "技术布局分析")
//@NoArgsConstructor
//@AllArgsConstructor
public class TechnicalAnalysisReport {

//    @ApiModelProperty(value = "技术领域布局（关键词云）")
//    private List<NameCountVO> technicalKeywordCloud;
//
//    @ApiModelProperty(value = "技术领域布局-技术专注领域")
//    private List<String> coreTechnologyFocus;
//
//    @ApiModelProperty(value = "重点技术路线")
//    private List<TechEvolutionPath> techEvolutionPathList;
//
//    @ApiModelProperty(value = "近几年的技术领域布局（主题词）")
//    private List<String> recentTechnicalLayout;
//
//    @ApiModelProperty(value = "重点技术趋势")
//    private List<TechnologyTrend> coreTechnologyTrendAnalysis;
//
//    @ApiModelProperty(value = "PCT申请量")
//    private Long pctApplicationNum = 0L;
//
//    @ApiModelProperty(value = "在指定有效期的PCT申请量")
//    private Long validPctApplicationNum = 0L;
//
//    @ApiModelProperty("布局国家/地区数量")
//    private Long layoutCountriesNum = 0L;
//
//    @ApiModelProperty("专利数量最多的国家/地区")
//    private String mostPatentCountry;
//
//    @ApiModelProperty("是否在拓展全球化技术布局")
//    private boolean isExpandGlobalTechnologyLayout;
//
//    @ApiModelProperty(value = "技术领域布局图")
//    private List<NameCountWithLngAndLatVO> technologyLayout;
//
//    @ApiModelProperty(value = "技术影响力分析")
//    private TechImpactAnalysis techImpactAnalysis;
//
//    @ApiModelProperty("研发可持续性分析")
//    private RnDSustainabilityAnalysis rnDSustainabilityAnalysis;
//
//    @ApiModelProperty("研发团队稳定性")
//    private RnDTeamStructure rnDTeamStructure;
//
//    @Data
//    @ApiModel(description = "技术趋势")
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class TechnologyTrend {
//
//        @ApiModelProperty(value = "技术领域")
//        private String name;
//
//        @ApiModelProperty(value = "技术趋势")
//        private List<NameCountVO> industryTrendList;
//
//    }
//
//    @Data
//    @ApiModel("重点技术演进路线")
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class TechEvolutionPath {
//        @ApiModelProperty("时间点")
//        private String timePoint;
//
//        @ApiModelProperty("技术演进路线")
//        private List<String> techEvolutions;
//
//    }
//
//    @Data
//    @ApiModel("技术影响力分析")
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class TechImpactAnalysis {
//        @ApiModelProperty("技术影响力指数")
//        private String impactFactor;
//
//        @ApiModelProperty("平均被引次数")
//        private String avgCitationCount;
//
//        @ApiModelProperty("较行业均值比例")
//        private String exceedRatio;
//
//        @ApiModelProperty("核心专利被引次数")
//        private Integer corePatentCitationCount;
//
//        @ApiModelProperty("所有专利被引次数")
//        private Integer allPatentCitationCount;
//
//        @ApiModelProperty("专利对外许可数量")
//        private Integer patentPermitCount;
//
//        @ApiModelProperty("引用次数最多专利前5")
//        private List<PatentInfo> top5CitedPatents;
//
//        @ApiModelProperty("引用次数最多企业前5")
//        private List<CompanyCitation> top5CitedCompanies;
//    }
//
//    @Data
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class PatentInfo {
//
//        @ApiModelProperty("专利公开号")
//        private String publicCode;
//
//        @ApiModelProperty("专利名称")
//        private String patentName;
//
//        @ApiModelProperty("被引用次数")
//        private int citationCount;
//
//        @ApiModelProperty("预计到期日")
//        private String expiryDate;
//    }
//
//    @Data
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class CompanyCitation {
//        @ApiModelProperty("公司名称")
//        private String companyName;
//
//        @ApiModelProperty("引用次数")
//        private int citationCount;
//    }
//
//    @Data
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class RnDSustainabilityAnalysis {
//
//        @ApiModelProperty("公司成立年份")
//        private String companyEstablishYear;
//
//        @ApiModelProperty("最早专利申请年份")
//        private String earliestPatentYear;
//
//        @ApiModelProperty("最长连续申请年数")
//        private int longestConsecutivePatentYear;
//
//        @ApiModelProperty("最多专利申请量的年份")
//        private String mostPatentYear;
//
//        @ApiModelProperty("最多专利申请量的年份的申请量")
//        private int mostPatentYearCount;
//
//        @ApiModelProperty("最早发明授权年份")
//        private String earliestInventorYear;
//
//        @ApiModelProperty("最长连续发明授权年数")
//        private int longestConsecutiveInventorYear;
//
//        @ApiModelProperty("最多发明授权量的年份")
//        private String mostInventorYear;
//
//        @ApiModelProperty("最多发明授权量的年份的授权量")
//        private int mostInventorYearCount;
//
//        @ApiModelProperty("近10年专发明专利复合增速")
//        private double patentApplyYearlySpeed;
//
//        @ApiModelProperty("专利申请年份数据")
//        private List<NameCountVO> patentApplyYearData;
//
//        @ApiModelProperty("发明授权年份数据")
//        private List<NameCountVO> inventorApplyYearData;
//
//        @ApiModelProperty("专利申请年化增速年份数据")
//        private List<NameCountBigDecimalVO> patentApplyYearlyData;
//    }
//
//    @Data
//    @ApiModel("研发团队稳定性")
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class RnDTeamStructure {
//
//        @ApiModelProperty("发明人数量")
//        private Long inventorCount;
//
//        @ApiModelProperty("发明数量最多的发明人")
//        private String inventorWithMostPatents;
//
//        @ApiModelProperty("发明数量最多的发明人的发明数量")
//        private int inventorWithMostPatentsCount;
//
//        @ApiModelProperty("发明人人均活跃时间")
//        private BigDecimal inventorAverageActiveTime;
//
//        @ApiModelProperty("研发团队稳定性指数")
//        private String stabilityIndex;
//
//        @ApiModelProperty("较行业均值")
//        private double exceedRatio;
//
//        @ApiModelProperty("发明人均专利年研发量")
//        private double patentYearlyRnD;
//
//        @ApiModelProperty("人均研发活跃时间")
//        private double inventorActiveTime;
//
//        @ApiModelProperty("人均专利年研发量")
//        private String inventorAveragePatentCount;
//
//        @ApiModelProperty("发明人在近5年的发明数量统计")
//        private List<NameCountMultiVO> inventorYearCount;
//    }


}

