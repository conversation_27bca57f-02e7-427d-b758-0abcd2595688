package com.quantchi.nanping.innovation.knowledge.center.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/19 10:50
 */
@Data
@TableName("company_patent_relation")
@ApiModel(value = "CompanyPatentRelation对象", description = "")
public class CompanyPatentRelation extends BaseTime {

    @ApiModelProperty("主键id")
    @TableId
    private Integer relationId;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("专利id")
    private String patentId;

    @ApiModelProperty("公开号")
    private String publicCode;

    @ApiModelProperty("简单同族")
    private String simpleFamily;

    @ApiModelProperty("专利类型")
    private String patentType;
}
