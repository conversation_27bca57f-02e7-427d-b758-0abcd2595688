package com.quantchi.nanping.innovation.knowledge.center.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.quantchi.nanping.innovation.knowledge.center.utils.serializer.BigDecimalNoDecimalPlaceSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "技术质量分析")
@NoArgsConstructor
@AllArgsConstructor
public class PatentValueAnalysis {

    @ApiModelProperty(value = "总专利价值")
    @JsonSerialize(using = BigDecimalNoDecimalPlaceSerializer.class)
    private BigDecimal totalPatentValue;

    @ApiModelProperty(value = "总专利价值单位")
    private String totalPatentValueUnit = "万元";

    @ApiModelProperty(value = "平均专利价值")
    @JsonSerialize(using = BigDecimalNoDecimalPlaceSerializer.class)
    private BigDecimal averagePatentValue;

    @ApiModelProperty(value = "平均专利价值单位")
    private String averagePatentValueUnit = "万元";

    @ApiModelProperty("占比最高的发明类型")
    private String mostInventType;

    @ApiModelProperty(value = "占比最高的发明类型占比")
    //@JsonSerialize(using = BigDecimalNoDecimalPlaceSerializer.class)
    private BigDecimal mostInventTypePercentile;

    @ApiModelProperty(value = "发明专利占比")
    //@JsonSerialize(using = BigDecimalNoDecimalPlaceSerializer.class)
    private BigDecimal inventionPatentRatio;

    @ApiModelProperty(value = "发明专利件数")
    private Integer inventionPatentNum;

    @ApiModelProperty(value = "专利价值分布")
    private PatentValueDistribution patentValueDistribution;

    @ApiModelProperty(value = "前五核心专利列表")
    private List<CorePatent> top5CorePatents;

    @Data
    @ApiModel(description = "核心专利")
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorePatent {

        @ApiModelProperty(value = "专利id")
        private String patentId;

        @ApiModelProperty(value = "专利名称")
        private String patentName;

        @ApiModelProperty(value = "预测价值（单位：万元）")
        private String predictedValue;

        @ApiModelProperty(value = "专利类型")
        private String patentType;

        @ApiModelProperty(value = "法律状态")
        private String legalStatus;

        @ApiModelProperty(value = "公开号")
        private String publicationNumber;

        @ApiModelProperty(value = "公开日期")
        private String publicDate;

        @ApiModelProperty(value = "IPC分类")
        private String ipcClassification;

        @ApiModelProperty(value = "发明人")
        private List<String> inventors;
    }
}

