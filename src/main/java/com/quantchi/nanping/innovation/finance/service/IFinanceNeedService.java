package com.quantchi.nanping.innovation.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.finance.model.FinanceNeed;
import com.quantchi.nanping.innovation.service.IFileService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 15:50
 */
public interface IFinanceNeedService extends IService<FinanceNeed> {
    /**
     * 按产业链查询
     *
     * @param chainId
     * @return
     */
    List<FinanceNeed> listByChainId(String chainId);
}
