package com.quantchi.nanping.innovation.finance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/20 13:15
 */
@Data
@TableName("finance_product")
public class FinanceProduct extends BaseTime {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("银行名称")
    private String bank;

    @ApiModelProperty("最高额度")
    private Integer maxAmount;

    @ApiModelProperty("最高期限")
    private Integer maxDeadline;

    @ApiModelProperty("最低利率")
    private BigDecimal interestRate;

    @ApiModelProperty("融资方式")
    private String method;

    @ApiModelProperty("产业链id")
    private String chainId;

}
