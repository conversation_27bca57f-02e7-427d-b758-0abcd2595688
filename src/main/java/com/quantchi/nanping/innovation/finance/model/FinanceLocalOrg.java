package com.quantchi.nanping.innovation.finance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/20 13:22
 */
@Data
@TableName("finance_local_org")
public class FinanceLocalOrg extends BaseTime {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("机构名称")
    private String name;

    @ApiModelProperty("机构简称")
    private String shortName;

    @ApiModelProperty("管理规模")
    private BigDecimal scale;

    @ApiModelProperty("注册地")
    private String registration;
}
