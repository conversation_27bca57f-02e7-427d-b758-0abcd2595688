package com.quantchi.nanping.innovation.finance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/20 13:22
 */
@Data
@TableName("finance_loan")
public class FinanceLoan extends BaseTime {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("贷款品种")
    private String loanType;

    @ApiModelProperty("户数")
    private Integer loanNum;

    @ApiModelProperty("金额（亿元）")
    private BigDecimal loanAmount;
}
