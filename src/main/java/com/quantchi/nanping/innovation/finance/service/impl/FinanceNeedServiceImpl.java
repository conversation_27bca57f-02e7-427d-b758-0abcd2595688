package com.quantchi.nanping.innovation.finance.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.finance.dao.FinanceNeedMapper;
import com.quantchi.nanping.innovation.finance.model.FinanceNeed;
import com.quantchi.nanping.innovation.finance.service.IFinanceNeedService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 15:50
 */
@Service
public class FinanceNeedServiceImpl extends ServiceImpl<FinanceNeedMapper, FinanceNeed> implements IFinanceNeedService {
    @Override
    public List<FinanceNeed> listByChainId(String chainId) {
        return this.list(Wrappers.lambdaQuery(FinanceNeed.class)
                .eq(FinanceNeed::getChainId, chainId));
    }
}
