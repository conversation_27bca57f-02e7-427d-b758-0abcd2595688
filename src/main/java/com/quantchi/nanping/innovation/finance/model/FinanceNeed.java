package com.quantchi.nanping.innovation.finance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/12 13:40
 */
@Data
@TableName("finance_need")
public class FinanceNeed  extends BaseTime {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("融资类型")
    private String financeType;

    @ApiModelProperty("需求金额")
    private BigDecimal amount;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("对接状态")
    @TableField(exist = false)
    private String status;
}
