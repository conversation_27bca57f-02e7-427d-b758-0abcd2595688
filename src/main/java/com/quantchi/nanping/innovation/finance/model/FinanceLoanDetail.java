package com.quantchi.nanping.innovation.finance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.quantchi.nanping.innovation.demand.util.EncryptTypeHandler;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/20 13:22
 */
@Data
@ApiModel("贷款详情")
@TableName(value = "finance_loan_detail", autoResultMap = true)
public class FinanceLoanDetail extends BaseTime {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("一级节点id")
    private String chainNodeId;

    @ApiModelProperty("一级节点名称")
    private String chainNodeName;

    @ApiModelProperty("贷款对象")
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String creditObject;

    @ApiModelProperty("贷款类别")
    private String loanType;

    @ApiModelProperty("金额（万元）")
    private BigDecimal loanAmount;

    @ApiModelProperty("贷款年份")
    private Integer loanYear;

    @ApiModelProperty("贷款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date loanDate;

    @ApiModelProperty("来源需求跟进id")
    private String demandProcessId;
}
