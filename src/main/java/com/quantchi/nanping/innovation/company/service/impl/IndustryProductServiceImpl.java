package com.quantchi.nanping.innovation.company.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.company.dao.IndustryProductDAO;
import com.quantchi.nanping.innovation.company.model.IndustryProduct;
import com.quantchi.nanping.innovation.company.model.IndustryProductPrice;
import com.quantchi.nanping.innovation.company.model.bo.ProductBO;
import com.quantchi.nanping.innovation.company.service.IIndustryProductPriceService;
import com.quantchi.nanping.innovation.company.service.IIndustryProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/5 17:39
 */
@Service
public class IndustryProductServiceImpl extends ServiceImpl<IndustryProductDAO, IndustryProduct> implements IIndustryProductService {

    @Autowired
    private IIndustryProductPriceService priceService;

    @Override
    public Map<String, List<String>> getProductGroupByChain() {
        List<IndustryProduct> productList = this.list();
        return productList.stream().collect(Collectors.groupingBy(IndustryProduct::getChainId,
                Collectors.mapping(IndustryProduct::getProductName, Collectors.toList())));
    }

    @Override
    public Page<IndustryProduct> page(ProductBO productBO) {
        Page<IndustryProduct> pageResult = this.page(new Page<>(productBO.getPageNum(), productBO.getPageSize()), Wrappers.lambdaQuery(IndustryProduct.class)
                .like(StringUtils.isNotEmpty(productBO.getProductName()), IndustryProduct::getProductName, productBO.getProductName())
                .in(CollectionUtils.isNotEmpty(productBO.getChainIds()), IndustryProduct::getChainId, productBO.getChainIds()));
        List<IndustryProduct> productList = pageResult.getRecords();
        if (CollectionUtils.isEmpty(productList)) {
            return pageResult;
        }
        // 查询最近两天的价格
        for (IndustryProduct product : productList) {
            List<IndustryProductPrice> priceList = priceService.list(Wrappers.lambdaQuery(IndustryProductPrice.class)
                    .in(IndustryProductPrice::getProductName, product.getProductName())
                    .orderByDesc(IndustryProductPrice::getCreateTime)
                    .last("limit 2"));
            product.setPrice(priceList.get(0).getPrice());
            product.setCreateTime(priceList.get(0).getCreateTime());
            if (priceList.size() == 2){
                product.setLastPrice(priceList.get(1).getPrice());
                product.setLastPriceDate(priceList.get(1).getCreateTime());
            }
            // 计算价格变化幅度
            if (product.getPrice() != null && product.getLastPrice() != null) {
                product.setPriceChange(product.getPrice().subtract(product.getLastPrice())
                        .multiply(new BigDecimal(100)).divide(product.getLastPrice(), 2, RoundingMode.HALF_UP));
            }
        }
        return pageResult;
    }
}
