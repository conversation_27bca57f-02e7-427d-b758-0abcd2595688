package com.quantchi.nanping.innovation.company.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/26 14:10
 */
@ApiModel("产业分析报告")
@Data
@TableName("industry_report")
public class IndustryReport extends BaseTime {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("所属产业链id")
    private String chainId;

    @ApiModelProperty("所属产业链名称")
    private String chainName;

    @ApiModelProperty("文件id")
    private String fileId;

    @ApiModelProperty("子段落")
    @TableField(exist = false)
    private List<IndustryReportParagraph> subParagraphs;

}
