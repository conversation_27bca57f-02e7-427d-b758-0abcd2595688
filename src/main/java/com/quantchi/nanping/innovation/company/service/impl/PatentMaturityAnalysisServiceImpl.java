package com.quantchi.nanping.innovation.company.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.company.dao.PatentMaturityAnalysisDAO;
import com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis;
import com.quantchi.nanping.innovation.company.service.IPatentMaturityAnalysisService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/24 17:24
 */
@Service
public class PatentMaturityAnalysisServiceImpl extends ServiceImpl<PatentMaturityAnalysisDAO, PatentMaturityAnalysis> implements IPatentMaturityAnalysisService {

    @Autowired
    private IndustryChainService industryChainService;

    @Override
    public Map<String, JSONObject> listRecent10Years(String chainId, String nodeId) {
        if (StringUtils.isEmpty(nodeId)) {
            nodeId = industryChainService.getParentNodeIdByChainId(chainId);
        }
        List<PatentMaturityAnalysis> resultList = this.list(Wrappers.lambdaQuery(PatentMaturityAnalysis.class)
                .eq(PatentMaturityAnalysis::getChainId, chainId)
                .eq(PatentMaturityAnalysis::getNodeId, nodeId)
                .orderByDesc(PatentMaturityAnalysis::getYear)
                .last("limit 10"));
        Map<String, JSONObject> resultMap = new LinkedHashMap<>();
        for (int i = resultList.size() - 1; i >=0; i--) {
            JSONObject analysisObj = new JSONObject();
            PatentMaturityAnalysis analysis = resultList.get(i);
            analysisObj.put("rateA", analysis.getRateA());
            analysisObj.put("rateN", analysis.getRateN());
            analysisObj.put("rateP", analysis.getRateP());
            analysisObj.put("rateV", analysis.getRateV());
            resultMap.put(String.valueOf(resultList.get(i).getYear()), analysisObj);
        }
        return resultMap;
    }
}
