package com.quantchi.nanping.innovation.company.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.company.model.IndustryProduct;
import com.quantchi.nanping.innovation.company.model.IndustryProductPrice;
import com.quantchi.nanping.innovation.company.model.bo.ProductBO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/20 16:33
 */
public interface IIndustryProductPriceService extends IService<IndustryProductPrice> {
//    /**
//     * 按产业链的产品分组情况
//     *
//     * @return
//     */
//    Map<String, List<String>> getProductGroupByChain();
//
//    /**
//     * 获取产品价格变动趋势
//     *
//     * @param chainId
//     * @param productName
//     * @return
//     */
//    Map<String, Map<String, String>> getProductPriceTend(String chainId, String productName);
//
//    /**
//     * 获取最大值
//     *
//     * @param chainId
//     * @param productName
//     * @return
//     */
//    String getMaxPrice(String chainId, String productName);
//
//    /**
//     * 获取最小值
//     *
//     * @param chainId
//     * @param productName
//     * @return
//     */
//    String getMinPrice(String chainId, String productName);
}
