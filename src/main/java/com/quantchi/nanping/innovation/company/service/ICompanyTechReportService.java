package com.quantchi.nanping.innovation.company.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/24 10:15
 */
public interface ICompanyTechReportService {
    /**
     * 查询目标类型的技术分析报告
     *
     * @param chainId
     * @param nodeId
     * @param tabId
     * @return
     */
    Map<String, Object> getReport(String chainId, String nodeId, Integer tabId);

    /**
     * 查询产业链的分析报告图片base64
     *
     * @param chainId
     * @return
     */
    List<String> getH5Report(String chainId);
}
