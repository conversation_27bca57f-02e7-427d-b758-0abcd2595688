package com.quantchi.nanping.innovation.company.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/5 17:15
 */
@ApiModel("服务侧看产业产品")
@Data
@TableName("industry_product")
public class IndustryProduct{

    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("产业链名称")
    private String chainName;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("计价单位")
    private String pricingUnit;

    @ApiModelProperty("价格")
    @TableField(exist = false)
    private BigDecimal price;

    @ApiModelProperty("涨跌幅")
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal priceChange;

    @ApiModelProperty("上次价格")
    @TableField(exist = false)
    private BigDecimal lastPrice;

    @ApiModelProperty("上次价格更新时间")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastPriceDate;

    @ApiModelProperty("更新状态")
    @TableField(exist = false)
    private String updateStatus;

}
