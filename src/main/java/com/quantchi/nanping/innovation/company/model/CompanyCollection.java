package com.quantchi.nanping.innovation.company.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/12 16:31
 */
@ApiModel("企业收藏")
@Data
@TableName("company_collection")
public class CompanyCollection extends BaseTime {

    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("企业社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("收藏种类 company：企业 expert：人才 patent：专利 news：资讯 policy：政策")
    private String collectType;

    @ApiModelProperty("收藏实体id")
    private String collectId;

    @ApiModelProperty("收藏实体标题")
    private String collectTitle;

    @ApiModelProperty("是否失效，用于标识es实体数据被删除")
    @TableField(exist = false)
    private boolean valid;

    @ApiModelProperty("详细信息")
    @TableField(exist = false)
    private Map<String, Object> detail;
}
