package com.quantchi.nanping.innovation.company.model.bo;

import com.quantchi.nanping.innovation.company.model.PatentIpcEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PatentIpcBO extends PatentIpcEntity implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "技术分类数量")
    private Long num;

}
