package com.quantchi.nanping.innovation.company.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.company.model.IndustryReport;
import com.quantchi.nanping.innovation.company.model.IndustryReportParagraph;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/26 15:19
 */
public interface IIndustryReportService extends IService<IndustryReport> {

    /**
     * 报告列表
     *
     * @return
     */
    List<IndustryReport> listReport();

    /**
     * 报告段落详情
     *
     * @param paragraphId
     * @return
     */
    IndustryReportParagraph getParagraph(String paragraphId);
}
