package com.quantchi.nanping.innovation.company.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.type.MapType;
import com.quantchi.nanping.innovation.demand.util.EncryptTypeHandler;
import com.quantchi.nanping.innovation.model.BaseTime;
import com.quantchi.nanping.innovation.utils.MapTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/11 11:07
 */
@ApiModel("贷款测评")
@Data
@TableName(value = "loan_evaluation", autoResultMap = true)
public class LoanEvaluation extends BaseTime {

    @TableId(type = IdType.AUTO)
    private Integer evaluationId;

    @ApiModelProperty("企业名称")
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String companyName;

    @ApiModelProperty("企业信用代码")
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String socialCreditCode;

    @ApiModelProperty("测评类别")
    @NotBlank
    private String evaluationType;

    @ApiModelProperty("测评内容")
    @TableField(typeHandler = MapTypeHandler.class)
    @NotNull
    private Map<String, Object> content;

    @ApiModelProperty("测评得分")
    @NotNull
    private Integer score;
}
