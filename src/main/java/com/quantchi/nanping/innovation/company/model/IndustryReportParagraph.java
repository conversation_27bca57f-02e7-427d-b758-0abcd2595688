package com.quantchi.nanping.innovation.company.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/26 14:10
 */
@ApiModel("产业分析报告段落")
@Data
@TableName("industry_report_paragraph")
public class IndustryReportParagraph extends BaseTime {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("产业报告id")
    private String reportId;

    @ApiModelProperty("起始页码")
    private Integer pageNum;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("父段落id")
    private String parentId;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("子段落")
    @TableField(exist = false)
    private List<IndustryReportParagraph> subParagraphs;
}
