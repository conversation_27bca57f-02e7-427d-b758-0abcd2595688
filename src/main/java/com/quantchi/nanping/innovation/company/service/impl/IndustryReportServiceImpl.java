package com.quantchi.nanping.innovation.company.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.company.dao.IndustryReportDAO;
import com.quantchi.nanping.innovation.company.dao.IndustryReportParagraphDAO;
import com.quantchi.nanping.innovation.company.model.IndustryReport;
import com.quantchi.nanping.innovation.company.model.IndustryReportParagraph;
import com.quantchi.nanping.innovation.company.service.IIndustryReportService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/26 15:19
 */
@Service
public class IndustryReportServiceImpl extends ServiceImpl<IndustryReportDAO, IndustryReport> implements IIndustryReportService {

    @Autowired
    private IndustryReportParagraphDAO paragraphDAO;

    @Override
    public List<IndustryReport> listReport() {
        List<IndustryReport> reports = this.list(Wrappers.lambdaQuery(IndustryReport.class).orderByAsc(IndustryReport::getChainId));
        Map<String, IndustryReport> reportMap = reports.stream().collect(Collectors.toMap(IndustryReport::getId, Function.identity()));
        List<IndustryReportParagraph> paragraphs = paragraphDAO.selectList(Wrappers.lambdaQuery(IndustryReportParagraph.class)
                .in(IndustryReportParagraph::getReportId, reportMap.keySet())
                .isNull(IndustryReportParagraph::getParentId)
                .orderByAsc(IndustryReportParagraph::getSort));
        for (IndustryReportParagraph paragraph: paragraphs){
            List<IndustryReportParagraph> subParagraphs = reportMap.get(paragraph.getReportId()).getSubParagraphs();
            if (subParagraphs == null){
                subParagraphs = new ArrayList<>();
                subParagraphs.add(paragraph);
            }else{
                subParagraphs.add(paragraph);
            }
            reportMap.get(paragraph.getReportId()).setSubParagraphs(subParagraphs);
        }
        return reports;
    }

    @Override
    public IndustryReportParagraph getParagraph(String paragraphId) {
        List<IndustryReportParagraph> paragraphs = paragraphDAO.selectList(Wrappers.lambdaQuery(IndustryReportParagraph.class)
                .likeRight(IndustryReportParagraph::getId, paragraphId)
                .orderByAsc(IndustryReportParagraph::getSort));
        Map<String, IndustryReportParagraph> paragraphMap = paragraphs.stream().collect(Collectors.toMap(IndustryReportParagraph::getId, Function.identity()));
        IndustryReportParagraph root = null;
        for (IndustryReportParagraph paragraph: paragraphs){
            if (StringUtils.isEmpty(paragraph.getParentId())){
                root = paragraph;
                continue;
            }
            IndustryReportParagraph parent = paragraphMap.get(paragraph.getParentId());
            if (parent.getSubParagraphs() == null){
                parent.setSubParagraphs(new ArrayList<>());
            }
            parent.getSubParagraphs().add(paragraph);
        }
        return root;
    }
}
