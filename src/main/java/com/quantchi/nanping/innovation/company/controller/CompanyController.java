package com.quantchi.nanping.innovation.company.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.common.core.domain.ResultInfo;
import com.quantchi.common.core.utils.StringUtils;
import com.quantchi.nanping.innovation.auth.aop.CheckThirdAccess;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.company.model.IndustryReport;
import com.quantchi.nanping.innovation.company.model.IndustryReportParagraph;
import com.quantchi.nanping.innovation.company.model.LoanEvaluation;
import com.quantchi.nanping.innovation.company.model.bo.EntityRelateNodeBO;
import com.quantchi.nanping.innovation.company.service.*;
import com.quantchi.nanping.innovation.config.aop.*;
import com.quantchi.nanping.innovation.demand.condition.ThirdSyn;
import com.quantchi.nanping.innovation.demand.model.Demand;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.demand.model.enums.DemandTypeEnum;
import com.quantchi.nanping.innovation.demand.model.vo.DemandStatistics;
import com.quantchi.nanping.innovation.demand.service.IDemandMngService;
import com.quantchi.nanping.innovation.model.enums.BusinessType;
import com.quantchi.nanping.innovation.service.IFileService;
import com.quantchi.nanping.innovation.insight.model.bo.ExpertPageBo;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.model.bo.NewsPageBo;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/6/12 10:45
 */
@RestController
@Api(tags = "服务侧(企业用户)")
@RequestMapping("/company")
@Validated
@PlatformAuthCheck(type = {"1"})
@Metrics
public class CompanyController {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private ICompanyNodeRelationService companyNodeRelationService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private ICompanyTechReportService techReportService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private IDemandMngService demandMngService;

    @Autowired
    private ICompanyCollectionService collectionService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IFileService fileService;

    @Autowired
    private IIndustryReportService reportService;

    @Autowired
    private ILoanEvaluationService loanEvaluationService;

    @PostMapping("/get/news/list")
    @ApiOperation("获取资讯列表")
    @PlatformAuthCheck()
    @AntiReptile
    //@Log(title = "服务侧-获取资讯列表")
    public Result getNewsList(@Validated @RequestBody NewsPageBo newsPageBo) {
        return ResultConvert.success(companyService.getNewsList(newsPageBo));
    }

    @PostMapping("/get/expert/list")
    @ApiOperation("获取专家列表")
    @AntiReptile
    //@Log(title = "服务侧-获取专家列表")
    public Result getNewsList(@Validated @RequestBody ExpertPageBo expertPageBo) {
        return ResultConvert.success(talentService.page(expertPageBo));
    }

    @GetMapping("/get/company/info")
    @ApiOperation("获取企业详情")
    //@Log(title = "服务侧-获取企业详情")
    public Result getCompanyInfo() {
        UserInfoEntity currentUser = sysLoginService.getUserInfo(false);
        String creditCode = currentUser.getSocialCreditCode();
        Map<String, Object> info = companyService.getCompanyInfoByCreditCode(creditCode);
        info.put("name", currentUser.getAccount());
        info.put("collect_num", collectionService.countBySocialCode(creditCode));
        return ResultConvert.success(info);
    }

    @GetMapping("/get/company/index")
    @ApiOperation("获取企业需求等指标")
    //@Log(title = "服务侧-获取企业需求等指标")
    public Result getCompanyIndex() throws Exception {
        Map<String, Object> statisticsMap = new HashMap<>();
        String creditCode = sysLoginService.getUserInfo(false).getSocialCreditCode();
        DemandStatistics demandStatistics = demandMngService.getStatistics(creditCode);
        statisticsMap.putAll(BeanUtil.beanToMap(demandStatistics));
        Long collectNum = collectionService.countBySocialCode(creditCode);
        statisticsMap.put("collectNum", collectNum);
        return ResultConvert.success(statisticsMap);
    }

    @ApiOperation("查询企业挂接节点树")
    @GetMapping("/tree")
    @PlatformAuthCheck()
    @ApiImplicitParam(name = "companyId", value = "企业id", required = true, dataType = "String")
    //@Log(title = "服务侧-获取企业需求等指标")
    public Result<List<IndustryChainNode>> getTreeByCompanyId(@NonNull String companyId, boolean onlyRelated) {
        return ResultConvert.success(companyNodeRelationService.getTreeByEntityId(companyId, true, false, onlyRelated));
    }

    @ApiOperation("保存企业挂接节点树")
    @PostMapping("/tree/save")
    @RepeatSubmit
    @ApiImplicitParam(name = "companyId", value = "企业id", required = true, dataType = "String")
    @Log(title = "服务侧-保存企业挂接节点树", businessType = BusinessType.INSERT)
    public Result<Boolean> getTreeByCompanyId(@Validated @RequestBody EntityRelateNodeBO param) {
        return ResultConvert.success(companyNodeRelationService.save(param.getCompanyId(), param.getChainId(), param.getNodeIds()));
    }

    @ApiOperation("技术分析报告")
    @GetMapping("/report")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "chainId", value = "产业链id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "nodeId", value = "节点id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "tabId", value = "标签页id", required = true, dataType = "Integer")
    })
    @Log(title = "企业门户")
    @AntiReptile
    public Result<Map<String, Object>> getReportByCompanyId(@NotBlank String chainId, String nodeId, @NotNull Integer tabId) {
        return ResultConvert.success(techReportService.getReport(chainId, nodeId, tabId));
    }

    @ApiOperation("产业分析报告")
    @GetMapping("/report/h5")
    @PlatformAuthCheck()
    //@Log(title = "服务侧H5-产业分析报告")
    public Result<List<String>> getH5Report(@NotBlank String chainId) {
        return ResultConvert.success(techReportService.getH5Report(chainId));
    }

    @ApiOperation("技术分析报告节点筛选树,也用于企业门户需求填报节点筛选")
    @GetMapping("/report/tree")
    @PlatformAuthCheck()
    //@Log(title = "服务侧-技术分析报告节点筛选树")
    public Result<List<IndustryChainNode>> getSelectedTree() {
        return ResultConvert.success(industryChainService.getSelectedTreeIncludesTwoLevel(null));
    }

    @ApiOperation("收藏")
    @GetMapping("/collect")
    @RepeatSubmit
    @Log(title = "服务侧-收藏", businessType = BusinessType.INSERT)
    public Result collect(@NotBlank String dataId, @NotBlank String type) {
        return ResultConvert.success(collectionService.collect(dataId, type));
    }

    @ApiOperation("取消收藏")
    @GetMapping("/collect/remove")
    @RepeatSubmit
    @Log(title = "服务侧-取消收藏", businessType = BusinessType.DELETE)
    public Result removeFromCollection(String dataId) {
        return ResultConvert.success(collectionService.remove(dataId));
    }

    @ApiOperation("收藏统计")
    @GetMapping("/collect/statistics")
    @Log(title = "企业首页")
    public Result getCollectionStatistics() {
        return ResultConvert.success(collectionService.getCollectionStatistics());
    }

    @ApiOperation("收藏列表")
    @GetMapping("/collect/list")
    //@Log(title = "服务侧-收藏列表")
    public Result getCollectionStatistics(String keyword, @NotBlank String type, @NotNull Integer pageSize, @NotNull Integer pageNum) {
        return ResultConvert.success(collectionService.getCollections(type, keyword, pageSize, pageNum));
    }

    @ApiOperation("我想要联系")
    @GetMapping("/contact")
    @RepeatSubmit
    @Log(title = "服务侧-我想要联系")
    public Result contact(String expertId, String techId) {
        if (StringUtils.isEmpty(expertId) && StringUtils.isEmpty(techId)) {
            throw new BusinessException("缺少主体ID");
        }
        Map<String, Object> demandPreInfo = new HashMap<>();
        boolean contactExpert = StringUtils.isEmpty(techId);
        String title = null, content = null;
        if (contactExpert) {
            // 获得专家信息
            Map<String, Object> expertInfo = elasticsearchHelper.getDataById(EsIndexEnum.EXPERT.getEsIndex(), expertId, null, null);
            String orgName = (String) expertInfo.get("org");
            String domainName = (String) ((List<Map<String, Object>>) expertInfo.get("chain")).get(0).get("name");
            String expertName = (String) expertInfo.get("name");
            // 拼装需求提交信息
            title = "联系人才";
            content = "想与" + orgName + domainName + expertName + "专家联系";
        } else {
            Map<String, Object> techInfo = elasticsearchHelper.getDataById(EsIndexEnum.BOTTLENECK.getEsIndex(), techId, null, null);
            String techName = (String) techInfo.get("name");
            title = "技术需求";
            content = techName;
        }
        // 获得企业信息
        String creditCode = sysLoginService.getUserInfo(false).getSocialCreditCode();
//        String creditCode = "91350781MA349EGX5X";
        Map<String, Object> companyInfo = companyService.findCompanyByCreditCode(creditCode);
        String companyId = (String) companyInfo.get("id");
        if (StringUtils.isEmpty(companyId)) {
            // 非链上企业
            companyId = creditCode;
        }
        List<IndustryChainNode> nodeTree = companyNodeRelationService.getTreeByEntityId(companyId, true, false, true);
        Demand demand = new Demand();
        demand.setCompanyId(companyId);
        demand.setUserId(creditCode);
        demand.setUserName((String) companyInfo.get("name"));
        demand.setTitle(title);
        demand.setContent(content);
        demand.setType(contactExpert ? DemandTypeEnum.TALENT.getName() : DemandTypeEnum.TECH.getName());
        IndustryChainNode defaultNode = findFirstLeafNode(nodeTree.get(0));
        demand.setChainId(defaultNode.getChainId());
        demand.setChainName(industryChainService.getChainNameById(demand.getChainId()));
        demand.setChainNodeId(defaultNode.getId());
        demand.setChainNodeName(defaultNode.getName());
        demandPreInfo.put("demand", demand);
        demandPreInfo.put("tree", nodeTree);
        return ResultConvert.success(demandPreInfo);
    }

    @ApiOperation(value = "产业分析报告下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, dataType = "String")
    })
    @GetMapping("/report/download")
    @PlatformAuthCheck()
    @Log(title = "服务侧-产业分析报告下载", businessType = BusinessType.EXPORT)
    public void download(@RequestParam @NotBlank String fileId, HttpServletResponse response) {
        FileInfo fileInfo = fileService.getById(fileId);
        if (!"report".equals(fileInfo.getRelatedType())) {
            throw new BusinessException("无权访问该文件");
        }
        fileService.download(fileId, false, response);
    }

    @ApiOperation(value = "产业分析报告列表")
    @GetMapping("/report/list")
    @PlatformAuthCheck()
    @AntiReptile
    public Result<List<IndustryReport>> listReport(String entityId, @RequestParam(defaultValue = "0", required = false) int userType) {
        List<IndustryReport> reportList = reportService.listReport();
        if (StpUtil.isLogin() && org.apache.commons.lang3.StringUtils.isNotEmpty(entityId)) {
            // 查询企业所在的链
            Pair<Set<String>, Set<String>> chainInfo = companyNodeRelationService.getRelatedNodeIdsByEntityId(entityId,
                    UserInfoEntity.USER_COMPANY == userType, UserInfoEntity.USER_EXPERT == userType);
            Set<String> chainIds = chainInfo.getLeft();
            if (CollectionUtils.isNotEmpty(chainIds)) {
                reportList.removeIf(r -> !chainIds.contains(r.getChainId()));
            }
        }
        return ResultConvert.success(reportList);
    }

    @ApiOperation(value = "产业分析报告段落")
    @GetMapping("/report/paragraph")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "paragraphId", value = "段落id", required = true, dataType = "String")
    })
    @PlatformAuthCheck()
    @AntiReptile
    //@Log(title = "服务侧-产业分析报告段落")
    public Result<IndustryReportParagraph> getParagraph(@RequestParam @NotBlank String paragraphId) {
        return ResultConvert.success(reportService.getParagraph(paragraphId));
    }

    @ApiOperation(value = "贷款额度评测")
    @PostMapping("/loan/evaluation")
    @PlatformAuthCheck()
    @Log(title = "服务侧-贷款额度评测", businessType = BusinessType.INSERT)
    public Result evaluateLoan(@RequestBody LoanEvaluation loanEvaluation) {
        UserInfoEntity userInfo = sysLoginService.getUserInfo(false);
        if (UserInfoEntity.USER_COMPANY != userInfo.getUserType()) {
            throw new BusinessException("评测仅对企业用户开放");
        }
        loanEvaluation.setCompanyName(userInfo.getUsername());
        loanEvaluation.setCompanyName(userInfo.getSocialCreditCode());
        return ResultConvert.success(loanEvaluationService.add(loanEvaluation));
    }

    @ApiOperation(value = "企呼我应-节点选择")
    @GetMapping("/node")
    @RateLimiter
    @Log(title = "企呼我应-节点选择", businessType = BusinessType.OTHER)
    @CheckThirdAccess
    public ResultInfo<List<IndustryChainNode>> getNode(@NotBlank String socialCode) {
        Map<String, Object> company = companyService.findCompanyByCreditCode(socialCode);
        if (company == null || !company.containsKey("chain") || CollectionUtils.isEmpty((List<Map<String, Object>>)company.get("chain"))){
            return com.quantchi.common.core.utils.ResultConvert.success(industryChainService.listAllTrees());
        }
        return com.quantchi.common.core.utils.ResultConvert.success(companyNodeRelationService.getTreeByEntityId((String)company.get("id"), true, false, true));
    }

    private IndustryChainNode findFirstLeafNode(IndustryChainNode root) {
        if (CollectionUtils.isEmpty(root.getChildren())) {
            return root;
        }
        IndustryChainNode firstLeafNode = null;
        for (IndustryChainNode node : root.getChildren()) {
            if (CollectionUtils.isEmpty(node.getChildren())) {
                firstLeafNode = node;
            } else {
                firstLeafNode = findFirstLeafNode(node);
            }
            if (firstLeafNode != null) {
                break;
            }
        }
        return firstLeafNode;
    }

}
