package com.quantchi.nanping.innovation.company.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.company.model.PatentMaturityAnalysis;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/24 17:24
 */
public interface IPatentMaturityAnalysisService extends IService<PatentMaturityAnalysis> {

    /**
     * 查询近十年的成熟度
     *
     * @param chainId
     * @param nodeId
     * @return
     */
    Map<String, JSONObject> listRecent10Years(String chainId, String nodeId);
}
