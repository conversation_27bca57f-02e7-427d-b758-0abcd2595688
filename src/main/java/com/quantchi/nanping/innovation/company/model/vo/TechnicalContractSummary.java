package com.quantchi.nanping.innovation.company.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.quantchi.nanping.innovation.model.bo.TwoLevelIndex;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 技术成果合同统计
 *
 * <AUTHOR>
 * @date 2024/3/14 11:15
 */
@Data
public class TechnicalContractSummary {

    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal totalAmount;

    private Integer totalCount;

    private List<TwoLevelIndex> indexList;
}
