package com.quantchi.nanping.innovation.company.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.company.model.CompanyCollection;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/12 16:36
 */
public interface ICompanyCollectionService extends IService<CompanyCollection> {
    /**
     * 计算企业收藏数
     *
     * @param socialCode
     * @return
     */
    Long countBySocialCode(String socialCode);

    /**
     * 收藏
     *
     * @param dataId
     * @param type
     * @return
     */
    boolean collect(String dataId, String type);

    /**
     * 收藏类型统计
     *
     * @return
     */
    Map<String, Long> getCollectionStatistics();

    /**
     * 取消收藏
     *
     * @param dataId
     * @return
     */
    boolean remove(String dataId);

    /**
     * 收藏列表
     *
     * @param type
     * @param keyword
     * @param pageSize
     * @param pageNum
     * @return
     */
    Page<Map<String, Object>> getCollections(String type, String keyword, Integer pageSize, Integer pageNum);

    /**
     * 获得当前登录用户收藏列表
     *
     * @param socialCode
     * @param type
     * @return
     */
    List<CompanyCollection> listByType(String socialCode, String type);

    /**
     * 关键字搜索
     *
     * @param keyword
     * @param type
     * @param pageSize
     * @param pageNum
     * @return
     */
    @Deprecated
    Page<Map<String, Object>> search(String keyword, String type, Integer pageSize, Integer pageNum);
}
