package com.quantchi.nanping.innovation.company.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.imageaudit20191230.models.ScanTextRequest;
import com.aliyun.imageaudit20191230.models.ScanTextResponse;
import com.aliyun.imageaudit20191230.models.ScanTextResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.tea.TeaModel;
import com.quantchi.common.core.exception.base.BusinessException;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.exception.SensitiveException;
import com.quantchi.nanping.innovation.company.model.NpChatMessage;
import com.quantchi.nanping.innovation.company.service.IModelChatService;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import com.zhipu.oapi.ClientV4;
import com.zhipu.oapi.Constants;
import com.zhipu.oapi.service.v4.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024/7/8 11:25
 */
@Service
@Slf4j
public class ModelChatServiceImpl implements IModelChatService {

    /**
     * 历史对话保存轮数
     */
    private final static int HISTORY_CHAT_ROUNDS = 10;

    /**
     * 对话角色
     */
    private final static String ROLE_USER = "user";
    private final static String ROLE_ASSISTANT = "assistant";
    /**
     * 输入内容长度限制
     */
    private final static Integer MESSAGE_MAX_LENGTH = 1000;

    /**
     * 历史消息redis存放前缀
     */
    private final static String HISTORY_REDIS_KEY = "chat:history:";
    /**
     * 当前会话锁
     */
    private final static String CURRENT_REDIS_KEY = "chat:input:";
    private final static String CURRENT_REDIS_VALUE = "ON";
    /**
     * 业务id
     */
    private static final String REQUEST_ID_TEMPLATE = "nplc-%s";

    /**
     * 回答标志
     */
    private static final String ANSWER_YES = "YES";

    /**
     * 客户端
     */
    private static ClientV4 CLIENT = null;

    /**
     * 示例问题
     */
    private static final List<String> EXAMPLE_QUESTION = Arrays.asList("我要找关于竹条锯切的专利技术","我要找关于鸡肉深加工的技术","我要找茶叶品种改良方面的专家","查找福建农林大学林下经济的专家");

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private StringRedisCache redisCache;

    @Value("${chat.apiSecretKey}")
    private String apiSecretKey;

    @Autowired
    private com.aliyun.imageaudit20191230.Client aliyunAuditClient;

    @PostConstruct
    private void init() {
        CLIENT = new ClientV4.Builder(apiSecretKey).build();
    }

    @Override
    public void send(String chatKey, String chatId, List<ChatMessage> messages, SseEmitter emitter) {
        StringBuilder output = new StringBuilder();
        final String requestId = String.format(REQUEST_ID_TEMPLATE, chatId);
        final ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(Constants.ModelChatGLM4)
                .stream(Boolean.TRUE)
                .invokeMethod(Constants.invokeMethod)
                .messages(messages)
                .requestId(requestId)
                .toolChoice("auto")
                .temperature(0.5f)
                .build();
        taskExecutor.execute(() -> {
            ModelApiResponse sseModelApiResp = CLIENT.invokeModelApi(chatCompletionRequest);
            if (sseModelApiResp.isSuccess()) {
                sseModelApiResp.getFlowable()
                        .doOnNext(chunk -> {
                            {
                                if ("stop".equalsIgnoreCase(chunk.getChoices().get(0).getFinishReason()) && output.toString().contains("暂时无法理解")) {
                                    chunk.getChoices().get(0).setFinishReason("no_analysis");
                                }
                                if ("stop".equalsIgnoreCase(chunk.getChoices().get(0).getFinishReason()) && output.toString().contains("包含违法信息")) {
                                    chunk.getChoices().get(0).setFinishReason("sensitive");
                                }
                                // 如果判断为敏感信息
                                if ("sensitive".equalsIgnoreCase(chunk.getChoices().get(0).getFinishReason())) {
                                    // 强制清除用户消息并记录操作日志
                                    clearMessages(chatId);
                                }
                                try {
                                    emitter.send(chunk.getChoices().get(0));
                                    output.append(chunk.getChoices().get(0).getDelta().getContent());
                                } catch (IOException e) {
                                    emitter.completeWithError(e);
                                    redisCache.unLock(chatKey, CURRENT_REDIS_VALUE);
                                }
                            }
                        })
                        .doOnComplete(() -> {
                            // 追加本轮助手输出
                            ChatMessage assistantMessage = new ChatMessage(ChatMessageRole.ASSISTANT.value(), output.toString());
                            addLatestMessage(assistantMessage, messages);
                            // 保存截至目前的消息
                            saveMessages(chatId, messages);
                            emitter.complete();
                        })
                        .lastElement()
                        .blockingGet();
            }
            redisCache.unLock(chatKey, CURRENT_REDIS_VALUE);
        });
    }

    @Override
    public String send(String chatKey, String chatId, List<ChatMessage> messages) {
        final String requestId = String.format(REQUEST_ID_TEMPLATE, chatId);
        final ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(Constants.ModelChatGLM4)
                .stream(Boolean.FALSE)
                .invokeMethod(Constants.invokeMethod)
                .messages(messages)
                .requestId(requestId)
//                .tools(Arrays.asList(buildPatentSearchParseTool()))
                .toolChoice("auto")
                .temperature(0.01f)
                .topP(0.1f)
                .build();
        ModelApiResponse sseModelApiResp = CLIENT.invokeModelApi(chatCompletionRequest);
        String message = null;
        if (sseModelApiResp.isSuccess()) {
//            JsonNode resultNode = sseModelApiResp.getData().getChoices().get(0).getMessage().getTool_calls().get(0).getFunction().getArguments();
            message = (String) sseModelApiResp.getData().getChoices().get(0).getMessage().getContent();
//            // 追加本轮助手输出
//            ChatMessage assistantMessage = new ChatMessage(ChatMessageRole.ASSISTANT.value(), message);
//            addLatestMessage(assistantMessage, messages);
//            // 保存截至目前的消息
//            saveMessages(chatId, messages);
        } else {
            if (1301 == sseModelApiResp.getData().getError().getCode()) {
                log.error("用户id：{}输入涉及敏感内容：{}", StpUtil.getLoginIdAsString(), messages.get(messages.size() - 1).getContent());
                throw new SensitiveException("用户输入涉及敏感内容");
            }
        }
        if (StringUtils.isNotEmpty(message) && message.contains("暂时无法理解")) {
            message = null;
        }
        if (StringUtils.isNotEmpty(chatKey)){
            redisCache.unLock(chatKey, CURRENT_REDIS_VALUE);
        }
        return message;
    }

    /**
     * 专利关键词抽取函数
     *
     * @return
     */
    private ChatTool buildPatentSearchParseTool() {
        // sseModelApiResp.getData().getChoices().get(0).getMessage().getTool_calls().get(0).getFunction().getArguments()
        ChatTool tool = new ChatTool();
        tool.setType("function");
        ChatFunctionParameters parameters = new ChatFunctionParameters("object", "{\n" +
                "          \"keywords\": {\n" +
                "            \"type\": \"string\",\n" +
                "            \"description\": \"专利检索关键词或延伸关键词，例如：重组竹\"\n" +
                "          },\n" +
                "          \"applicants\": {\n" +
                "            \"type\": \"string\",\n" +
                "            \"description\": \"申请人或机构名称，如果是简称转换成完整标准名称，例如：东北农业大学，中国科学院\"\n" +
                "          }\n" +
                "        }", Arrays.asList("name", "org"));
        tool.setFunction(new ChatFunction("get_patent_search_parse", "获取专利检索输入中的关键信息", parameters, Arrays.asList("name", "org"),
                new Function<Object, Object>() {
                    @Override
                    public Object apply(Object o) {
                        return o;
                    }
                }));
        return tool;
    }

    @Override
    public void checkMessage(ChatMessage message) {
        if (message == null) {
            throw new BusinessException("输入内容不得为空");
        }
        if (((String) message.getContent()).length() >= MESSAGE_MAX_LENGTH) {
            throw new BusinessException("输入内容长度请控制在1000字以内");
        }
    }

    @Override
    public List<ChatMessage> getHistoryMessages(String chatId) {
        String messagesStr = redisCache.get(HISTORY_REDIS_KEY + chatId);
        if (StringUtils.isEmpty(messagesStr)) {
            return new ArrayList<>(0);
        }
        return JSONObject.parseArray(messagesStr, ChatMessage.class);
    }

    @Override
    public void clearMessages(String chatId) {
        String messagesStr = redisCache.get(HISTORY_REDIS_KEY + chatId);
        if (StringUtils.isNotEmpty(messagesStr)) {
            redisCache.remove(HISTORY_REDIS_KEY + chatId);
        }
    }

    @Override
    public void addLatestMessage(ChatMessage message, List<ChatMessage> historyMessages) {
        // 超出5轮对话则删除最之前的一轮对话
        if (historyMessages.size() >= HISTORY_CHAT_ROUNDS) {
            historyMessages.remove(0);
            historyMessages.remove(0);
        }
        historyMessages.add(message);
    }

    @Override
    public void saveMessages(String chatId, List<ChatMessage> messages) {
        redisCache.put(HISTORY_REDIS_KEY + chatId, JSONObject.toJSONString(messages), 1, TimeUnit.DAYS);
    }

    @Override
    public SseEmitter throwException(SseEmitter emitter, String message) {
        Choice choice = new Choice();
        choice.setFinishReason("error");
        choice.setMessage(new ChatMessage(ROLE_ASSISTANT, message));
        try {
            emitter.send(choice);
        } catch (IOException e) {
            emitter.completeWithError(e);
        }
        emitter.complete();
        return emitter;
    }

    @Override
    public void checkSensitiveWords(String chatKey, String chatId, NpChatMessage currentMessage) {
        List<ChatMessage> messages = new ArrayList<>();
        String originalMessage = currentMessage.getMessage().getContent().toString();
        if (EXAMPLE_QUESTION.contains(originalMessage)){
            return;
        }
        // 增加最新的用户消息
        String currentMessageContent = "你是内容检测助手，如果用户输入违反公序良俗或包含违禁词，则输出“YES”，否则输出“NO”。用户输入：" + originalMessage;
        currentMessage.getMessage().setContent(currentMessageContent);
        messages.add(currentMessage.getMessage());
        String isSensitive = send(chatKey, chatId, messages);
        if (ANSWER_YES.equals(isSensitive)) {
            log.error("用户id：{}输入涉及敏感内容：{}", StpUtil.getLoginIdAsString(), currentMessageContent);
            // 强制清除用户消息并记录操作日志
            clearMessages(chatId);
            throw new SensitiveException("用户输入涉及敏感内容");
        }
        currentMessage.getMessage().setContent(originalMessage);
    }

    @Override
    public boolean checkSensitiveWordsByAliyun(String content) {
        if (EXAMPLE_QUESTION.contains(content)){
            return false;
        }
        ScanTextRequest.ScanTextRequestTasks tasks = new ScanTextRequest.ScanTextRequestTasks()
                .setContent(content);
        List<String> lables = Arrays.asList("spam", "politics", "abuse", "terrorism",
                "porn", "flood", "contraband", "ad");
        List<ScanTextRequest.ScanTextRequestLabels> requestLabels = new ArrayList<>();
        for (String label: lables){
            requestLabels.add(new ScanTextRequest.ScanTextRequestLabels()
                    .setLabel(label));
        }
        com.aliyun.imageaudit20191230.models.ScanTextRequest scanTextRequest = new com.aliyun.imageaudit20191230.models.ScanTextRequest()
                .setLabels(requestLabels)
                .setTasks(java.util.Arrays.asList(
                        tasks
                ));
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印API的返回值
            ScanTextResponse response = aliyunAuditClient.scanTextWithOptions(scanTextRequest, runtime);
            if (200 == response.getStatusCode()){
                List<ScanTextResponseBody.ScanTextResponseBodyDataElementsResults> results = response.getBody().getData().getElements().get(0).getResults();
                for (ScanTextResponseBody.ScanTextResponseBodyDataElementsResults result: results){
                    if ("review".equals(result.getSuggestion())){
                        return false;
                    }
                    if ("block".equals(result.getSuggestion())){
                        return false;
                    }
                }
            }else{
                log.error("阿里云敏感词检测异常，返回：{}",com.aliyun.teautil.Common.toJSONString(TeaModel.buildMap(response)));
                throw new BusinessException("敏感词检测异常");
            }
        } catch (Exception error) {
            log.error("阿里云敏感词检测异常，返回：{}",com.aliyun.teautil.Common.toJSONString(error));
            throw new BusinessException("敏感词检测异常");
        }
        return true;
    }

    @Override
    public boolean checkExpertRelated(String chatKey, String chatId, NpChatMessage currentMessage) {
        List<ChatMessage> messages = new ArrayList<>();
        String originalMessage = currentMessage.getMessage().getContent().toString();
        if (EXAMPLE_QUESTION.contains(originalMessage)){
            return true;
        }
        // 增加最新的用户消息
        String currentMessageContent = "你是一位产业专家。如果用户输入是产业需求或者技术点分析，返回：YES；" +
                "如果不涉及氟新材料、白羽鸡、竹、山、水、茶、ES纤维、物联网电池产业相关技术，返回：NO。用户输入：" + currentMessage.getMessage().getContent().toString();
        currentMessage.getMessage().setContent(currentMessageContent);
        messages.add(currentMessage.getMessage());
        String isSensitive = send(chatKey, chatId, messages);
        currentMessage.getMessage().setContent(originalMessage);
        return ANSWER_YES.equals(isSensitive);
    }

}
