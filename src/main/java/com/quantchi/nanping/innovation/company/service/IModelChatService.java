package com.quantchi.nanping.innovation.company.service;

import com.quantchi.nanping.innovation.company.model.NpChatMessage;
import com.zhipu.oapi.service.v4.model.ChatMessage;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * 大模型问答服务
 *
 * <AUTHOR>
 * @date 2024/7/8 11:25
 */
public interface IModelChatService {

    /**
     * 发送对话(以流的形式返回)
     *
     * @param chatKey
     * @param chatId
     * @param messages
     * @param emitter
     */
    void send(String chatKey, String chatId, List<ChatMessage> messages, SseEmitter emitter);

    /**
     * 发送对话(一次性返回)
     *
     * @param chatKey
     * @param chatId
     * @param messages
     * @return
     */
    String send(String chatKey, String chatId, List<ChatMessage> messages);

    /**
     * 校验输入的合法性
     *
     * @param message
     */
    void checkMessage(ChatMessage message);

    /**
     * 获取历史会话消息
     *
     * @return
     */
    List<ChatMessage> getHistoryMessages(String chatId);

    /**
     * 清除会话历史消息
     *
     * @param chatId
     */
    void clearMessages(String chatId);

    /**
     * 增加最新的一条消息
     *
     * @param message
     * @param historyMessages
     */
    void addLatestMessage(ChatMessage message, List<ChatMessage> historyMessages);

    /**
     * 保存消息
     *
     * @param chatId
     * @param messages
     * @return
     */
    void saveMessages(String chatId, List<ChatMessage> messages);

    /**
     * 推送异常信息
     *
     * @param emitter
     * @param message
     * @return
     */
    SseEmitter throwException(SseEmitter emitter, String message);

    /**
     * 检测敏感输入
     *
     * @param chatKey
     * @param chatId
     * @param currentMessage
     */
    void checkSensitiveWords(String chatKey, String chatId, NpChatMessage currentMessage);

    /**
     * (阿里云)检测敏感输入
     *
     * @param content
     */
    boolean checkSensitiveWordsByAliyun(String content);

    /**
     * 确认是否是人才需求
     *
     * @param chatKey
     * @param chatId
     * @param currentMessage
     */
    boolean checkExpertRelated(String chatKey, String chatId, NpChatMessage currentMessage);
}
