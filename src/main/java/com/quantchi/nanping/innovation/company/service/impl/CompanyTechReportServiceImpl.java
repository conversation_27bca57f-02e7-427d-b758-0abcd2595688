package com.quantchi.nanping.innovation.company.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.nanping.innovation.company.dao.PatentIpcDAO;
import com.quantchi.nanping.innovation.company.model.PatentIpcEntity;
import com.quantchi.nanping.innovation.company.model.bo.PatentIpcBO;
import com.quantchi.nanping.innovation.company.model.bo.PatentTrendBO;
import com.quantchi.nanping.innovation.company.model.enums.EntTechReportTabEnum;
import com.quantchi.nanping.innovation.company.service.ICompanyTechReportService;
import com.quantchi.nanping.innovation.company.service.IPatentMaturityAnalysisService;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.IFileService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.metrics.ValueCount;
import org.elasticsearch.search.aggregations.metrics.ValueCountAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/3/24 10:15
 */
@Service
@Slf4j
public class CompanyTechReportServiceImpl implements ICompanyTechReportService {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private ThreadPoolTaskExecutor companyExecutor;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private PatentIpcDAO patentIpcDAO;

    @Autowired
    private IPatentMaturityAnalysisService patentMaturityAnalysisService;

    @Autowired
    private IFileService fileService;

    @Autowired
    private StringRedisCache redisCache;

    @Override
    public Map<String, Object> getReport(String chainId, String nodeId, Integer tabId) {
        Map<String, Object> resultMap = new HashMap<>();
        EntTechReportTabEnum currentTab = EntTechReportTabEnum.findById(tabId);
        final BoolQueryBuilder boolQueryBuilder = buildChainIdQuery(chainId, nodeId);
        CompletableFuture<Void> allOf = null;
        switch (currentTab) {
            case OVERVIEW:
                allOf = getOverview(boolQueryBuilder, resultMap);
                break;
            case REGIONAL:
                allOf = analyzeRegional(chainId, nodeId, resultMap);
                break;
            case KEY_PATENTS:
                allOf = analyzeKeyPatents(boolQueryBuilder, resultMap);
                break;
            case INNOVATION_WORD_CLOUD:
                allOf = getWordCloud(chainId, boolQueryBuilder, resultMap);
                break;
            case TECHNICAL_THEME:
                allOf = analyzeTechTheme(boolQueryBuilder, resultMap);
                break;
            case TECHNICAL_MATURITY:
                allOf = getTechnicalMaturity(chainId, nodeId, resultMap);
                break;
            default:
                allOf = CompletableFuture.allOf();
        }
        try {
            allOf.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }

    @Override
    public List<String> getH5Report(String chainId) {
        List<FileInfo> fileInfoList = fileService.list(Wrappers.lambdaQuery(FileInfo.class).eq(FileInfo::getRelatedType, FileInfo.RELATED_TYPE_H5_REPORT)
                .likeRight(FileInfo::getFileId, chainId)
                .orderByAsc(FileInfo::getFileId));
        return fileInfoList.stream().map(FileInfo::getDownloadUrl).collect(Collectors.toList());
    }

    private CompletableFuture<Void> getOverview(BoolQueryBuilder boolQueryBuilder, Map<String, Object> resultMap) {
        //补充专利趋势
        final CompletableFuture<Void> patentTendFuture = CompletableFuture.runAsync(() -> {
            resultMap.put("patentTend", getPatentTrend(boolQueryBuilder));
        }, companyExecutor);
        //补充法律状态
        final CompletableFuture<Void> legalStatusFuture = CompletableFuture.runAsync(() -> {
            resultMap.put("legalStatus", getFieldAggregation(boolQueryBuilder, "status"));
        }, companyExecutor);
        //补充专利类型
        final CompletableFuture<Void> patentTypeFuture = CompletableFuture.runAsync(() -> {
            resultMap.put("patentType", getFieldAggregation(boolQueryBuilder, "patent_type"));
        }, companyExecutor);
        //补充技术生命周期
        final CompletableFuture<Void> techPeriodFuture = CompletableFuture.runAsync(() -> {
            resultMap.put("techPeriod", getPatentTrendByParams(boolQueryBuilder, "apply_date", null, "inventors.name", null));
        }, companyExecutor);
        return CompletableFuture.allOf(patentTendFuture, legalStatusFuture, patentTypeFuture, techPeriodFuture);
    }

    private CompletableFuture<Void> analyzeRegional(String chainId, String nodeId, Map<String, Object> resultMap) {
        //补充技术来源国/地区排名
        final CompletableFuture<Void> sourceRankFuture = CompletableFuture.runAsync(() -> {
            final BoolQueryBuilder boolQueryBuilder = buildChainIdQuery(chainId, nodeId);
            Map<String, Long> sourceRankMap = getFieldAggregation(boolQueryBuilder, "nation");
            resultMap.put("sourceRank", sourceRankMap);
            List<String> nationWordsList = new LinkedList<>();
            for (Map.Entry<String, Long> entry : sourceRankMap.entrySet()) {
                nationWordsList.add(entry.getKey());
            }
            //补充技术来源国/地区趋势分析
            resultMap.put("sourceTend", getPatentTrendByParams(boolQueryBuilder, "apply_date", "nation", null, nationWordsList));

        }, companyExecutor);
        //各省申请排名
        final CompletableFuture<Void> provinceApplyRankFuture = CompletableFuture.runAsync(() -> {
            final BoolQueryBuilder boolQueryBuilder = buildChainIdQuery(chainId, nodeId);
            Map<String, Long> provinceApplyRankMap = getFieldAggregation(boolQueryBuilder, "province.name");
            resultMap.put("provinceApplyRank", provinceApplyRankMap);
            List<String> provinceWordsList = new LinkedList<>();
            for (Map.Entry<String, Long> entry : provinceApplyRankMap.entrySet()) {
                provinceWordsList.add(entry.getKey());
            }
            //补充各省申请趋势
            resultMap.put("provinceApplyTend", getPatentTrendByParams(boolQueryBuilder, "apply_date", "province.name", null, provinceWordsList));

        }, companyExecutor);
        return CompletableFuture.allOf(sourceRankFuture, provinceApplyRankFuture);
    }

    private CompletableFuture<Void> analyzeTechTheme(BoolQueryBuilder boolQueryBuilder, Map<String, Object> resultMap) {
        //技术构成分析
        final CompletableFuture<Void> techCompositionFuture = CompletableFuture.runAsync(() -> {
            Map<String, Long> techMap = getFieldAggregation(boolQueryBuilder, "ipc_category");

            List<String> techList = new LinkedList<>();
            for (Map.Entry<String, Long> entry : techMap.entrySet()) {
                techList.add(entry.getKey());
            }
            if (CollectionUtils.isEmpty(techList)){
                return;
            }
            //补全技术描述
            List<PatentIpcEntity> ipcList = new ArrayList<>();
            QueryWrapper<PatentIpcEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("ipc_category", techList);
            ipcList = patentIpcDAO.selectList(queryWrapper);
            Map<String, String> descMap = ipcList.stream().collect(Collectors.toMap(PatentIpcEntity::getIpcCategory, PatentIpcEntity::getDes));
            List<PatentIpcBO> techCompositionList = new ArrayList<>();
            for (Map.Entry<String, Long> entry : techMap.entrySet()) {
                PatentIpcBO ipcBo = new PatentIpcBO();
                ipcBo.setIpcCategory(entry.getKey());
                ipcBo.setNum(entry.getValue());
                ipcBo.setDes(descMap.get(entry.getKey()));
                techCompositionList.add(ipcBo);
            }
            resultMap.put("techComposition", techCompositionList);
            //技术分支申请趋势
            resultMap.put("branchApplyTend", getPatentTrendByParams(boolQueryBuilder, "apply_date", "ipc_category", null, techList));
        }, companyExecutor);
        return CompletableFuture.allOf(techCompositionFuture);
    }

    private synchronized Map<String, Long> getFieldAggregation(BoolQueryBuilder boolQueryBuilder, String field) {
        boolQueryBuilder.mustNot(QueryBuilders.termQuery(field, ""));
        //申请
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + field).field(field);
        AggregationPageResult bucketsAggregationPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.GLOBAL_PATENT.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        return parseFieldAggregationFromResponse(field, bucketsAggregationPageResult.getSearchResponse());
    }

    public static Map<String, Long> parseFieldAggregationFromResponse(final String field, final SearchResponse searchResponse) {
        Map<String, Long> resultMap = new LinkedHashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + field);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            if (null != bucket) {
                resultMap.put(bucket.getKeyAsString(), bucket.getDocCount());
            }
        }
        return resultMap;
    }

    private Map<String, PatentTrendBO> getPatentTrend(BoolQueryBuilder boolQueryBuilder) {
        Map<String, PatentTrendBO> resultMap = new LinkedHashMap<>();
        int pastYears = 10;
        //获取近10年数据
        LocalDate localDate = new LocalDate();
        localDate = localDate.minusYears(pastYears);
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("apply_date").gte(localDate.toString("yyyy-MM-dd")));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("publish_year").gte(localDate.toString("yyyy")));
        DateHistogramAggregationBuilder aggregationBuilder;
        AggregationPageResult pageResult;
        //申请
        aggregationBuilder = AggregationBuilders.dateHistogram(TERMS_BUCKET_PREFIX + "year").calendarInterval(DateHistogramInterval.YEAR).format("yyyy").field("apply_date");
        pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.GLOBAL_PATENT.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        Map<String, Long> applyMap = parsePatentTendAggregationFromResponse(pageResult.getSearchResponse());
        //授权
        aggregationBuilder = AggregationBuilders.dateHistogram(TERMS_BUCKET_PREFIX + "year").calendarInterval(DateHistogramInterval.YEAR).format("yyyy").field("public_date");
        pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.GLOBAL_PATENT.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        Map<String, Long> publishMap = parsePatentTendAggregationFromResponse(pageResult.getSearchResponse());

        for (int i = 0; i < pastYears; i++) {
            String theYear = localDate.plusYears(i).toString("yyyy");
            PatentTrendBO trendBo = new PatentTrendBO();
            trendBo.setApplyNum(applyMap.get(theYear));
            trendBo.setPublishNum(publishMap.get(theYear));
            if (null != trendBo.getApplyNum() && null != trendBo.getPublishNum() && 0L != trendBo.getApplyNum()) {
                trendBo.setPublishRate(new BigDecimal(trendBo.getPublishNum()).divide(new BigDecimal(trendBo.getApplyNum()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            }
            resultMap.put(theYear, trendBo);
        }
        return resultMap;
    }

    public static Map<String, Long> parsePatentTendAggregationFromResponse(final SearchResponse searchResponse) {
        Map<String, Long> trendMap = new HashMap<>();
        Histogram histogram = (Histogram) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "year");
        List<? extends Histogram.Bucket> buckets = histogram.getBuckets();
        for (Histogram.Bucket bucket : buckets) {
            trendMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return trendMap;
    }

    private synchronized Map<String, Object> getPatentTrendByParams(BoolQueryBuilder boolQueryBuilder, String yearField, String bucketField, String countField, List<String> includeWords) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        int pastYears = 10;
        //获取近10年数据
        LocalDate localDate = new LocalDate();
        localDate = localDate.minusYears(pastYears);
        if (StringUtils.isNotBlank(bucketField)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery(bucketField, ""));
        }
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("apply_date").gte(localDate.toString("yyyy-MM-dd")));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("publish_year").gte(localDate.toString("yyyy")));

        DateHistogramAggregationBuilder aggregationBuilder;
        AggregationPageResult pageResult;
        aggregationBuilder = AggregationBuilders.dateHistogram(TERMS_BUCKET_PREFIX + yearField).calendarInterval(DateHistogramInterval.YEAR).format("yyyy").field(yearField);
        if (StringUtils.isNotBlank(bucketField)) {
            if (CollectionUtils.isNotEmpty(includeWords)) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery(bucketField, includeWords));
            }
            TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + bucketField).field(bucketField)
                    .includeExclude(new IncludeExclude(includeWords.toArray(new String[includeWords.size()]), null));
            aggregationBuilder.subAggregation(termsAggregationBuilder);
        }
        if (StringUtils.isNotBlank(countField)) {
            ValueCountAggregationBuilder valueCountAggregationBuilder = AggregationBuilders.count(TERMS_BUCKET_PREFIX + countField).field(countField);
            aggregationBuilder.subAggregation(valueCountAggregationBuilder);
        }
        pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.GLOBAL_PATENT.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        Map<String, Object> handleMap = parsePatentTrendByParamsAggregationFromResponse(yearField, bucketField, countField, pageResult.getSearchResponse());

        for (int i = 0; i < pastYears; i++) {
            String theYear = localDate.plusYears(i).toString("yyyy");
            resultMap.put(theYear, handleMap.get(theYear));
        }
        return resultMap;
    }

    public static Map<String, Object> parsePatentTrendByParamsAggregationFromResponse(String yearField, String bucketField, String countField, final SearchResponse searchResponse) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        Histogram histogram = (Histogram) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + yearField);
        List<? extends Histogram.Bucket> buckets = histogram.getBuckets();
        for (Histogram.Bucket bucket : buckets) {//年份
            Map<String, Object> subMap = new LinkedHashMap<>();
            //字段聚合
            if (StringUtils.isNotBlank(bucketField)) {
                Terms subFieldTerms = (Terms) bucket.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + bucketField);
                List<? extends Terms.Bucket> subFieldBuckets = subFieldTerms.getBuckets();
                for (Terms.Bucket subBucket : subFieldBuckets) {
                    subMap.put(subBucket.getKeyAsString(), subBucket.getDocCount());
                }
            }
            // 字段数值统计
            if (StringUtils.isNotBlank(countField)) {
                ValueCount valueCount = (ValueCount) bucket.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + countField);
                subMap.put("countValue", valueCount.getValue());
                subMap.put("docCount", bucket.getDocCount());
            }
            resultMap.put(bucket.getKeyAsString(), subMap);
        }
        return resultMap;
    }

    /**
     * 获取技术分析度
     *
     * @param chainId
     * @param nodeId
     * @param resultMap
     * @return
     */
    private CompletableFuture<Void> getTechnicalMaturity(String chainId, String nodeId, Map<String, Object> resultMap) {
        final CompletableFuture<Void> maturityFuture = CompletableFuture.runAsync(() -> {
            resultMap.put("maturity", patentMaturityAnalysisService.listRecent10Years(chainId, nodeId));
        }, companyExecutor);
        return CompletableFuture.allOf(maturityFuture);
    }

    /**
     * 创新词云
     *
     * @param chainId
     * @param boolQueryBuilder
     * @param resultMap
     * @return
     */
    private CompletableFuture<Void> getWordCloud(String chainId, BoolQueryBuilder boolQueryBuilder, Map<String, Object> resultMap) {
        // 创新词云
        final CompletableFuture<Void> wordCloudFuture = CompletableFuture.runAsync(() -> {
            final String termKey = TERMS_BUCKET_PREFIX + "tag";
            TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(termKey)
                    .field("tag").order(BucketOrder.count(false)).size(1000);
            AggregationPageResult aggregationResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.GLOBAL_PATENT.getEsIndex(),
                    boolQueryBuilder, aggregationBuilder);
            final ParsedStringTerms stringTerms = (ParsedStringTerms) aggregationResult.getSearchResponse().getAggregations().asMap().get(termKey);
            List<CommonIndexBO> wordRank = new ArrayList<>();
            stringTerms.getBuckets().forEach(bucket -> {
                wordRank.add(new CommonIndexBO(bucket.getKeyAsString(), bucket.getDocCount(), null));
            });
            resultMap.put("wordRank", wordRank);
        }, companyExecutor);
        // 旭日图
        final CompletableFuture<Void> graphFuture = CompletableFuture.runAsync(() -> {
            // 罗列所有二级和三级节点
            List<IndustryChainNode> nodeList = industryChainService.getNodesByChainIdAndLevels(chainId, 2, 3);
            Map<String, IndustryChainNode> nodeMap = nodeList.stream().collect(Collectors.toMap(IndustryChainNode::getId, Function.identity()));
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", nodeMap.keySet()));
            final String termKey = TERMS_BUCKET_PREFIX + "chain_node.id";
            TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(termKey).field("chain_node.id")
                    .size(nodeMap.size())
                    .includeExclude(new IncludeExclude(nodeMap.keySet().toArray(new String[nodeMap.size()]), null));
            AggregationPageResult aggregationResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.GLOBAL_PATENT.getEsIndex(),
                    boolQueryBuilder, aggregationBuilder);
            final ParsedStringTerms stringTerms = (ParsedStringTerms) aggregationResult.getSearchResponse().getAggregations().asMap().get(termKey);
            stringTerms.getBuckets().forEach(bucket -> {
                nodeMap.get(bucket.getKeyAsString()).getProperties().put("docCount", bucket.getDocCount());
            });
            resultMap.put("graph", industryChainService.buildTrees(nodeList));
        }, companyExecutor);
        return CompletableFuture.allOf(wordCloudFuture, graphFuture);
    }

    /**
     * 重点专利分析
     *
     * @param boolQueryBuilder
     * @return
     */
    private CompletableFuture<Void> analyzeKeyPatents(BoolQueryBuilder boolQueryBuilder, Map<String, Object> resultMap) {
        final int topNum = 10;
        // 被引用最多的专利(前十)
        final CompletableFuture<Void> topReferencedFuture = CompletableFuture.runAsync(() -> {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.size(topNum);
//            searchSourceBuilder.fetchSource(new String[]{"apply_code", "applicants", "title_norm", "public_date", "ct_times"}, null);
            searchSourceBuilder.sort("ct_times", SortOrder.DESC);
            searchSourceBuilder.query(boolQueryBuilder);
            SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.GLOBAL_PATENT.getEsIndex());
            SearchHits hits = searchResponse.getHits();
            List<Map<String, Object>> topReferencedPatents = new ArrayList<>();
            for (SearchHit hit : hits) {
                topReferencedPatents.add(hit.getSourceAsMap());
            }
            resultMap.put("topReferenced", topReferencedPatents);
        }, companyExecutor);
        // 规模最大的专利家族
        final CompletableFuture<Void> topFamilyFuture = CompletableFuture.runAsync(() -> {
            final String termKey = TERMS_BUCKET_PREFIX + "ipc_category";
            TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(termKey)
                    .field("ipc_category").order(BucketOrder.count(false)).size(topNum)
                    .subAggregation(AggregationBuilders.topHits("top_ct_times").sort("ct_times", SortOrder.DESC).size(1));
            AggregationPageResult aggregationResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.GLOBAL_PATENT.getEsIndex(), boolQueryBuilder, aggregationBuilder);
            final ParsedStringTerms stringTerms = (ParsedStringTerms) aggregationResult.getSearchResponse().getAggregations().asMap().get(termKey);
            List<Map<String, Object>> patentsFamilyRank = new ArrayList<>();
            stringTerms.getBuckets().forEach(bucket -> {
                Map<String, Object> family = new HashMap<>();
                ParsedTopHits parsedTopHits = (ParsedTopHits) bucket.getAggregations().asMap().get("top_ct_times");
                for (SearchHit hit : parsedTopHits.getHits()){
                    family.put("apply_code", hit.getSourceAsMap().get("apply_code"));
                    family.put("applicants", hit.getSourceAsMap().get("applicants"));
                    family.put("title_norm", hit.getSourceAsMap().get("title_norm"));
                    family.put("public_date", hit.getSourceAsMap().get("public_date"));
                }
                family.put("name", bucket.getKeyAsString());
                family.put("data", bucket.getDocCount());
                patentsFamilyRank.add(family);
            });
            resultMap.put("topFamily", patentsFamilyRank);
        }, companyExecutor);
        return CompletableFuture.allOf(topReferencedFuture, topFamilyFuture);
    }

    private BoolQueryBuilder buildChainIdQuery(String chainId, String nodeId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.id", chainId));
        if (StringUtils.isNotEmpty(nodeId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", nodeId));
        }
        return boolQueryBuilder;
    }
}
