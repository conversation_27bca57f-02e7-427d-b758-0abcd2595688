package com.quantchi.nanping.innovation.company.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("patent_ipc")
@ApiModel(value="PatentIpc对象", description="IPC技术分类说明")
public class PatentIpcEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "IPC技术分类")
    private String ipcCategory;

    @ApiModelProperty(value = "技术分类描述")
    private String des;


}
