package com.quantchi.nanping.innovation.company.utils;

import com.quantchi.nanping.innovation.company.model.TechPoint;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 问答技术点分析
 *
 * <AUTHOR>
 * @date 2024/5/30 14:16
 */
public class ChatTechPointUtil {

    /**
     * 技术点id前缀
     */
    private static final String ID_PREFIX = "point_";

    /**
     * 段落标识
     */
    private static final String PARAGRAPH_SPILT = "\\n\\n";

    /**
     * 标题标识
     */
    private static final String TITLE_REGEX = "^#+\\s+(.*)";
    private static final Pattern TITLE_PATTERN = Pattern.compile(TITLE_REGEX);

    /**
     * 分析起始标识
     */
    private static final String ANALYSIS_START_SPACE = "** -";
    private static final String ANALYSIS_START = "**-";

    /**
     * 解析技术点
     *
     * @param content
     * @return
     */
    public static List<TechPoint> analysisTechPoint(String content) {
        String[] paragraphArray = content.split(PARAGRAPH_SPILT);
        if (paragraphArray.length <= 0) {
            return new ArrayList<>(0);
        }
        List<TechPoint> pointList = new ArrayList<>();
        for (int i = 0; i < paragraphArray.length; i++) {
            String point = extractTitle(paragraphArray[i]);
            String pointAnalysis = extractAnalysis(paragraphArray[i]);
            if (StringUtils.isAnyEmpty(point, pointAnalysis)) {
                continue;
            }
            pointList.add(new TechPoint(ID_PREFIX + UUID.randomUUID(), point, pointAnalysis));
        }
        return pointList;
    }

    /**
     * 截取标题
     *
     * @return
     */
    private static String extractTitle(String content) {
        if (content.contains("<br>")) {
            content = content.split("<br>")[0];
        }
        if (!content.startsWith("### ")){
            content = "### " + content;
        }
        Matcher matcher = TITLE_PATTERN.matcher(content);
        while (matcher.find()) {
            String fullTitle = matcher.group(1);
            if (fullTitle.contains("：")) {
                String[] titleArray = fullTitle.split("：");
                String realTitle = titleArray.length > 1 ? titleArray[1] : titleArray[0];
                return realTitle
                        .replace(StringUtils.SPACE, StringUtils.EMPTY);
            } else {
                return fullTitle.replace("###", StringUtils.EMPTY)
                        .replace(StringUtils.SPACE, StringUtils.EMPTY);
            }
        }
        return null;
    }

    /**
     * 截取分析内容
     *
     * @return
     */
    private static String extractAnalysis(String content) {
        String symbol = ANALYSIS_START;
        int startIndex = content.indexOf(symbol);
        if (startIndex < 0) {
            symbol = ANALYSIS_START_SPACE;
            startIndex = content.indexOf(symbol);
            if (startIndex < 0) {
                return null;
            }
        }
        return content.substring(startIndex).replace(symbol, StringUtils.EMPTY)
                .replace(StringUtils.SPACE, StringUtils.EMPTY);
    }

//    public static void main(String[] args){
//        String content = "技术点1：竹笋食品加工技术\n" +
//                "**技术分析：**-竹笋食品加工技术包括原料处理、切割、烹饪和包装等环节。研究重点是如何保持竹笋的营养价值和口感，同时延长产品的保质期。\n" +
//                "\n" +
//                "技术点2：竹笋保鲜技术\n" +
//                "**技术分析：**-竹笋保鲜技术涉及采用低温冷藏、调节气体成分、使用保鲜剂等方法，以减缓竹笋的新陈代谢，延长其保鲜期。\n" +
//                "\n" +
//                "技术点3：食品加工设备优化\n" +
//                "**技术分析：**-针对竹笋加工过程中设备效率低下的问题，需要对现有设备进行优化，提高生产效率和降低能耗。\n" +
//                "\n" +
//                "技术点4：食品安全与质量控制\n" +
//                "**技术分析：**-食品安全和质量控制在竹笋食品加工过程中至关重要。需关注生产过程中的卫生管理、有害物质检测以及产品质量标准制定等方面。";
//        System.out.println(ChatTechPointUtil.analysisTechPoint(content));
//    }
}
