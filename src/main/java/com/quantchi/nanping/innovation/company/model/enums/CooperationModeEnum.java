package com.quantchi.nanping.innovation.company.model.enums;

/**
 * 成果交易合作方式
 *
 * <AUTHOR>
 * @date 2024/3/6 17:54
 */
public enum CooperationModeEnum {

    DEVELOPMENT(0, "技术开发"),
    TRANSFER(1, "技术转让"),
    LICENSE(2, "技术许可"),
    SERVICE(3, "技术服务"),
    CONSULTATION(4, "技术咨询");

    private Integer type;

    private String name;

    CooperationModeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * 按type查找
     *
     * @param type
     * @return
     */
    public static CooperationModeEnum findByType(Integer type) {
        for (CooperationModeEnum modeEnum : CooperationModeEnum.values()) {
            if (modeEnum.type.equals(type)) {
                return modeEnum;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }
}
