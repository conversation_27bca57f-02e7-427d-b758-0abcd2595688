package com.quantchi.nanping.innovation.company.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.company.dao.LoanEvaluationDAO;
import com.quantchi.nanping.innovation.company.model.LoanEvaluation;
import com.quantchi.nanping.innovation.company.service.ILoanEvaluationService;
import com.quantchi.nanping.innovation.utils.AESUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/1/11 13:45
 */
@Service
public class LoanEvaluationServiceImpl extends ServiceImpl<LoanEvaluationDAO, LoanEvaluation> implements ILoanEvaluationService {
    @Override
    public boolean add(LoanEvaluation loanEvaluation) {
        LoanEvaluation evaluationBefore = this.getOne(Wrappers.lambdaQuery(LoanEvaluation.class)
                .eq(LoanEvaluation::getSocialCreditCode, AESUtil.encrypt(loanEvaluation.getSocialCreditCode()))
                .eq(LoanEvaluation::getEvaluationType, loanEvaluation.getEvaluationType()));
        if (evaluationBefore != null){
            evaluationBefore.setContent(loanEvaluation.getContent());
            evaluationBefore.setScore(loanEvaluation.getScore());
            this.updateById(evaluationBefore);
        }else{
            this.save(loanEvaluation);
        }
        return true;
    }
}
