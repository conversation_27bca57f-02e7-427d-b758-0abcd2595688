package com.quantchi.nanping.innovation.company.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/24 16:56
 */
@Data
@ApiModel("专利技术成熟度分析")
@TableName("patent_analyze")
public class PatentMaturityAnalysis {

    @ApiModelProperty("产业链id")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String chainId;

    @ApiModelProperty("节点id")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String nodeId;

    @ApiModelProperty("年份")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Integer year;

    @ApiModelProperty("当年发明授权量/过去五年发明授权量")
    private BigDecimal rateV;

    @ApiModelProperty("当年发明授权量/当年（发明授权 + 实用新型）总量")
    private BigDecimal rateA;

    @ApiModelProperty("(当年发明授权量 + 实用新型)/当年（发明授权 + 实用新型 + 外观设计）总量")
    private BigDecimal rateP;

    @ApiModelProperty("sqrt(v+a),(v+a)的平方根计算")
    private BigDecimal rateN;
}
