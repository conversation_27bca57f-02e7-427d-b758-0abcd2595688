package com.quantchi.nanping.innovation.company.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/23 17:35
 */
@ApiModel("企业/专家节点挂接参数")
@Data
public class EntityRelateNodeBO {

    @ApiModelProperty("企业id")
    //@NotBlank
    private String companyId;

    @ApiModelProperty("专家id")
    //@NotBlank
    private String expertId;

    @ApiModelProperty("产业链id")
    @NotBlank
    private String chainId;

    @ApiModelProperty("挂接节点id")
    private Set<String> nodeIds;
}
