package com.quantchi.nanping.innovation.company.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.common.core.exception.base.BusinessException;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.model.NpChatMessage;
import com.quantchi.nanping.innovation.company.model.TechPoint;
import com.quantchi.nanping.innovation.company.model.bo.PatentVectorMatchBO;
import com.quantchi.nanping.innovation.company.model.bo.PersonMatchBO;
import com.quantchi.nanping.innovation.company.model.bo.StandardVectorMatchBO;
import com.quantchi.nanping.innovation.company.service.IModelChatService;
import com.quantchi.nanping.innovation.company.utils.ChatTechPointUtil;
import com.quantchi.nanping.innovation.component.LocalModelComponent;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.config.aop.RateLimiter;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.ResultCodeEnum;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.service.library.StandardService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.nanping.innovation.utils.*;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import com.zhipu.oapi.service.v4.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/10 17:13
 */
@RestController
@Api(tags = "大模型对话")
@RequestMapping("/chat")
@Validated
@PlatformAuthCheck(type = {"1"})
@Metrics
@Slf4j
public class ChatController {

    @Value("${model.expertMatch}")
    private String expertMatchUrl;

    @Autowired
    private StringRedisCache redisCache;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IModelChatService chatService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private PatentService patentService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private StandardService standardService;

    @Autowired
    private LocalModelComponent localModelComponent;

    @Value("${local.model.chatApi}")
    private String chatApi;

    @Value("${local.model.dsApi}")
    private String dsApi;

    @Value("${local.model.dsToken}")
    private String dsToken;

    @Value("${local.model.preInput.apiKey}")
    private String preInputApiKey;

    @Value("${local.model.expert.apiKey}")
    private String expertApiKey;

    @Value("${local.model.sensitive.apiKey}")
    private String sensitiveApiKey;

    @Value("${local.model.patentExtract.apiKey}")
    private String patentExtractApiKey;

    @Value("${local.model.standard.apiKey}")
    private String standardApiKey;

    /**
     * 当前会话锁
     */
    private final static String CURRENT_REDIS_KEY = "chat:input:";
    private final static String CURRENT_REDIS_VALUE = "ON";

    /**
     * 匹配尝试次数redis存放前缀
     */
    private final static String MATCH_REDIS_KEY = "match:count:";

    /**
     * 匹配尝试次数限制
     */
    private final static Integer MATCH_COUNT_LIMIT = 10;

    /**
     * 技术点存放
     */
    private final static String POINT_REDIS_KEY = "chat:point:";

    //已废弃
    @PostMapping(value = "/expert/input", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("搜人才-智能问答")
    @PlatformAuthCheck()
    @Log(title = "服务侧-搜人才-智能问答")
    @RateLimiter(key = "rate_limit:chat:", count = 20)
    public SseEmitter chat(@Validated @RequestBody @NotNull NpChatMessage currentMessage, HttpServletResponse response) {
        // 设置响应
        response.setHeader("Content-Type", "text/event-stream;charset=utf-8");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Connection", "keep-alive");
        SseEmitter emitter = new SseEmitter();
        if (!StpUtil.isLogin()) {
            return chatService.throwException(emitter, "请重新登录");
        }
        // 校验用户输入长度
        chatService.checkMessage(currentMessage.getMessage());
        // 获取历史对话记录
        String chatId = currentMessage.getChatId();
        // 不允许连续输入
        String chatKey = CURRENT_REDIS_KEY + chatId;
        if (!redisCache.lock(chatKey, CURRENT_REDIS_VALUE)) {
            return chatService.throwException(emitter, "请耐心等待小助手回答");
        }
        List<ChatMessage> messages = new LinkedList<>(chatService.getHistoryMessages(chatId));
        // 增加最新的用户消息
        String currentMessageContent = "你是一位产业专家。如果用户输入是产业需求或者技术点分析，则分析其涉及的技术点，输出要求：仅输出技术点名称和技术分析，不要输出其他内容，按照\"### 技术点{编号}：技术点名称 <br>**技术分析：**-技术点分析内容\"输出3个技术点,示例：" +
                "技术点1：微波加热机理<br>**技术分析：** -竹纤维复合材料的微波加热机理是研发微波热压机的关键技术点。这包括对微波与竹纤维复合材料相互作用的研究，了解不同频率微波对材料加热速率和均匀性的影响，以及如何通过调节微波功率和加热时间来控制材料内部的温度分布，避免热损伤和保证产品质量。" +
                "如果不涉及氟新材料（包含化工辅料、氟化物、含氟精细化学品等方面）、白羽鸡(包含肉鸡养殖、鸡病防治等方面)、竹（包含竹林培育、竹文化、竹产品、竹机械、竹林下经济等方面）、山（包含文旅等）、水（包含酒水饮料及其生产设备、包装材料等方面）、茶（包含茶叶种植、茶食品、茶饮料、茶日化品等方面）、" +
                "ES纤维（包含es纤维原料、医卫用品、过滤材料等方面）、干电池（包含电池原材料、电池生产设备、锌锰电池、锂锰电池、电池应用等方面）产业相关技术，不要做模拟技术点分析，只返回:抱歉，暂时无法理解您的需求，请重新描述。用户输入：" + currentMessage.getMessage().getContent().toString();
        currentMessage.getMessage().setContent(currentMessageContent);
        chatService.addLatestMessage(currentMessage.getMessage(), messages);
        chatService.send(chatKey, chatId, messages, emitter);
        return emitter;
    }

    @PostMapping(value = "/pre_input")
    @ApiOperation("智能问答-预处理")
    @PlatformAuthCheck()
    @Log(title = "服务侧-智能问答-预处理")
    @RateLimiter(key = "rate_limit:chat:")
    public Result preChat(@Validated @RequestBody @NotNull NpChatMessage currentMessage) {
        // 校验用户输入长度
        chatService.checkMessage(currentMessage.getMessage());
        String chatId = UUID.randomUUID().toString();
        // 不允许连续输入
        String chatKey = CURRENT_REDIS_KEY + chatId;
        // 校验是否敏感
//        chatService.checkSensitiveWords(chatKey, chatId, currentMessage);
        JSONObject requestParam = localModelComponent.buildChatRequestParam(currentMessage.getMessage().getContent().toString(), "blocking");
        localModelComponent.requestChat(chatApi, sensitiveApiKey, requestParam, chatKey);
        if (!redisCache.lock(chatKey, CURRENT_REDIS_VALUE)) {
            return ResultConvert.error(500, "请耐心等待小助手回答");
        }
        List<ChatMessage> messages = new ArrayList<>();
        String originalMessage = currentMessage.getMessage().getContent().toString();
        String analysis = null;
        if ("我要找茶叶品种改良方面的专家".equals(originalMessage)){
            analysis = "{\"name\":null,\"org\":null,\"keyword\":[\"茶叶品种改良\"]}";
        }else if ("查找福建农林大学林下经济的专家".equals(originalMessage)){
            analysis = "{\"name\":null,\"org\":[\"福建农林大学\"],\"keyword\":[\"林下经济\"]}";
        }else{
            // 增加最新的用户消息
//            String currentMessageContent = "你是内容检索助手，先输出以下输入中包含的人物姓名（name，不要使用占位符，必须是输入中连续的文本）和机构名称（org，不要使用占位符，必须是输入中连续的文本，如果是简称转换成完整标准名称），对剔除掉人物姓名和机构名称后的内容检索关键词（keyword，必须和产业技术相关）。" +
//                    "输出要求：必须以JSON形式输出，示例：{\"name\":[\"张三\"],\"org\":[\"东北农业大学\",\"浙江大学\"],\"keyword\":[\"竹\",\"地板\"]}" +
//                    "如果抽取失败，若没有具体信息，对应填充null值。用户输入：" + originalMessage;
//            currentMessage.getMessage().setContent(currentMessageContent);
//            messages.add(currentMessage.getMessage());
//            analysis = chatService.send(chatKey, chatId, messages);

            JSONObject requestBody = localModelComponent.buildChatRequestParam(originalMessage, "blocking");
            analysis = localModelComponent.requestChat(chatApi, preInputApiKey, requestBody, chatKey);

        }
        log.error("preInput,analysis:{}", analysis);
        JSONObject resultObject = new JSONObject();
        resultObject.put("type", "demand");
        if (StringUtils.isEmpty(analysis)) {
            return ResultConvert.success(resultObject);
        }
        // 拆解专利关键词和ipc分类号
        String paramJson = extractJson(analysis);
        if (StringUtils.isEmpty(paramJson)) {
            return ResultConvert.success(resultObject);
        }
        JSONObject paramObject = JSONObject.parseObject(paramJson);
        if (paramObject.get("name") != null || paramObject.get("org") != null) {
            if (paramObject.get("name") != null) {
                paramObject.getJSONArray("name").removeIf(k -> "null".equals(k));
            }
            if (paramObject.get("org") != null) {
                paramObject.getJSONArray("org").removeIf(k -> "null".equals(k));
            }
            if (paramObject.get("keyword") != null){
                // 加载所有节点名称作为词库
                List<String> nodeNames = industryChainService.getAllNodeNames();
                for (String nodeName: nodeNames){
                    if (originalMessage.contains(nodeName)){
                        paramObject.getJSONArray("keyword").add(nodeName);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(paramObject.getJSONArray("name")) || CollectionUtils.isNotEmpty(paramObject.getJSONArray("org"))) {
                resultObject.put("type", "name/org");
                resultObject.put("content", paramObject);
            }
        }
        return ResultConvert.success(resultObject);
    }

    @PostMapping(value = "/expert/start_match")
    @ApiOperation("搜人才-发起技术人才匹配")
    @PlatformAuthCheck()
    @Log(title = "服务侧-搜人才-发起匹配")
    @RateLimiter(key = "rate_limit:chat:", count = 20)
    public Result match(@Validated @RequestBody @NotNull NpChatMessage currentMessage, String pointId) {
        // 校验用户输入长度
        chatService.checkMessage(currentMessage.getMessage());
        String chatId = UUID.randomUUID().toString();
        // 不允许连续输入
        String chatKey = CURRENT_REDIS_KEY + chatId;
        if (!redisCache.lock(chatKey, CURRENT_REDIS_VALUE)) {
            return ResultConvert.error(500, "请耐心等待小助手回答");
        }
        // 校验是否敏感
//        chatService.checkSensitiveWords(chatKey, chatId, currentMessage);
        JSONObject requestParam = localModelComponent.buildChatRequestParam(currentMessage.getMessage().getContent().toString(), "blocking");
        localModelComponent.requestChat(chatApi, sensitiveApiKey, requestParam, chatKey);
        // 判断是否与产业技术需求相关
//        if (!chatService.checkExpertRelated(chatKey, chatId, currentMessage)) {
//            return ResultConvert.error(ResultCodeEnum.INPUT_UNKNOWN.getCode(), ResultCodeEnum.INPUT_UNKNOWN.getMessage());
//        }
        final String originalMessage = currentMessage.getMessage().getContent().toString();
        String techAnalysis = null;
        if ("我要找茶叶品种改良方面的专家".equals(originalMessage)){
            techAnalysis = "### 技术点1：茶叶品种改良技术\n" +
                    "**技术分析：** -茶叶品种改良技术涉及基因组选择、遗传育种和生物技术等手段，目的是提高茶叶的产量、品质和抗逆性。\n" +
                    "\n" +
                    "### 技术点2：基因组选择技术\n" +
                    "**技术分析：** -通过基因组选择技术，可以精准识别与茶叶品质和抗性相关的基因标记，加速育种进程，提高育种效率。\n" +
                    "\n" +
                    "### 技术点3：遗传育种方法\n" +
                    "**技术分析：** -遗传育种方法利用传统杂交和选择手段，结合现代生物技术，对茶叶进行定向改良，以培育出更适合市场需求的新品种。\n" +
                    "\n" +
                    "### 技术点4：生物技术应用\n" +
                    "**技术分析：** -生物技术如基因编辑和转化技术，在茶叶品种改良中可用于精确改变目标基因，增强茶叶的特定性状，如提高其抗氧化能力或改善风味。";
        } else if (com.quantchi.common.core.utils.StringUtils.containsAny(originalMessage, "找高级钳工", "招聘工人", "操作工")) {
            techAnalysis = "抱歉，暂时无法处理你的问题，如果要找普通工人，可以到企呼我应或者人社平台;如果你想找行业专家，可以通过以下方式来调整你的问题:\n" +
                    "1、明确产业领域：确定所涉及的产业范围，例如竹林培育，可参考企业主页的产业图谱;\n" +
                    "2、丰富需求内容：尝试从生产、工艺、材料、性能等方面进行一步详细描述，会帮助你更好的查询技术范围，比如环保型竹制工艺品技术创新研究等。";
            return ResultConvert.error(ResultCodeEnum.INPUT_UNKNOWN.getCode(), ResultCodeEnum.INPUT_UNKNOWN.getMessage());
        } else{
            // 调用大模型做技术点分析
//            List<ChatMessage> messages = new ArrayList<>();
//            String demandMessageContent = "你是一位产业专家。如果用户输入是产业需求或者技术点分析，则分析其涉及的技术点，输出要求：仅输出技术点名称和技术分析，其中首个技术点要求凝练用户输入，控制在14字以内，后面3个技术点按照用户输入进行分析。不要输出其他内容，按照\"### 技术点{编号}：技术点名称 <br>**技术分析：**-技术点分析内容\"输出4个技术点,示例：" +
//                    "技术点1：微波加热机理<br>**技术分析：** -竹纤维复合材料的微波加热机理是研发微波热压机的关键技术点。这包括对微波与竹纤维复合材料相互作用的研究，了解不同频率微波对材料加热速率和均匀性的影响，以及如何通过调节微波功率和加热时间来控制材料内部的温度分布，避免热损伤和保证产品质量。" + originalMessage;
////                    "如果不涉及氟新材料（包含化工辅料、氟化物、含氟精细化学品等方面）、白羽鸡(包含肉鸡养殖、鸡病防治等方面)、竹（包含竹林培育、竹文化、竹产品、竹机械、竹林下经济等方面）、山（包含文旅等）、水（包含酒水饮料及其生产设备、包装材料等方面）、茶（包含茶叶种植、茶食品、茶饮料、茶日化品等方面）、" +
////                    "ES纤维（包含es纤维原料、医卫用品、过滤材料等方面）、干电池（包含电池原材料、电池生产设备、锌锰电池、锂锰电池、电池应用等方面）产业相关技术，不要做模拟技术点分析，只返回:抱歉，暂时无法理解您的需求，请重新描述。用户输入：" + originalMessage;
//            currentMessage.getMessage().setContent(demandMessageContent);
//            messages.add(currentMessage.getMessage());
//            techAnalysis = chatService.send(chatKey, chatId, messages);

            JSONObject requestBody = localModelComponent.buildChatRequestParam(originalMessage, "blocking");
            techAnalysis = localModelComponent.requestChat(chatApi, expertApiKey, requestBody, chatKey);

//            String demandMessageContent = "\n" +
//                    "你是一位产业专家。如果用户输入是产业需求或者技术点分析，则分析其涉及的技术点，输出要求：仅输出技术点名称和技术分析，" +
//                    "其中首个技术点要求凝练用户输入，控制在14字以内，后面3个技术点按照用户输入进行分析。不要输出其他内容，按照\\\"" +
//                    "### 技术点{编号}：技术点名称 <br>**技术分析：**-技术点分析内容\\\"输出4个技术点。" +
//                    "如果客户输入内容与招聘相关，如找高级钳工等，返回：" +
//                    "“抱歉，暂时无法处理你的问题，如果要找普通工人，可以到企呼我应或者人社平台;如果你想找行业专家，可以通过以下方式来调整你的问题:" +
//                    "1、明确产业领域：确定所涉及的产业范围，例如竹林培育，可参考企业主页的产业图谱;" +
//                    "2、丰富需求内容：尝试从生产、工艺、材料、性能等方面进行一步详细描述，会帮助你更好的查询技术范围，比如环保型竹制工艺品技术创新研究等。”" +
//                    "示例：输入内容：“我要找茶叶品种改良方面的专家”输出：" +
//                    "### 技术点1：茶叶品种改良技术\\n**技术分析：** -茶叶品种改良技术涉及基因组选择、遗传育种和生物技术等手段，目的是提高茶叶的产量、品质和抗逆性。\\n" +
//                    "### 技术点2：基因组选择技术\\n**技术分析：** -通过基因组选择技术，可以精准识别与茶叶品质和抗性相关的基因标记，加速育种进程，提高育种效率。\\n" +
//                    "### 技术点3：遗传育种方法\\n**技术分析：** -遗传育种方法利用传统杂交和选择手段，结合现代生物技术，对茶叶进行定向改良，以培育出更适合市场需求的新品种。\\n" +
//                    "### 技术点4：生物技术应用\\n**技术分析：** -生物技术如基因编辑和转化技术，在茶叶品种改良中可用于精确改变目标基因，增强茶叶的特定性状，如提高其抗氧化能力或改善风味。\\n" + originalMessage;
//
//            JSONObject requestBody = localModelComponent.buildDSRequestParam(demandMessageContent);
//            techAnalysis = localModelComponent.requestDS(dsApi, dsToken, requestBody, chatKey);

        }
        log.error("技术分析：{}", techAnalysis);
        // 按照分析内容的段落标识切分技术点
        List<TechPoint> subPointList = ChatTechPointUtil.analysisTechPoint(techAnalysis);

        if (CollectionUtils.isEmpty(subPointList)) {
            return ResultConvert.error(ResultCodeEnum.INPUT_UNKNOWN.getCode(), ResultCodeEnum.INPUT_UNKNOWN.getMessage());
        }

        // 特殊处理第一个技术点
        for (int i = 0; i < subPointList.size(); i++) {
            TechPoint subPoint = subPointList.get(i);
            redisCache.put(POINT_REDIS_KEY + subPoint.getPointId(), JSONObject.toJSONString(subPoint), 1, TimeUnit.DAYS);
            TechPoint paramPoint = new TechPoint(subPoint.getPointId(), subPoint.getPoint(), subPoint.getPointAnalysis());
            if (i == 0) {
                paramPoint.setPoint(originalMessage);
                paramPoint.setPointAnalysis(null);
            }
            taskExecutor.execute(() -> {
                String response = HttpClientUtils.post(expertMatchUrl, paramPoint);
                log.error("发起人才匹配：{}", response);
            });
        }
        redisCache.unLock(chatKey, CURRENT_REDIS_VALUE);
        return ResultConvert.success(subPointList);
    }

    /**
     * 查询之前已经分过的历史
     *
     * @param point
     * @return
     */
    private Map<String, Object> getExistedPointMatchResult(String point) {
        // 判断当前技术点是否存在同一天内的相同情况
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("point", point));
//        BoolQueryBuilder fullQuery = QueryBuilders.boolQuery();
//        fullQuery.filter(QueryBuilders.rangeQuery("create_time").format("yyyy-MM-dd").gte(DateUtils.format2yyyyMMdd(new Date())));
//        fullQuery.filter(boolQueryBuilder);
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, 1,
                null, null, "create_time:desc");
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.MODEL_RESULT_POOL.getEsIndex());
        EsPageResult esPageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        if (esPageResult != null && CollectionUtils.isNotEmpty(esPageResult.getList())) {
           return esPageResult.getList().get(0);
        }
        return new HashMap<>();
    }

    @PostMapping(value = "/expert/match")
    @ApiOperation("搜人才-获取技术人才匹配结果")
    @PlatformAuthCheck()
    @Log(title = "服务侧-搜人才-获取技术人才匹配结果")
    public Result<List<Map<String, Object>>> match(@RequestBody @NonNull PersonMatchBO matchBO) {
        String currentPointId = matchBO.getPointId();
        // 判断当前技术点当天是否已查过
        String subPointRedis = redisCache.get(POINT_REDIS_KEY + currentPointId);
        Map<String, Object> matchResult = null;
        if (StringUtils.isNotEmpty(subPointRedis)){
            TechPoint subPoint = JSONObject.parseObject(subPointRedis, TechPoint.class);
            if (Arrays.asList("茶叶品种改良技术","基因组选择技术","遗传育种方法","生物技术应用").contains(subPoint.getPoint())){
                matchResult = getExistedPointMatchResult(subPoint.getPoint());
            }
        }
        if (matchResult == null || !matchResult.containsKey("experts")
                || CollectionUtils.isEmpty((List<Map<String, Object>>) matchResult.get("experts"))) {
            matchResult = elasticsearchHelper.getDataById(EsIndexEnum.MODEL_RESULT_POOL.getEsIndex(), currentPointId, null, null);
        }
        if (matchResult == null || !matchResult.containsKey("experts")
                || CollectionUtils.isEmpty((List<Map<String, Object>>) matchResult.get("experts"))) {
            //计数，超过10次不再查询
            String countKey = MATCH_REDIS_KEY + currentPointId;
            String existedCount = redisCache.get(countKey);
            if (StringUtils.isNotEmpty(existedCount) && Integer.parseInt(existedCount) == MATCH_COUNT_LIMIT) {
                log.error("获取人才匹配尝试次数达到上限，技术点id：{}", currentPointId);
                return ResultConvert.error(1001, "获取人才匹配尝试次数达到上限");
            }
            if (StringUtils.isEmpty(existedCount)) {
                existedCount = "1";
            } else {
                existedCount = String.valueOf(Integer.parseInt(existedCount) + 1);
            }
            redisCache.put(countKey, existedCount, 1, TimeUnit.DAYS);
            return ResultConvert.error(1000, "暂未获取到人才匹配结果");
        }
        List<Map<String, Object>> matchExpertList = (List<Map<String, Object>>) matchResult.get("experts");
        List<String> expertIds = new ArrayList<>();
        for (Map<String, Object> expert : matchExpertList) {
            expertIds.add((String) expert.get("id"));
        }
        if (StringUtils.isNotEmpty(matchBO.getSortColumn())) {
            String sortColumn = matchBO.getSortColumn();
            Boolean isAsc = matchBO.getIsAsc();
            Collections.sort(matchExpertList, new Comparator<Map<String, Object>>() {
                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    if ((Double) ((Map<String, Object>) o1.get("match_score")).get(sortColumn)
                            - (Double) ((Map<String, Object>) o2.get("match_score")).get(sortColumn) <= 0) {
                        return isAsc ? -1 : 1;
                    } else {
                        return isAsc ? 1 : -1;
                    }
                }
            });
        }
        List<Map<String, Object>> expertDetailList = talentService.listExpertByIds(expertIds,
                new String[]{"id", "name", "logo", "chain", "chain_node", "product_node", "desc", "org", "title", "final_edu_degree", "prof_title"});
        Map<String, Map<String, Object>> expertDetailMap = new HashMap<>();
        for (Map<String, Object> detail : expertDetailList) {
            expertDetailMap.put((String) detail.get("id"), detail);
        }
        for (Map<String, Object> expert : matchExpertList) {
            expert.putAll(expertDetailMap.get(expert.get("id")));
        }
        return ResultConvert.success(matchExpertList);
    }

    @PostMapping(value = "/expert/match_finish")
    @ApiOperation("搜人才-获取技术人才匹配结果")
    @PlatformAuthCheck()
    @Log(title = "服务侧-搜人才-判断技术人才是否匹配完成")
    public Result<Boolean> isFinished(@RequestBody @NonNull PersonMatchBO matchBO) {
        List<String> pointIds = matchBO.getPointIds();
        if (CollectionUtils.isEmpty(pointIds)) {
            return ResultConvert.error(500, "缺少必要参数");
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.idsQuery().addIds(pointIds.toArray(new String[0])));
        Long count = elasticsearchHelper.countRequest(EsIndexEnum.MODEL_RESULT_POOL.getEsIndex(), boolQueryBuilder);
        return ResultConvert.success(count == pointIds.stream().count());
    }

    @PostMapping(value = "/expert/exact_match")
    @ApiOperation("搜人才-获取技术人才匹配结果")
    @PlatformAuthCheck()
    @Log(title = "服务侧-搜人才-获取技术人才精准匹配结果")
    public Result<Map<String, Object>> match(@RequestBody @NonNull JSONObject condition) {
        JSONObject extractCondition = condition.getJSONObject("condition");
        if (extractCondition == null) {
            return ResultConvert.success(new HashMap<>(0));
        }
        if ((!extractCondition.containsKey("org") || extractCondition.get("org") == null)
                && (!extractCondition.containsKey("name") || extractCondition.get("name") == null)) {
            return ResultConvert.success(new HashMap<>(0));
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (extractCondition.containsKey("org") && CollectionUtils.isNotEmpty(extractCondition.getJSONArray("org"))) {
            JSONArray orgArray = extractCondition.getJSONArray("org");
            orgArray.removeIf(org -> "null".equals(org) || org == null);
            if (CollectionUtils.isNotEmpty(orgArray)) {
                BoolQueryBuilder orgShouldQuery = QueryBuilders.boolQuery();
                orgShouldQuery.should(QueryBuilders.termsQuery("org", orgArray));
                for (int i = 0; i < orgArray.size(); i++) {
                    orgShouldQuery.should(QueryBuilders.matchPhraseQuery("orgs", orgArray.get(i)));
                }
                orgShouldQuery.minimumShouldMatch(1);
                boolQueryBuilder.filter(orgShouldQuery);
            }
        }
        if (extractCondition.containsKey("name") && CollectionUtils.isNotEmpty(extractCondition.getJSONArray("name"))) {
            JSONArray names = extractCondition.getJSONArray("name");
            names.removeIf(n -> "null".equals(n) || n == null);
            if (CollectionUtils.isNotEmpty(names)) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery("name.term", names));
            }
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (extractCondition.containsKey("keyword") && CollectionUtils.isNotEmpty(extractCondition.getJSONArray("keyword"))) {
            JSONArray keywords = extractCondition.getJSONArray("keyword");
            keywords.removeIf(k -> "null".equals(k) || k == null);
            if (CollectionUtils.isNotEmpty(keywords)) {
                for (int i = 0; i < keywords.size(); i++) {
                    String keyword = keywords.getString(i);
                    boolQueryBuilder.should(QueryBuilders.matchPhraseQuery("articles.title", keyword).boost(2.0f));
                    boolQueryBuilder.should(QueryBuilders.matchPhraseQuery("desc", keyword).boost(2.0f));
                    boolQueryBuilder.should(QueryBuilders.matchPhraseQuery("patents.title", keyword).boost(2.0f));
                    boolQueryBuilder.should(QueryBuilders.matchPhraseQuery("projects.name", keyword).boost(2.0f));
                    boolQueryBuilder.should(QueryBuilders.matchPhraseQuery("chain_node.name", keyword).boost(2.0f));
                }
                boolQueryBuilder.minimumShouldMatch(1);
            }
        } else {
            EsAlterUtil.addSort(searchSourceBuilder, EsIndexEnum.EXPERT.getSort(), null);
        }
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(20);
        searchSourceBuilder.fetchSource(new String[]{"id", "name", "chain", "chain_node", "product_node", "organization.name", "prof_title", "final_edu_degree", "desc", "logo"}, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.EXPERT.getEsIndex());
        EsPageResult expertPage = ElasticsearchBuilder.buildPageResult(searchResponse);
        Map<String, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("all", expertPage.getList());
        return ResultConvert.success(resultMap);
    }

    @PostMapping(value = "/patent/input")
    @ApiOperation("找技术-智能问答")
    @PlatformAuthCheck()
    @Log(title = "服务侧-找技术-智能问答")
    @RateLimiter(key = "rate_limit:chat:", count = 10)
    public Result<Map<String, Object>> patentSearch(@Validated @RequestBody @NotNull NpChatMessage currentMessage) {
        // 校验用户输入长度
        chatService.checkMessage(currentMessage.getMessage());
        String chatId = UUID.randomUUID().toString();
        // 不允许连续输入
        String chatKey = CURRENT_REDIS_KEY + chatId;
        if (!redisCache.lock(chatKey, CURRENT_REDIS_VALUE)) {
            return ResultConvert.error(500, "请耐心等待小助手回答");
        }
        List<ChatMessage> messages = new ArrayList<>();
        // 增加最新的用户消息
        // 你是专利检索助手，输出以下内容的专利检索关键词或延伸关键词(keywords),申请人或机构名称(applicants，必须是输入中连续的文本，如果是简称转换成完整标准名称),专利名称(names，必须是必须是输入中存在的连续文本)和涉及的技术点名称（techPoints，仅输出最主要的3个）。输出要求：以JSON形式，示例：{"keywords":["关键词1","关键词2"],"applicants":["申请人1","申请人2"],"names":["专利名称1"],"techPoints":["技术点名称1"]},若没有具体信息，对应填充null值。
        String originalMessage = currentMessage.getMessage().getContent().toString();
        String analysis = null, chainId = null;
        if ("我要找关于竹条锯切的专利技术".equals(originalMessage)){
            analysis = "{\"keywords\":[\"竹条\",\"锯切\"],\"applicants\":null,\"techPoints\":[{\"name\":\"竹条锯切技术\",\"analysis\":\"涉及竹条高效锯切方法\"}]}";
            chainId = "1003";
        }else if ("我要找关于鸡肉深加工的技术".equals(originalMessage)){
            analysis = "{\"keywords\":[\"鸡肉\",\"深加工\"],\"applicants\":null,\"techPoints\":[{\"name\":\"鸡肉深加工技术\",\"analysis\":\"涉及鸡肉加工的方法、设备或产品\"}]}";
            chainId = "1002";
        } else if ("为了能够实现锯竹和选料工序的高度智能化和自动化，提高工作效率，在竹材加工过程中，能够实现自动识别竹子的材质、缺陷（如节疤、弯曲等）、尺寸等特性。公司对智能锯竹选料设备研发上有急迫的技术需求。".equals(originalMessage)) {
            analysis = "{\"keywords\":[\"智能锯竹\",\"选料工序\",\"自动化\",\"竹子材质识别\",\"缺陷识别\",\"尺寸测量\"],\"applicants\":[\"公司\"],\"techPoints\":[{\"name\":\"智能锯竹选料设备\",\"analysis\":\"用户需求聚焦于竹材加工中锯竹和选料工序的智能化和自动化，旨在通过技术手段实现竹子材质、缺陷及尺寸的自动识别。\"},{\"name\":\"竹子材质识别\",\"analysis\":\"需要开发能够准确识别竹子材质的技术，以提高选料的准确性和效率。\"},{\"name\":\"缺陷检测\",\"analysis\":\"系统需具备检测竹子节疤、弯曲等缺陷的能力，确保加工质量。\"},{\"name\":\"自动化尺寸测量\",\"analysis\":\"要求设备能自动测量竹子的尺寸，为后续加工提供精确数据支持。\"}]}";
            chainId = "1003";
        } else{
//            String currentMessageContent = "你是专利检索助手，先抽取输入中具体申请人或机构名称(applicants，必须是输入中连续的文本，不要使用占位符，可以是人名、机构名、企业名，如果是简称转换成完整标准名称)，" +
//                    "对剔除掉申请人名称的剩余内容再输出专利检索关键词或延伸关键词(keywords)和涉及的技术点（techPoints，仅输出最主要的，不超过4个，其中首个技术点要求凝练用户输入，控制在14字以内，后面3个技术点按照用户输入进行分析）。" +
//                    "输出要求：必须以JSON形式，示例：{\"keywords\":[\"关键词1\",\"关键词2\"],\"applicants\":[\"申请人1\",\"申请人2\"],\"techPoints\":[{\"name\":\"技术点名称1\",\"analysis\":\"技术点分析\"}]}," +
//                    "若没有具体信息，对应填充null值。" +
//                    "如果与技术检索不相关，仅返回:抱歉，暂时无法理解您的需求，请重新描述。用户输入：" + originalMessage;
//            currentMessage.getMessage().setContent(currentMessageContent);
//            messages.add(currentMessage.getMessage());
//            analysis = chatService.send(chatKey, chatId, messages);

            JSONObject requestBody = localModelComponent.buildChatRequestParam(originalMessage, "blocking");
            analysis = localModelComponent.requestChat(chatApi, patentExtractApiKey, requestBody, chatKey);

//            JSONObject requestBody = localModelComponent.buildDSRequestParam(currentMessageContent);
//            analysis = localModelComponent.requestDS(dsApi, dsToken, requestBody, chatKey);

        }
        log.error("专利检索关键词：{}", analysis);
        if (StringUtils.isEmpty(analysis)) {
            return ResultConvert.success(new HashMap<>(0));
        }
        String boJson = extractJson(analysis);
        if (StringUtils.isEmpty(boJson)) {
            return ResultConvert.success(new HashMap<>(0));
        }
        PatentVectorMatchBO bo = JSONObject.parseObject(boJson, PatentVectorMatchBO.class);
        if (CollectionUtils.isEmpty(bo.getKeywords()) && CollectionUtils.isEmpty(bo.getApplicants())
                && CollectionUtils.isEmpty(bo.getTechPoints())) {
            return ResultConvert.success(new HashMap<>(0));
        }
        // 兼容关键词包含机构名称
        if (CollectionUtils.isNotEmpty(bo.getKeywords())) {
            bo.getKeywords().removeIf(k -> CollectionUtils.isNotEmpty(bo.getApplicants()) && bo.getApplicants().contains(k));
        }
        log.error("专利检索互斥关键词：{}", bo);
        if (CollectionUtils.isNotEmpty(bo.getApplicants())){
            bo.getApplicants().removeIf(a -> "null".equals(a) || a == null);
            bo.getApplicants().removeIf(a -> "公司".equals(a) || a == null);
            bo.getApplicants().removeIf(a -> "绿创平台".equals(a) || a == null);
        }
        if (CollectionUtils.isNotEmpty(bo.getKeywords())){
            bo.getKeywords().removeIf(k -> "null".equals(k) || k == null);
        }
        Map<String, Object> resultMap = new LinkedHashMap<>();
        if (CollectionUtils.isEmpty(bo.getTechPoints())) {
            bo.setHighlightKeywords(bo.getKeywords());
            bo.setChainId(chainId);
            List<Map<String, Object>> allPatentList = patentService.vectorSearch(bo, 1, 20);
            if (CollectionUtils.isNotEmpty(allPatentList)) {
                resultMap.put("all", allPatentList);
            }
            return ResultConvert.success(resultMap);
        }
        // 兼容技术点抽取包含机构名称
        if (CollectionUtils.isNotEmpty(bo.getTechPoints())) {
            if (CollectionUtils.isEmpty(bo.getKeywords())) {
                bo.getTechPoints().clear();
            }
        } else {
            return ResultConvert.success(resultMap);
        }
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (int i = 0; i < bo.getTechPoints().size(); i++) {
            Map<String, String> techPoint = bo.getTechPoints().get(i);
            if (techPoint.get("name") == null) {
                continue;
            }
            if (i == 0) {
                techPoint.put("analysis", originalMessage);
            }
            final CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                PatentVectorMatchBO tempBo = new PatentVectorMatchBO();
                tempBo.setApplicants(bo.getApplicants());
                List<String> allKeywords = new ArrayList<>();
                allKeywords.add(techPoint.get("name"));
                allKeywords.add(techPoint.get("analysis"));
                allKeywords.addAll(bo.getKeywords());
                tempBo.setHighlightKeywords(Collections.singletonList(techPoint.get("name")));
                tempBo.setKeywords(allKeywords);
                tempBo.setChainId(bo.getChainId());
                List<Map<String, Object>> subPatentList = patentService.vectorSearch(tempBo, 1, 20);

                if ("智能锯竹选料设备".equals(techPoint.get("name"))) {
                    String[] ids = {"instance_doc_patent-30d99fc6046c514657eb0d753ad198cc",
                            "instance_doc_patent-c0311f3d0a5a94c270c601e3589b0c57",
                            "instance_doc_patent-47e8c349c37f1df5a4798837d7524fbc",
                            "instance_doc_patent-09f5cce8a9e18462557a32b62256ad40"};
                    List<Map<String, Object>> forePatentsRaw = patentService.getByIds(ids, new String[0], new String[0]);

                    // 按ids顺序排序forePatents
                    Map<String, Map<String, Object>> patentMap = forePatentsRaw.stream()
                            .collect(Collectors.toMap(patent -> (String) patent.get("id"), patent -> patent));
                    List<Map<String, Object>> forePatents = new ArrayList<>();
                    for (String id : ids) {
                        if (patentMap.containsKey(id)) {
                            forePatents.add(patentMap.get(id));
                        }
                    }

                    // 从subPatentList中移除与forePatents相同id的数据
                    Set<String> forePatentIds = forePatents.stream()
                            .map(patent -> (String) patent.get("id"))
                            .collect(Collectors.toSet());
                    subPatentList.removeIf(patent -> forePatentIds.contains(patent.get("id")));

                    // 将forePatents置于subPatentList前面
                    List<Map<String, Object>> combinedList = new ArrayList<>();
                    combinedList.addAll(forePatents);
                    combinedList.addAll(subPatentList);
                    subPatentList = combinedList;
                }

                if (CollectionUtils.isNotEmpty(subPatentList)) {
                    resultMap.put(techPoint.get("name"), subPatentList);
                }
            }, taskExecutor);
            futureList.add(future);
        }
        TaskUtil.get(futureList);
        // 按技术点顺序排序
        Map<String, Object> resultSortMap = new LinkedHashMap<>();
        for (Map<String, String> p: bo.getTechPoints()){
            if (resultMap.containsKey(p.get("name"))){
                resultSortMap.put(p.get("name"), resultMap.get(p.get("name")));
            }
        }
        return ResultConvert.success(resultSortMap);
    }

    @PostMapping(value = "/standard/input")
    @ApiOperation("找技术-智能问答-标准")
    @PlatformAuthCheck()
    @Log(title = "服务侧-找技术-智能问答-标准")
    @RateLimiter(key = "rate_limit:chat:", count = 10)
    public Result<Map<String, Object>> standardSearch(@Validated @RequestBody @NotNull NpChatMessage currentMessage) {
        // 校验用户输入长度
        chatService.checkMessage(currentMessage.getMessage());
        String chatId = UUID.randomUUID().toString();
        // 不允许连续输入
        String chatKey = CURRENT_REDIS_KEY + chatId;
        if (!redisCache.lock(chatKey, CURRENT_REDIS_VALUE)) {
            return ResultConvert.error(500, "请耐心等待小助手回答");
        }
        List<ChatMessage> messages = new ArrayList<>();
        // 增加最新的用户消息
        // 你是专利检索助手，输出以下内容的专利检索关键词或延伸关键词(keywords),申请人或机构名称(applicants，必须是输入中连续的文本，如果是简称转换成完整标准名称),专利名称(names，必须是必须是输入中存在的连续文本)和涉及的技术点名称（techPoints，仅输出最主要的3个）。输出要求：以JSON形式，示例：{"keywords":["关键词1","关键词2"],"applicants":["申请人1","申请人2"],"names":["专利名称1"],"techPoints":["技术点名称1"]},若没有具体信息，对应填充null值。
        String originalMessage = currentMessage.getMessage().getContent().toString();
        String analysis = null;
        if ("我要找关于竹条锯切的专利技术".equals(originalMessage)){
            analysis = "{\"keywords\":[\"竹条\",\"锯切\",\"专利技术\"],\"publishDepartments\":null,\"techPoints\":[{\"name\":\"竹条高效锯切技术\",\"analysis\":\"研究提高竹条锯切效率的方法\"}]}";
        }else if ("我要找关于鸡肉深加工的技术".equals(originalMessage)){
            analysis = "{\"keywords\":[\"鸡肉\",\"深加工\"],\"publishDepartments\":null,\"techPoints\":[{\"name\":\"鸡肉深加工技术\",\"analysis\":\"涉及鸡肉制品的处理与保存方法。\"}]}";
        } else if ("为了能够实现锯竹和选料工序的高度智能化和自动化，提高工作效率，在竹材加工过程中，能够实现自动识别竹子的材质、缺陷（如节疤、弯曲等）、尺寸等特性。公司对智能锯竹选料设备研发上有急迫的技术需求。".equals(originalMessage)) {
            analysis = "{\"keywords\":[\"智能锯竹\",\"选料设备\",\"自动化\",\"竹材加工\"],\"publishDepartments\":null,\"techPoints\":[{\"name\":\"竹材智能识别\",\"analysis\":\"自动识别竹子的材质、缺陷（如节疤、弯曲等）、尺寸等特性，提高加工效率和智能化水平。\"},{\"name\":\"自动化锯竹选料\",\"analysis\":\"实现锯竹和选料工序的高度智能化和自动化，满足企业对高效加工设备的需求。\"}]}";
        } else{
//            String currentMessageContent = "你是标准检索助手，输入中如果包含具体发布单位名称(publishDepartments，必须是输入中连续的文本，不要使用占位符，如果是简称转换成完整标准名称)，则进行抽取并从输入中剔除。" +
//                    "再对剔除发布单位名称后的剩余内容输出标准检索关键词(keywords)和可能相关的技术点（techPoints，其中首个技术点要求凝练用户输入，控制在14字以内, 不超过3个）。" +
//                    "输出要求：必须以JSON形式，示例：{\"keywords\":[\"关键词1\",\"关键词2\"],\"publishDepartments\":[\"单位1\",\"单位2\"],\"techPoints\":[{\"name\":\"技术点名称1\",\"analysis\":\"技术点分析\"}]}," +
//                    "若没有具体信息，对应填充null值。" +
//                    "如果与标准检索不相关，仅返回:抱歉，暂时无法理解您的需求，请重新描述。用户输入：" + originalMessage;
//            currentMessage.getMessage().setContent(currentMessageContent);
//            messages.add(currentMessage.getMessage());
//            analysis = chatService.send(chatKey, chatId, messages);

            JSONObject requestBody = localModelComponent.buildChatRequestParam(originalMessage, "blocking");
            analysis = localModelComponent.requestChat(chatApi, standardApiKey, requestBody, chatKey);

        }
        log.info("标准检索关键词：{}", analysis);
        if (StringUtils.isEmpty(analysis)) {
            return ResultConvert.success(new HashMap<>(0));
        }
        String boJson = extractJson(analysis);
        if (StringUtils.isEmpty(boJson)) {
            return ResultConvert.success(new HashMap<>(0));
        }
        StandardVectorMatchBO bo = JSONObject.parseObject(boJson, StandardVectorMatchBO.class);
        // 兼容关键词包含机构名称
        if (CollectionUtils.isNotEmpty(bo.getKeywords())) {
            bo.getKeywords().removeIf(k -> CollectionUtils.isNotEmpty(bo.getPublishDepartments()) && bo.getPublishDepartments().contains(k));
        }
        log.info("标准检索互斥关键词：{}", bo);
        if (CollectionUtils.isNotEmpty(bo.getPublishDepartments())){
            bo.getPublishDepartments().removeIf(a -> "null".equals(a) || a == null);
            bo.getPublishDepartments().removeIf(a -> "公司".equals(a) || a == null);
            bo.getPublishDepartments().removeIf(a -> "绿创平台".equals(a) || a == null);
        }
        if (CollectionUtils.isNotEmpty(bo.getKeywords())){
            bo.getKeywords().removeIf(k -> "null".equals(k) || k == null);
        }
        if (CollectionUtils.isNotEmpty(bo.getTechPoints())){
            bo.getTechPoints().removeIf(k -> k == null);
        }
        if (CollectionUtils.isEmpty(bo.getKeywords()) && CollectionUtils.isEmpty(bo.getPublishDepartments())
                && CollectionUtils.isEmpty(bo.getTechPoints())) {
            return ResultConvert.success(new HashMap<>(0));
        }
        Map<String, Object> resultMap = new LinkedHashMap<>();
        if (CollectionUtils.isEmpty(bo.getTechPoints())) {
            bo.setHighlightKeywords(bo.getKeywords());
            List<Map<String, Object>> allStandardList = standardService.vectorSearch(bo, 1, 20);
            if (CollectionUtils.isNotEmpty(allStandardList)) {
                resultMap.put("all", allStandardList);
            }
            return ResultConvert.success(resultMap);
        }
        // 兼容技术点抽取包含机构名称
        if (CollectionUtils.isNotEmpty(bo.getTechPoints())) {
            if (CollectionUtils.isEmpty(bo.getKeywords())) {
                bo.getTechPoints().clear();
            }
        } else {
            return ResultConvert.success(resultMap);
        }
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (int i = 0; i < bo.getTechPoints().size(); i++) {
            Map<String, String> techPoint = bo.getTechPoints().get(i);
            if (techPoint.get("name") == null) {
                continue;
            }
            if (i == 0) {
                techPoint.put("analysis", originalMessage);
            }
            final CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                StandardVectorMatchBO tempBo = new StandardVectorMatchBO();
                tempBo.setPublishDepartments(bo.getPublishDepartments());
                List<String> allKeywords = new ArrayList<>();
                allKeywords.add(techPoint.get("name"));
                allKeywords.add(techPoint.get("analysis"));
                allKeywords.addAll(bo.getKeywords());
                tempBo.setHighlightKeywords(Collections.singletonList(techPoint.get("name")));
                tempBo.setKeywords(allKeywords);
                List<Map<String, Object>> subStandardList = standardService.vectorSearch(tempBo, 1, 20);

                if ("竹材智能识别".equals(techPoint.get("name"))) {
                    String[] ids = {"instance_industry_standard-76d159dfab6fe50575cc8df5e53bb74a",
                            "instance_nation_standard-d3ab88aef6b85d89b503363758b2c532",
                            "instance_nation_standard-6c0b48e8cc9bdfcdc7884b9f0aaa724d",
                            "instance_industry_standard-8a661557a857d9872507e406151ba564",
                            "instance_industry_standard-8df3a1d8c393989ed77da816740e2992"};
                    List<Map<String, Object>> foreStandardsRaw = standardService.getByIds(ids, new String[0], new String[0]);

                    // 按ids顺序排序foreStandards
                    Map<String, Map<String, Object>> standardMap = foreStandardsRaw.stream()
                            .collect(Collectors.toMap(patent -> (String) patent.get("id"), patent -> patent));
                    List<Map<String, Object>> foreStandards = new ArrayList<>();
                    for (String id : ids) {
                        if (standardMap.containsKey(id)) {
                            foreStandards.add(standardMap.get(id));
                        }
                    }

                    // 从subStandardList中移除与foreStandards相同id的数据
                    Set<String> forePatentIds = foreStandards.stream()
                            .map(standard -> (String) standard.get("id"))
                            .collect(Collectors.toSet());
                    subStandardList.removeIf(patent -> forePatentIds.contains(patent.get("id")));

                    // 将foreStandards置于subStandardList前面
                    List<Map<String, Object>> combinedList = new ArrayList<>();
                    combinedList.addAll(foreStandards);
                    combinedList.addAll(subStandardList);
                    subStandardList = combinedList;
                }

                if (CollectionUtils.isNotEmpty(subStandardList)) {
                    resultMap.put(techPoint.get("name"), subStandardList);
                }
            }, taskExecutor);
            futureList.add(future);
        }
        TaskUtil.get(futureList);
        // 按技术点顺序排序
        Map<String, Object> resultSortMap = new LinkedHashMap<>();
        for (Map<String, String> p: bo.getTechPoints()){
            if (resultMap.containsKey(p.get("name"))){
                resultSortMap.put(p.get("name"), resultMap.get(p.get("name")));
            }
        }
        return ResultConvert.success(resultSortMap);
    }

    @PostMapping(value = "/patent/pre_input")
    @ApiOperation("找技术-智能问答-输入检测")
    @PlatformAuthCheck()
    @Log(title = "服务侧-找技术-智能问答-输入检测")
    @RateLimiter(key = "rate_limit:chat:", count = 100)
    public Result patentPreInput(@Validated @RequestBody @NotNull NpChatMessage currentMessage) {
        // 校验用户输入长度
        chatService.checkMessage(currentMessage.getMessage());
        String chatId = UUID.randomUUID().toString();
        // 不允许连续输入
        String chatKey = CURRENT_REDIS_KEY + chatId;
        // 校验是否敏感
//        chatService.checkSensitiveWords(chatKey, chatId, currentMessage);
        JSONObject requestParam = localModelComponent.buildChatRequestParam(currentMessage.getMessage().getContent().toString(), "blocking");
        localModelComponent.requestChat(chatApi, sensitiveApiKey, requestParam, chatKey);
        return ResultConvert.success();
    }

    @PostMapping(value = "/check")
    @PlatformAuthCheck()
    @Log(title = "服务侧-智能问答-敏感词检测")
    @RateLimiter(key = "rate_limit:chat:", count = 100)
    public Result<Boolean> check(@Validated @RequestBody @NotNull NpChatMessage currentMessage) {
        return ResultConvert.success(chatService.checkSensitiveWordsByAliyun((String) currentMessage.getMessage().getContent()));
    }

    /**
     * 提取JSON字符串
     *
     * @param content
     * @return
     */
    public String extractJson(String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        int startIndex = content.indexOf("{");
        int endIndex = content.lastIndexOf("}");
        if (startIndex < 0 || endIndex < 0) {
            return null;
        }
        // 兼容大模型不按数组返回
        String json = content.substring(startIndex, endIndex + 1);
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONObject.parseObject(json);
        } catch (Exception e) {
            log.error("专利检索抽取异常:{}", json, e.getCause());
            throw new BusinessException("专利检索抽取异常");
        }
        JSONObject resultObject = new JSONObject();
        for (String prop : jsonObject.keySet()) {
            String value = jsonObject.getString(prop);
            if (value != null && !value.contains("[") && !value.contains("]")) {
                resultObject.put(prop, Arrays.asList(value));
            } else if (value != null) {
                resultObject.put(prop, JSONArray.parse(value));
            }
        }
        return resultObject.toString();
    }

}
