package com.quantchi.nanping.innovation.company.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.model.bo.ProductBO;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.company.service.IIndustryProductPriceService;
import com.quantchi.nanping.innovation.company.service.IIndustryProductService;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.model.IndustryChain;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/12/20 16:35
 */
@RestController
@Api(tags = "服务侧(看产业)")
@RequestMapping("/product")
@Validated
@PlatformAuthCheck(type = {"1"})
@Metrics
public class ProductController {

    @Autowired
    private IndustryChainService chainService;

    @Autowired
    private IIndustryProductPriceService priceService;

    @Autowired
    private ICompanyNodeRelationService companyNodeRelationService;

    @Autowired
    private IIndustryProductService productService;

    @GetMapping("/condition")
    @ApiOperation("获取产品筛选条件")
    @Log(title = "服务侧-看产业")
    @AntiReptile
    public Result getProductCondition(String entityId, @RequestParam(defaultValue = "0", required = false) int userType) {
        List<Map<String, Object>> chainList = chainService.listMaps(Wrappers.lambdaQuery(IndustryChain.class)
                .orderByAsc(IndustryChain::getId));
        Set<String> chainIds = new HashSet<>();
        if (StpUtil.isLogin() && StringUtils.isNotEmpty(entityId)) {
            // 查询企业所在的链
            Pair<Set<String>, Set<String>> chainInfo = companyNodeRelationService.getRelatedNodeIdsByEntityId(entityId,
                    UserInfoEntity.USER_COMPANY == userType, UserInfoEntity.USER_EXPERT == userType);
            chainIds = chainInfo.getLeft();
        }
        Map<String, List<String>> productGroup = productService.getProductGroupByChain();
        List<Map<String, Object>> resultChainList = new ArrayList<>();
        for (Map<String, Object> chain : chainList) {
            String chainId = (String) chain.get("id");
            if (chainIds.contains(chainId)) {
                Map<String, Object> propertyMap = new HashMap<>();
                propertyMap.put("located", true);
                chain.put("properties", propertyMap);
            }
            if (productGroup.containsKey(chainId)) {
                chain.put("children", productGroup.get(chainId));
                resultChainList.add(chain);
            }
        }
        return ResultConvert.success(resultChainList);
    }

//    @GetMapping("/price")
//    @ApiOperation("获取产品价格变动")
//    @AntiReptile
//    public Result getProductPrice(@NotBlank String chainId, @NotBlank String productName) {
//        return ResultConvert.success(priceService.getProductPriceTend(chainId, productName));
//    }
//
//    @GetMapping("/price_interval")
//    @ApiOperation("获取产品价格区间")
//    @AntiReptile
//    public Result getProductInterval(@NotBlank String chainId, @NotBlank String productName) {
//        String maxPrice = priceService.getMaxPrice(chainId, productName);
//        String minPrice = priceService.getMinPrice(chainId, productName);
//        Map<String, String> resultMap = new HashMap<>();
//        resultMap.put("max", maxPrice);
//        resultMap.put("min", minPrice);
//        return ResultConvert.success(resultMap);
//    }

    @PostMapping("/page")
    @ApiOperation("产品价格列表")
    @AntiReptile
    @Log(title = "服务侧-看产业-产品价格")
    public Result pageProductPrice(@RequestBody @Validated ProductBO pageBO) {
        Set<String> chainIds = new HashSet<>();
        if (StpUtil.isLogin() && StringUtils.isNotEmpty(pageBO.getEntityId())) {
            // 查询企业所在的链
            Pair<Set<String>, Set<String>> chainInfo = companyNodeRelationService.getRelatedNodeIdsByEntityId(pageBO.getEntityId(),
                    UserInfoEntity.USER_COMPANY == pageBO.getUserType(), UserInfoEntity.USER_EXPERT == pageBO.getUserType());
            chainIds = chainInfo.getLeft();
            if (CollectionUtils.isNotEmpty(chainIds)){
                pageBO.setChainIds(chainIds);
            }
        }
        return ResultConvert.success(productService.page(pageBO));
    }
}
