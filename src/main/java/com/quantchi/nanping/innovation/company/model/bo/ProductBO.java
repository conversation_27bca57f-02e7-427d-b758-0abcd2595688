package com.quantchi.nanping.innovation.company.model.bo;

import com.quantchi.nanping.innovation.model.bo.PageBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/1/5 18:10
 */
@Data
public class ProductBO extends PageBO {

    @ApiModelProperty("产业链id")
    private Set<String> chainIds;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("用户实体id")
    private String entityId;

    @ApiModelProperty("用户类型")
    private Integer userType;

//    @ApiModelProperty("更新状态")
//    private String updateStatus;
}
