package com.quantchi.nanping.innovation.company.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/3/23 15:54
 */
@ApiModel("企业与产业链节点自定义挂接关系")
@Data
@TableName("company_node_custom_relation")
public class CompanyNodeCustomRelation {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("节点id")
    private String nodeId;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public CompanyNodeCustomRelation(String companyId, String chainId, String nodeId, String nodeName) {
        this.companyId = companyId;
        this.chainId = chainId;
        this.nodeId = nodeId;
        this.nodeName = nodeName;
    }
}
