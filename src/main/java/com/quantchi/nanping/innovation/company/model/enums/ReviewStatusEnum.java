package com.quantchi.nanping.innovation.company.model.enums;

/**
 * 成果登记审核状态
 *
 * <AUTHOR>
 * @date 2024/3/6 17:54
 */
public enum ReviewStatusEnum {

    PENDING_REVIEW(0, "待审核"),
    APPROVED(1, "审核通过");

    private Integer status;

    private String name;

    ReviewStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    /**
     * 按status查找
     *
     * @param status
     * @return
     */
    public static ReviewStatusEnum findByStatus(Integer status) {
        for (ReviewStatusEnum statusEnum : ReviewStatusEnum.values()) {
            if (statusEnum.status.equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }
}
