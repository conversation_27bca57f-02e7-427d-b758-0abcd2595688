package com.quantchi.nanping.innovation.company.model.bo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/30 15:46
 */
@ApiModel("人才匹配搜索条件")
@Data
public class PersonMatchBO {

    @ApiModelProperty("技术点id")
    @NotNull
    private String pointId;

    @ApiModelProperty("排序字段")
    private String sortColumn;

    @ApiModelProperty("和排序字段一起使用，是否升序")
    private Boolean isAsc;

    @ApiModelProperty("技术点ids")
    private List<String> pointIds;

}
