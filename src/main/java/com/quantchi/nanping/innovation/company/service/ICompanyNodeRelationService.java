package com.quantchi.nanping.innovation.company.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.company.model.CompanyNodeCustomRelation;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.ChainNodeBo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/23 16:01
 */
public interface ICompanyNodeRelationService extends IService<CompanyNodeCustomRelation> {

    /**
     * 查询企业/用户的节点挂接树
     *
     * @param entityId
     * @param isCompany
     * @param isExpert
     * @param onlyRelated
     * @return
     */
    List<IndustryChainNode> getTreeByEntityId(String entityId, boolean isCompany, boolean isExpert, boolean onlyRelated);

    /**
     * 保存企业挂接节点数
     *
     * @param entityId
     * @param chainId
     * @param nodeIds2Insert
     * @return
     */
    boolean save(String entityId, String chainId, Set<String> nodeIds2Insert);

    /**
     * 获得企业/用户的产业链节点标签
     *
     * @param entityId
     * @param isCompany
     * @param isExpert
     * @return
     */
    List<ChainNodeBo> getChainNodeBoByEntityId(String entityId, boolean isCompany, boolean isExpert);

    /**
     * 查询企业/用户所在的《产业链ids，产业节点ids》
     *
     * @param entityId
     * @param isCompany
     * @param isExpert
     * @return
     */
    Pair<Set<String>, Set<String>> getRelatedNodeIdsByEntityId(String entityId, boolean isCompany, boolean isExpert);
}
