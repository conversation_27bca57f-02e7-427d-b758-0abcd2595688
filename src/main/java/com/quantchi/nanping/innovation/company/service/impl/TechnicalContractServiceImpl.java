package com.quantchi.nanping.innovation.company.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.company.dao.TechnicalContractInfoDAO;
import com.quantchi.nanping.innovation.company.model.TechnicalContractInfo;
import com.quantchi.nanping.innovation.company.model.enums.CooperationModeEnum;
import com.quantchi.nanping.innovation.company.model.enums.ReviewStatusEnum;
import com.quantchi.nanping.innovation.company.model.vo.TechnicalContractSummary;
import com.quantchi.nanping.innovation.company.service.ITechnicalContractService;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.TwoLevelIndex;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/6 18:00
 */
@Service
public class TechnicalContractServiceImpl extends ServiceImpl<TechnicalContractInfoDAO, TechnicalContractInfo> implements ITechnicalContractService {

    /**
     * 换算单位：万
     */
    private static final BigDecimal UNIT_TEN_THOUSAND = new BigDecimal(10000);

    @Override
    public TechnicalContractSummary countByType() {
        List<TechnicalContractInfo> infoList = this.list(Wrappers.lambdaQuery(TechnicalContractInfo.class)
                .eq(TechnicalContractInfo::getReviewStatusType, ReviewStatusEnum.APPROVED.getStatus()));
        // 按类型进行数量统计
        Map<String, Long> countMap = infoList.stream().collect(Collectors.groupingBy(TechnicalContractInfo::getCooperationMode, Collectors.counting()));
        // 按类型进行金额统计
        Map<String, BigDecimal> amountMap = infoList.stream().filter(i -> i.getContractTransactionAmount() != null).collect(Collectors.groupingBy(TechnicalContractInfo::getCooperationMode
                , Collectors.mapping(TechnicalContractInfo::getContractTransactionAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        TechnicalContractSummary summary = new TechnicalContractSummary();
        List<TwoLevelIndex> resultIndexList = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (CooperationModeEnum modeEnum : CooperationModeEnum.values()) {
            TwoLevelIndex index = new TwoLevelIndex();
            String mode = modeEnum.getName();
            index.setName(mode);
            List<CommonIndexBO> boList = new ArrayList<>(2);
            boList.add(new CommonIndexBO("contractNum", countMap.containsKey(mode) ? countMap.get(mode) : 0, null));
            // 按万进行换算
            BigDecimal amount = amountMap.containsKey(mode) ? amountMap.get(mode) : BigDecimal.ZERO;
            amount = amount.divide(UNIT_TEN_THOUSAND, 2, RoundingMode.HALF_UP);
            totalAmount = totalAmount.add(amount);
            boList.add(new CommonIndexBO("contractAmount", amount.toString(), null));
            index.setChildList(boList);
            resultIndexList.add(index);
        }
        summary.setTotalCount(infoList.size());
        summary.setTotalAmount(totalAmount);
        summary.setIndexList(resultIndexList);
        return summary;
    }
}
