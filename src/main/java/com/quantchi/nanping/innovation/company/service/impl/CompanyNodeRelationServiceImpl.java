package com.quantchi.nanping.innovation.company.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.company.dao.CompanyNodeRelationDAO;
import com.quantchi.nanping.innovation.company.model.CompanyNodeCustomRelation;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.model.IndustryChain;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.ChainNodeBo;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/23 16:02
 */
@Service
public class CompanyNodeRelationServiceImpl extends ServiceImpl<CompanyNodeRelationDAO, CompanyNodeCustomRelation> implements ICompanyNodeRelationService {

    private static final String RELATED = "related";

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IndustryChainService industryChainService;

    @Override
    public List<IndustryChainNode> getTreeByEntityId(String entityId, boolean isCompany, boolean isExpert, boolean onlyRelated) {
        Pair<Set<String>, Set<String>> relateInfo = getRelatedNodeIdsByEntityId(entityId, isCompany, isExpert);
        Set<String> chainIds = relateInfo.getLeft();
        Set<String> relateNodeIds = relateInfo.getRight();
        if (CollectionUtils.isEmpty(chainIds)) {
            if (onlyRelated) {
                // 来自需求，罗列所有树
                return industryChainService.listAllTrees();
            }
            return new ArrayList<>(0);
        }
        List<IndustryChainNode> nodeList = industryChainService.getNodesByChainIds(chainIds);
        for (IndustryChainNode node : nodeList) {
            if (relateNodeIds.contains(node.getId())) {
                node.getProperties().put(RELATED, true);
            }
        }
        return industryChainService.buildTrees(nodeList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(String entityId, String chainId, Set<String> nodeIds2Insert) {
        // 删除原来的
        this.remove(Wrappers.lambdaQuery(CompanyNodeCustomRelation.class)
                .eq(CompanyNodeCustomRelation::getCompanyId, entityId));
        // 新增
        if (CollectionUtils.isEmpty(nodeIds2Insert)) {
            return true;
        }
        List<IndustryChainNode> targetNodeList = industryChainService.listNodeNameByNodeIds(nodeIds2Insert);
        Map<String, String> nodeNameMap = targetNodeList.stream().collect(Collectors.toMap(IndustryChainNode::getId, IndustryChainNode::getName));
        List<CompanyNodeCustomRelation> relations2Insert = new ArrayList<>(nodeIds2Insert.size());
        for (String nodeId : nodeIds2Insert) {
            relations2Insert.add(new CompanyNodeCustomRelation(entityId, chainId, nodeId, nodeNameMap.get(nodeId)));
        }
        return this.saveBatch(relations2Insert);
    }

    @Override
    public List<ChainNodeBo> getChainNodeBoByEntityId(String entityId, boolean isCompany, boolean isExpert) {
        Pair<Set<String>, Set<String>> relateInfo = getRelatedNodeIdsByEntityId(entityId, isCompany, isExpert);
        Set<String> chainIds = relateInfo.getLeft();
        Set<String> relateNodeIds = relateInfo.getRight();
        if (CollectionUtils.isEmpty(relateNodeIds) || CollectionUtils.isEmpty(chainIds)) {
            return null;
        }
        List<IndustryChain> chains = industryChainService.listByIds(chainIds);
        Map<String, String> chainMap = chains.stream().collect(Collectors.toMap(IndustryChain::getId, IndustryChain::getName));
        List<IndustryChainNode> nodeList = industryChainService.getNodeNameByNodeIds(relateNodeIds);
        List<ChainNodeBo> resultList = new ArrayList<>();
        for (IndustryChainNode node : nodeList) {
            String chanId = node.getChainId();
            resultList.add(new ChainNodeBo(chanId, chainMap.get(chanId), node.getId(), node.getName()));
        }
        return resultList;
    }

    @Override
    public Pair<Set<String>, Set<String>> getRelatedNodeIdsByEntityId(String entityId, boolean isCompany, boolean isExpert) {
        Set<String> chainIds = new HashSet<>();
        Set<String> relateNodeIds = new HashSet<>();
        if (StringUtils.isEmpty(entityId)){
            return new ImmutablePair<>(chainIds, relateNodeIds);
        }
        List<CompanyNodeCustomRelation> relations = this.list(Wrappers.lambdaQuery(CompanyNodeCustomRelation.class)
                .eq(CompanyNodeCustomRelation::getCompanyId, entityId));
        if (CollectionUtils.isEmpty(relations) && (isCompany || isExpert)) {
            // 查询ES挂接节点
            Map<String, Object> source = elasticsearchHelper.getDataById(isExpert ? EsIndexEnum.EXPERT.getEsIndex() : EsIndexEnum.COMPANY.getEsIndex(),
                    entityId, new String[]{"product_node", "chain"}, null);
            if (source == null || source.size() == 0){
                return new ImmutablePair<>(chainIds, relateNodeIds);
            }
            for (Map<String, Object> productNode : (List<Map<String, Object>>) source.get("product_node")) {
                relateNodeIds.add((String) productNode.get("id"));
            }
            for (Map<String, Object> chain : (List<Map<String, Object>>) source.get("chain")) {
                chainIds.add((String) chain.get("id"));
            }
        } else if (CollectionUtils.isNotEmpty(relations)){
            relateNodeIds = relations.stream().map(CompanyNodeCustomRelation::getNodeId).collect(Collectors.toSet());
            chainIds = relations.stream().map(CompanyNodeCustomRelation::getChainId).collect(Collectors.toSet());
        }
        return new ImmutablePair<>(chainIds, relateNodeIds);
    }

}
