package com.quantchi.nanping.innovation.company.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/5/30 14:20
 */
@ApiModel("技术点")
@Data
public class TechPoint {

    @ApiModelProperty("技术点id")
    private String pointId;

    @ApiModelProperty("技术点")
    private String point;

    @ApiModelProperty("技术点分析")
    private String pointAnalysis;

    public TechPoint(String pointId, String point, String pointAnalysis){
        this.pointId = pointId;
        this.point = point;
        this.pointAnalysis = pointAnalysis;
    }
}
