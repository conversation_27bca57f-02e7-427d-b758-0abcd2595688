package com.quantchi.nanping.innovation.company.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.company.dao.CompanyCollectionDAO;
import com.quantchi.nanping.innovation.company.model.CompanyCollection;
import com.quantchi.nanping.innovation.company.service.ICompanyCollectionService;
import com.quantchi.nanping.innovation.knowledge.center.config.EsBoostProperties;
import com.quantchi.nanping.innovation.knowledge.center.config.FieldBoostProperty;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/12 16:36
 */
@Service
public class CompanyCollectionServiceImpl extends ServiceImpl<CompanyCollectionDAO, CompanyCollection> implements ICompanyCollectionService {

    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Resource
    private EsBoostProperties esBoostProperties;

    @Autowired
    private IEs8Service es8Service;

    @Override
    public Long countBySocialCode(String socialCode) {
        return this.count(Wrappers.lambdaQuery(CompanyCollection.class)
                .eq(CompanyCollection::getSocialCreditCode, socialCode));
    }

    @Override
    public boolean collect(String dataId, String type) {
        String socialCreditCode = loginService.findCollectEntityId();
        CompanyCollection existed = find(socialCreditCode, dataId, type);
        if (existed != null) {
            throw new BusinessException("请勿重复收藏");
        }
        // 查找对应实体
        EsIndexEnum indexEnum = EsIndexEnum.getEsIndexEnumByType(type);
        String titleColumn = indexEnum.getTitleColumn().split(":")[1];
        Map<String, Object> entity = elasticsearchHelper.getDataById(indexEnum.getEsIndex(), dataId,
                Arrays.asList(titleColumn).toArray(new String[0]), null);
        CompanyCollection collection = new CompanyCollection();
        collection.setSocialCreditCode(socialCreditCode);
        collection.setCollectType(type);
        collection.setCollectId(dataId);
        String collectTitle = null;
        if (titleColumn.contains(".")){
            String[] titleColumnPath = titleColumn.split("\\.");
            collectTitle = (String) ((Map<String, Object>)entity.get(titleColumnPath[0])).get(titleColumnPath[1]);
        }else{
            collectTitle = (String) entity.get(titleColumn);
        }
        collection.setCollectTitle(collectTitle);
        collection.setId(UUID.randomUUID().toString());
        this.save(collection);
        return true;
    }

    @Override
    public Map<String, Long> getCollectionStatistics() {
        String companyId = loginService.findCollectEntityId();
        List<CompanyCollection> collections = this.list(Wrappers.lambdaQuery(CompanyCollection.class)
                .select(CompanyCollection::getId, CompanyCollection::getCollectType)
                .eq(CompanyCollection::getSocialCreditCode, companyId));
        if (CollectionUtils.isEmpty(collections)){
            Map<String, Long> resultMap = new LinkedHashMap<>();
            resultMap.put("company", 0L);
            resultMap.put("expert", 0L);
            resultMap.put("patent", 0L);
            resultMap.put("news", 0L);
            resultMap.put("policy", 0L);
            return resultMap;
        }
        return collections.stream().collect(Collectors.groupingBy(CompanyCollection::getCollectType, Collectors.counting()));
    }

    @Override
    public boolean remove(String dataId) {
        String socialCreditCode = loginService.findCollectEntityId();
        return this.remove(Wrappers.lambdaQuery(CompanyCollection.class)
                .eq(CompanyCollection::getSocialCreditCode, socialCreditCode)
                .eq(CompanyCollection::getCollectId, dataId));
    }

    @Override
    public Page<Map<String, Object>> getCollections(String type, String keyword, Integer pageSize, Integer pageNum) {
        String socialCreditCode = loginService.findCollectEntityId();
        Page<Map<String, Object>> page = this.pageMaps(new Page<>(pageNum, pageSize), Wrappers.lambdaQuery(CompanyCollection.class)
                .select(CompanyCollection::getCollectId, CompanyCollection::getCollectType, CompanyCollection::getCollectTitle)
                .eq(CompanyCollection::getSocialCreditCode, socialCreditCode)
                .eq(CompanyCollection::getCollectType, type)
                .like(StringUtils.isNotEmpty(keyword), CompanyCollection::getCollectTitle, keyword)
                .orderByDesc(CompanyCollection::getCreateTime));
        List<Map<String, Object>> collections = page.getRecords();
        if (CollectionUtils.isEmpty(collections)) {
            return page;
        }
        // ES查询对应数据
        List<String> dataIds = new ArrayList<>();
        for (Map<String, Object> collection : collections) {
            dataIds.add((String) collection.get("collect_id"));
        }
        EsIndexEnum indexEnum = EsIndexEnum.getEsIndexEnumByType(type);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.idsQuery().addIds(dataIds.toArray(new String[0])));
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, dataIds.size(), null, null, null);
        List<Map<String, Object>> dataList = null;
        if (EsIndexEnum.PATENT.getType().equals(type)){
            searchSourceBuilder.fetchSource(null, new String[]{"ti_vector","ab_vector"});
            // 查询es8
            SearchResponse searchResponse = es8Service.request(searchSourceBuilder, EsIndexEnum.PATENT_VECTOR.getEsIndex(), null, null);
            EsPageResult esPageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
            dataList = esPageResult.getList();
        }else{
            SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, indexEnum.getEsIndex());
            EsPageResult esPageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
            dataList = esPageResult.getList();
        }
        Map<String, Map<String, Object>> dataMap = new HashMap<>();
        for (Map<String, Object> data : dataList) {
            dataMap.put(String.valueOf(data.get("id")), data);
        }
        // 组装数据详情
        String titleColumn = indexEnum.getTitleColumn().split(":")[1];
        for (Map<String, Object> collection : collections) {
            String dataId = String.valueOf(collection.get("collect_id"));
            if (dataMap.containsKey(dataId)){
                collection.putAll(dataMap.get(dataId));
                collection.put("valid", true);
            }else{
                String titleContent = (String) collection.get("collect_title");
                if (titleColumn.contains(".")){
                    String[] columnLevel = titleColumn.split("\\.");
                    JSONObject columnObject = new JSONObject();
                    columnObject.put(columnLevel[1], titleContent);
                    collection.put(columnLevel[0], columnObject);
                }else{
                    collection.put(titleColumn, titleContent);
                }
                collection.put("valid", false);
                collection.put("id", dataId);
            }
            collection.put("collected", true);
        }
        return page;
    }

    @Override
    public List<CompanyCollection> listByType(String socialCode, String type) {
        return this.list(Wrappers.lambdaQuery(CompanyCollection.class)
                .eq(CompanyCollection::getSocialCreditCode, socialCode)
                .eq(CompanyCollection::getCollectType, type));
    }

    @Override
    public Page<Map<String, Object>> search(String keyword, String type, Integer pageSize, Integer pageNum) {
        String socialCreditCode = loginService.findCollectEntityId();
        List<CompanyCollection> collections = this.list(Wrappers.lambdaQuery(CompanyCollection.class)
                .eq(CompanyCollection::getSocialCreditCode, socialCreditCode)
                .eq(CompanyCollection::getCollectType, type));
        if(CollectionUtils.isEmpty(collections)){
            return new Page<>();
        }
        List<String> collectIds = collections.stream().map(CompanyCollection::getCollectId).collect(Collectors.toList());
        EsIndexEnum esIndexEnum = EsIndexEnum.getEsIndexEnumByType(type);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.idsQuery().addIds(collectIds.toArray(new String[0])));
        if (StringUtils.isNotBlank(keyword)) {
            List<FieldBoostProperty> boostList = esBoostProperties.getFieldBoostListByIndex(esIndexEnum.getEsIndex());
            com.quantchi.nanping.innovation.knowledge.center.utils.ElasticsearchBuilder.keywordQueryWithOperator(boolQuery, keyword,
                    Operator.AND, boostList);
        }
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQuery, pageNum, pageSize, null, null, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, esIndexEnum.getEsIndex());
        EsPageResult esPageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        List<Map<String, Object>> dataList = esPageResult.getList();
        // 组装数据详情
        for (Map<String, Object> collection : dataList) {
            collection.put("collected", true);
        }
        Page<Map<String, Object>> resultPage = new Page<>();
        resultPage.setTotal(esPageResult.getTotal());
        resultPage.setRecords(dataList);
        resultPage.setSize(pageSize);
        resultPage.setCurrent(pageNum);
        resultPage.setPages(resultPage.getTotal()/pageSize + 1);
        return resultPage;
    }

    private CompanyCollection find(String socialCode, String dataId, String type) {
        return this.getOne(Wrappers.lambdaQuery(CompanyCollection.class)
                .eq(CompanyCollection::getSocialCreditCode, socialCode)
                .eq(CompanyCollection::getCollectId, dataId)
                .eq(CompanyCollection::getCollectType, type));
    }
}
