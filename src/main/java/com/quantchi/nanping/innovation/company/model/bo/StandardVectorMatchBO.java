package com.quantchi.nanping.innovation.company.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 找技术标准检索条件
 *
 * <AUTHOR>
 */
@Data
public class StandardVectorMatchBO {

    @ApiModelProperty("关键词")
    private List<String> keywords;

    @ApiModelProperty("发布单位")
    private List<String> publishDepartments;

    @ApiModelProperty("需要高亮的内容")
    private List<String> highlightKeywords;

    @ApiModelProperty("技术点名称")
    private List<Map<String, String>> techPoints;

}
