package com.quantchi.nanping.innovation.company.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.company.model.IndustryProduct;
import com.quantchi.nanping.innovation.company.model.bo.ProductBO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/5 17:39
 */
public interface IIndustryProductService extends IService<IndustryProduct> {
    /**
     * 按产业链分组
     *
     * @return
     */
    Map<String, List<String>> getProductGroupByChain();

    /**
     * 分页查询
     *
     * @param productBO
     * @return
     */
    Page<IndustryProduct> page(ProductBO productBO);
}
