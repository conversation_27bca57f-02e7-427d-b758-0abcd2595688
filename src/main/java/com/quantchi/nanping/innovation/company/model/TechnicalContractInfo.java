package com.quantchi.nanping.innovation.company.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.quantchi.nanping.innovation.demand.util.EncryptTypeHandler;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/6 17:35
 */
@Data
@ApiModel("技术合同登记信息")
@TableName("technical_contract_info")
public class TechnicalContractInfo extends BaseTime {

    @TableId(type = IdType.ASSIGN_UUID)
    private String infoId;

    @ApiModelProperty("合作方式类型 0：技术开发 1：技术转让 2：技术许可 3：技术服务 4：技术咨询")
    @NotNull
    @Min(value = 0)
    @Max(value = 4)
    private Integer cooperationModeType;

    @ApiModelProperty("合作方式名称")
    private String cooperationMode;

    @ApiModelProperty("合同项目名称")
    @NotBlank
    @Length(max = 100)
    private String projectName;

    @ApiModelProperty("技术合同登记编号")
    @NotBlank
    @Length(max = 50)
    private String contractNo;

    @ApiModelProperty("登记时间")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date registrationDate;

    @ApiModelProperty("登记机构")
    @NotBlank
    @Length(max = 50)
    private String registrationOrg;

    @ApiModelProperty("转让方名称")
    @NotBlank
    @Length(max = 50)
    private String transferorName;

    @ApiModelProperty("受让方名称")
    @NotBlank
    @Length(max = 50)
    private String transfereeName;

    @ApiModelProperty("技术合同成交额(元)")
    @NotNull
    @DecimalMin(value = "0")
    @Digits(integer = 20, fraction = 0)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal contractTransactionAmount;

    @ApiModelProperty("技术交易额(元)")
    @NotNull
    @DecimalMin(value = "0")
    @Digits(integer = 20, fraction = 0)
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal transactionVolume;

    @ApiModelProperty("联系人")
    @NotBlank
    @Length(max = 50)
    private String contacts;

    @ApiModelProperty("联系方式")
    @NotBlank
    @Length(max = 50)
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String contactInfo;

    @ApiModelProperty("技术成果简介")
    @Length(min = 0, max = 1000)
    private String introduction;

    @ApiModelProperty("审核状态 0：待审核 1：审核通过")
    private Integer reviewStatusType;

    @ApiModelProperty("审核状态")
    @TableField(exist = false)
    private String reviewStatus;

}
