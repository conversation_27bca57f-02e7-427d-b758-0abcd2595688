package com.quantchi.nanping.innovation.company.model.enums;

/**
 * 企业服务侧技术分析报告标签页
 *
 * <AUTHOR>
 * @date 2023/3/24 10:32
 */
public enum EntTechReportTabEnum {

    /**
     * 概况
     */
    OVERVIEW(1, "概况"),
    /**
     * 地域分析
     */
    REGIONAL(2, "地域分析"),
    /**
     * 技术主题分析
     */
    TECHNICAL_THEME(3, "技术主题分析"),
    /**
     * 重点专利分析
     */
    KEY_PATENTS(4, "重点专利分析"),
    /**
     * 创新词云
     */
    INNOVATION_WORD_CLOUD(5, "创新词云"),
    /**
     * 技术成熟度分析
     */
    TECHNICAL_MATURITY(6, "技术成熟度分析");

    private Integer id;

    private String name;

    EntTechReportTabEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    /**
     * 按id查找
     *
     * @param id
     * @return
     */
    public static EntTechReportTabEnum findById(Integer id) {
        for (EntTechReportTabEnum indexEnum : EntTechReportTabEnum.values()) {
            if (indexEnum.id.equals(id)) {
                return indexEnum;
            }
        }
        return null;
    }
}
