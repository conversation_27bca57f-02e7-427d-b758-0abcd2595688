package com.quantchi.nanping.innovation.company.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.company.model.TechnicalContractInfo;
import com.quantchi.nanping.innovation.company.model.vo.TechnicalContractSummary;
import com.quantchi.nanping.innovation.model.bo.TwoLevelIndex;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/6 17:59
 */
public interface ITechnicalContractService extends IService<TechnicalContractInfo> {

    /**
     * 按产业链和技术合同类型统计数量和金额
     *
     * @return
     */
    TechnicalContractSummary countByType();
}
