package com.quantchi.nanping.innovation.company.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.model.bo.EntityRelateNodeBO;
import com.quantchi.nanping.innovation.company.service.ICompanyCollectionService;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.config.aop.RepeatSubmit;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.TechCommissioner;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.model.enums.BusinessType;
import com.quantchi.nanping.innovation.service.ITechCommissionerService;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/12 10:45
 */
@RestController
@Api(tags = "服务侧(专家用户)")
@RequestMapping("/expert")
@Validated
@PlatformAuthCheck(type = {"1"})
@Metrics
public class ExpertController {

    @Autowired
    private ICompanyNodeRelationService companyNodeRelationService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private ITechCommissionerService commissionerService;

    @Autowired
    ICompanyCollectionService collectionService;

    @GetMapping("/get/expert/info")
    @ApiOperation("获取专家详情")
    @Log(title = "专家首页")
    public Result getExpertInfo() {
        UserInfoEntity currentUser = sysLoginService.getUserById(StpUtil.getLoginIdAsString(), true);
        String phone = currentUser.getPhone();
        TechCommissioner expertInfo = commissionerService.getByPhone(phone);
        Map<String, Object> info = new HashMap<>();
        info.put("name", currentUser.getAccount());
        info.put("collect_num", collectionService.countBySocialCode(currentUser.getId()));
        if (expertInfo != null){
            info.put("degree", expertInfo.getDegree());
            info.put("prof_title", expertInfo.getTitle());
            info.put("id", expertInfo.getDataId());
            info.putAll(talentService.getInfoById(expertInfo.getDataId()));
        }else{
            info.putAll(talentService.getInfoById(currentUser.getId()));
        }
        return ResultConvert.success(info);
    }

    @ApiOperation("查询专家挂接节点树")
    @GetMapping("/tree")
    @PlatformAuthCheck()
    @ApiImplicitParam(name = "expertId", value = "专家id", required = true, dataType = "String")
    //@Log(title = "服务侧-查询专家挂接节点树")
    public Result<List<IndustryChainNode>> getTreeByCompanyId(@NonNull String expertId, boolean onlyRelated) {
        return ResultConvert.success(companyNodeRelationService.getTreeByEntityId(expertId, false, true, onlyRelated));
    }

    @ApiOperation("保存专家挂接节点树")
    @PostMapping("/tree/save")
    @RepeatSubmit
    @Log(title = "服务侧-保存专家挂接节点树", businessType = BusinessType.INSERT)
    public Result<Boolean> getTreeByCompanyId(@Validated @RequestBody EntityRelateNodeBO param) {
        return ResultConvert.success(companyNodeRelationService.save(param.getExpertId(), param.getChainId(), param.getNodeIds()));
    }

}
