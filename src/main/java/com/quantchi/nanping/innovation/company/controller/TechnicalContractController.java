package com.quantchi.nanping.innovation.company.controller;

import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.model.TechnicalContractInfo;
import com.quantchi.nanping.innovation.company.model.enums.CooperationModeEnum;
import com.quantchi.nanping.innovation.company.model.enums.ReviewStatusEnum;
import com.quantchi.nanping.innovation.company.service.ITechnicalContractService;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.RateLimiter;
import com.quantchi.nanping.innovation.config.aop.RepeatSubmit;
import com.quantchi.nanping.innovation.model.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/3/6 18:01
 */
@RestController
@Api(tags = "服务侧(成果交易)")
@RequestMapping("/ach_trade")
@Validated
@Metrics
public class TechnicalContractController {

    @Autowired
    private ITechnicalContractService contractService;

    @ApiOperation("提交")
    @PostMapping("/submit")
    @RepeatSubmit
    @Log(title = "服务侧-提交成果交易", businessType = BusinessType.INSERT, isSaveRequestData = false)
    @RateLimiter(count = 5)
    public Result submit(@Validated @RequestBody TechnicalContractInfo info) {
        info.setInfoId(UUID.randomUUID().toString());
        info.setCooperationMode(CooperationModeEnum.findByType(info.getCooperationModeType()).getName());
        info.setReviewStatusType(ReviewStatusEnum.PENDING_REVIEW.getStatus());
        contractService.save(info);
        return ResultConvert.success("提交成功");
    }

    @ApiOperation("技术合同成果交易统计（合同数量、合同金额）")
    @GetMapping("/count")
    @Log(title = "治理侧-科技攻关-成果交易统计", businessType = BusinessType.OTHER)
    public Result count() {
        return ResultConvert.success(contractService.countByType());
    }
}
