package com.quantchi.nanping.innovation.company.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 找技术专利检索条件
 *
 * <AUTHOR>
 */
@Data
public class PatentVectorMatchBO {

    @ApiModelProperty("关键词")
    private List<String> keywords;

    @ApiModelProperty("申请人")
    private List<String> applicants;

    @ApiModelProperty("专利名称")
    private List<String> names;

    @ApiModelProperty("需要高亮的内容")
    private List<String> highlightKeywords;

    @ApiModelProperty("技术点名称")
    private List<Map<String, String>> techPoints;

    @ApiModelProperty("产业链id")
    private String chainId;

}
