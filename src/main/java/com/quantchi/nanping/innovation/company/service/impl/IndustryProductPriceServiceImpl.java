package com.quantchi.nanping.innovation.company.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.company.dao.IndustryProductPriceDAO;
import com.quantchi.nanping.innovation.company.model.IndustryProductPrice;
import com.quantchi.nanping.innovation.company.model.bo.ProductBO;
import com.quantchi.nanping.innovation.company.service.IIndustryProductPriceService;
import com.quantchi.nanping.innovation.utils.DateUtils;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/20 16:33
 */
@Service
public class IndustryProductPriceServiceImpl extends ServiceImpl<IndustryProductPriceDAO, IndustryProductPrice> implements IIndustryProductPriceService {
//    @Override
//    public Map<String, List<String>> getProductGroupByChain() {
//        List<IndustryProductPrice> productList = this.list(Wrappers.lambdaQuery(IndustryProductPrice.class)
//                .groupBy(IndustryProductPrice::getChainId, IndustryProductPrice::getProductName));
//        return productList.stream().collect(Collectors.groupingBy(IndustryProductPrice::getChainId,
//                Collectors.mapping(IndustryProductPrice::getProductName, Collectors.toList())));
//    }
//
//    @Override
//    public Map<String, Map<String, String>> getProductPriceTend(String chainId, String productName) {
//        LocalDate now = new LocalDate();
//        LocalDate threeMonthAgo = now.minusMonths(3);
//        List<IndustryProductPrice> productList = this.list(Wrappers.lambdaQuery(IndustryProductPrice.class)
//                .eq(IndustryProductPrice::getChainId, chainId)
//                .eq(IndustryProductPrice::getProductName, productName)
//                .isNotNull(IndustryProductPrice::getPrice)
//                .ge(IndustryProductPrice::getPublishDate, threeMonthAgo.toString("yyyy-MM-dd"))
//                .le(IndustryProductPrice::getPublishDate, now.toString("yyyy-MM-dd"))
//                .orderByAsc(IndustryProductPrice::getPublishDate));
//        Map<String, Map<String, String>> resultMap = new HashMap<>();
//        for (IndustryProductPrice price: productList){
//            resultMap.computeIfAbsent(price.getGroupName(), k->new LinkedHashMap<>());
//            resultMap.get(price.getGroupName()).put(DateUtils.dateFormat(price.getPublishDate()), price.getPrice());
//        }
//        return resultMap;
//    }
//
//    @Override
//    public String getMaxPrice(String chainId, String productName) {
//        LocalDate now = new LocalDate();
//        LocalDate threeMonthAgo = now.minusMonths(3);
//        return this.getOne(Wrappers.lambdaQuery(IndustryProductPrice.class)
//                .eq(IndustryProductPrice::getChainId, chainId)
//                .eq(IndustryProductPrice::getProductName, productName)
//                .ge(IndustryProductPrice::getPublishDate, threeMonthAgo.toString("yyyy-MM-dd"))
//                .le(IndustryProductPrice::getPublishDate, now.toString("yyyy-MM-dd"))
//                .isNotNull(IndustryProductPrice::getPrice)
//                .last("order by price * 1 desc limit 1")).getPrice();
//    }
//
//    @Override
//    public String getMinPrice(String chainId, String productName) {
//        LocalDate now = new LocalDate();
//        LocalDate threeMonthAgo = now.minusMonths(3);
//        return this.getOne(Wrappers.lambdaQuery(IndustryProductPrice.class)
//                .eq(IndustryProductPrice::getChainId, chainId)
//                .eq(IndustryProductPrice::getProductName, productName)
//                .ge(IndustryProductPrice::getPublishDate, threeMonthAgo.toString("yyyy-MM-dd"))
//                .le(IndustryProductPrice::getPublishDate, now.toString("yyyy-MM-dd"))
//                .isNotNull(IndustryProductPrice::getPrice)
//                .last("order by price * 1 asc limit 1")).getPrice();
//    }
}
