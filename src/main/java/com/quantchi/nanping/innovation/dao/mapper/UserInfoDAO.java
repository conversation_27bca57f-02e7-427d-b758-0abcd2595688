package com.quantchi.nanping.innovation.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UserInfoDAO extends BaseMapper<UserInfoEntity> {

    @Select("select * from user_info where account = #{account} and password = #{password} and platform_type = #{platformType} and is_valid = 1")
    UserInfoEntity getUserInfoByAccountAndPassword(@Param("account") String account, @Param("password") String password, @Param("platformType") Integer platformType);

}
