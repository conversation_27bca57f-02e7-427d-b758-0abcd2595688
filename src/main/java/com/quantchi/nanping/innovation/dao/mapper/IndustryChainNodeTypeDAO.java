package com.quantchi.nanping.innovation.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.nanping.innovation.model.IndustryChainNodeType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/14 15:41
 */
@Mapper
public interface IndustryChainNodeTypeDAO extends BaseMapper<IndustryChainNodeType> {

    /**
     * 条件分页查询链节点
     *
     * @param page
     * @param chainId
     * @param nodeId
     * @param nodeTypeId
     * @param keyword
     * @param dbDialect
     * @return
     */
    Page<IndustryChainNodeType> page(Page page, @Param("chainId") String chainId, @Param("nodeId") String nodeId,
                                   @Param("nodeTypeId") Integer nodeTypeId, @Param("keyword") String keyword, @Param("dbDialect") String dbDialect);
}
