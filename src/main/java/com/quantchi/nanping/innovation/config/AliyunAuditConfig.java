package com.quantchi.nanping.innovation.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云内容审核配置
 *
 * <AUTHOR>
 * @date 2024/7/23 14:38
 */
@Configuration
public class AliyunAuditConfig {

    @Value("${oss.sensitive.check.accessKey}")
    private String accessKey;

    @Value("${oss.sensitive.check.accessSecretKey}")
    private String accessSecret;

    @Bean
    public com.aliyun.imageaudit20191230.Client createClient() throws Exception {
        /*
          初始化配置对象com.aliyun.teaopenapi.models.Config
          Config对象存放 AccessKeyId、AccessKeySecret、endpoint等配置
         */
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(accessKey)
                .setAccessKeySecret(accessSecret);
        // 访问的域名
        config.endpoint = "imageaudit.cn-shanghai.aliyuncs.com";
        return new com.aliyun.imageaudit20191230.Client(config);
    }
}
