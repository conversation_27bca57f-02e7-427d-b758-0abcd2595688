package com.quantchi.nanping.innovation.config.aop.aspect;

import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.config.aop.RateLimiter;
import com.quantchi.nanping.innovation.model.enums.LimitType;
import com.quantchi.nanping.innovation.utils.ServletUtils;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RateType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 限流处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@Aspect
@Component
public class RateLimiterAspect {

    @Autowired
    private StringRedisCache redisCache;

    @Before("@annotation(rateLimiter)")
    public void doBefore(final JoinPoint point, final RateLimiter rateLimiter) throws Throwable {
        final int time = rateLimiter.time();
        final int count = rateLimiter.count();
        final String combineKey = getCombineKey(rateLimiter, point);
        try {
            RateType rateType = RateType.OVERALL;
            if (rateLimiter.limitType() == LimitType.CLUSTER) {
                rateType = RateType.PER_CLIENT;
            }
            final long number = redisCache.rateLimiter(combineKey, rateType, count, time);
            if (number == -1) {
                throw new BusinessException("访问过于频繁，请稍候再试");
            }
            log.info("限制令牌 => {}, 剩余令牌 => {}, 缓存key => '{}'", count, number, combineKey);
        } catch (final BusinessException e) {
            throw e;
        } catch (final Exception e) {
            throw new RuntimeException("服务器限流异常，请稍候再试");
        }
    }

    public String getCombineKey(final RateLimiter rateLimiter, final JoinPoint point) {
        final StringBuilder stringBuffer = new StringBuilder(rateLimiter.key());
        if (rateLimiter.limitType() == LimitType.IP) {
            // 获取请求ip
            stringBuffer.append(ServletUtils.getClientIP()).append("-");
        } else if (rateLimiter.limitType() == LimitType.CLUSTER) {
            // 获取客户端实例id
            stringBuffer.append(redisCache.getClient().getId()).append("-");
        }
        final MethodSignature signature = (MethodSignature) point.getSignature();
        final Method method = signature.getMethod();
        final Class<?> targetClass = method.getDeclaringClass();
        stringBuffer.append(targetClass.getName()).append("-").append(method.getName());
        return stringBuffer.toString();
    }
}
