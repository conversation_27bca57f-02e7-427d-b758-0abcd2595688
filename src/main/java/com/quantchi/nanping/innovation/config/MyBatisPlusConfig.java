package com.quantchi.nanping.innovation.config;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.quantchi.nanping.innovation.utils.SpringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/9 5:09 下午
 * @description
 */
@Configuration
public class MyBatisPlusConfig {

    @Value("${spring.profiles.active}")
    private String profile;

    public final static ThreadLocal<String> DYNAMIC_TABLE_NAME_THREAD_LOCAL = new ThreadLocal<>();

    public static final List<String> dynamicTableNameList = Arrays.asList("node", "patent_dimension", "patent_node");

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 防止全表更新与删除
        // 针对 update 和 delete 语句
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        // 动态表名
        final DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameInnerInterceptor.setTableNameHandler((sql, tableName) -> {
            if (dynamicTableNameList.contains(tableName)) {
                final String chainEnName = DYNAMIC_TABLE_NAME_THREAD_LOCAL.get();
                if (CharSequenceUtil.isBlank(chainEnName)) {
                    return tableName;
                } else {
                    DYNAMIC_TABLE_NAME_THREAD_LOCAL.remove();
                    // 链名称加上原表名，如ningbo_new_energy_vehicle_node
                    return chainEnName + "_" + tableName;
                }
            }
            return tableName;
        });
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(profile.startsWith("prod") ? DbType.MYSQL : DbType.POSTGRE_SQL));
        return interceptor;
    }

    @Component
    public class MyFillHandler implements MetaObjectHandler {

        @Override
        public void insertFill(MetaObject metaObject) {
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
            this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        }

        @Override
        public void updateFill(MetaObject metaObject) {
            this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
        }
    }
}
