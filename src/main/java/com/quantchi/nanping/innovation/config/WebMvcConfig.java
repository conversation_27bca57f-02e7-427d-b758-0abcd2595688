package com.quantchi.nanping.innovation.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import com.quantchi.nanping.innovation.config.interceptor.AuthInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 免登路由
     */
    public static final List<String> notLoginUrls = Arrays.asList("/user/valid_login", "/user/valid_login_code", "/user/captcha", "/user/check", "/user/wechat_auth",
            "/company/get/news/list", "/company/report/paragraph", "/company/report/list", "/demand/list/portal", "/demand/submit/portal",
            "/knowledge/get_source_map", "/knowledge/getNodeSetting", "/knowledge/getNavSetting", "/knowledge/filtering", "/knowledge/recommend_page","/knowledge/detail", "/knowledge/similar",
            "/file/upload", "/common/industry/tree", "/user/valid_ca_login", "/user/ca_random", "/user/modify_pass", "/user/valid_login_1", "/user/valid_login_2","/product/*", "/common/industry/field",
            "/ach_trade/submit", "/demand/syn", "/demand/syn/finance","/chat/input", "/company/node", "/user/loginByEBus", "/patent/byCompanyId", "/user/validLoginMZT",
            "/file/preview", "/export/companyReportFromBigData");

    /**
     * 注册拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册路由拦截器，自定义认证规则
        registry.addInterceptor(new SaInterceptor(obj -> {
                    // 根据路由划分模块，不同模块不同鉴权
                    SaRouter.match("/cockpit/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/common/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/company/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/server/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/finance/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/fusionCockpit/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/geography/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/insight/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/knowledge/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/overcome/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/overview/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/insight/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/user/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/portrait/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/tech_attract/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/commission/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/demand/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/file/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/ach_trade/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/patent/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/chat/**", r -> StpUtil.checkLogin());
                    SaRouter.match("/export/**", r -> StpUtil.checkLogin());
                })).addPathPatterns("/**")
                .excludePathPatterns(notLoginUrls.toArray(new String[0]));
        // 权限拦截
        registry.addInterceptor(new AuthInterceptor()).addPathPatterns("/**")
                .excludePathPatterns(notLoginUrls.toArray(new String[0]));
    }

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        configurer.setDefaultTimeout(-1L);
    }

}
