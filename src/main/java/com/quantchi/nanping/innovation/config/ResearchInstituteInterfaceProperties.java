package com.quantchi.nanping.innovation.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 研究院接口配置类
 *
 * <AUTHOR>
 * @date 2022/11/28 10:27
 */
@ConfigurationProperties(prefix = "research.institute.interface")
@Component
@Data
public class ResearchInstituteInterfaceProperties {

    /**
     * 企业评价接口
     */
    private String entEvaluationUrl;

    /**
     * 人才评价接口
     */
    private String expertEvaluationUrl;

}
