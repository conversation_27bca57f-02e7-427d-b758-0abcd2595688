package com.quantchi.nanping.innovation.config.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import com.quantchi.nanping.innovation.config.WebMvcConfig;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.utils.HttpClientUtils;
import com.quantchi.nanping.innovation.utils.RequestContext;
import com.quantchi.nanping.innovation.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/9/30 14:04
 */
@Component
@Slf4j
public class AuthInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String ip = HttpClientUtils.getIpAddress(request);
        //RequestContext.setAttribute(RequestContext.IP, ip);
        log.error("--------------请求来源ip：{}", ip);
        if (!StpUtil.isLogin()){
            return true;
        }
        String userId = StpUtil.getLoginIdAsString();
        if (userId.startsWith("wechat")){
            if (Arrays.asList("/api/company/report/h5", "/api/company/report").contains(request.getRequestURI())){
                return true;
            }
            for (String notLoginUrl: WebMvcConfig.notLoginUrls){
                if (request.getRequestURI().contains(notLoginUrl)){
                    return true;
                }
            }
            return false;
        }
        UserInfoEntity userInfo = SpringUtils.getBean(SysLoginService.class).getUserById(userId, false);
        // 判断ip是否合法
        if (StringUtils.isNotEmpty(userInfo.getLimitedAddress())
                && !userInfo.getLimitedAddress().equals(ip)){
            return false;
        }
        // 简单设置城市，后期按照账号权限控制
        RequestContext.setAttribute(RequestContext.CITY, userInfo.getCity());
        RequestContext.setAttribute(RequestContext.AREA, userInfo.getArea());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        RequestContext.remove();
    }
}
