package com.quantchi.nanping.innovation.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/12/13 9:55
 */
@Component
@ConfigurationProperties(prefix = "inspur.oss")
@Data
@Profile({"prod","prod-server"})
public class InspurOSSProperties {

    private String keyId;

    private String keySecret;

    private String bucketName;

    private String endpoint;
}
