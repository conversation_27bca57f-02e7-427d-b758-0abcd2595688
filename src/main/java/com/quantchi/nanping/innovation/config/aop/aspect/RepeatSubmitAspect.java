package com.quantchi.nanping.innovation.config.aop.aspect;

import cn.dev33.satoken.SaManager;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.config.aop.RepeatSubmit;
import com.quantchi.nanping.innovation.model.enums.ResultCodeEnum;
import com.quantchi.nanping.innovation.utils.ServletUtils;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 防止重复提交(参考美团GTIS防重系统)
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Aspect
@Component
public class RepeatSubmitAspect {

    private static final ThreadLocal<String> KEY_CACHE = new ThreadLocal<>();

    /**
     * 防重提交 redis key
     */
    private static final String REPEAT_SUBMIT_KEY = "repeat_submit:";
    private static final ParameterNameDiscoverer NAME_DISCOVERER = new DefaultParameterNameDiscoverer();
    private static final ExpressionParser PARSER = new SpelExpressionParser();

    @Autowired
    private StringRedisCache redisCache;

    @Before("@annotation(repeatSubmit)")
    public void doBefore(final JoinPoint point, final RepeatSubmit repeatSubmit) throws Throwable {
        // 如果注解不为0 则使用注解数值
        long interval = 0;
        if (repeatSubmit.interval() > 0) {
            interval = repeatSubmit.timeUnit().toMillis(repeatSubmit.interval());
        }
        if (interval < 1000) {
            throw new BusinessException("提交间隔时间不能小于'1'秒");
        }
        final HttpServletRequest request = ServletUtils.getRequest();
        final String nowParams = argsArrayToString(point.getArgs());

        // 请求地址（作为存放cache的key值）
        final String url = request.getRequestURI();

        // 唯一值（没有消息头则使用请求地址）
        String submitKey = StringUtils.trimToEmpty(request.getHeader(SaManager.getConfig().getTokenName()));

        submitKey = SecureUtil.md5(submitKey + ":" + nowParams);
        // 唯一标识（指定key + url + 消息头）
        final String cacheRepeatKey = REPEAT_SUBMIT_KEY + url + submitKey;
        final String key = redisCache.get(cacheRepeatKey);
        if (key == null) {
            redisCache.put(cacheRepeatKey, "", Duration.ofMillis(interval).toMillis(), TimeUnit.MILLISECONDS);
            KEY_CACHE.set(cacheRepeatKey);
        } else {
            String message = repeatSubmit.message();
            if (StringUtils.startsWith(message, "{") && StringUtils.endsWith(message, "}")) {
                message = StringUtils.substring(message, 1, message.length() - 1);
            }
            throw new BusinessException(message);
        }
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(repeatSubmit)", returning = "jsonResult")
    public void doAfterReturning(final JoinPoint joinPoint, final RepeatSubmit repeatSubmit, final Object jsonResult) {
        if (jsonResult instanceof Result) {
            try {
                final Result<?> resultInfo = (Result<?>) jsonResult;
                // 成功则不删除redis数据 保证在有效时间内无法重复提交
                if (ResultCodeEnum.SUCCESS.getCode().equals(resultInfo.getHeader().getCode())) {
                    return;
                }
                redisCache.remove(KEY_CACHE.get());
            } finally {
                KEY_CACHE.remove();
            }
        }
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "@annotation(repeatSubmit)", throwing = "e")
    public void doAfterThrowing(final JoinPoint joinPoint, final RepeatSubmit repeatSubmit, final Exception e) {
        redisCache.remove(KEY_CACHE.get());
        KEY_CACHE.remove();
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(final Object[] paramsArray) {
        final StringBuilder params = new StringBuilder();
        if (paramsArray != null) {
            for (final Object o : paramsArray) {
                if (ObjectUtil.isNotNull(o) && !isFilterObject(o)) {
                    try {
                        params.append(JSONObject.toJSONString(o)).append(" ");
                    } catch (final Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return params.toString().trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        final Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            final Collection collection = (Collection) o;
            for (final Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            final Map map = (Map) o;
            for (final Object value : map.entrySet()) {
                final Map.Entry entry = (Map.Entry) value;
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }

}
