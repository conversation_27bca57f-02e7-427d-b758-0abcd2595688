package com.quantchi.nanping.innovation.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ResourceUtils;

import javax.servlet.MultipartConfigElement;
import java.io.File;
import java.io.FileNotFoundException;

/**
 * <AUTHOR>
 * @date 2022/8/1 13:30
 */
@Configuration
@Data
public class FileConfig {

    @Value("${file.upload.root}")
    private String uploadRootDirectory;

    @Value("${file.upload.dir}")
    private String uploadDirectory;

    @Value("${file.upload.temp}")
    private String uploadTempDirectory;

    @Value("${file.upload.h5.report}")
    private String h5ReportDirectory;

    @Bean
    MultipartConfigElement multipartConfigElement() throws FileNotFoundException {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        String tempDir = uploadRootDirectory + File.separator + uploadTempDirectory;
        File tmpDirFile = new File(tempDir);
        // 判断文件夹是否存在
        if (!tmpDirFile.exists()) {
            tmpDirFile.mkdirs();
        }
        factory.setLocation(tempDir);
        return factory.createMultipartConfig();
    }

    public String getUploadDirectory(){
        return uploadRootDirectory + File.separator + uploadDirectory;
    }

}
