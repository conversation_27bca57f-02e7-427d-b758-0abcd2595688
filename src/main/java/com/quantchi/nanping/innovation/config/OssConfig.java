package com.quantchi.nanping.innovation.config;

import com.inspurcloud.oss.client.impl.OSSClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 * @date 2023/9/26 11:20
 */
@Slf4j
@Configuration
@EnableCaching
@Profile({"prod","prod-server"})
public class OssConfig {

    @Autowired
    private InspurOSSProperties inspurOssProperties;

    @Bean
    public OSSClientImpl ossClientImpl(){
        return new OSSClientImpl(inspurOssProperties.getEndpoint(), inspurOssProperties.getKeyId(), inspurOssProperties.getKeySecret());
    }
}
