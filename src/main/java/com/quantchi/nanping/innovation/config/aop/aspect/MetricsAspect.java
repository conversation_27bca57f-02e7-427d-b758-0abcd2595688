package com.quantchi.nanping.innovation.config.aop.aspect;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.model.vo.ResultInfo;
import com.quantchi.nanping.innovation.utils.AESUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Objects;

/**
 * Bean的优先级设置为最高
 */
@Aspect
@Component
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class MetricsAspect {

    @Value(value = "${interface.encrypt}")
    private Integer interfaceEncrypt;

    @Pointcut("@annotation(com.quantchi.nanping.innovation.config.aop.Metrics) || @within(com.quantchi.nanping.innovation.config.aop.Metrics)")
    public void withAnnotationMetrics() {
    }

    @Around("withAnnotationMetrics()")
    public Object metrics(final ProceedingJoinPoint pjp) throws Throwable {
        final MethodSignature signature = (MethodSignature) pjp.getSignature();
        final String name = signature.toShortString();
        Object returnValue;
        final Instant start = Instant.now();
        try {
            returnValue = pjp.proceed();
            if (returnValue != null) {
                log.info("method:{},success,cost:{}ms,uri:{}",
                        name, Duration.between(start, Instant.now()).toMillis(),
                        getRequestURI());
            }
        } catch (final Exception ex) {
            log.error("method:{},fail,cost:{}ms,uri:{}", name, Duration.between(start, Instant.now()).toMillis(), getRequestURI());
            log.error(name, ex);
            throw ex;
        }
        // 如果接口不需要加密，那么直接返回
        if (!Objects.equals(interfaceEncrypt, 1)) {
            return returnValue;
        }
        if (returnValue instanceof Result) {
            final Result resultInfo = (Result) returnValue;
            resultInfo.setBody(AESUtil.encrypt(
                    JSONObject.toJSONString(resultInfo.getBody(), SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect)));
            return resultInfo;
        } else if (returnValue instanceof String) {
            if (((String) returnValue).contains("body")) {
                final JSONObject jsonObject = JSONObject.parseObject((String) returnValue);
                final Object body = jsonObject.get("body");
                jsonObject.put("body", AESUtil.encrypt(
                        JSONObject.toJSONString(body, SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect)));
                return jsonObject.toJSONString();
            } else {
                return AESUtil.encrypt(
                        JSONObject.toJSONString(returnValue, SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect));
            }
        } else if (returnValue instanceof JSONObject) {
            final JSONObject jsonObject = (JSONObject) returnValue;
            final Object body = jsonObject.get("body");
            if (body != null){
                jsonObject.put("body", AESUtil.encrypt(
                        JSONObject.toJSONString(body, SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect)));
            }
            return jsonObject;
        } else {
            return returnValue;
        }
    }

    public static String getRequestURI() {
        final HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        final String queryString = request.getQueryString();
        if (!StringUtils.hasText(queryString)) {
            return request.getRequestURI();
        }
        return request.getRequestURI() + "?" + queryString;
    }
}
