package com.quantchi.nanping.innovation.config.filter;

import com.quantchi.nanping.innovation.auth.RequestWrapper;
import com.quantchi.nanping.innovation.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/8/28 15:44
 */
@Component
@Order(-1)
@Slf4j
public class CorsFilter implements Filter {

    @Value("${spring.profiles.active}")
    private String profile;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Filter<PERSON>hain filterChain) throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        if ("testk8s".equals(profile)) {
            response.setHeader("Access-Control-Allow-Origin", "http://test.nanping-h5.quant-chi.com:30802");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Allow-Methods", "*");
            response.setHeader("Access-Control-Max-Age", "86400");
            response.setHeader("Access-Control-Allow-Headers", "*");
        }

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        RequestWrapper myRequestWrapper = null;
        // 获取请求body
        try {
            myRequestWrapper = new RequestWrapper(request);
        } catch (IOException e) {
            log.error("get request body exception", e);
            throw new RuntimeException(e);
        }
        // 如果是OPTIONS则结束请求
        if (HttpMethod.OPTIONS.toString().equals(myRequestWrapper.getMethod())) {
            response.setStatus(HttpStatus.NO_CONTENT.value());
            return;
        }
        filterChain.doFilter(myRequestWrapper, servletResponse);
    }
}
