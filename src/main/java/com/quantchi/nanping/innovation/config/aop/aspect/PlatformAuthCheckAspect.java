package com.quantchi.nanping.innovation.config.aop.aspect;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.common.exception.RedirectException;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/21 12:56
 */
@Component
@Slf4j
@Aspect
public class PlatformAuthCheckAspect {

    @Autowired
    private SysLoginService sysLoginService;

    @Pointcut("@within(com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck)")
    public void permissionPointCut() {

    }

    @Before("permissionPointCut()")
    public void beforeMethod(JoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        //请求的类名以及方法名
        Class<?> targetClass = point.getTarget().getClass();
        String methodName = signature.getName();
        Method method = null;
        try {
            method = targetClass.getMethod(methodName, signature.getParameterTypes());
        } catch (NoSuchMethodException e) {
            log.error("权限校验，对应方法不存在", e);
            return;
        }
        Set<String> types = new HashSet<>();
        if (method.getAnnotation(PlatformAuthCheck.class) != null) {
            types.addAll(Arrays.asList(method.getAnnotation(PlatformAuthCheck.class).type()));
        }else if (targetClass.getAnnotation(PlatformAuthCheck.class) != null) {
            types.addAll(Arrays.asList(targetClass.getAnnotation(PlatformAuthCheck.class).type()));
        }
        if (CollectionUtils.isEmpty(types)){
            return;
        }
        if (StpUtil.isLogin()){
            UserInfoEntity userInfo = sysLoginService.getUserInfo(false);
            if (!types.contains(String.valueOf(userInfo.getPlatformType()))){
                if (UserInfoEntity.PLATFORM_GOV == userInfo.getPlatformType()){
                    throw new RedirectException(409, "跳转服务侧登录");
                }else{
                    throw new RedirectException(408, "跳转治理侧登录");
                }
            }
        }
    }
}
