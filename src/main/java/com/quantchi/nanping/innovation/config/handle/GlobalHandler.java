package com.quantchi.nanping.innovation.config.handle;

import cn.dev33.satoken.exception.NotLoginException;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.common.exception.MessageException;
import com.quantchi.nanping.innovation.common.exception.RedirectException;
import com.quantchi.nanping.innovation.common.exception.SensitiveException;
import com.quantchi.nanping.innovation.model.enums.ResultCodeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: cslu
 * @date: 2018/10/24
 */
@ControllerAdvice
public class GlobalHandler {

    private Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 问答指定异常码
     */
    private static final Integer CHAT_ERROR_CODE = 1003;

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseBody
    public Result handleRRException(NotLoginException e, HttpServletRequest request) {
        e.printStackTrace();
        return ResultConvert.error(ResultCodeEnum.NO_LOGIN_ERROR.getCode(), ResultCodeEnum.NO_LOGIN_ERROR.getMessage());
    }

    /**
     * 处理重定向异常
     */
    @ExceptionHandler(RedirectException.class)
    @ResponseBody
    public Result handleRRException(RedirectException e, HttpServletRequest request) {
        e.printStackTrace();
        return ResultConvert.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public Result handleRRException(BusinessException e, HttpServletRequest request) {
        e.printStackTrace();
        return ResultConvert.error(isChatRequest(request) ? CHAT_ERROR_CODE : ResultCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage());
    }

    @ExceptionHandler(value = Throwable.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public String errorHandler(HttpServletRequest req, Throwable e) throws Exception {
        e.printStackTrace();
        logger.error(e.getMessage(), e);
        return e.getMessage();
    }

    /**
     * 错误消息异常
     *
     * @param e 异常对象
     * @return Result
     */
    @ExceptionHandler(MessageException.class)
    @ResponseBody
    public Result messageExceptionHandle(MessageException e, HttpServletRequest request) {
        if (e.getResultCodeEnum() != null) {
            return ResultConvert.error(e.getResultCodeEnum());
        } else {
            return ResultConvert.error(ResultCodeEnum.SYSTEM_ERROR);
        }
    }


    /**
     * 默认异常处理
     *
     * @param e 异常对象
     * @return Result
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Result otherExceptionHandle(Exception e, HttpServletRequest request) {
        e.printStackTrace();
        return ResultConvert.error(isChatRequest(request) ? CHAT_ERROR_CODE : ResultCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage());
    }

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(SensitiveException.class)
    @ResponseBody
    public Result handleSensitiveException(SensitiveException e) {
        return ResultConvert.error(ResultCodeEnum.INPUT_SENSITIVE.getCode(), ResultCodeEnum.INPUT_SENSITIVE.getMessage());
    }

    private boolean isChatRequest(HttpServletRequest request) {
        return request.getRequestURI().contains("/chat");
    }
}
