package com.quantchi.nanping.innovation.model.admin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.service.SignDataGeneration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 角色和菜单关联 sys_role_menu
 *
 * <AUTHOR> Li
 */

@Data
@TableName("sys_role_menu")
public class SysRoleMenu implements SignDataGeneration {

    /**
     * 角色ID
     */
    @TableId(type = IdType.INPUT)
    private Long roleId;

    /**
     * 菜单ID
     */
    private Long menuId;

    @ApiModelProperty("签名")
    private String sign;

    @ApiModelProperty("签名证书")
    private String signCert;

    @Override
    public String generateData2Sign() {
        return StringUtils.joinWith("_", this.roleId, this.menuId);
    }

}
