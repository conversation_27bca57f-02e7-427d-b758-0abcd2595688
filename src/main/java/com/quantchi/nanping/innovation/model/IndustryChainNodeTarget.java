package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/4/14 14:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("industry_chain_node_target")
@ApiModel(value = "IndustryChainNodeTarget对象", description = "产业链节点靶向企业")
public class IndustryChainNodeTarget {

    @ApiModelProperty(value = "主键id")
    @TableId("id")
    private Integer id;

    @ApiModelProperty(value = "产业链id")
    private String chainId;

    @ApiModelProperty(value = "产业链节点id")
    private String nodeId;

    @ApiModelProperty(value = "企业id")
    private String companyId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

}
