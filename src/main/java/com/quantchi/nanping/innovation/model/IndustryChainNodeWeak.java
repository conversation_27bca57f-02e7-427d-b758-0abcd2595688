package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/6/12 9:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("industry_chain_node_weak")
@ApiModel(value = "IndustryChainNodeWeak对象", description = "节点薄弱环节")
public class IndustryChainNodeWeak {

    @ApiModelProperty(value = "主键id")
    @TableId("id")
    private Integer id;

    @ApiModelProperty(value = "产业链id")
    private String chainId;

    @ApiModelProperty(value = "es实体id")
    private String dataId;

    @ApiModelProperty(value = "薄弱环节名称")
    private String name;

    @ApiModelProperty(value = "薄弱环节分析")
    private String description;

    @ApiModelProperty(value = "靶向企业id")
    private String companyId;

    @ApiModelProperty(value = "靶向企业名称")
    private String companyName;
}
