package com.quantchi.nanping.innovation.model.enums;


/**
 * <AUTHOR>
 * @date 2021/11/15
 * 链类型
 */
public enum ChainTypeEnum {

    /**
     * 链类型
     */
    ALL("四链融合", "20000000", 1),
    INNOVATION("创新链", "20010000", 2),
    INDUSTRY("产业链", "20020000", 3),
    EXPERT("人才链", "20030000", 4),
    FINANCE("资金链", "20040000", 5);
    /**
     * 名称
     */
    private String name;

    /**
     * 指标id
     */
    private String indexId;

    /**
     * 指标类型
     */
    private Integer indexType;


    public static ChainTypeEnum getEnumByType(Integer indexType) {
        for (ChainTypeEnum value : ChainTypeEnum.values()) {
            if (value.getIndexType().equals(indexType)) {
                return value;
            }
        }
        return null;
    }

    public static ChainTypeEnum toEnum(String name) {
        for (ChainTypeEnum value : ChainTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }

    ChainTypeEnum(String name, String indexId, Integer indexType) {
        this.name = name;
        this.indexId = indexId;
        this.indexType = indexType;
    }

    public String getName() {
        return name;
    }

    public String getIndexId() {
        return indexId;
    }

    public Integer getIndexType() {
        return indexType;
    }
}
