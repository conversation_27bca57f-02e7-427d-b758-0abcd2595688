package com.quantchi.nanping.innovation.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2023/1/4 14:10
 * 通用指标
 */
@Data
@NoArgsConstructor
public class CommonIndexVO {

    @ApiModelProperty("父级指标名称")
    private String parentName;

    @ApiModelProperty("指标名称")
    private String name;

    @ApiModelProperty("指标值")
    private Object data;

    @ApiModelProperty("单位")
    private String unit;

    public CommonIndexVO(String name, Object data) {
        this.name = name;
        this.data = data;
    }
}
