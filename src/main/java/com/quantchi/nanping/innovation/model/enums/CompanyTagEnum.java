package com.quantchi.nanping.innovation.model.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 企业标签
 *
 * <AUTHOR>
 * @date 2023/3/6 18:08
 */
public enum CompanyTagEnum {

    /**
     * 科技型中小企业
     */
    TECH_SMES("科技型中小企业", "科技型中小企业"),
    /**
     * 高新技术企业
     */
    HIGH_TECH("高新技术企业", "高新技术企业"),
    /**
     * 科技小巨人
     */
    TECH_GIANT("科技小巨人", "科技小巨人企业"),
    /**
     * 创新领军
     */
    INNOVATION_LEADER("创新领军", "科技领军企业");

    private String name;

    private String alias;

    public String getName() {
        return name;
    }

    public String getAlias() {
        return alias;
    }

    CompanyTagEnum(final String name, final String alias) {
        this.name = name;
        this.alias = alias;
    }

    /**
     * 罗列企业类型
     *
     * @return
     */
    public static List<String> listCompanyTags() {
        List<String> tags = new ArrayList<>();
        for (CompanyTagEnum indexEnum : CompanyTagEnum.values()) {
            tags.add(indexEnum.name);
        }
        return tags;
    }

    /**
     * 按名称查找
     *
     * @param name
     * @return
     */
    public static CompanyTagEnum findByName(String name) {
        for (CompanyTagEnum indexEnum : CompanyTagEnum.values()) {
            if (name.equals(indexEnum.name)) {
                return indexEnum;
            }
        }
        return null;
    }

}
