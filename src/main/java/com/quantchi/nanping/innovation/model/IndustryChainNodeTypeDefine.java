package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/4/14 14:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("industry_chain_node_type_define")
@ApiModel(value = "IndustryChainNodeTypeDefine对象", description = "强补固拓定义描述")
public class IndustryChainNodeTypeDefine {

    @ApiModelProperty(value = "主键id")
    @TableId("id")
    private Integer id;

    @ApiModelProperty(value = "产业链id")
    private String chainId;

    @ApiModelProperty(value = "产业链节点类型id：强1/补2/固3/拓4链")
    private Integer typeId;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "类型：定义define或者特征feature")
    private String type;

    @ApiModelProperty(value = "排序")
    private Integer sort;
}
