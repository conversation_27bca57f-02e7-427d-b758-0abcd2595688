package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/1/11 21:03
 */
@Data
public class IndexQuery {

    @ApiModelProperty("父父指标id")
    private String ppid;

    @ApiModelProperty("父指标id")
    private String pid;

    @ApiModelProperty("指标id")
    private String indexId;

    @ApiModelProperty("区域id")
    private String regionId;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("节点id")
    private String chainNodeId;

    @ApiModelProperty("是否查询节点")
    private boolean isNode;
}
