package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/1/12 16:09
 * 驾驶舱地图资源
 */
@Data
public class CockpitGeoBO {

    @ApiModelProperty("区域id")
    private String regionId;

    @ApiModelProperty("区域名称")
    private String regionName;

    @ApiModelProperty("四链融合指数")
    private BigDecimal fusion;

    @ApiModelProperty("企业数")
    private Long companyCount;

    @ApiModelProperty("rd占比")
    private BigDecimal rd;

    @ApiModelProperty("优势产业")
    private String advantage;
}
