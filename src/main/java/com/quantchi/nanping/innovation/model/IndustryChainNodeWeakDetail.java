package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/6/12 9:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("industry_chain_node_weak_detail")
@ApiModel(value = "IndustryChainNodeWeakDetail对象", description = "节点薄弱环节详情")
public class IndustryChainNodeWeakDetail {

    @ApiModelProperty(value = "主键id")
    @TableId("id")
    private Integer id;

    @ApiModelProperty(value = "产业链id")
    private String chainId;

    @ApiModelProperty(value = "es实体id")
    private String dataId;

    @ApiModelProperty(value = "薄弱环节名称")
    private String name;

    @ApiModelProperty(value = "技术简介")
    private String description;

    @ApiModelProperty(value = "原因分析")
    private String reasonAnalysis;
}
