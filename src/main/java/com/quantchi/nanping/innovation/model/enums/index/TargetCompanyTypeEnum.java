package com.quantchi.nanping.innovation.model.enums.index;

import java.util.ArrayList;
import java.util.List;

/**
 * 靶向企业统计指标
 *
 * <AUTHOR>
 * @date 2023/3/6 16:51
 */
public enum TargetCompanyTypeEnum {
    /**
     * 延链企业
     */
    EXTENDED_CHAIN("51200001", "延链企业"),
    /**
     * 补链企业
     */
    COMPLEMENTARY_CHAIN("51200002", "补链企业"),
    /**
     * 强链企业
     */
    STRONG_CHAIN("51200003", "强链企业");

    private String indexId;

    private String indexName;

    TargetCompanyTypeEnum(String indexId, String indexName) {
        this.indexId = indexId;
        this.indexName = indexName;
    }

    public String getIndexId() {
        return indexId;
    }

    public String getIndexName() {
        return indexName;
    }

    /**
     * 罗列所有指标id
     *
     * @return
     */
    public static List<String> listIndexIds() {
        List<String> indexIds = new ArrayList<>();
        for (TargetCompanyTypeEnum indexEnum : TargetCompanyTypeEnum.values()) {
            indexIds.add(indexEnum.indexId);
        }
        return indexIds;
    }

}
