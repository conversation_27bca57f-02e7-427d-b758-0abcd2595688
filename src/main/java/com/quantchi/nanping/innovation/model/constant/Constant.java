package com.quantchi.nanping.innovation.model.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 静态值
 *
 * <AUTHOR>
 * @since 2020/9/14
 */
public class Constant {

    public static final Map<String, String> ZJ_CITY_MAP = new HashMap<>();

    static {
        ZJ_CITY_MAP.put("division/000330100", "杭州市");
        ZJ_CITY_MAP.put("division/000330200", "宁波市");
        ZJ_CITY_MAP.put("division/000330300", "温州市");
        ZJ_CITY_MAP.put("division/000330400", "嘉兴市");
        ZJ_CITY_MAP.put("division/000330500", "湖州市");
        ZJ_CITY_MAP.put("division/000330600", "绍兴市");
        ZJ_CITY_MAP.put("division/000330700", "金华市");
        ZJ_CITY_MAP.put("division/000330800", "衢州市");
        ZJ_CITY_MAP.put("division/000330900", "舟山市");
        ZJ_CITY_MAP.put("division/000331000", "台州市");
        ZJ_CITY_MAP.put("division/000331100", "丽水市");
    }

    /**
     * 指标条件空值
     */
    public static final String INDEX_EMPTY_CONDITION = "0";

}
