package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/28 10:33 上午
 * @description
 */
@Data
public class FinanceCountBo {

    @ApiModelProperty("国内总金额")
    private BigDecimal totalAmountInCountry;

    @ApiModelProperty("国内总笔数")
    private Long totalStrokeCountInCountry;

    @ApiModelProperty("省内总金额")
    private BigDecimal totalAmountInProvince;

    @ApiModelProperty("省内总笔数")
    private Long totalStrokeCountInProvince;

    @ApiModelProperty("机构所在地区")
    private String area;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("投资领域")
    private List<String> investment;
}
