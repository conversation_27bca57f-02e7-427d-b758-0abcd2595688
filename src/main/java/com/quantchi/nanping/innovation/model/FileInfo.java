package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/1 11:09
 */
@Data
@ApiModel("文件信息")
@TableName("file_info")
public class FileInfo extends BaseTime {

    /**
     * 需求
     */
    public static final String RELATED_TYPE_DEMAND = "demand";
    /**
     * 需求跟进
     */
    public static final String RELATED_TYPE_DEMAND_PROCESS = "demand_process";
    /**
     * 需求评价
     */
    public static final String RELATED_TYPE_DEMAND_EVALUATION = "demand_evaluation";
    /**
     * 产业分析报告
     */
    public static final String RELATED_TYPE_REPORT = "report";
    /**
     * h5-产业分析报告
     */
    public static final String RELATED_TYPE_H5_REPORT = "h5_report";
    /**
     * 用户意见
     */
    public static final String RELATED_TYPE_SUGGESTION = "suggestion";

    public static final List<String> RELATED_TYPE_SYSTEM = Arrays.asList(RELATED_TYPE_REPORT, RELATED_TYPE_H5_REPORT);

    @ApiModelProperty("文件id")
    @TableId(value = "id")
    private String fileId;

    @ApiModelProperty("创建用户id")
    @TableField(value = "create_user_id")
    //@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String createUserId;

    @ApiModelProperty("文件原始名称")
    @TableField(value = "original_file_name")
    private String originalFileName;

    @ApiModelProperty("文件别名")
    @TableField(value = "file_name_alias")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String fileNameAlias;

    @ApiModelProperty("文件相对下载地址")
    @TableField(value = "relative_download_url")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String relativeDownloadUrl;

    @ApiModelProperty("文件下载地址")
    @TableField(value = "download_url")
    private String downloadUrl;

    @ApiModelProperty("关联对象id")
    @TableField(value = "related_id")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String relatedId;

    @ApiModelProperty("关联对象类型 process:需求受理流程")
    @TableField(value = "related_type")
    private String relatedType;
}