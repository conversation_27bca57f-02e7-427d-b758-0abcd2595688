package com.quantchi.nanping.innovation.model.vo;

import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/25 16:44
 */
@Data
@ApiModel("薄弱节点分析")
public class WeakNodeAnalysisVO extends IndustryChainNode {

    @ApiModelProperty("es实体id")
    private String dataId;

    @ApiModelProperty("简介")
    private String techDesc;

    @ApiModelProperty("创新得分")
    private BigDecimal innovationScore;

    @ApiModelProperty("企业数")
    private Long companyNum;

    @ApiModelProperty("关键技术数")
    private Long techNum;

    @ApiModelProperty("靶向企业")
    private List<Map<String, Object>> targetCompanyList;

    @ApiModelProperty("顺序")
    private Integer sort;
}
