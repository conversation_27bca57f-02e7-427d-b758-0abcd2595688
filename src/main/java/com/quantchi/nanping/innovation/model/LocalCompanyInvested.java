package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.quantchi.nanping.innovation.knowledge.center.utils.serializer.BigDecimalSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 11:09
 */
@Data
@TableName("local_company_invested")
public class LocalCompanyInvested extends BaseTime {

    @TableId
    private String investId;

    @ApiModelProperty("投资方")
    private String investorName;

    @ApiModelProperty("被投资企业id")
    private String investeeId;

    @ApiModelProperty("被投资企业")
    private String investeeName;

    @ApiModelProperty("股权占比")
    private BigDecimal shareholdingRatio;

    @TableField(exist = false)
    private String name;

    @TableField(exist = false)
    private Integer level;

    @TableField(exist = false)
    private String path;

    @TableField(exist = false)
    private List<LocalCompanyInvested> children;
}
