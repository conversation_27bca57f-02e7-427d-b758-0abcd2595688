package com.quantchi.nanping.innovation.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户登录对象
 */
@Data
@NoArgsConstructor
public class LoginBody {

    @NotBlank(message = "{用户名不能为空}")
    @ApiModelProperty("用户名")
    private String account;

    @NotBlank(message = "{密码不能为空}")
    @ApiModelProperty("用户密码")
    private String password;

    @NotNull(message = "登录平台不能为空")
    @ApiModelProperty("登录平台类型 0治理侧 1服务侧")
    private Integer platformType;

    @ApiModelProperty("验证码")
    private String captcha;

    @ApiModelProperty("验证码id")
    private String uid;

    @ApiModelProperty("首次验证凭证")
    private String firstValidKey;

    @ApiModelProperty("请求ip")
    private String ip;

}
