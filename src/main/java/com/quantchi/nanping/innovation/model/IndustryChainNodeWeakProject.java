package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/6/12 9:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("industry_chain_node_weak_project")
@ApiModel(value = "IndustryChainNodeWeakProject对象", description = "关键核心技术相关项目")
public class IndustryChainNodeWeakProject {

    @ApiModelProperty(value = "主键id")
    @TableId("id")
    private Integer id;

    @ApiModelProperty(value = "产业链id")
    private String chainId;

    @ApiModelProperty(value = "关键核心技术实体id")
    private String weakDataId;

    @ApiModelProperty(value = "关键核心技术实体名称")
    private String weakName;

    @ApiModelProperty(value = "企业id")
    private String companyId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "项目类别")
    private String projectType;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "资金额度(数值)")
    private Integer fundLimitAmount;

    @ApiModelProperty(value = "资金额度")
    private String fundLimit;

    @ApiModelProperty(value = "项目状态")
    private String projectState;
}
