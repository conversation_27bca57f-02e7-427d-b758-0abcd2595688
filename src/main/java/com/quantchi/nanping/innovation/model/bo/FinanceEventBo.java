package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/27 12:22 下午
 * @description
 */
@Data
public class FinanceEventBo extends PageBO{

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("所属产业")
    private String chainId;

    @ApiModelProperty("投资时间")
    private String financeDate;

    @ApiModelProperty("投资方")
    private String investor;

    @ApiModelProperty("省id")
    private String provinceId;

    @ApiModelProperty("市id")
    private String cityId;

    @ApiModelProperty("区县id")
    private String areaId;

    @ApiModelProperty("发生范围 true：市内 false：省内")
    private Integer isCity;
}
