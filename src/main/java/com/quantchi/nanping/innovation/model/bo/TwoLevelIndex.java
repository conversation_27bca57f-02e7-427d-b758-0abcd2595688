package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/1/4 14:10
 * 通用指标
 */
@Data
@NoArgsConstructor
public class TwoLevelIndex {

    @ApiModelProperty("父指标名称")
    private String name;

    @ApiModelProperty("父指标数据")
    private Object data;

    @ApiModelProperty("子指标详情")
    private List<CommonIndexBO> childList = new ArrayList<>();

    public TwoLevelIndex(String name){
        this.name = name;
    }


}
