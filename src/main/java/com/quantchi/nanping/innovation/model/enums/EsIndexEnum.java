package com.quantchi.nanping.innovation.model.enums;

import cn.hutool.extra.spring.SpringUtil;
import com.quantchi.common.core.exception.base.BusinessException;
import com.quantchi.common.core.utils.StringUtils;
import com.quantchi.nanping.innovation.common.exception.MessageException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/5/22
 * es 索引
 */
@Getter
public enum EsIndexEnum {
    /**
     * 索引集合
     */
    COMPANY("company", "企业", "nanping_innovation_company", "text:name", "field_rank:desc", true, null),
    FINANCING("financing", "融资", "nanping_innovation_company_financing", "text:financing_company.name", "financing_time:desc", true, new String[]{"id", "chain_node", "financing_company", "financing_round", "investment_agency", "financing_time", "financing_scale", "financing_amount", "financing_amount_cal", "financing_currency"}),
    BOTTLENECK("bottleneck", "风险", "nanping_innovation_bottleneck", "text:name", "sort_num:asc", true, null),
    DEMAND("demand", "需求", "nanping_innovation_demand", "text:name", "commit_date:desc", true, null),
    PROJECT("project", "项目", "nanping_innovation_project", "text:name", "start_date:desc", true, null),
//    TASK("task", "榜单", "nanping_innovation_task", "text:name", "", true, null),
    ACHIEVEMENT("achievement", "成果", "nanping_innovation_achievement", "text:name", "publish_date:desc", true, null),
    PLATFORM("platform", "平台", "nanping_innovation_platform", "text:name", "", true, null),
    NEWS("news", "资讯", "nanping_innovation_news", "text:title", "publish_time:desc", false, null),
    POLICY("policy", "政策", "nanping_innovation_policy", "text:title", "publish_time:desc", false, null),
    REPORT("report", "研报", "nanping_innovation_report", "text:name", "", false, null),
    EXPERT("expert", "人才", "nanping_innovation_expert", "text:name", "field_rank:desc", true, new String[]{"id", "logo","name", "desc","final_edu_degree", "orgs", "organization", "chain", "research_fields", "prof_title", "product_node", "match_score", "score_model", "labels", "honors", "tag", "source","research_fields"}),
    PATENT("patent", "专利", "nanping_innovation_patent", "text:name", "public_date:desc", false, new String[]{"id", "title", "public_code", "public_date","apply_date","patent_type", "name", "abstract","chain", "product_node","applicants","inventors","status"}),
    PATENT_VECTOR("patent", "专利", "nanping_innovation_patent_portrait", "text:name", "public_date:desc", false, new String[]{"id", "title", "public_code", "public_date","apply_date","patent_type", "name", "abstract","abstract_cn","chain", "product_node","applicants","inventors","status"}),
    STANDARD("standard", "标准", "nanping_innovation_standard", "text:standard_name", "publish_date:desc", false, null),
//    SOFTWARE("software", "软著", "nanping_innovation_software", "text:name", "", false, new String[]{"name", "version", "approval_date", "short_name", "registration_number"}),
//    PRODUCT("product", "产品", "nanping_innovation_company_product", "text:name", "", false, new String[]{"name", "desc"}),
    COMPANY_META("meta", "企业补充信息", "nanping_innovation_company_meta", "", "", false, null),
//    COMPANY_HONOR("honor", "企业荣誉资质", "nanping_innovation_company_meta", "", "", false, null),
//    COMPANY_INVESTMENT("invest", "全国企业对外投资", "nanping_innovation_company_meta", "", "", false, null),
//    COMPANY_CHANGE("change", "全国企业变更", "nanping_innovation_company_meta", "", "", false, null),
//    COMPANY_BRANCH("branch", "分支机构", "nanping_innovation_company_meta", "", "", false, null),
//    COMPANY_STOCK("stock", "上市信息", "nanping_innovation_company_meta", "", "", false, null),
//    COMPANY_JUDGEMENT("judgement", "裁判文书", "nanping_innovation_company_meta", "", "", false, null),
//    COMPANY_ILLEGAL("illegal", "严重违法记录", "nanping_innovation_company_meta", "", "", false, null),
//    COMPANY_PUNISH("punish", "行政处罚", "nanping_innovation_company_meta", "", "", false, null),
//    COMPANY_ABNORMAL("abnormal", "经营风险", "nanping_innovation_company_meta", "", "", false, null),
//    PATENT_INVENTOR("patentInventor", "", "", "", "", false, null),
    TEAM_POOL("teamPool", "团队测评池", "nanping_kjt_innovation_teampool", "", "total_score:desc", false, null),
    MODEL_RESULT_POOL("modelResultPool", "大模型推荐结果", "nanping_tech_ai_teampool", "", null, false, null),
    GLOBAL_PATENT("globalPatent", "全球专利", "nanping_innovation_global_patent", "", "", false, null);

    @ApiModelProperty("索引类型")
    private final String type;

    @ApiModelProperty("索引名称")
    private final String indexName;

    @ApiModelProperty("实际库里的索引")
    private final String esIndex;

    @ApiModelProperty("是否禁用")
    private Boolean disabled;

    @ApiModelProperty("库搜索字段")
    private final String[] esSearchFields;


    /**
     * 标题对应字段及格式
     */
    private final String titleColumn;

    private final String sort;

    EsIndexEnum(String type, String indexName, String esIndex, String titleColumn, String sort, Boolean disabled, String[] esSearchFields) {
        this.type = type;
        this.indexName = indexName;
        this.esIndex = esIndex;
        this.disabled = disabled;
        this.esSearchFields = esSearchFields;
        this.titleColumn = titleColumn;
        this.sort = sort;
    }

    public static String getEsIndexByType(final String type) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getEsIndex();
            }
        }
        throw new BusinessException("这个类型" + type + "找不到对应的实际索引");
    }

    public static EsIndexEnum getEsIndexByIndex(final String index) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getEsIndex().equals(index)) {
                return value;
            }
        }
        throw new BusinessException("这个索引" + index + "找不到对应的实际索引");
    }

    public static EsIndexEnum getEsIndexEnumByType(final String type) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new MessageException(ResultCodeEnum.INPUT_ERROR);
    }

    public String getEsIndex() {
        return esIndex + ("pre".equals(SpringUtil.getActiveProfile())? "_dev": StringUtils.EMPTY);
    }

}
