package com.quantchi.nanping.innovation.model.enums.index;

import java.util.ArrayList;
import java.util.List;

/**
 * 四链融合指数指标
 *
 * <AUTHOR>
 * @date 2023/3/3 17:35
 */
public enum FusionIndexEnum {

    /**
     * 综合指数指标
     */
    COMPOSITE("20000000", "四链融合指数", "综合指数", Boolean.TRUE),
    /**
     * 创新链指数指标
     */
    INNOVATION_CHAIN("20010000", "创新链指数", "", Boolean.FALSE),
    /**
     * 资金链指数指标
     */
    FUND_CHAIN("20040000", "资金链指数", "", Boolean.FALSE),
    /**
     * 人才链指数指标
     */
    PERSON_CHAIN("20030000", "人才链指数", "", Boolean.FALSE),
    /**
     * 产业链指数指标
     */
    INDUSTRY_CHAIN("20020000", "产业链指数", "", Boolean.FALSE),;
//    /**
//     * 双链融合指数指标
//     */
//    DOUBLE_CHAIN("20050000", "双链融合指数", "", Boolean.FALSE);

    private String indexId;

    private String indexName;

    private String alias;

    private Boolean parent;

    FusionIndexEnum(String indexId, String indexName, String alias, Boolean parent) {
        this.indexId = indexId;
        this.indexName = indexName;
        this.alias = alias;
        this.parent = parent;
    }

    public String getIndexId() {
        return indexId;
    }

    public String getIndexName() {
        return indexName;
    }

    public String getAlias() {
        return alias;
    }

    public Boolean getParent() {
        return parent;
    }

    /**
     * 罗列所有指标id
     *
     * @return
     */
    public static List<String> listIndexIds() {
        List<String> indexIds = new ArrayList<>();
        for (FusionIndexEnum indexEnum : FusionIndexEnum.values()) {
            indexIds.add(indexEnum.indexId);
        }
        return indexIds;
    }

    /**
     * 获取父指标
     *
     * @return
     */
    public static FusionIndexEnum getParentIndexEnum() {
        for (FusionIndexEnum indexEnum : FusionIndexEnum.values()) {
            if (indexEnum.parent) {
                return indexEnum;
            }
        }
        return null;
    }

    /**
     * 根据指标id获取名称
     *
     * @param indexId
     * @return
     */
    public static String getNameById(String indexId) {
        for (FusionIndexEnum indexEnum : FusionIndexEnum.values()) {
            if (indexEnum.indexId.equals(indexId)) {
                return indexEnum.indexName;
            }
        }
        return null;
    }

}
