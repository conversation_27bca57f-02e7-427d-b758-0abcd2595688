package com.quantchi.nanping.innovation.model.enums.index;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 关联企业指标
 *
 * <AUTHOR>
 * @date 2023/3/7 14:36
 */
public enum EntRelateIndexEnum {
    /**
     * 产业排名
     */
    INDUSTRY_CHAIN_RANK("51000001", "产业排名"),
    /**
     * 绍兴市排名
     */
    SHAOXING_RANK("51000002", "绍兴市排名"),
    /**
     * 市高新技术企业平均有效专利数
     */
    HIGH_TECH_ENT_PATENT_AVG_NUM("51000003", "市高新技术企业平均有效专利数");

    private String indexId;

    private String indexName;

    EntRelateIndexEnum(String indexId, String indexName) {
        this.indexId = indexId;
        this.indexName = indexName;
    }

    public String getIndexId() {
        return indexId;
    }

    public String getIndexName() {
        return indexName;
    }

    /**
     * 罗列科创系数面板-排名指标
     *
     * @return
     */
    public static List<String> listRankIndexIds() {
        List<EntRelateIndexEnum> targetEnums = Arrays.asList(INDUSTRY_CHAIN_RANK, SHAOXING_RANK);
        List<String> indexIds = new ArrayList<>();
        for (EntRelateIndexEnum indexEnum : targetEnums) {
            indexIds.add(indexEnum.indexId);
        }
        return indexIds;
    }

}
