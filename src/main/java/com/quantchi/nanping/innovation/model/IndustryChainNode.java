package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("industry_chain_node")
@ApiModel(value = "IndustryChainNode对象", description = "")
public class IndustryChainNode {

    @ApiModelProperty(value = "节点id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "产业链节点id")
    @TableField("chain_id")
    private String chainId;

    @ApiModelProperty(value = "上位节点id")
    @TableField("parent_id")
    private String parentId;

    @ApiModelProperty(value = "层级")
    @TableField("level")
    private Integer level;

    @ApiModelProperty(value = "完整路径")
    @TableField("path")
    private String path;

    @ApiModelProperty(value = "节点名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "节点名称,暂时当作别名")
    @TableField("name_en")
    private String nameEn;

    @ApiModelProperty(value = "节点介绍")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "是否为叶子节点，1为是，0为不是")
    @TableField("is_leaf")
    private Integer isLeaf;

    @ApiModelProperty(value = "是否为薄弱节点，1为是，0为不是")
    @TableField("is_weak")
    private Integer isWeak;

    @ApiModelProperty(value = "薄弱节点原因分析")
    @TableField("weak_reason")
    private String weakReason;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "有效标记，1位有效，0为无效")
    @TableField("is_valid")
    @TableLogic(value = "1", delval = "0")
    private Integer isValid;

    @TableField(exist = false)
    @ApiModelProperty("子节点")
    private List<IndustryChainNode> children = new ArrayList<>();

    @TableField(exist = false)
    @ApiModelProperty("扩展属性")
    private Map<String, Object> properties = new HashMap<>();

}
