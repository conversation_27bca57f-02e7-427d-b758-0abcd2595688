package com.quantchi.nanping.innovation.model.enums.index;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 区县指标
 *
 * <AUTHOR>
 * @date 2023/3/6 9:33
 */
public enum RegionIndexEnum {

    /**
     * 专利数量
     */
    PATENT_NUM("10080101", "专利数量", "", Boolean.FALSE),
    /**
     * 高新企业数
     */
    HIGH_TECH_ENT_NUM("10080102", "高新企业数", "", Boolean.FALSE),
    /**
     * 企业数量
     */
    ENT_NUM("10080103", "企业数量", "", Boolean.FALSE),
    /**
     * E类以上人才数量
     */
    E_PLUS_PERSON_NUM("10080104", "E类以上人才数量", "", Boolean.FALSE),
    /**
     * 需求数量
     */
    DEMAND_NUM("10080105", "需求数量", "", Boolean.FALSE),
    /**
     * 贷款规模
     */
    LOANS_TOTAL("10080106", "贷款规模", "", Boolean.FALSE),
    /**
     * R&D占比
     */
    R_D_RATIO("10080107", "R&D占比", "", Boolean.FALSE);

    private String indexId;

    private String indexName;

    private String alias;

    private Boolean parent;

    RegionIndexEnum(String indexId, String indexName, String alias, Boolean parent) {
        this.indexId = indexId;
        this.indexName = indexName;
        this.alias = alias;
        this.parent = parent;
    }

    public String getIndexId() {
        return indexId;
    }

    public String getIndexName() {
        return indexName;
    }

    public String getAlias() {
        return alias;
    }

    /**
     * 罗列所有指标id
     *
     * @return
     */
    public static List<String> listIndexIds() {
        List<String> indexIds = new ArrayList<>();
        for (RegionIndexEnum indexEnum : RegionIndexEnum.values()) {
            indexIds.add(indexEnum.indexId);
        }
        return indexIds;
    }

    /**
     * 罗列首页区域详情指标id
     *
     * @return
     */
    public static List<String> listIndexIds4RegionDetail() {
        List<RegionIndexEnum> targetEnums = Arrays.asList(PATENT_NUM, E_PLUS_PERSON_NUM, ENT_NUM, DEMAND_NUM, LOANS_TOTAL);
        List<String> indexIds = new ArrayList<>();
        for (RegionIndexEnum indexEnum : targetEnums) {
            indexIds.add(indexEnum.indexId);
        }
        return indexIds;
    }

    /**
     * 罗列首页区域简单指标id
     *
     * @return
     */
    public static List<String> listIndexIds4PortalRegion() {
        List<RegionIndexEnum> targetEnums = Arrays.asList(HIGH_TECH_ENT_NUM, R_D_RATIO);
        List<String> indexIds = new ArrayList<>();
        for (RegionIndexEnum indexEnum : targetEnums) {
            indexIds.add(indexEnum.indexId);
        }
        return indexIds;
    }
}
