package com.quantchi.nanping.innovation.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * CA证书用户登录对象
 */
@Data
@NoArgsConstructor
public class CALoginBody {

    @NotBlank(message = "签名不能为空")
    @ApiModelProperty("证书")
    private String cert;

    @NotBlank(message = "签名不能为空")
    @ApiModelProperty("签名")
    private String sign;

    @NotNull(message = "随机数不能为空")
    @ApiModelProperty("随机数")
    private String random;

}
