package com.quantchi.nanping.innovation.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 区域优势产业
 *
 * <AUTHOR>
 * @date 2023/5/16 10:46
 */
public enum AreaAdvantageIndustryEnum {
    /**
     * 延平区
     */
    AREA_YANPING("延平区", "division/000350702", "物联网电池"),
    /**
     * 建阳区
     */
    AREA_JIANYAN("建阳区", "division/000350703", "茶产业"),
    /**
     * 顺昌县
     */
    AREA_SHUNCHANG("顺昌县", "division/000350721", "竹产业"),
    /**
     * 浦城县
     */
    AREA_PUCHENG("浦城县", "division/000350722", "白羽鸡"),
    /**
     * 光泽县
     */
    AREA_GUANGZE("光泽县", "division/000350723", "白羽鸡"),
    /**
     * 松溪县
     */
    AREA_SONGXI("松溪县", "division/000350724", "ES纤维"),
    /**
     * 政和县
     */
    AREA_ZHENGHE("政和县", "division/000350725", "竹产业"),
    /**
     * 邵武市
     */
    AREA_SHAOWU("邵武市", "division/000350781", "氟化工"),
    /**
     * 武夷山市
     */
    AREA_WUYISHAN("武夷山市", "division/000350782", "山产业"),
    /**
     * 建瓯市
     */
    AREA_JIANOU("建瓯市", "division/000350783", "竹产业"),
    ;

    private String areaName;

    private String areaId;

    private String advantage;

    AreaAdvantageIndustryEnum(String areName, String areaId, String advantage){
        this.areaName = areName;
        this.areaId = areaId;
        this.advantage = advantage;
    }

    public String getAreaName() {
        return areaName;
    }

    public String getAreaId() {
        return areaId;
    }

    public String getAdvantage() {
        return advantage;
    }

    public static Map<String, String> getAdvantageMap(){
        Map<String, String> advantageMap = new HashMap<>();
        for (AreaAdvantageIndustryEnum e: AreaAdvantageIndustryEnum.values()){
            advantageMap.put(e.getAreaId(), e.getAdvantage());
        }
        return advantageMap;
    }
}
