package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/24 14:26
 */
@Data
@ApiModel("指标")
@TableName("index_composite")
public class IndexComposite {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("父id")
    private String pid;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("名称")
    private String nameEn;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("保留小数位数")
    private Integer digit;

    @ApiModelProperty("维度1说明")
    private String dimension1;

    @ApiModelProperty("维度2说明")
    private String dimension2;

    @ApiModelProperty("维度3说明")
    private String dimension3;

    @ApiModelProperty("备注")
    private String remark;

}
