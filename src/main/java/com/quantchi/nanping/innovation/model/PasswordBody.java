package com.quantchi.nanping.innovation.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 用户修改密码对象
 *
 * <AUTHOR> Li
 */
@Data
public class PasswordBody {

    /**
     * 用户名
     */
    @NotBlank(message = "{user.username.not.blank}")
    private String username;

    /**
     * 旧密码
     */
    private String oldPassword;

    /**
     * 新密码
     */
    private String newPassword;

    /**
     * 旧操作密码
     */
    private String oldOperatePassword;

    /**
     * 新操作密码
     */
    private String newOperatePassword;

    /**
     * 首次验证凭证
     */
    @NotNull(message = "缺少流程信息")
    private Integer procedureNum;

}
