package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/2/22 17:55
 */
@Data
@ApiModel("企业科创指数分析指标数据")
@TableName("index_company")
public class IndexCompany {

    /**
     * 行业平均标准，默认companyId值
     */
    public static final String INDUSTRY_STANDARDS = "0";

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("指标id")
    private String indexId;

    @ApiModelProperty("指标数值")
    private BigDecimal data;

    @ApiModelProperty("统计年份")
    private String statYear;

    @ApiModelProperty("指标名称")
    @TableField(exist = false)
    private String indexName;

    @ApiModelProperty("父指标名称")
    @TableField(exist = false)
    private String parentIndexName;

    @ApiModelProperty("区县名称")
    @TableField(exist = false)
    private String areaName;

}
