package com.quantchi.nanping.innovation.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2022/9/1 15:16
 */
@Data
@TableName("dm_division")
@NoArgsConstructor
@AllArgsConstructor
public class DmDivisionEntity {

    @ApiModelProperty("省市区编码")
    private String id;

    @ApiModelProperty("上级编码")
    private String parentId;

    @ApiModelProperty("省市区名称")
    private String name;

    @ApiModelProperty("省市区层级位置")
    private Integer level;
}
