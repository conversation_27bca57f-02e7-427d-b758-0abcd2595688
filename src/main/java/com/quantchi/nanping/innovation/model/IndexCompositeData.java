package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/24 17:17
 */
@Data
@ApiModel("综合指标数据表")
@TableName("index_composite_data")
public class IndexCompositeData {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("关联指标id,对应index_composite表id")
    private String indexId;

    @ApiModelProperty("区域id,对应dm_division表id")
    private String regionId;

    @ApiModelProperty("链id,对应industry_chain表id")
    private String chainId;

    @ApiModelProperty("数值")
    private BigDecimal data;

    @ApiModelProperty("维度1")
    private String dimension1;

    @ApiModelProperty("维度2")
    private String dimension2;

    @ApiModelProperty("维度3")
    private String dimension3;

    @ApiModelProperty("统计时间")
    private Date statTime;
}