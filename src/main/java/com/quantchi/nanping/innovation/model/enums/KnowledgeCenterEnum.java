package com.quantchi.nanping.innovation.model.enums;

import com.quantchi.common.core.exception.base.BusinessException;
import com.quantchi.nanping.innovation.common.exception.MessageException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/2/7
 * 知识中心es 索引
 */
@Getter
public enum KnowledgeCenterEnum {

    /**
     * 索引集合
     */
    COMPANY("company", "企业", "shaoxing_innovation_company", "text:name", "", false),
    EXPERT("expert", "人才", "shaoxing_innovation_expert", "text:name", "", false),
    PLATFORM("platform", "平台", "shaoxing_innovation_platform", "text:name", "", false),
    PROJECT("project", "项目", "shaoxing_innovation_project", "text:name", "", false),
    ACHIEVEMENT("achievement", "成果", "shaoxing_innovation_achievement", "text:name", "", false),
    POLICY("policy", "政策", "shaoxing_innovation_policy", "text:name", "", false),
    FINANCING("financing", "融资", "shaoxing_innovation_company_financing", "keyword:financing_company.name", "financing_time:desc", false),
    NEWS("news", "资讯", "shaoxing_innovation_news", "text:name", "", false),
    PATENT("patent", "专利", "shaoxing_innovation_patent", "text:name", "", false),
    ;

    @ApiModelProperty("索引类型")
    private final String type;

    @ApiModelProperty("索引名称")
    private final String indexName;

    @ApiModelProperty("实际库里的索引")
    private final String esIndex;

    @ApiModelProperty("是否禁用")
    private Boolean disabled;


    /**
     * 标题对应字段及格式
     */
    private final String titleColumn;

    private final String sort;

    KnowledgeCenterEnum(String type, String indexName, String esIndex, String titleColumn, String sort, Boolean disabled) {
        this.type = type;
        this.indexName = indexName;
        this.esIndex = esIndex;
        this.disabled = disabled;
        this.titleColumn = titleColumn;
        this.sort = sort;
    }

    public static String getEsIndexByType(final String type) {
        for (final KnowledgeCenterEnum value : KnowledgeCenterEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getEsIndex();
            }
        }
        throw new BusinessException("这个类型" + type + "找不到对应的实际索引");
    }

    public static KnowledgeCenterEnum getEsIndexByIndex(final String index) {
        for (final KnowledgeCenterEnum value : KnowledgeCenterEnum.values()) {
            if (value.getEsIndex().equals(index)) {
                return value;
            }
        }
        throw new BusinessException("这个索引" + index + "找不到对应的实际索引");
    }

    public static KnowledgeCenterEnum getEsIndexEnumByType(final String type) {
        for (final KnowledgeCenterEnum value : KnowledgeCenterEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new MessageException(ResultCodeEnum.INPUT_ERROR);
    }

}
