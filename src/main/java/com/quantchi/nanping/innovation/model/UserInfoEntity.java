package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.quantchi.nanping.innovation.service.SignDataGeneration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/9/7 14:08
 */
@Data
@TableName(value = "user_info", autoResultMap = true)
public class UserInfoEntity extends BaseTime implements SignDataGeneration {

    /**
     * 治理侧
     */
    public static final int PLATFORM_GOV = 0;

    /**
     * 服务侧
     */
    public static final int PLATFORM_USER = 1;

    /**
     * 企业用户
     */
    public static final int USER_COMPANY = 0;

    /**
     * 个人用户
     */
    public static final int USER_PERSON = 1;

    /**
     * 科特派专家
     */
    public static final int USER_EXPERT = 2;

    public static final String DISABLE = "1";

    public static final String ADMIN_ID = "1";

    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("用户名称")
    private String username;

    @ApiModelProperty("统一社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("账号密码")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String password;

    @ApiModelProperty("操作密码")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String operatePassword;

    @ApiModelProperty("更新密码时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updatePasswordTime;

    @ApiModelProperty("手机号码")
    //@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String phone;

    @ApiModelProperty("城市")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String city;

    @ApiModelProperty("区域")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String area;

    @ApiModelProperty("0政府侧 1企业侧 2治理侧管理后台")
    private Integer platformType;

    @ApiModelProperty("用户类型 0：企业 1：普通用户 2：科特派专家 3:政府侧 4：管理员")
    private Integer userType;

    @ApiModelProperty("账号状态 （0正常 1停用）")
    private String status;

    @ApiModelProperty("企业实体id")
    @TableField(exist = false)
    private String companyId;

    @ApiModelProperty("专家id")
    @TableField(exist = false)
    private String expertId;

    @ApiModelProperty("是否为链上企业")
    @TableField(exist = false)
    private boolean inChain;

    @ApiModelProperty("是否为普通用户")
    @TableField(exist = false)
    private boolean ordinary;

    @ApiModelProperty("菜单权限")
    @TableField(exist = false)
    private Map<String, Boolean> menuMap;

    /**
     * 删除标志（1代表存在 0代表删除）
     */
    @TableLogic(value = "1", delval = "0")
    private Integer isValid;

    @ApiModelProperty("CA证书序列号")
    private String caNo;

    @ApiModelProperty("签名")
    private String sign;

    @ApiModelProperty("签名证书")
    private String signCert;

    @ApiModelProperty("加密索引")
    private Integer encryptIndex;

    @ApiModelProperty("限定的IP地址")
    private String limitedAddress;

    @ApiModelProperty("登录过期时间，内容格式：[过期时间,有效过期时间]")
    private String loginTimeOut;

    @ApiModelProperty("所在产业链ids")
    @TableField(exist = false)
    private List<String> inChainIds;

    @ApiModelProperty("登录平台：zsnp-掌上南平；mzt-闵政通")
    private String platform;

    @ApiModelProperty("城市")
    private String cityName;

    @ApiModelProperty("区域")
    private String areaName;

    /**
     * 判断是否为管理员
     *
     * @return
     */
    public boolean isAdmin() {
        return "1".equals(this.getId());
    }

    @Override
    public String generateData2Sign() {
        return StringUtils.joinWith("_", this.id, this.account, this.username,
                this.city, this.area, this.status, this.userType);
    }
}
