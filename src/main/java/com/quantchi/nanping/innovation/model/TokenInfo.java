package com.quantchi.nanping.innovation.model;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TokenInfo extends SaTokenInfo {

    @ApiModelProperty("账号错误信息")
    private String accountError;

    @ApiModelProperty("密码错误信息")
    private String passwordError;

    public TokenInfo(final SaTokenInfo saTokenInfo) {
        BeanUtil.copyProperties(saTokenInfo, this);
    }
}
