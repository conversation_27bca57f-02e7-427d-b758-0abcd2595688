package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/12/29 4:30 下午
 * @description
 */
@Data
public class YearFinanceBo {

    @ApiModelProperty("投融资企业数量")
    private Long totalCompanyNum;

    @ApiModelProperty("本年新增投融资数量")
    private Long yearCompanyNum;

    @ApiModelProperty("累计投融资金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("本年新增投融资金额")
    private BigDecimal yearAmount;

    @ApiModelProperty("投融资笔数")
    private Long totalFinanceNum;

    @ApiModelProperty("本年新增投融资笔数")
    private Long yearFinanceNum;

    @ApiModelProperty("平均投融资金额")
    private BigDecimal avgFinanceAmount;

    @ApiModelProperty("本年平均投融资金额")
    private BigDecimal yearAvgFinanceAmount;

}
