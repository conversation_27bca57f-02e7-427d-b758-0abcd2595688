package com.quantchi.nanping.innovation.model.vo;

import com.quantchi.nanping.innovation.model.IndustryChainNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/12/27 14:37
 */
@Data
public class ChainNodeTreeVO {

    @ApiModelProperty(value = "节点id")
    private String id;

    @ApiModelProperty(value = "产业链id")
    private String chainId;

    @ApiModelProperty(value = "上位节点id")
    private String parentId;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "完整路径")
    private String path;

    @ApiModelProperty(value = "节点名称")
    private String name;

    @ApiModelProperty(value = "节点介绍")
    private String description;

    @ApiModelProperty(value = "是否为叶子节点，1为是，0为不是")
    private Integer isLeaf;

    @ApiModelProperty(value = "节点类型：1-产品节点，2-技术节点")
    private Integer type;

    @ApiModelProperty("节点状态")
    private String nodeStatus;

    @ApiModelProperty("四链融合指数")
    private BigDecimal fusion;

    @ApiModelProperty("高新企业数")
    private Long topCompanyCount;

    @ApiModelProperty("企业数")
    private Long externalCompanyCount;

    @ApiModelProperty("全国企业数")
    private Long nationCompanyCount;

    @ApiModelProperty("产业链类型：强链、补链、延链")
    private List<String> chainIndustryType;

    @ApiModelProperty("本地人才数")
    private Integer localExpertCount;

    @ApiModelProperty("市外人才数")
    private Integer externalExpertCount;

    @ApiModelProperty("是否有C类以上人才")
    private boolean topExpertExist;

    @ApiModelProperty("项目补助")
    private BigDecimal subsidy;

    @ApiModelProperty("投融资")
    private BigDecimal financing;

    @ApiModelProperty("是否有企业成功融资")
    private Boolean financingSuccess;

    @ApiModelProperty("是否为关键技术节点")
    private Boolean keyTech;

    @ApiModelProperty("下级")
    private List<ChainNodeTreeVO> childList = new ArrayList<>();

    /**
     * 转换IndustryChainNode类型
     * @param node
     * @return
     */
    public static ChainNodeTreeVO transfromChainNode(IndustryChainNode node){
        ChainNodeTreeVO nodeTreeVO = new ChainNodeTreeVO();
        BeanUtils.copyProperties(node, nodeTreeVO);
        if (StringUtils.isNotEmpty(node.getNameEn())){
            nodeTreeVO.setName(node.getNameEn());
        }
        return nodeTreeVO;
    }
}
