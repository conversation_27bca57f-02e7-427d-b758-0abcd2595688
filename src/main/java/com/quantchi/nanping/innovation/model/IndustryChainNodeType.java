package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/14 14:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("industry_chain_node_type")
@ApiModel(value = "IndustryChainNodeType对象", description = "")
public class IndustryChainNodeType {

    @ApiModelProperty(value = "主键id")
    @TableId("id")
    private Integer id;

    @ApiModelProperty(value = "产业链id")
    private String chainId;

    @ApiModelProperty(value = "产业链节点id")
    private String nodeId;

    @ApiModelProperty(value = "产业链节点名称")
    private String nodeName;

    @ApiModelProperty(value = "产业链节点类型：强/补/固/拓链")
    private String nodeType;

    @ApiModelProperty(value = "产业链节点类型id：强1/补2/固3/拓4链")
    private Integer typeId;

    /**
     * 维度分数
     */
//    @ApiModelProperty("政策大势")
//    private BigDecimal policyScore;
//
//    @ApiModelProperty("需求趋势")
//    private BigDecimal demandScore;
//
//    @ApiModelProperty("竞争优势")
//    private BigDecimal competitionScore;
//
//    @ApiModelProperty("要素能势")
//    private BigDecimal elementScore;
//
//    @ApiModelProperty("空间位势")
//    private BigDecimal spaceScore;

}
