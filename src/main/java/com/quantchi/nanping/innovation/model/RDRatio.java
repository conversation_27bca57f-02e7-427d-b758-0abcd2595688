package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/5/15 17:33
 */
@Data
@ApiModel("科技局南平市全社会R&D经费投入情况")
@TableName("t_kjj_rd_info")
public class RDRatio {

    @ApiModelProperty("占GDP比重（%")
    private BigDecimal ratio;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("所属区县id")
    private String areaId;

    @ApiModelProperty("所属区县")
    private String area;

    @ApiModelProperty("所属市id")
    private String cityId;

    @ApiModelProperty("所属市")
    private String city;
}
