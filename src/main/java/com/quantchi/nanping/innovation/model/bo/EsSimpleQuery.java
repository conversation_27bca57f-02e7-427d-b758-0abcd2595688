package com.quantchi.nanping.innovation.model.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/1/15 0:22
 */
@Data
public class EsSimpleQuery extends PageBO {

    @ApiModelProperty("查询库表类型:bottleneck技术、project项目、company企业")
    private String resourceType;

    @ApiModelProperty("关键字")
    private String keywords;

    @ApiModelProperty("区域id")
    private String regionId;

    @ApiModelProperty("链id")
    private String chainId;

    @ApiModelProperty("节点id")
    private String chainNodeId;

    @ApiModelProperty("区域类型(用于企业列表)：0：本市 1：本省 2：全国")
    private Integer regionType;

    @ApiModelProperty("查询条件")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private BoolQueryBuilder queryBuilder;

    @ApiModelProperty("排序分值设置")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<FunctionScoreQueryBuilder.FilterFunctionBuilder> filterFunctionBuilders;

    @ApiModelProperty("当前筛选分类")
    private String currentCategory;

    @ApiModelProperty("全部筛选分类")
    private List<String> allCategories;

    @ApiModelProperty("多字段筛选")
    private Map<String, String> keywordMap;
}
