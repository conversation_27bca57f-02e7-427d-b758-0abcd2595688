package com.quantchi.nanping.innovation.model.enums;

import cn.hutool.core.text.CharSequenceUtil;

/**
 * <AUTHOR>
 * @date 2020/5/22
 * 通用返回code
 */
public enum ResultCodeEnum {
    /**
     * 错误码 错误信息
     */
    SYSTEM_ERROR(-1, "系统错误"),

    IMPORT_ERROR(1000, "参数不能为空"),

    ANALYSIS_ERROR(1001, "分析失败"),

    PERMISSION_NOT_ALLOWED(2000, "暂无权限"),

    LOGIN_FAILED(2001, "用户名或密码错误"),

    ACCOUNT_EXIST(2002, "账号已存在"),

    NO_NODE_MATCH(300, "未匹配到节点"),

    USER_PASSWORD_ERROR(3001, "原密码错误"),

    SCORE_NOT_NULL(3002, "请填写分数"),

    BOTTLENECK_DUPLICATE(4001, "风险已存在"),

    INPUT_ERROR(101,"参数错误"),

    NO_LOGIN_ERROR(401,"Token无效"),

    SUCCESS(200,"success"),

    INPUT_SENSITIVE(1004,"用户输入包含敏感信息"),

    INPUT_UNKNOWN(1005,"不理解用户输入"),

    USER_ERROR(1, "自定义异常"),
    ;

    private Integer code;

    private String message;

    ResultCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(final String message) {
        if (CharSequenceUtil.isNotBlank(message)) {
            this.message = message;
        }
    }
}
