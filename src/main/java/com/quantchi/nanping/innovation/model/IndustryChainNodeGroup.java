package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("industry_chain_node_group")
@ApiModel(value = "IndustryChainNodeGroup对象", description = "增加分组的产业节点")
public class IndustryChainNodeGroup extends BaseTime{

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "节点id")
    @TableField("node_id")
    private String nodeId;

    @ApiModelProperty(value = "产业链节点id")
    @TableField("chain_id")
    private String chainId;

    @ApiModelProperty(value = "父节点id")
    @TableField("parent_id")
    private String parentId;

    @ApiModelProperty(value = "层级")
    @TableField("level")
    private Integer level;

    @ApiModelProperty(value = "完整路径")
    @TableField("path")
    private String path;

    @ApiModelProperty(value = "节点名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "别名")
    @TableField("alias")
    private String alias;

    @TableField(exist = false)
    @ApiModelProperty("子节点")
    private List<IndustryChainNodeGroup> childList = new ArrayList<>();

    @TableField(exist = false)
    @ApiModelProperty("本地企业数量")
    private Long localCompanyNum;

    @TableField(exist = false)
    @ApiModelProperty("外部企业数量")
    private Long externalCompanyNum;

    @TableField(exist = false)
    @ApiModelProperty("扩展属性")
    private Map<String, Object> properties = new HashMap<>();

    @TableField(exist = false)
    @ApiModelProperty("产业链类型：强链、补链、延链")
    private List<String> chainIndustryType;

}
