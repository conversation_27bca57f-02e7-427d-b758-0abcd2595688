package com.quantchi.nanping.innovation.model.admin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.service.SignDataGeneration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 用户和角色关联 sys_user_role
 *
 * <AUTHOR> Li
 */

@Data
@TableName("sys_user_role")
public class SysUserRole implements SignDataGeneration {

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    @TableId(type = IdType.INPUT)
    private String userId;

    /**
     * 角色ID
     */
    @ApiModelProperty("角色ID")
    private Long roleId;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("签名")
    private String sign;

    @ApiModelProperty("签名证书")
    private String signCert;

    @Override
    public String generateData2Sign() {
        return StringUtils.joinWith("_", this.userId, this.roleId);
    }

}
