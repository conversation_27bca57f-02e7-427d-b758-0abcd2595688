package com.quantchi.nanping.innovation.model.enums;

import com.quantchi.nanping.innovation.common.exception.MessageException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/5/22
 */
@Getter
public enum InsightTypeEnum {
    /**
     * 索引集合
     */
    COMPANY("company", "企业", "company", "", "", true),
    BOTTLENECK("bottleneck", "技术", "bottleneck", "", "", true),
    EXPERT("expert", "人才", "expert", "", "", true),
    FINANCING("financing", "投融资明细", "financing", "keyword:financing_company.name", "financing_time:desc", true),
    DEMAND("demand", "需求", "demand", "", "source_type:asc", true),
    PROJECT("project", "项目", "project", "", "project_type:asc", true),
    ACHIEVEMENT("achievement", "成果", "achievement", "", "type:asc", true),
    CHAIN_NODE("node", "链节点", "", "", "", true),
    COMPANY_IN("companyIn", "本地企业", "company", "", "", true),
    COMPANY_TARGET("companyTarget", "靶向企业", "company", "", "", true),
    COMPANY_EXTERNAL("companyExternal", "外部企业", "company", "", "", true),
    EXPERT_IN("expertIn", "本地人才", "expert", "", "", true),
    EXPERT_OUT("expertOut", "省外高层次专家", "expert", "", "", true),
    FINANCING_STOCK("financingStock", "股权融资明细", "financing", "keyword:financing_company.name", "financing_time:desc", true),
    ;

    @ApiModelProperty("类型")
    private final String type;

    @ApiModelProperty("名称")
    private final String indexName;

    @ApiModelProperty("对应EsIndexEnum的type")
    private final String esType;

    @ApiModelProperty("是否禁用")
    private Boolean disabled;


    /**
     * 标题对应字段及格式
     */
    private final String titleColumn;

    private final String sort;

    InsightTypeEnum(String type, String indexName, String esType, String titleColumn, String sort, Boolean disabled) {
        this.type = type;
        this.indexName = indexName;
        this.esType = esType;
        this.disabled = disabled;
        this.titleColumn = titleColumn;
        this.sort = sort;
    }

    public static InsightTypeEnum getEsIndexEnumByType(String type) {
        for (final InsightTypeEnum value : InsightTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new MessageException(ResultCodeEnum.INPUT_ERROR);
    }

}
