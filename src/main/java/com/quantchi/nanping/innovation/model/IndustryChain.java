package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("industry_chain")
@ApiModel(value = "IndustryChain对象", description = "")
public class IndustryChain {

    @ApiModelProperty(value = "产业链id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "产业链名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "产业链英文名称")
    @TableField("name_en")
    private String nameEn;

    @ApiModelProperty(value = "层级")
    @TableField("level")
    private Integer level;

    @ApiModelProperty(value = "上位节点id")
    @TableField("parent_id")
    private String parentId;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "是否完善，1为完善，0为不完善")
    @TableField("is_complete")
    private Boolean complete;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "有效标记，1为有效，0为无效")
    private Integer isValid;

}
