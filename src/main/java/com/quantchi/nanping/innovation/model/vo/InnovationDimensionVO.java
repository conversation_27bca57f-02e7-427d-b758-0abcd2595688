package com.quantchi.nanping.innovation.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/1 17:04
 */
@Data
@ApiModel("科创系数雷达维度")
public class InnovationDimensionVO {

    @ApiModelProperty("维度名称")
    private String dimensionName;

    @ApiModelProperty("行业平均标准数值")
    private BigDecimal industry;

    @ApiModelProperty("本企业数值")
    private BigDecimal company;

    public void setData(boolean industry, BigDecimal data){
        if (industry){
            setIndustry(data);
        }else{
            setCompany(data);
        }
    }
}
