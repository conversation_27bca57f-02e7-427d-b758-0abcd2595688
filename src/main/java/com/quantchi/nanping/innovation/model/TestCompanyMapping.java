package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/10 17:32
 */
@Data
@ApiModel("测试使用，企业账号社会信用代码映射")
@TableName("test_company_mapping")
public class TestCompanyMapping extends BaseTime{

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty("原始统一社会信用代码")
    private String originalCode;

    @ApiModelProperty("替换后的企业名称")
    private String replacedName;

    @ApiModelProperty("替换后统一社会信用代码")
    private String replacedCode;
}
