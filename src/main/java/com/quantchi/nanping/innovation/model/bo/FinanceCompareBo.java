package com.quantchi.nanping.innovation.model.bo;

import lombok.Data;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/28 10:33 上午
 * @description
 */
@Data
public class FinanceCompareBo {


    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 增长率
     */
    private BigDecimal rate;

    /**
     * 按年份统计时初始化
     *
     * @param map
     * @param pastYears
     */
    public static void initByYear(Map<String, FinanceCompareBo> map, int pastYears) {
        LocalDate localDate = new LocalDate();
        localDate = localDate.minusYears(pastYears);
        for (int i = 1; i <= pastYears; i++) {
            FinanceCompareBo tempBo = new FinanceCompareBo();
            map.put(localDate.plusYears(i).toString("yyyy"), tempBo);
        }
    }
}
