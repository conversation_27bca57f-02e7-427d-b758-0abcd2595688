package com.quantchi.nanping.innovation.model.enums;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/15
 * 瓶颈类型
 */
public enum BottleneckTypeEnum {

    /**
     * 成立时间
     */
    IN_CITY("市内有基础实现攻关突破", "市内有基础攻关", "7"),
    RISK("潜在风险", "潜在风险", "6"),
    NO_REPLACE("尚无国产替代方案", "尚无国产替代", "5"),
    OUT_PROVINCE("省外有基础实现攻关突破", "省外有基础实现攻关突破", "4"),
    IN_PROVINCE("省内有基础实现攻关突破", "省内有基础攻关", "3"),
    MAY("有可能抢占技术制高点", "有可能抢占技术制高点", "2"),
    HAVE("已实现国产化替代", "已实现国产替代", "1"),
    ;
    /**
     * 名称
     */
    private String name;

    /**
     * 简称
     */
    private String simpleName;
    /**
     * 大于年份
     */
    private String value;

    BottleneckTypeEnum(String name, String simpleName, String value) {
        this.name = name;
        this.simpleName = simpleName;
        this.value = value;
    }

    public final static List<String> getNames() {
        List<String> nameList = new ArrayList<>();
        for (BottleneckTypeEnum value : BottleneckTypeEnum.values()) {
            nameList.add(value.getName());
        }
        return nameList;
    }

    public final static String getNameByValue(String value) {
        List<String> nameList = new ArrayList<>();
        for (BottleneckTypeEnum temp : BottleneckTypeEnum.values()) {
            if (temp.getValue().equals(value)) {
                return temp.getName();
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public String getSimpleName() {
        return simpleName;
    }
}
