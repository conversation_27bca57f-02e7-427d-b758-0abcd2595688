package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/29 10:53 上午
 * @description
 */
@Data
public class CompanyPageBo {

    private String projectName;

    private String companyName;

    private String companyType;

    private String chainId;

    private String areaId;

    private Integer pageNum;

    private Integer pageSize;

    @ApiModelProperty("是否为靶向企业，非南平的上市企业视为靶向企业")
    private Boolean target;

    @ApiModelProperty("是否为薄弱环节靶向企业")
    private Boolean weakTarget;

    private String nodeTypeId;

    private List<String> nodeIdList;

    private List<String> companyIds;

}
