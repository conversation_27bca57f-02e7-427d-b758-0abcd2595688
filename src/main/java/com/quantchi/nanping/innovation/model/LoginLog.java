package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.service.SignDataGeneration;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/8/1 10:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("login_log")
@ApiModel(value = "LoginLog对象", description = "登录日志")
public class LoginLog extends BaseTime implements SignDataGeneration {

    public static final String SUCCESS = "登录成功";

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 登录用户
     */
    private String userId;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 登录状态
     */
    private String message;

    @ApiModelProperty("签名")
    private String sign;

    @ApiModelProperty("签名证书")
    private String signCert;

    @Override
    public String generateData2Sign() {
        return StringUtils.joinWith("_", this.getCreateTime(), this.userId, this.loginIp, this.message);
    }
}
