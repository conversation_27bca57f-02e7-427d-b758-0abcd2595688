package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/5/17 15:29
 */
@Data
public class EvaluationLog {

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @ApiModelProperty("测评文本")
    private String content;

    @ApiModelProperty("测评内容")
    private String jsonData;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("测评类型")
    private Integer type;
}
