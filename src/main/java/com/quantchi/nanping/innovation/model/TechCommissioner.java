package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/19 13:48
 */
@Data
@ApiModel("科技特派员")
@TableName("tech_commissioner")
public class TechCommissioner {

    @TableId(type=IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("科特派级别")
    private String level;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("服务单位")
    private String serviceObj;

    @ApiModelProperty("学历")
    private String degree;

    @ApiModelProperty("职称")
    private String title;

    @ApiModelProperty("科特派类型 0：个人 1：团队 2：法人")
    private Integer type;

    @ApiModelProperty("实体id")
    private String dataId;

    @ApiModelProperty("市id")
    private String cityId;

    @ApiModelProperty("区县id")
    private String areaId;

    @ApiModelProperty("手机号码")
    //@TableField(typeHandler = EncryptSMTypeHandler.class)
    private String phone;

    @ApiModelProperty("加密索引")
    private Integer encryptIndex;

}
