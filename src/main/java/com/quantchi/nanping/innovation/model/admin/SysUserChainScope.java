package com.quantchi.nanping.innovation.model.admin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/2/28 16:07
 */
@Data
@ApiModel("用户产业链可见范围")
@TableName("sys_user_chain_scope")
public class SysUserChainScope extends BaseTime {

    @TableId(type = IdType.ASSIGN_UUID)
    private String scopeId;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("产业链id")
    private String chainId;
}
