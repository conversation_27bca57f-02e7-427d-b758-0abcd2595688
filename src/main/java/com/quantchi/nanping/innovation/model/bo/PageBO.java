package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2021/3/5
 * 分页基础类
 */
@Data
public class PageBO {

    /**
     * 页面大小
     */
    @NotNull(message = "页面大小不能为空")
    @ApiModelProperty("页面大小")
    @DecimalMax(value = "100", message = "已超出搜索范围，请调整搜索条件")
    private Integer pageSize;

    /**
     * 页面编号
     */
    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页面编号")
    private Integer pageNum;
}
