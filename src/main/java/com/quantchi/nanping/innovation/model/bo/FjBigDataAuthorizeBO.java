package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class FjBigDataAuthorizeBO {

    @ApiModelProperty("授权书文件")
    private MultipartFile file;

    @ApiModelProperty("授权名称")
    private String authName;

    @ApiModelProperty("法人代表证件类型")
    private String certType;

    @ApiModelProperty("法人代表证件号码")
    private String certCode;

    @ApiModelProperty("法人代表姓名")
    private String certName;

    @ApiModelProperty("法人代表电话")
    private String certPhone;

    @ApiModelProperty("纳税人名称")
    private String taxpayer;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("企业社会统一信用代码")
    private String uscc;

    @ApiModelProperty("授权生效开始时间。格式：YYYYMMDD")
    private String effectiveStartTime;

    @ApiModelProperty("授权生效结束时间。格式：YYYYMMDD")
    private String effectiveEndTime;

    @ApiModelProperty("应用端协议书内部编码，每个授权书的编码要唯一")
    private String orderNo;

    @ApiModelProperty("签署协议时间。YYYY-MM-DD HH24:MI:SS")
    private String signTime;


}
