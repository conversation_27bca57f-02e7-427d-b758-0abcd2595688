package com.quantchi.nanping.innovation.model.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/1/6 3:55 下午
 * @description
 */
@Data
public class CompanyAnalyzeBo {


    /**
     * 企业总数
     */
    private Long totalNum = 0L;

    /**
     * 链主企业（数据缺失）
     */
//    private Long masterNum;

    /**
     * 科技型中小企业总数
     */
    private Long middleAndSmallNum =0L;

    /**
     * 科技型中小企业比例
     */
    private BigDecimal middleAndSmallRate;

    /**
     * 高新技术企业总数
     */
    private Long highTechNum = 0L;

    /**
     * 高新技术企业比例
     */
    private BigDecimal highTechRate;

    /**
     * 科技小巨人企业总数
     */
    private Long giantNum = 0L;

    /**
     * 科技小巨人企业比例
     */
    private BigDecimal giantNumRate;

    /**
     * 科技领军企业总数
     */
    private Long leadNum = 0L;

    /**
     * 科技领军企业比例
     */
    private BigDecimal leadNumRate;
}
