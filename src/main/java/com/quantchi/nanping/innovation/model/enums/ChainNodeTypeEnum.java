package com.quantchi.nanping.innovation.model.enums;


/**
 * 节点类型
 * <AUTHOR>
 * @date 2021/11/15
 */
public enum ChainNodeTypeEnum {

    /**
     * 强链
     */
    STRONG("强链", 1),
    /**
     * 补链
     */
    INDUSTRY("补链", 2),
    /**
     * 固链
     */
    EXPERT("固链", 3),
    /**
     * 拓链
     */
    FINANCE("拓链", 4);
    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private Integer type;


    public static Integer getTypeByName(String name) {
        for (ChainNodeTypeEnum value : ChainNodeTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getType();
            }
        }
        return null;
    }

    public static ChainNodeTypeEnum toEnum(String name) {
        for (ChainNodeTypeEnum value : ChainNodeTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }

    ChainNodeTypeEnum(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }
}
