package com.quantchi.nanping.innovation.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/3/1 17:20
 */
@Data
@ApiModel("科创排名情况")
public class InnovationRankVO {

    @ApiModelProperty("产业排名")
    private String industryRank;

    @ApiModelProperty("本市排名")
    private String cityRank;

    @ApiModelProperty("排名比例，超过多少的企业")
    private Integer rankPercent;

    @ApiModelProperty("绍兴市排名")
    private Integer rank;

    @ApiModelProperty("更新时间")
    @JSONField(format = "yyyy-MM-dd")
    private Date updateTime;

}
