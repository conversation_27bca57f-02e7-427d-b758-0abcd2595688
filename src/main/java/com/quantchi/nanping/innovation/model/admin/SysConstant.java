package com.quantchi.nanping.innovation.model.admin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/1/4 14:33
 */
@Data
@ApiModel("系统常量")
@TableName("sys_constant")
public class SysConstant {

    @TableId(type = IdType.AUTO)
    private Integer constantId;

    @ApiModelProperty(value = "常量编码")
    @NotBlank
    private String constantCode;

    @ApiModelProperty(value = "常量名称")
    @NotBlank
    private String constantName;

    @ApiModelProperty(value = "关联主体id")
    @NotBlank
    private String subjectId;

    @ApiModelProperty(value = "关联主体名称")
    @NotBlank
    private String subjectName;

    @ApiModelProperty(value = "常量内容")
    @NotBlank
    private String content;

}
