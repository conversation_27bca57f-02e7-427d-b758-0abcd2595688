package com.quantchi.nanping.innovation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/11 15:59
 */
@Data
@ApiModel("区域节点布局")
@TableName("area_node_relation")
public class AreaNodeRelation extends BaseTime{

    @TableId(type = IdType.AUTO)
    private Integer relationId;

    @ApiModelProperty("区县id")
    private String areaId;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("节点id")
    private String nodeId;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("节点排序")
    private Integer sort;
}
