package com.quantchi.nanping.innovation.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ResultInfo<T> {
    /**
     * 返回数据
     */
    @ApiModelProperty("数据")
    private T body;
    /**
     * 数据头
     */
    @ApiModelProperty("数据头")
    private CommonHeader header;

    public ResultInfo(final CommonHeader header) {
        this.header = header;
    }

    public ResultInfo(final T body, final CommonHeader header) {
        this.body = body;
        this.header = header;
    }

    public ResultInfo(final Integer code, final String message, final T body) {
        final CommonHeader commonHeader = new CommonHeader();
        commonHeader.setCode(code);
        commonHeader.setMessage(message);
        this.body = body;
        this.header = commonHeader;
    }
}
