package com.quantchi.nanping.innovation.model.admin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import com.quantchi.nanping.innovation.model.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/4 13:43
 */
@Data
@ApiModel("用户反馈建议")
@TableName("sys_usage_suggestion")
public class SysUsageSuggestion extends BaseTime {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "标题")
    @NotBlank
    @Length(min = 1, max = 100)
    private String title;

    @ApiModelProperty(value = "内容")
    @NotBlank
    @Length(min = 1, max = 500)
    private String content;

    @ApiModelProperty(value = "附件")
    @TableField(exist = false)
    private List<FileInfo> fileInfoList;

}
