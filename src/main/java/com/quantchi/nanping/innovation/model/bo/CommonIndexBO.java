package com.quantchi.nanping.innovation.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2023/1/4 14:10
 * 通用指标
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonIndexBO {

    @ApiModelProperty("指标id")
    private String id;

    @ApiModelProperty("指标名称")
    private String name;

    @ApiModelProperty("指标值")
    private Object data;

    @ApiModelProperty("单位")
    private String unit;

    public CommonIndexBO(String name, Object data) {
        this.name = name;
        this.data = data;
    }

    public CommonIndexBO(String name, Object data, String unit) {
        this.name = name;
        this.data = data;
        this.unit = unit;
    }

    public static List<CommonIndexBO> buildList(Map<String, Long> countMap){
        List<CommonIndexBO> boList = new ArrayList<>(countMap.size());
        for (Map.Entry<String, Long> count: countMap.entrySet()){
            boList.add(new CommonIndexBO(null, count.getKey(), count.getValue(), null));
        }
        Collections.sort(boList, new Comparator<CommonIndexBO>() {
            @Override
            public int compare(CommonIndexBO o1, CommonIndexBO o2) {
                if ("其他".equals(o1.getName())){
                    return 1;
                }
                return 0;
            }
        });
        return boList;
    }
}
