package com.quantchi.nanping.innovation.model.vo;

import com.quantchi.nanping.innovation.model.entity.DmDivisionEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/2 14:10
 */
@Data
public class DmDivisionVO {

    @ApiModelProperty("省市县编码")
    private String value;

    @ApiModelProperty("省市县名称")
    private String label;

    @ApiModelProperty("父节点编码")
    private String parentId;

    @ApiModelProperty("子节点列表")
    private List<DmDivisionVO> children;

    public DmDivisionVO(DmDivisionEntity entity) {
        this.value = entity.getId();
        this.label = entity.getName();
        this.parentId = entity.getParentId();
        this.children = new ArrayList<>();
    }
}
