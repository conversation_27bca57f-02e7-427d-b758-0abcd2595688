package com.quantchi.nanping.innovation.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/5/21
 * 返回值与状态码
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("通用返回数据头")
public class CommonHeader {

    /**
     * 返回状态码
     * -1:系统级错误
     * 0:正常
     * 大于0 用户级错误
     */
    @ApiModelProperty("状态码")
    private Integer code;
    /**
     * 状态码描述
     */
    @ApiModelProperty("状态码描述")
    private String message;
}
