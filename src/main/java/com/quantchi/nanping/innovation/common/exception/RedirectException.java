
package com.quantchi.nanping.innovation.common.exception;

import lombok.Data;

/**
 * 自定义异常
 */
@Data
public class RedirectException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private String msg;
    private int code = 500;

    public RedirectException(String msg) {
        super(msg);
        this.msg = msg;
    }

    public RedirectException(String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
    }

    public RedirectException(int code, String msg) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    public RedirectException(String msg, int code, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }


}
