
package com.quantchi.nanping.innovation.common.exception;

import lombok.Data;

/**
 * 大模型输入异常
 */
@Data
public class SensitiveException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private String msg;
    private int code = 1004;

    public SensitiveException(String msg) {
        super(msg);
        this.msg = msg;
    }

    public SensitiveException(String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
    }

    public SensitiveException(int code, String msg) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    public SensitiveException(String msg, int code, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }

}
