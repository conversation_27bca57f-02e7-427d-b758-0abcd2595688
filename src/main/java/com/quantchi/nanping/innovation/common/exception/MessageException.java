package com.quantchi.nanping.innovation.common.exception;


import com.quantchi.nanping.innovation.model.enums.ResultCodeEnum;

/**
 * <AUTHOR>
 * @date 2019/4/22 11:54
 * 异常消息处理
 */
public class MessageException extends RuntimeException {

    private static final long serialVersionUID = 1L;


    private ResultCodeEnum resultCodeEnum;

    public MessageException() {
    }

    public MessageException(ResultCodeEnum errCodeEnum) {
        super();
        this.setResultCodeEnum(errCodeEnum);
    }

    public MessageException(final ResultCodeEnum errCodeEnum, final String message) {
        super();
        errCodeEnum.setMessage(message);
        this.setResultCodeEnum(errCodeEnum);
    }

    public ResultCodeEnum getResultCodeEnum() {
        return resultCodeEnum;
    }

    public void setResultCodeEnum(ResultCodeEnum resultCodeEnum) {
        this.resultCodeEnum = resultCodeEnum;
    }
}
