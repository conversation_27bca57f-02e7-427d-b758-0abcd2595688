package com.quantchi.nanping.innovation.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/21
 * 通用返回值类
 *
 * @param <T>
 */

@Data
@ApiModel("通用返回数据")
public class Result<T> {

    /**
     * 返回数据
     */
    @ApiModelProperty("数据")
    private T body;
    /**
     * 数据头
     */
    @ApiModelProperty("数据头")
    private CommonHeader header;

    public Result() {
    }

    public Result(CommonHeader header) {
        this.header = header;
    }

    public Result(T body, CommonHeader header) {
        this.body = body;
        this.header = header;
    }
}
