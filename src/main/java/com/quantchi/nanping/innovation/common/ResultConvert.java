package com.quantchi.nanping.innovation.common;


import com.quantchi.nanping.innovation.model.enums.ResultCodeEnum;

/**
 * <AUTHOR>
 * @date 2020/5/21
 * 返回值包装类
 */
public class ResultConvert {
    public static final Integer SUCCESS_CODE = 200;
    public static final Integer ERROR_CODE = 0;

    public static <T> Result<T> success() {
        CommonHeader commonHeader = new CommonHeader(200, "success");
        return new Result<>(commonHeader);
    }

    public static <T> Result<T> success(T data) {
        CommonHeader commonHeader = new CommonHeader(200, "success");
        return new Result<>(data, commonHeader);
    }

    public static <T> Result<T> error(Integer code, String message) {
        CommonHeader commonHeader = new CommonHeader(code, message);
        return new Result<>(commonHeader);
    }

    public static <T> Result<T> error(ResultCodeEnum resultCodeEnum) {
        CommonHeader commonHeader = new CommonHeader(resultCodeEnum.getCode(), resultCodeEnum.getMessage());
        return new Result<>(commonHeader);
    }
}
