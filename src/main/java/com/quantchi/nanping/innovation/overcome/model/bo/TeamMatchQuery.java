package com.quantchi.nanping.innovation.overcome.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.TreeSet;

/**
 * <AUTHOR>
 * @date 2021/11/9
 * 团队测评查询
 */
@Data
public class TeamMatchQuery {

    @ApiModelProperty("测评id")
    private String demand_id;

    @ApiModelProperty("节点id列表")
    private TreeSet<String> chain_node;

    @ApiModelProperty("企业建立时间列表")
    private TreeSet<String> company_establish;

    @ApiModelProperty("企业标签列表")
    private TreeSet<String> company_label;

    @ApiModelProperty("本地企业建立时间列表")
    private TreeSet<String> local_company_establish;

    @ApiModelProperty("本地企业标签列表")
    private TreeSet<String> local_company_label;

    @ApiModelProperty("研究机构列表")
    private TreeSet<String> org_label;

    @ApiModelProperty("人才级别列表")
    private TreeSet<String> expert_level;

    @ApiModelProperty("人才职称列表")
    private TreeSet<String> expert_professional;

    @ApiModelProperty("人才标签列表")
    private TreeSet<String> expert_label;

    @ApiModelProperty("人才机构列表")
    private TreeSet<String> expert_org;
}
