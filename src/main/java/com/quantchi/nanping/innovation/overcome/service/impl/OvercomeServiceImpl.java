package com.quantchi.nanping.innovation.overcome.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.dao.mapper.EvaluationLogDAO;
import com.quantchi.nanping.innovation.demand.model.DemandConfig;
import com.quantchi.nanping.innovation.insight.model.bo.PersonStatsBo;
import com.quantchi.nanping.innovation.model.EvaluationLog;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.entity.DmDivisionEntity;
import com.quantchi.nanping.innovation.model.enums.BottleneckTypeEnum;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.InsightTypeEnum;
import com.quantchi.nanping.innovation.model.enums.ResultCodeEnum;
import com.quantchi.nanping.innovation.overcome.model.bo.*;
import com.quantchi.nanping.innovation.overcome.model.constant.EvaluationConstant;
import com.quantchi.nanping.innovation.overcome.model.enums.EstablishTypeEnum;
import com.quantchi.nanping.innovation.overcome.service.OvercomeService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.AchievementService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.service.library.ResourceService;
import com.quantchi.nanping.innovation.service.library.RiskService;
import com.quantchi.nanping.innovation.utils.*;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.model.SearchSourceQuery;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @create 2023/1/4 15:05
 */
@Service
@Slf4j
public class OvercomeServiceImpl implements OvercomeService {

    @Value("${overcome_evaluation_url}")
    private String OVERCOME_EVALUATION;

    @Resource
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private AchievementService achievementService;

    @Autowired
    private EvaluationLogDAO evaluationLogDAO;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private RiskService riskService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private StringRedisCache redisCache;

    @Override
    public List<CommonIndexBO> getKeyIndex(String chainId, String cityId, String areaId, boolean auth, boolean includeAchievement) {
        List<String> indexNameList = Arrays.asList("专利","优势产品", "STS项目", "区域发展项目");
        // 项目统计
        final BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, cityId, areaId, auth);
        queryBuilder.filter(QueryBuilders.termsQuery("project_type", Arrays.asList("STS项目", "区域发展项目")));
        final String aggregationTerm = TERMS_BUCKET_PREFIX + "type";
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(aggregationTerm).field("project_type").order(BucketOrder.count(false));
        AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PROJECT.getEsIndex(), queryBuilder, aggregationBuilder);
        Terms terms = (Terms) pageResult.getSearchResponse().getAggregations().asMap().get(aggregationTerm);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        Map<String, Long> typeCountMap = new HashMap<>();
        List<CommonIndexBO> boList = new ArrayList<>();
        for (Terms.Bucket bucket : buckets) {
            typeCountMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        // 专利和成果统计
        if (includeAchievement) {
            List<CommonIndexBO> achievementIndexList = achievementService.getAchievementTypeMap(chainId, null);
            for (CommonIndexBO bo : achievementIndexList) {
                if (!Arrays.asList("优势产品", "专利").contains(bo.getName())) {
                    continue;
                }
                typeCountMap.put(bo.getName(), (Long) bo.getData());
            }
        }
        for (String index : indexNameList) {
            boList.add(new CommonIndexBO(index, typeCountMap.containsKey(index) ? typeCountMap.get(index) : 0, null));
        }
        return boList;
    }

    @Override
    public Map<String, List<CommonIndexBO>> getStatMap(String cityId, String areaId, boolean auth) {
        Map<String, List<CommonIndexBO>> res = new HashMap<>();
        BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(null, null, cityId, areaId, auth);
        // 项目产业链分类情况
        Map<String, Long> projectAggMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.PROJECT, queryBuilder, "chain.name");
        List<CommonIndexBO> projectList = new ArrayList<>();
        for (String key : projectAggMap.keySet()) {
            projectList.add(new CommonIndexBO(key, projectAggMap.get(key)));
        }
        res.put("project", projectList);
        queryBuilder.mustNot(QueryBuilders.termQuery("source", "全国科技奖励"));
        Map<String, Long> achievementAggMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.ACHIEVEMENT, queryBuilder, "type");
        List<CommonIndexBO> achievementList = new ArrayList<>();
        for (String key : achievementAggMap.keySet()) {
            achievementList.add(new CommonIndexBO(key, achievementAggMap.get(key)));
        }
        res.put("achievement", achievementList);
        return res;
    }

    @Override
    public EsPageResult listProject(ProjectQuery projectQuery) {
        SearchSourceQuery sourceQuery = new SearchSourceQuery();
        BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(projectQuery.getChainId(), null, null, null, true);
        // 项目名称
        if (StringUtils.isNotBlank(projectQuery.getName())) {
            queryBuilder.must(QueryBuilders.matchPhraseQuery("name", projectQuery.getName()));
        }
        // 承担单位
        if (StringUtils.isNotBlank(projectQuery.getUndertakingUnit())) {
            queryBuilder.must(QueryBuilders.wildcardQuery("undertaking_unit.name", "*" + projectQuery.getUndertakingUnit() + "*"));
        }
        // 负责人
        if (StringUtils.isNotBlank(projectQuery.getDirector())) {
            queryBuilder.must(QueryBuilders.wildcardQuery("director", "*" + projectQuery.getDirector() + "*"));
        }
        // 进展状态
        if (StringUtils.isNotBlank(projectQuery.getProjectStatus())) {
            queryBuilder.must(QueryBuilders.termQuery("project_status", projectQuery.getProjectStatus()));
        }
        String index = EsIndexEnum.PROJECT.getEsIndex();
        // 项目类型
        if (Arrays.asList("优势产品", "专利").contains(projectQuery.getProjectType())) {
            index = EsIndexEnum.ACHIEVEMENT.getEsIndex();
            queryBuilder.filter(QueryBuilders.termQuery("type", projectQuery.getProjectType()));
        } else {
            EsAlterUtil.keywordsForMutiFields(queryBuilder, CommonConstant.DIVISION_NANPING.getId(), new String[]{"city.id"});
            queryBuilder.filter(QueryBuilders.termQuery("project_type", projectQuery.getProjectType()));
        }
        sourceQuery.setQueryBuilder(queryBuilder);
        sourceQuery.setPageNum(projectQuery.getPageNum());
        sourceQuery.setPageSize(projectQuery.getPageSize());
//        sourceQuery.setIncludes(new String[]{"id", "name", "chain.name", "undertaking_unit", "director", "amount", "project_status"});
        SearchSourceBuilder searchSource = ElasticsearchBuilder.buildSearchSource(sourceQuery);
        SearchResponse searchResponse = elasticsearchHelper
                .pageByFields(searchSource, index);
        EsPageResult pageResult = ElasticsearchBuilder
                .buildPageResultWithHighlight(searchResponse, null, null);
        pageResult.setPageSize(projectQuery.getPageSize());
        return pageResult;
    }

    /**
     * 首页成果类型统计
     *
     * @return
     */
    @Override
    public List<CommonIndexBO> statAchievementByType(String chainId) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(chainId)) {
            queryBuilder.filter(QueryBuilders.termsQuery("chain.id", chainId));
        }
        queryBuilder.mustNot(QueryBuilders.termQuery("source", "全国科技奖励"));
        Map<String, Long> achievementAggMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.ACHIEVEMENT, queryBuilder, "type");
        List<CommonIndexBO> achievementList = new ArrayList<>();
        for (String key : achievementAggMap.keySet()) {
            achievementList.add(new CommonIndexBO(key, achievementAggMap.get(key)));
        }
        return achievementList;
    }


    /**
     * 需求测评
     *
     * @param query
     * @return
     */
    @Override
    public Result<String> demandMatch(EvaluationQuery query) {
        Map<String, Object> demandMap = new HashMap<>();
        demandMap.put("demand_content", query.getContent());
        demandMap.put("chain_id", query.getChainId());
        demandMap.put("chain_node_id", query.getChainNodeId());
        String res = HttpClientUtils.post(OVERCOME_EVALUATION + EvaluationConstant.DEMAND_IMPORT, demandMap);
        if (StringUtils.isNotBlank(res)) {
            JSONObject resJson = JSONObject.parseObject(res);
            if (StringUtils.isBlank(resJson.getString("demand_id"))) {
                return ResultConvert.error(300, resJson.getString("message"));
            }
            return ResultConvert.success(resJson.getString("demand_id"));
        } else {
            return ResultConvert.error(ResultCodeEnum.ANALYSIS_ERROR);
        }
    }

    private String getDemandMatchResult(String content){
        Map<String, Object> demandMap = new HashMap<>();
        demandMap.put("demand_content", content);
        String res = HttpClientUtils.post(OVERCOME_EVALUATION + EvaluationConstant.DEMAND_IMPORT, demandMap);
        if (StringUtils.isNotBlank(res)) {
            JSONObject resJson = JSONObject.parseObject(res);
            if (StringUtils.isBlank(resJson.getString("demand_id"))) {
                log.error("需求测评返回异常：{}", resJson.getString("message"));
                return StringUtils.EMPTY;
            }
            return resJson.getString("demand_id");
        } else {
            return StringUtils.EMPTY;
        }
    }

    /**
     * 需求测评详情
     *
     * @param id
     * @return
     */
    @Override
    public Result<JSONObject> demandGet(String id) {
        // 放置设定的分数
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        String res = HttpClientUtils.post(OVERCOME_EVALUATION + EvaluationConstant.DEMAND_GET, map);
        if (StringUtils.isNotBlank(res)) {
            String demandConfigStr = redisCache.get("report:" + id);
            DemandConfig demandConfig = null;
            if (StringUtils.isNotBlank(demandConfigStr)){
                demandConfig = JSONObject.parseObject(demandConfigStr, DemandConfig.class);
            }
            JSONObject resJson = JSONObject.parseObject(res);
            addOverAll(resJson, demandConfig);
            return ResultConvert.success(resJson);
        } else {
            return ResultConvert.success();
        }
    }

    /**
     * 简版需求测评
     *
     * @param query
     * @return
     */
    @Override
    public Result<String> demandSimpleMatch(EvaluationQuery query) {
        EvaluationLog demandImportLog = new EvaluationLog();
        String uuid = UUID.randomUUID().toString().trim().replaceAll("-", "");
        demandImportLog.setId(uuid);
        demandImportLog.setContent(query.getContent());
        demandImportLog.setType(2);
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("demand_content", query.getContent());
        String matchRes = HttpClientUtils.post(OVERCOME_EVALUATION + EvaluationConstant.DEMAND_SIMPLE_GET, queryMap);
        JSONObject matchResJson = JSON.parseObject(matchRes);
        matchResJson.put("similar_demand_count", matchResJson.getJSONArray("similar_demand").size());
        matchResJson.put("project_zj_count", matchResJson.getJSONArray("project_zj").size());
        matchResJson.put("project_nsfc_count", matchResJson.getJSONArray("project_nsfc").size());
        matchResJson.put("risk_count", matchResJson.getJSONArray("risk").size());
        demandImportLog.setJsonData(matchResJson.toJSONString());
        evaluationLogDAO.insert(demandImportLog);
        return ResultConvert.success(uuid);
    }

    /**
     * 需求测评详情
     *
     * @param id
     * @return
     */
    @Override
    public Result<JSONObject> demandSimpleGet(String id) {
        EvaluationLog log = evaluationLogDAO.selectById(id);
        if (log == null) {
            return ResultConvert.error(ResultCodeEnum.ANALYSIS_ERROR);
        }
        //隐藏断供断链风险
        JSONObject jsonRes = JSONObject.parseObject(log.getJsonData());
        return ResultConvert.success(jsonRes);
    }

    private void addOverAll(JSONObject resJson, DemandConfig demandConfig) {
        if (resJson == null) {
            return;
        }
        //1.新增综述链信息
        Set<String> chainSet = new LinkedHashSet<>();
        if (resJson.getJSONArray("parse_res") != null) {
            JSONArray parseRes = resJson.getJSONArray("parse_res");
            for (int i = 0; i < parseRes.size(); i++) {
                chainSet.add(resJson.getJSONArray("parse_res").getJSONObject(i).getString("chain_name"));
            }
            // 按照score进行降序
            Collections.sort(parseRes, new Comparator<JSONObject>(){
                @Override
                public int compare(JSONObject n1, JSONObject n2) {
                    return n2.getInteger("score").compareTo(n1.getInteger("score"));
                }
            });
        }
        resJson.put("overall_chain", chainSet);
        //2.新增综述企业类型
        Map<String, Integer> companyNumMap = new HashMap<>();
        if (resJson.getJSONArray("company_res") != null) {
            for (int i = 0; i < resJson.getJSONArray("company_res").size(); i++) {
                JSONArray companyAry = resJson.getJSONArray("company_res").getJSONObject(i).getJSONArray("company");
                if (companyAry != null) {
                    for (int j = 0; j < companyAry.size(); j++) {
                        if (companyAry.getJSONObject(j).getJSONArray("tag_agg") != null) {
                            for (int k = 0; k < companyAry.getJSONObject(j).getJSONArray("tag_agg").size(); k++) {
                                JSONObject aggObject = companyAry.getJSONObject(j).getJSONArray("tag_agg").getJSONObject(k);
                                String tagKey = aggObject.getString("tag");
                                companyNumMap.computeIfAbsent(tagKey, c -> new Integer(0));
                                companyNumMap.put(tagKey, companyNumMap.get(tagKey) + aggObject.getInteger("count"));
                            }
                        }
                    }
                }
            }
        }
        //3.手动去掉断供断链维度
        JSONObject evaluateRes = resJson.getJSONObject("evaluate_res");
        if (evaluateRes != null) {
            JSONArray indicator = evaluateRes.getJSONArray("indicator");
            if (indicator != null) {
                for (int i = 0; i < indicator.size(); i++) {
                    JSONObject indicatorTemp = indicator.getJSONObject(i);
                    if (indicatorTemp.toString().contains("断供断链")) {
                        indicator.remove(i);
                        break;
                    }
                }
                // 替换分数
                if (demandConfig != null){
                    for (int i = 0; i < indicator.size(); i++){
                        JSONObject indicatorTemp = indicator.getJSONObject(i);
                        switch(indicatorTemp.getString("name")){
                            case "战略需求匹配":
                                if (demandConfig.getDemandMatchScore() != null && demandConfig.getDemandMatchScore() > 0){
                                    indicatorTemp.put("percent", BigDecimalUtil.divide(new BigDecimal(demandConfig.getDemandMatchScore()), new BigDecimal(27), 2, RoundingMode.HALF_UP));
                                }
                                indicatorTemp.put("score", demandConfig.getDemandMatchScore());
                                break;
                            case "国外贸易管控":
                                if (demandConfig.getTradeControlScore() != null && demandConfig.getTradeControlScore() > 0){
                                    indicatorTemp.put("percent", BigDecimalUtil.divide(new BigDecimal(demandConfig.getTradeControlScore()), new BigDecimal(12), 2, RoundingMode.HALF_UP));
                                }
                                indicatorTemp.put("score", demandConfig.getTradeControlScore());
                                break;
                            case "技术专利壁垒":
                                if (demandConfig.getPatentBarriersScore() != null && demandConfig.getPatentBarriersScore() > 0){
                                    indicatorTemp.put("percent", BigDecimalUtil.divide(new BigDecimal(demandConfig.getPatentBarriersScore()), new BigDecimal(22), 2, RoundingMode.HALF_UP));
                                }
                                indicatorTemp.put("score", demandConfig.getPatentBarriersScore());
                                break;
                            case "网络公开观点":
                                if (demandConfig.getOpenOpinionScore() != null && demandConfig.getOpenOpinionScore() > 0){
                                    indicatorTemp.put("percent", BigDecimalUtil.divide(new BigDecimal(demandConfig.getOpenOpinionScore()), new BigDecimal(17), 2, RoundingMode.HALF_UP));
                                }
                                indicatorTemp.put("score", demandConfig.getOpenOpinionScore());
                                break;
                            case "区域攻关基础":
                                if (demandConfig.getDevelopBaseScore() != null && demandConfig.getDevelopBaseScore() > 0){
                                    indicatorTemp.put("percent", BigDecimalUtil.divide(new BigDecimal(demandConfig.getDevelopBaseScore()), new BigDecimal(22), 2, RoundingMode.HALF_UP));
                                }
                                indicatorTemp.put("score", demandConfig.getDevelopBaseScore());
                                break;
                            default:
                        }
                    }
                    evaluateRes.put("score", demandConfig.getTotalScore());
                }
            }
        }
        JSONObject evaluateStandard = resJson.getJSONObject("evaluate_standard");
        if (evaluateStandard != null) {
            if (evaluateStandard.containsKey("断供断链匹配")) {
                evaluateStandard.remove("断供断链匹配");
            }
        }
        resJson.put("overall_company", companyNumMap);
    }

    /**
     * 需求列表
     *
     * @param query
     * @return
     */
    @Override
    public EsPageResult demandList(DemandQuery query) {
        EsSimpleQuery esSimpleQuery = new EsSimpleQuery();
        esSimpleQuery.setPageNum(query.getPageNum());
        esSimpleQuery.setPageSize(query.getPageSize());
        esSimpleQuery.setChainId(query.getChainId());
        esSimpleQuery.setResourceType(InsightTypeEnum.DEMAND.getType());
        // 查询条件
        BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(query.getChainId(), null, null, null, false);
        if (StringUtils.isNotBlank(query.getName())) {
            queryBuilder.must(QueryBuilders.matchPhraseQuery("name", query.getName()));
        }
        if (StringUtils.isNotBlank(query.getResearchContent())) {
            queryBuilder.must(QueryBuilders.matchPhraseQuery("research_content", query.getName()));
        }
        if (StringUtils.isNotBlank(query.getSourceType())) {
            queryBuilder.must(QueryBuilders.termsQuery("source_type", query.getSourceType()));
        }
        if (StringUtils.isNotBlank(query.getCommitDateStart())) {
            queryBuilder.must(QueryBuilders.rangeQuery("commit_date").gte(query.getCommitDateStart()));
        }
        if (StringUtils.isNotBlank(query.getCommitDateEnd())) {
            queryBuilder.must(QueryBuilders.rangeQuery("commit_date").lte(query.getCommitDateEnd()));
        }
        esSimpleQuery.setQueryBuilder(queryBuilder);
        EsPageResult pageResult = resourceService.listResource(esSimpleQuery);
        return pageResult;
    }

    /**
     * 团队测评
     *
     * @param query
     * @return
     */
    @Override
    public Result<String> teamMatch(EvaluationQuery query) {
        Map<String, Object> map = new HashMap<>();
        map.put("demand_name", query.getContent());
        String res = HttpClientUtils.post(OVERCOME_EVALUATION + EvaluationConstant.NEW_TEAM_MATCH, map);
        JSONObject jsonRes = JSONObject.parseObject(res);
        if (jsonRes.getJSONArray("parse_res").isEmpty()) {
            return ResultConvert.error(ResultCodeEnum.NO_NODE_MATCH);
        } else {
            return ResultConvert.success(jsonRes.getString("id"));
        }
    }

    /**
     * 根据条件查询团队测评新版页面
     *
     * @param queryBO
     * @return
     */
    @Override
    public JSONObject teamGetBySys(TeamMatchQuery queryBO) {
        JSONObject resJson = new JSONObject();
        TeamPoolQuery teamPoolQuery = new TeamPoolQuery();
        teamPoolQuery.setDemandId(queryBO.getDemand_id());
        teamPoolQuery.setStatus(0);
        teamPoolQuery.setPageNum(1);
        teamPoolQuery.setPageSize(200);
        EsPageResult esPageResult = listTeamPool(teamPoolQuery);
        if (esPageResult.getTotal() > 0) {
            JSONObject setting = new JSONObject();
            JSONArray expertAry = new JSONArray();
            JSONArray companyAry = new JSONArray();
            JSONArray localCompanyAry = new JSONArray();
            resJson.put("setting", setting);
            resJson.put("expert_list", expertAry);
            resJson.put("company_list", companyAry);
            resJson.put("local_company_list", localCompanyAry);
            JSONArray hitsAry = JSONArray.parseArray(JSON.toJSONString(esPageResult.getList()));
            //取第一条数据的冗余字段
            JSONObject first = hitsAry.getJSONObject(0);
            resJson.put("demand_id", first.get("demand_id"));
            resJson.put("demand_name", first.get("demand_name"));
            setting.put("chain_node", first.get("chain_node"));
            List<String> companyEstablishList = EstablishTypeEnum.getNames();
            Set<String> companyLabelList = new TreeSet<>();
            List<String> localCompanyEstablishList = EstablishTypeEnum.getNames();
            Set<String> localCompanyLabelList = new TreeSet<>();
            Set<String> orgLabelList = new TreeSet<>();
            Set<String> expertLevelList = new TreeSet<>();
            Set<String> expertLabelList = new TreeSet<>();
            Set<String> expertProfessionalList = new TreeSet<>();
            Set<String> expertOrgList = new TreeSet<>();
            setting.put("company_establish", companyEstablishList);
            setting.put("company_label", companyLabelList);
            setting.put("local_company_establish", localCompanyEstablishList);
            setting.put("local_company_label", localCompanyLabelList);
            setting.put("org_label", orgLabelList);
            setting.put("expert_level", expertLevelList);
            setting.put("expert_label", expertLabelList);
            setting.put("expert_professional", expertProfessionalList);
            setting.put("expert_org", expertOrgList);
//            List<String> pushedIds = teamPushLogDAO.listEntityId(first.getString("demand_id"));
            for (int i = 0; i < hitsAry.size(); i++) {
                JSONObject temp = hitsAry.getJSONObject(i);
                //南平不需要推送
//                if (CollectionUtils.isNotEmpty(pushedIds) && pushedIds.contains(temp.getString("entity_id"))) {
//                    temp.put("push_status", 1);
//                } else {
//                    temp.put("push_status", 0);
//                }
                temp.put("push_status", 0);
                temp.remove("demand_name");
                temp.remove("demand_id");
                boolean addFlag = true;
                if ("expert".equals(temp.getString("type"))) {
                    //专家
                    Set<String> tempLabelList = new TreeSet<>();
                    Set<String> tempLevelList = new TreeSet<>();
                    Set<String> tempProfessionalList = new TreeSet<>();
                    Set<String> expertTag = new TreeSet<>();
                    Set<String> tempExpertOrg = new TreeSet<>();
                    if (temp.containsKey("organization")) {
                        tempExpertOrg.add(temp.getString("organization"));
                    }
                    if (temp.getJSONArray("label") != null && !temp.getJSONArray("label").isEmpty()) {
                        for (int j = 0; j < temp.getJSONArray("label").size(); j++) {
                            JSONObject tempLabel = temp.getJSONArray("label").getJSONObject(j);
                            if (tempLabel.get("name") != null) {
                                tempLabelList.add(tempLabel.getString("name"));
                                expertTag.add(tempLabel.getString("name"));
                            }
                        }
                    }
                    if (temp.getJSONArray("title") != null && !temp.getJSONArray("title").isEmpty()) {
                        for (int j = 0; j < temp.getJSONArray("title").size(); j++) {
                            JSONObject tempTitle = temp.getJSONArray("title").getJSONObject(j);
                            if (tempTitle.get("level") != null && StringUtils.isNotBlank(tempTitle.getString("level"))) {
                                tempLevelList.add(tempTitle.getString("level"));
                            }
                            if (tempTitle.get("professional_title") != null && StringUtils.isNotBlank(tempTitle.getString("professional_title"))) {
                                tempProfessionalList.add(tempTitle.getString("professional_title"));
                            }
                        }
                    }
                    if (temp.getJSONArray("chain_node") != null && !temp.getJSONArray("chain_node").isEmpty()) {
                        for (int j = 0; j < temp.getJSONArray("chain_node").size(); j++) {
                            expertTag.add(temp.getJSONArray("chain_node").getJSONObject(j).getString("node_name"));
                        }
                    }
                    if (CollectionUtils.isNotEmpty(queryBO.getExpert_org()) && !tempExpertOrg.containsAll(queryBO.getExpert_org())) {
                        addFlag = false;
                    }
                    if (CollectionUtils.isNotEmpty(queryBO.getExpert_label()) && !tempLabelList.containsAll(queryBO.getExpert_label())) {
                        addFlag = false;
                    }
                    if (CollectionUtils.isNotEmpty(queryBO.getExpert_level()) && !tempLevelList.containsAll(queryBO.getExpert_level())) {
                        addFlag = false;
                    }
                    if (CollectionUtils.isNotEmpty(queryBO.getExpert_professional()) && !tempProfessionalList.containsAll(queryBO.getExpert_professional())) {
                        addFlag = false;
                    }
                    temp.put("tag", expertTag);
                    if (addFlag) {
                        expertAry.add(temp);
                    }
                    expertLabelList.addAll(tempLabelList);
                    expertLevelList.addAll(tempLevelList);
                    expertProfessionalList.addAll(tempProfessionalList);
                    expertOrgList.addAll(tempExpertOrg);
                } else if ("company".equals(temp.getString("type"))) {
                    // 外地企业
                    Set<String> tempLabelList = new TreeSet<>();
                    for (int j = 0; j < temp.getJSONArray("label").size(); j++) {
                        JSONObject tempLabel = temp.getJSONArray("label").getJSONObject(j);
                        tempLabelList.add(tempLabel.getString("name"));
                    }
                    if (CollectionUtils.isNotEmpty(queryBO.getCompany_label()) && !tempLabelList.containsAll(queryBO.getCompany_label())) {
                        addFlag = false;
                    }
                    if (CollectionUtils.isNotEmpty(queryBO.getCompany_establish())) {
                        String establishDate = temp.getString("establish_date");
                        if (StringUtils.isNotBlank(establishDate)) {
                            Integer years = DateUtil.thisYear() - DateUtil.year(DateUtil.parseDate(establishDate));
                            boolean checked = false;
                            for (String establish : queryBO.getCompany_establish()) {
                                if (EstablishTypeEnum.checkAvaitor(establish, years)) {
                                    checked = true;
                                    break;
                                }
                            }
                            addFlag = checked;
                        } else {
                            addFlag = false;
                        }
                    }
                    if (addFlag) {
                        companyAry.add(temp);
                    }
                    if (temp.getString("type").equals("company")) {
                        companyLabelList.addAll(tempLabelList);
                    } else {
                        orgLabelList.addAll(tempLabelList);
                    }
                } else {
                    // 本地企业
                    Set<String> tempLabelList = new TreeSet<>();
                    for (int j = 0; j < temp.getJSONArray("label").size(); j++) {
                        JSONObject tempLabel = temp.getJSONArray("label").getJSONObject(j);
                        tempLabelList.add(tempLabel.getString("name"));
                    }
                    if (CollectionUtils.isNotEmpty(queryBO.getLocal_company_label()) && !tempLabelList.containsAll(queryBO.getLocal_company_label())) {
                        addFlag = false;
                    }
                    if (CollectionUtils.isNotEmpty(queryBO.getLocal_company_establish())) {
                        String establishDate = temp.getString("establish_date");
                        if (StringUtils.isNotBlank(establishDate)) {
                            Integer years = DateUtil.thisYear() - DateUtil.year(DateUtil.parseDate(establishDate));
                            boolean checked = false;
                            for (String establish : queryBO.getLocal_company_establish()) {
                                if (EstablishTypeEnum.checkAvaitor(establish, years)) {
                                    checked = true;
                                    break;
                                }
                            }
                            addFlag = checked;
                        } else {
                            addFlag = false;
                        }
                    }
                    if (addFlag) {
                        localCompanyAry.add(temp);
                    }
                    if (temp.getString("type").equals("localCompany")) {
                        localCompanyLabelList.addAll(tempLabelList);
                    } else {
                        orgLabelList.addAll(tempLabelList);
                    }
                }
            }
        }
        if (resJson.get("company_list") != null && resJson.getJSONArray("company_list").size() > 20) {
            resJson.put("company_list", resJson.getJSONArray("company_list").subList(0, 20));
        }
        if (resJson.get("local_company_list") != null && resJson.getJSONArray("local_company_list").size() > 20) {
            resJson.put("local_company_list", resJson.getJSONArray("local_company_list").subList(0, 20));
        }
        if (resJson.get("expert_list") != null && resJson.getJSONArray("expert_list").size() > 20) {
            resJson.put("expert_list", resJson.getJSONArray("expert_list").subList(0, 20));
        }
        return resJson;
    }

    /**
     * 团队测评新版-手动添加的实体
     *
     * @param queryBO
     * @return
     */
    @Override
    public JSONObject teamGetByUser(TeamMatchQuery queryBO) {
        JSONObject resJson = new JSONObject();
//        SearchRequestBuilder searchBuilder = elasticsearchHelper.initSearchRequest(teampoolIndex, teampoolType, null);
//        // 初始化布尔查询
//        BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
//        searchBuilder.setQuery(filterBuilder);
//        filterBuilder.must(QueryBuilders.matchQuery("demand_id", queryBO.getDemand_id()));
//        filterBuilder.must(QueryBuilders.matchQuery("status", 1));
//        ElasticsearchHelper.pagingSet(searchBuilder, 1, 100);
//        String[] excludes = {"score", "agg_project", "agg_paper", "agg_patent", "demand_id", "demand_name"};
//        SearchResponse searchResponse = searchBuilder.setExplain(false).setFetchSource(null, excludes).execute().actionGet();
//        JSONObject source = ElasticsearchHelper.buildSearchResult(searchResponse);
//        System.out.println("source->" + source);
//        if (source.getLong("total") > 0) {
//            JSONArray expertAry = new JSONArray();
//            JSONArray companyAry = new JSONArray();
//            resJson.put("expert_list", expertAry);
//            resJson.put("company_list", companyAry);
//            JSONArray hitsAry = source.getJSONArray("hits");
//            List<String> pushedIds = teamPushLogDAO.listEntityId(queryBO.getDemand_id());
//            for (int i = 0; i < hitsAry.size(); i++) {
//                JSONObject temp = hitsAry.getJSONObject(i).getJSONObject("source");
//                if (CollectionUtils.isNotEmpty(pushedIds) && pushedIds.contains(temp.getString("entity_id"))) {
//                    temp.put("push_status", 1);
//                } else {
//                    temp.put("push_status", 0);
//                }
//                if ("expert".equals(temp.getString("type"))) {
//                    expertAry.add(temp);
//                } else {
//                    companyAry.add(temp);
//                }
//            }
//        }
        return resJson;
    }

    /**
     * 测评报告
     *
     * @param reportBO
     * @return
     */
    @Override
    public JSONObject teamReport(TeamReportQuery reportBO) {
        JSONObject resJson = new JSONObject();
        TeamPoolQuery teamPoolQuery = new TeamPoolQuery();
        teamPoolQuery.setDemandId(reportBO.getDemand_id());
        teamPoolQuery.setType(reportBO.getType());
        teamPoolQuery.setEntityIds(reportBO.getIds());
        teamPoolQuery.setPageNum(1);
        teamPoolQuery.setPageSize(10);
        EsPageResult esPageResult = listTeamPool(teamPoolQuery);
        if (esPageResult.getTotal() > 0) {
            JSONArray hitsAry = JSONArray.parseArray(JSON.toJSONString(esPageResult.getList()));
            //取第一条数据的冗余字段
            JSONObject first = hitsAry.getJSONObject(0);
            resJson.put("demand_id", first.get("demand_id"));
            resJson.put("demand_name", first.get("demand_name"));
            resJson.put("chain_node", first.get("chain_node"));
            List<TeamStatBO> patentList = new ArrayList<>();
            List<TeamStatBO> projectList = new ArrayList<>();
            List<TeamStatBO> paperList = new ArrayList<>();
            JSONArray entityAry = new JSONArray();
            for (int i = 0; i < hitsAry.size(); i++) {
                JSONObject temp = hitsAry.getJSONObject(i);
                if (temp.getJSONArray("agg_patent") != null) {
                    for (int j = 0; j < temp.getJSONArray("agg_patent").size(); j++) {
                        int count = temp.getJSONArray("agg_patent").getJSONObject(j).getInteger("relation");
                        if (count > 0) {
                            TeamStatBO teamStatBO = new TeamStatBO();
                            teamStatBO.setName(temp.getString("name"));
                            teamStatBO.setCount(count);
                            teamStatBO.setYear(temp.getJSONArray("agg_patent").getJSONObject(j).getString("year"));
                            patentList.add(teamStatBO);
                        }
                    }
                }
                if (temp.getJSONArray("agg_paper") != null) {
                    for (int j = 0; j < temp.getJSONArray("agg_paper").size(); j++) {
                        int count = temp.getJSONArray("agg_paper").getJSONObject(j).getInteger("relation");
                        if (count > 0) {
                            TeamStatBO teamStatBO = new TeamStatBO();
                            teamStatBO.setName(temp.getString("name"));
                            teamStatBO.setCount(count);
                            teamStatBO.setYear(temp.getJSONArray("agg_paper").getJSONObject(j).getString("year"));
                            paperList.add(teamStatBO);
                        }
                    }
                }
                if (temp.getJSONArray("agg_project") != null) {
                    for (int j = 0; j < temp.getJSONArray("agg_project").size(); j++) {
                        int count = temp.getJSONArray("agg_project").getJSONObject(j).getInteger("relation");
                        if (count > 0) {
                            TeamStatBO teamStatBO = new TeamStatBO();
                            teamStatBO.setName(temp.getString("name"));
                            teamStatBO.setCount(count);
                            teamStatBO.setYear(temp.getJSONArray("agg_project").getJSONObject(j).getString("year"));
                            projectList.add(teamStatBO);
                        }
                    }
                }
                entityAry.add(temp);
                temp.remove("agg_patent");
                temp.remove("agg_paper");
                temp.remove("agg_project");
                temp.remove("chain_node");
            }
            Map<String, List<TeamStatBO>> patentMap = patentList.stream().collect(Collectors.groupingBy(TeamStatBO::getYear));
            Map<String, List<TeamStatBO>> projectMap = projectList.stream().collect(Collectors.groupingBy(TeamStatBO::getYear));
            Map<String, List<TeamStatBO>> paperMap = paperList.stream().collect(Collectors.groupingBy(TeamStatBO::getYear));
            resJson.put("patent_list", patentMap);
            resJson.put("project_list", projectMap);
            resJson.put("paper_list", paperMap);
            resJson.put("list", entityAry);
        }
        return resJson;
    }

    @Override
    public List<CommonIndexBO> getRiskMap(String chainId, Set<String> ids) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(chainId)) {
            queryBuilder.filter(QueryBuilders.termQuery("chain.id", chainId));
        }
        if (CollectionUtils.isNotEmpty(ids)){
            queryBuilder.filter(QueryBuilders.idsQuery().addIds(ids.toArray(new String[0])));
        }
        // 关键技术分类情况，不支持按区域划分
        Map<String, Long> bottAggMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.BOTTLENECK, queryBuilder, "link_type");
        List<BottleneckTypeEnum> displayList = Arrays.asList(BottleneckTypeEnum.IN_CITY, BottleneckTypeEnum.IN_PROVINCE,
                BottleneckTypeEnum.OUT_PROVINCE, BottleneckTypeEnum.NO_REPLACE, BottleneckTypeEnum.HAVE);
        List<CommonIndexBO> bottList = new ArrayList<>();

        for (BottleneckTypeEnum typeEnum : displayList) {
            if (!bottAggMap.containsKey(typeEnum.getValue())) {
                continue;
            }
            bottList.add(new CommonIndexBO(typeEnum.getName(), bottAggMap.get(typeEnum.getValue())));
        }
        return bottList;
    }

    @Override
    public EsPageResult listTechReport(String chainId, Integer pageNum, Integer pageSize) {
        // 仅筛选weak表
        List<IndustryChainNodeWeak> weakNodes = industryChainService.listWeakNodesByChainId(chainId);
        if (CollectionUtils.isEmpty(weakNodes)){
            return new EsPageResult();
        }
        List<String> riskIds = weakNodes.stream().map(IndustryChainNodeWeak::getDataId).collect(Collectors.toList());
        // 去掉重复的riskId
        Set<String> riskSet = new HashSet<>();
        List<String> sortedRiskIds = new ArrayList<>();
        for (String riskId: riskIds){
            if(riskSet.contains(riskId)){
                continue;
            }
            sortedRiskIds.add(riskId);
            riskSet.add(riskId);
        }
        EsPageResult pageResult = riskService.page(chainId, pageNum, pageSize, sortedRiskIds);
        List<Map<String, Object>> riskList = pageResult.getList();
        // 按照riskId重新排序
        Map<String, Map<String, Object>> riskMap = new HashMap<>();
        for (Map<String, Object> risk: riskList){
            riskMap.put(String.valueOf(risk.get("id")), risk);
        }
        List<Map<String, Object>> sortedRiskList = new ArrayList<>();
        for (String riskId: sortedRiskIds){
            if (riskMap.containsKey(riskId)){
                sortedRiskList.add(riskMap.get(riskId));
            }
        }
        pageResult.setList(sortedRiskList);
        List<CompletableFuture<Void>> futureList = new ArrayList<>(pageSize);
        for (Map<String, Object> riskInfo: sortedRiskList) {
            final CompletableFuture<Void> riskFuture = CompletableFuture.runAsync(() -> {
                fillRiskReportDetail(riskInfo);
            }, taskExecutor);
            futureList.add(riskFuture);
        }
        TaskUtil.get(futureList);
        return pageResult;
    }

    @Override
    public Map<String, Object> getTechReportById(String techId) {
        Map<String, Object> riskInfo = riskService.getById(techId);
        fillRiskReportDetail(riskInfo);
        return riskInfo;
    }

    private void fillRiskReportDetail(Map<String, Object> riskInfo){
        String riskName = (String) riskInfo.get("name");
        // 需求测评
        String reportId = getDemandMatchResult(riskName);
        if (StringUtils.isNotEmpty(reportId)){
            Map<String, Object> map = new HashMap<>();
            map.put("id", reportId);
            String res = HttpClientUtils.post(OVERCOME_EVALUATION + EvaluationConstant.DEMAND_GET, map);
            if (StringUtils.isNotBlank(res)) {
                JSONObject resJson = JSONObject.parseObject(res);
                JSONObject evaluationRes = resJson.getJSONObject("evaluate_res");
                double score = evaluationRes.getDoubleValue("score");
                String demandType = score > 90? "高质量需求": (score >= 60? "重点需求":"普通需求");
                riskInfo.put("score", score);
                riskInfo.put("demandType", demandType);
                riskInfo.put("reportId", reportId);
            }
        }
        // 团队
        if (CollectionUtils.isNotEmpty((List<Map<String, Object>>)riskInfo.get("expert_list"))){
            riskInfo.put("recommend_expert",((List<Map<String, Object>>)riskInfo.get("expert_list")).get(0));
        }
        if (BottleneckTypeEnum.IN_CITY.getName().equals(riskInfo.get("research_status"))
                && CollectionUtils.isNotEmpty((List<Map<String, Object>>)riskInfo.get("city_advantage_enterprises"))){
            riskInfo.put("recommend_company", ((List<Map<String, Object>>)riskInfo.get("city_advantage_enterprises")).get(0));
        }else if (CollectionUtils.isNotEmpty((List<Map<String, Object>>)riskInfo.get("provinces_advantage_enterprises"))){
            riskInfo.put("recommend_company", ((List<Map<String, Object>>)riskInfo.get("provinces_advantage_enterprises")).get(0));
        }else if (CollectionUtils.isNotEmpty((List<Map<String, Object>>)riskInfo.get("other_provinces_advantage_enterprises"))){
            riskInfo.put("recommend_company", ((List<Map<String, Object>>)riskInfo.get("other_provinces_advantage_enterprises")).get(0));
        }else if (CollectionUtils.isNotEmpty((List<Map<String, Object>>)riskInfo.get("foreign_advantage_enterprises"))){
            riskInfo.put("recommend_company", ((List<Map<String, Object>>)riskInfo.get("foreign_advantage_enterprises")).get(0));
        }
    }


    private EsPageResult listTeamPool(TeamPoolQuery query) {
        EsSimpleQuery esSimpleQuery = new EsSimpleQuery();
        esSimpleQuery.setPageNum(query.getPageNum());
        esSimpleQuery.setPageSize(query.getPageSize());
        esSimpleQuery.setResourceType(EsIndexEnum.TEAM_POOL.getType());
        // 查询条件
        BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(null, null, null, null, false);
        if (StringUtils.isNotBlank(query.getDemandId())) {
            queryBuilder.must(QueryBuilders.matchQuery("demand_id", query.getDemandId()));
        }
        if (StringUtils.isNotBlank(query.getType())) {
            queryBuilder.must(QueryBuilders.matchQuery("type", query.getType()));
        }
        if (CollectionUtil.isNotEmpty(query.getEntityIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("entity_id", query.getEntityIds()));
        }
        if (query.getStatus() != null) {
            queryBuilder.must(QueryBuilders.matchQuery("status", query.getStatus()));
        }
        esSimpleQuery.setQueryBuilder(queryBuilder);
        EsPageResult pageResult = resourceService.listResource(esSimpleQuery);
        return pageResult;
    }
}
