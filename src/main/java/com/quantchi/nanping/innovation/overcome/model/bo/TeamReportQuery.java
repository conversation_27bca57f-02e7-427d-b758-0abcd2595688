package com.quantchi.nanping.innovation.overcome.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/9
 * 团队测评报告查询
 */
@Data
public class TeamReportQuery {

    @ApiModelProperty("测评id")
    private String demand_id;

    @ApiModelProperty("报告类型 expert或company")
    private String type;

    @ApiModelProperty("测评实体entity_id集合")
    private List<String> ids;
}
