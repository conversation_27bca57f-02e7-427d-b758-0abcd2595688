package com.quantchi.nanping.innovation.overcome.model.bo;

import com.quantchi.nanping.innovation.model.bo.PageBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.TreeSet;

/**
 * <AUTHOR>
 * @date 2021/11/9
 * 团队测评查询
 */
@Data
public class TeamPoolQuery extends PageBO {

    private String demandId;

    private Integer status;

    @ApiModelProperty("报告类型 expert或company")
    private String type;

    @ApiModelProperty("测评实体entity_id集合")
    private List<String> entityIds;
}
