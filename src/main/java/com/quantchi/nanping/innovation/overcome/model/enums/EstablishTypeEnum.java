package com.quantchi.nanping.innovation.overcome.model.enums;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/15
 * 企业成立时间
 */
public enum EstablishTypeEnum {

    /**
     * 成立时间
     */
    OVER_TEN("10年以上", 10, null),
    BETWEEN_FIVE_TEN("5~10年", 5, 9),
    BETWEEN_THREE_FIVE("3~5年", 3, 4),
    LOWER_THREE("3年以内", null, 2),
    ;
    /**
     * 名称
     */
    private String name;
    /**
     * 大于年份
     */
    private Integer gte;

    /**
     * 小于年份
     */
    private Integer lte;

    EstablishTypeEnum(String name, Integer gte, Integer lte) {
        this.name = name;
        this.gte = gte;
        this.lte = lte;
    }

    public final static List<String> getNames() {
        List<String> nameList = new ArrayList<>();
        for (EstablishTypeEnum value : EstablishTypeEnum.values()) {
            nameList.add(value.getName());
        }
        return nameList;
    }

    /**
     * 校验表达式
     *
     * @param name
     * @param years
     * @return
     */
    public final static boolean checkAvaitor(String name, Integer years) {
        for (EstablishTypeEnum value : EstablishTypeEnum.values()) {
            if (value.getName().equals(name)) {
                if ((value.getGte() == null || value.getGte() <= years)
                        && (value.getLte() == null || value.getLte() >= years)) {
                    return true;
                }
            }
        }
        return false;
    }

    public String getName() {
        return name;
    }

    public Integer getGte() {
        return gte;
    }

    public Integer getLte() {
        return lte;
    }
}
