package com.quantchi.nanping.innovation.overcome.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeakProject;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.overcome.model.bo.*;
import com.quantchi.nanping.innovation.overcome.service.OvercomeService;
import com.quantchi.nanping.innovation.service.INodeWeakProjectService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.service.library.RiskService;
import com.quantchi.tianying.model.EsPageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/27 10:13 上午
 * @description
 */
@RestController
@RequestMapping("/overcome")
@Api(tags = "技术攻关")
@PlatformAuthCheck(type = {"0"})
@Metrics
public class OvercomeController {

    @Autowired
    private OvercomeService overcomeService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private RiskService riskService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private INodeWeakProjectService weakProjectService;

    @GetMapping("/getKeyIndex")
    @ApiOperation("获取核心指标")
    @Log(title = "科技攻关")
    public Result<List<CommonIndexBO>> getKeyIndex() {
        return ResultConvert.success(overcomeService.getKeyIndex(null, null, null, true, true));
    }

    @GetMapping("/getStatMap")
    @ApiOperation("各清单分类统计")
    //@Log(title = "技术攻关-各清单分类统计")
    public Result<Map<String, List<CommonIndexBO>>> getStatMap() {
        return ResultConvert.success(overcomeService.getStatMap(null, null, true));
    }

    @GetMapping("/getRiskMap")
    @ApiOperation("风险清单分类统计")
    //@Log(title = "技术攻关-风险清单分类统计")
    public Result<List<CommonIndexBO>> getRiskMap(String chainId) {
        return ResultConvert.success(overcomeService.getRiskMap(chainId, null));
    }

    @PostMapping("/listProject")
    @ApiOperation("项目列表")
    //@Log(title = "技术攻关-项目列表")
    public Result<EsPageResult> listProject(@Validated @RequestBody ProjectQuery query) {
        return ResultConvert.success(overcomeService.listProject(query));
    }

    @ApiOperation(value = "需求测评")
    @PostMapping("/demand/match")
    @Log(title = "科技攻关-需求测评")
    public Result<String> demandMatch(@RequestBody EvaluationQuery query) {
        return overcomeService.demandMatch(query);
    }

    @ApiOperation(value = "需求测评-详情")
    @PostMapping("/demand/get")
    //@Log(title = "技术攻关-需求测评-详情")
    public Result<JSONObject> demandGet(@RequestBody IdQuery idQuery) {
        return overcomeService.demandGet(idQuery.getId());
    }

    @ApiOperation(value = "简版需求测评")
    @PostMapping("/demand/simple/match")
    //@Log(title = "技术攻关-简版需求测评")
    public Result<String> demandSimpleGet(@RequestBody EvaluationQuery query) {
        return overcomeService.demandSimpleMatch(query);
    }

    @ApiOperation(value = "简版需求测评-详情")
    @PostMapping("/demand/simple/get")
    //@Log(title = "技术攻关-简版需求测评-详情")
    public Result<JSONObject> demandSimpleGet(@RequestBody IdQuery idQuery) {
        return overcomeService.demandSimpleGet(idQuery.getId());
    }

    @ApiOperation(value = "需求列表")
    @PostMapping("/demand/list")
    //@Log(title = "技术攻关-需求列表")
    public Result demandList(@Validated @RequestBody DemandQuery query) {
        return ResultConvert.success(overcomeService.demandList(query));
    }

    @ApiOperation(value = "团队测评")
    @PostMapping("/team/match")
    @Log(title = "科技攻关-团队测评")
    public Result<String> teamMatch(@RequestBody EvaluationQuery query) {
        return overcomeService.teamMatch(query);
    }

    @ApiOperation(value = "团队测评-查询系统生成实体")
    @PostMapping("/team/getBySys")
    //@Log(title = "技术攻关-团队测评-查询系统生成实体")
    public Result<JSONObject> get(@RequestBody TeamMatchQuery queryBO) {
        return ResultConvert.success(overcomeService.teamGetBySys(queryBO));
    }

    @ApiOperation(value = "团队测评-查询手动生成实体")
    @PostMapping("/team/getByUser")
    //@Log(title = "技术攻关-团队测评-查询手动生成实体")
    public Result<JSONObject> getByUser(@RequestBody TeamMatchQuery queryBO) {
        return ResultConvert.success(overcomeService.teamGetByUser(queryBO));
    }

    @ApiOperation(value = "团队测评-报告详情")
    @PostMapping("/team/report")
    //@Log(title = "技术攻关-团队测评-报告详情")
    public Result<JSONObject> get(@RequestBody TeamReportQuery queryBO) {
        return ResultConvert.success(overcomeService.teamReport(queryBO));
    }

    @ApiOperation(value = "科技攻关推荐")
    @GetMapping("/tech/report")
    public Result<EsPageResult> listTechReport(@RequestParam @NotBlank String chainId,
                                               @RequestParam(defaultValue = "1") @Size(min = 1) Integer pageNum,
                                               @RequestParam(defaultValue = "5") @Size(min = 1, max = 5) Integer pageSize) {
        return ResultConvert.success(overcomeService.listTechReport(chainId, pageNum, pageSize));
    }

    @ApiOperation(value = "科技攻关关键技术报告")
    @GetMapping("/report")
    public Result<Map<String, Object>> getTechReport(@RequestParam @NotBlank String techId) {
        return ResultConvert.success(overcomeService.getTechReportById(techId));
    }

    @ApiOperation(value = "科技攻关指标")
    @GetMapping("/index")
    public Result<Map<String, Object>> getAnalysisIndex(@RequestParam @NotBlank String chainId) {
        Map<String, Object> resultMap = new HashMap<>();
        LocalDate lastYear = new LocalDate().minusYears(1);
        String lastYearStr = lastYear.toString("yyyy");
        // 18-22年申请专利数量(截止到去年为止的5年)
        Map<String, Long> yearMap = patentService.getPatentApplyTrendRecentYears(chainId, null,
                CommonConstant.DIVISION_NANPING.getId(), null, false, 5);
        resultMap.put("patentStartYear", "" + (Integer.parseInt(lastYearStr) - 4));
        resultMap.put("patentLastYear", lastYearStr);
        Long totalNum = 0L;
        List<Long> yearNumList = new ArrayList<>();
        for (Map.Entry<String, Long> currentYear : yearMap.entrySet()) {
            if (lastYearStr.equals(new LocalDate().toString("yyyy"))) {
                continue;
            } else {
                totalNum += currentYear.getValue();
                yearNumList.add(currentYear.getValue());
            }
        }
        resultMap.put("patentNum", totalNum);
        // 22年申请专利数量(上一年)
        Long lastYearPatentNum = yearMap.get(lastYearStr);
        resultMap.put("patentLastYearNum", lastYearPatentNum);
        // 专利年平均增长率
        Long lastNum = 0L;
        BigDecimal increaseRate = BigDecimal.ZERO;
        for (int i = 0; i < yearNumList.size(); i++) {
            if (i > 0) {
                lastNum = yearNumList.get(i - 1);
            }
            if (lastNum > 0){
                increaseRate = increaseRate.add(new BigDecimal(yearNumList.get(i) - lastNum)
                        .divide(new BigDecimal(lastNum), 2, RoundingMode.HALF_UP));
            }
        }
        increaseRate = increaseRate.divide(new BigDecimal(5)).multiply(new BigDecimal(100))
                .setScale(0, RoundingMode.HALF_UP);
        resultMap.put("increaseRate", increaseRate);
        // 链名称
        String chainName = industryChainService.getChainNameById(chainId);
        resultMap.put("chainName", chainName);
        // 关键技术数量
        Long techNum = riskService.count(chainId, null);
        resultMap.put("techNum", techNum);
        // 市内优势企业数
        Integer cityAdvantageNum = riskService.getCityAdvantageCompanyIdsByChainId(chainId).size();
        resultMap.put("cityAdvantageNum", cityAdvantageNum);
        // 项目申报数量
        Long projectApplyNum = weakProjectService.count(Wrappers.lambdaQuery(IndustryChainNodeWeakProject.class)
                .eq(IndustryChainNodeWeakProject::getChainId, chainId));
        resultMap.put("projectApplyNum", projectApplyNum);
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "成果类型统计")
    @GetMapping("/achievement/type")
    public Result<List<CommonIndexBO>> getAchievementType(@RequestParam @NotBlank String chainId) {
        return ResultConvert.success(overcomeService.statAchievementByType(chainId));
    }
}
