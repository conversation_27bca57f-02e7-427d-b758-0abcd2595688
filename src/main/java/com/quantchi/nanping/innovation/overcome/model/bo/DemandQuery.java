package com.quantchi.nanping.innovation.overcome.model.bo;

import com.quantchi.nanping.innovation.model.bo.PageBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/5/17 15:02
 */
@Data
public class DemandQuery extends PageBO {

    @ApiModelProperty("需求标题")
    private String name;

    @ApiModelProperty("需求内容")
    private String researchContent;

    @ApiModelProperty("产业链领域id")
    private String chainId;

    @ApiModelProperty("提出时间开始")
    private String commitDateStart;

    @ApiModelProperty("提出时间截止")
    private String commitDateEnd;

    @ApiModelProperty("需求来源")
    private String sourceType;
}
