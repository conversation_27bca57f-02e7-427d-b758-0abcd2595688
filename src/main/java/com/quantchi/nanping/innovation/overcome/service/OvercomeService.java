package com.quantchi.nanping.innovation.overcome.service;

import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.overcome.model.bo.*;
import com.quantchi.tianying.model.EsPageResult;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022/12/27 14:19
 * 攻关service
 */
public interface OvercomeService {

    /**
     * 根据地市id获取攻关核心指标
     *
     * @param chainId
     * @param cityId
     * @param areaId
     * @param auth
     * @param includeAchievement
     * @return
     */
    List<CommonIndexBO> getKeyIndex(String chainId, String cityId, String areaId, boolean auth, boolean includeAchievement);

    /**
     * 各清单分类统计
     *
     * @param cityId
     * @param areaId
     * @param auth
     * @return
     */
    Map<String, List<CommonIndexBO>> getStatMap(String cityId, String areaId, boolean auth);

    /**
     * 项目列表
     *
     * @param query
     * @return
     */
    EsPageResult listProject(ProjectQuery query);

    /**
     * 首页成果类型统计
     *
     * @return
     */
    List<CommonIndexBO> statAchievementByType(String chainId);

    /**
     * 需求测评
     *
     * @param query
     * @return
     */
    Result<String> demandMatch(EvaluationQuery query);

    /**
     * 需求测评详情
     *
     * @param id
     * @return
     */
    Result<JSONObject> demandGet(String id);

    /**
     * 简版需求测评
     *
     * @param query
     * @return
     */
    Result<String> demandSimpleMatch(EvaluationQuery query);

    /**
     * 需求测评详情
     *
     * @param id
     * @return
     */
    Result<JSONObject> demandSimpleGet(String id);

    /**
     * 需求列表
     *
     * @param query
     * @return
     */
    EsPageResult demandList(DemandQuery query);

    /**
     * 团队测评
     *
     * @param query
     * @return
     */
    Result<String> teamMatch(EvaluationQuery query);

    /**
     * 根据条件查询团队测评新版页面
     *
     * @param queryBO
     * @return
     */
    JSONObject teamGetBySys(TeamMatchQuery queryBO);

    /**
     * 团队测评新版-手动添加的实体
     *
     * @param queryBO
     * @return
     */
    JSONObject teamGetByUser(TeamMatchQuery queryBO);

    /**
     * 测评报告
     *
     * @param reportBO
     * @return
     */
    JSONObject teamReport(TeamReportQuery reportBO);

    /**
     * 关键技术分类统计
     *
     * @param chainId
     * @param ids
     * @return
     */
    List<CommonIndexBO> getRiskMap(String chainId, Set<String> ids);

    /**
     * 查询关键技术详情，并计算推荐团队和需求测评
     *
     * @param chainId
     * @param pageNum
     * @param pageSize
     * @return
     */
    EsPageResult listTechReport(String chainId, Integer pageNum, Integer pageSize);

    /**
     * 计算推荐团队和需求测评
     *
     * @param techId
     * @return
     */
    Map<String, Object> getTechReportById(String techId);
}
