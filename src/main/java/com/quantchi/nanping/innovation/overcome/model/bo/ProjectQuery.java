package com.quantchi.nanping.innovation.overcome.model.bo;

import com.quantchi.nanping.innovation.model.bo.PageBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/1/4 15:01
 */
@Data
@ApiModel("科技攻关项目查询条件")
public class ProjectQuery extends PageBO {

    @ApiModelProperty("项目名称/榜单名称")
    private String name;

    @ApiModelProperty("技术管理领域id")
    private String chainId;

    @ApiModelProperty("技术管理领域")
    private String chainName;

    @ApiModelProperty("承担单位")
    private String undertakingUnit;

    @ApiModelProperty("负责人")
    private String director;

    @ApiModelProperty("进展状态")
    private String projectStatus;

    @ApiModelProperty("项目类型")
    private String projectType;

}
