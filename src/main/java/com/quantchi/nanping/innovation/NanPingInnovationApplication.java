package com.quantchi.nanping.innovation;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@SpringBootApplication(scanBasePackages = {"com.quantchi.nanping","com.quantchi.tianying"})
@MapperScan({"com.quantchi.**.mapper","com.quantchi.**.dao"})
@EnableAsync
@EnableCaching
@EnableScheduling
@EnableSwagger2
@ConfigurationPropertiesScan
public class NanPingInnovationApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(NanPingInnovationApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(NanPingInnovationApplication.class);
    }

    /**
     * 全局ID生成器
     *
     * @return
     */
    @Bean
    public Snowflake snowflake() {
        return IdUtil.createSnowflake(1, 1);
    }
}
