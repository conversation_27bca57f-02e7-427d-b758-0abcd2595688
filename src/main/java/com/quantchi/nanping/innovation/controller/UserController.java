package com.quantchi.nanping.innovation.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.common.core.domain.ResultInfo;
import com.quantchi.common.core.exception.base.BusinessException;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.model.*;
import com.quantchi.nanping.innovation.model.bo.UserPageBo;
import com.quantchi.nanping.innovation.model.enums.BusinessType;
import com.quantchi.nanping.innovation.model.vo.EnterInfoVO;
import com.quantchi.nanping.innovation.service.IThirdFinanceService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.utils.*;
import com.wf.captcha.SpecCaptcha;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@Api(tags = "登录认证")
@RequestMapping("/user")
@Metrics
public class UserController {

    private final SysLoginService sysLoginService;

    @Autowired
    private StringRedisCache stringRedisCache;

    @Autowired
    private IThirdFinanceService thirdFinanceService;

//    @PostMapping("/valid_login")
//    @ApiOperation("用户登录(验证码)")
//    @Log(title = "用户-登录(验证码)")
//    @AntiReptile
//    public Result validLogin(@Validated @RequestBody LoginBody form) throws Exception {
//        // 用户登录
//        return sysLoginService.validLogin(form);
//    }

    @PostMapping("/valid_login_1")
    @ApiOperation("用户登录(首次认证)")
    @Log(title = "用户登录(首次认证)")
    @AntiReptile
    public Result validLogin1(@Validated @RequestBody LoginBody form, HttpServletRequest request) throws Exception {
        // 用户登录
        form.setIp(HttpClientUtils.getIpAddress(request));
        return sysLoginService.validLogin1(form);
    }

    @PostMapping("/valid_login_2")
    @ApiOperation("用户登录(二次认证)")
    @Log(title = "用户登录(二次认证)")
    @AntiReptile
    public Result validLogin2(@Validated @RequestBody LoginBody form) throws Exception {
        // 用户登录
        return sysLoginService.validLogin2(form);
    }

    @PostMapping("/valid_ca_login")
    @ApiOperation("用户登录(CA证书)")
    @Log(title = "用户-登录(CA证书)")
    @AntiReptile
    public Result<SaTokenInfo> validLogin(@Validated @RequestBody CALoginBody form) throws Exception {
        // 用户登录
        return sysLoginService.validLogin(form);
    }

    @GetMapping("/valid_login_code")
    @ApiOperation("用户登录(掌上南平)")
    @Log(title = "用户-登录(掌上南平)")
    public Result<SaTokenInfo> validLoginByCode(@Validated @NotBlank String code, HttpServletRequest request) throws Exception {
        // 用户登录
        return sysLoginService.validLoginByCode(code, HttpClientUtils.isPC(request));
    }

    @GetMapping("/userInfo")
    @ApiOperation(value = "获取当前用户信息")
    @SaCheckLogin
    //@Log(title = "用户-获取当前用户信息")
    public Result<UserInfoEntity> userInfo(boolean usePhone) {
        log.error("获取当前token:{}", JSONObject.toJSONString(StpUtil.getTokenValue()));
        UserInfoEntity userInfo = sysLoginService.getUserInfo(usePhone);
        if (!usePhone){
            userInfo.setPhone(null);
        }
        log.error("获取当前用户信息:{}", JSONObject.toJSONString(userInfo));
        return ResultConvert.success(userInfo);
    }

    @GetMapping("/authSign")
    @ApiOperation(value = "权限签名验证")
    @SaCheckLogin
    public Result authSign(String userId) {
        return ResultConvert.success(sysLoginService.getPermittedMenus(userId, true));
    }

    @GetMapping("/logout")
    @ApiOperation("注销登录")
    @Log(title = "用户-注销登录")
    public Result<Void> logout() {
        sysLoginService.logout();
        return ResultConvert.success();
    }

    @PostMapping("/enter_list")
    @ApiOperation(value = "获取企业用户列表")
    @SaCheckLogin
    @PlatformAuthCheck(type = {"0"})
    //@Log(title = "用户-获取企业用户列表")
    public Result<Page<EnterInfoVO>> listEnterprise(@Validated @RequestBody UserPageBo bo) {
        return ResultConvert.success(sysLoginService.page4Enterprise(bo));
    }

    @GetMapping("/captcha")
    @ResponseBody
    @ApiOperation("获得验证码")
    //@Log(title = "用户-登录获得验证码")
    @AntiReptile
    public Result<Dict> captcha() {
        final SpecCaptcha captcha = new SpecCaptcha(130, 48);
        final String verCode = captcha.text().toLowerCase();
        final String uid = IdUtil.simpleUUID();
        log.info("当前uid:{},验证码：{}", uid, verCode);
        stringRedisCache.put(uid, verCode, 3, TimeUnit.MINUTES);
        return ResultConvert.success(Dict.create().set("uid", uid).set("image", captcha.toBase64()));
    }

    @RequestMapping("/check")
    //@Log(title = "用户-图片下载鉴权")
    public ResponseEntity<Boolean> authImages(HttpServletRequest request, HttpServletResponse response) {
        boolean isValid = false;
        //获取请求头部
        String header = request.getHeader("x-original-uri");
        try {
            //截取鉴权参数，我这边为ticket 验证用户登录，可自行定义鉴权
            String token = header.split("=")[1];
            if (StringUtils.isNotBlank(token)) {
                String loginId = (String) StpUtil.getLoginIdByToken(token);
                isValid = loginId != null;
            }
            if (!isValid) {
                response.setHeader("WWW-authenticate", "Basic realm=\"没有权限\"");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
            return ResponseEntity.ok(true);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }
    }

    @GetMapping("/ca_random")
    @ResponseBody
    @ApiOperation("获得ca随机数")
    @AntiReptile
    public Result<String> getCARandom() {
        String random = SignUtil.getSignRandom();
        if (StringUtils.isEmpty(random)) {
            return ResultConvert.error(500, "密码服务调用异常");
        }
        return ResultConvert.success(random);
    }

    @GetMapping("/wechat_auth")
    @ResponseBody
    @ApiOperation("微信登录授权")
    public Result authWeChat() {
        StpUtil.login("wechat_" + UUID.randomUUID().toString(), 60 * 60 * 24);
        return ResultConvert.success(StpUtil.getTokenInfo());
    }

    @PostMapping("/modify_pass")
    @ApiOperation("用户密码修改")
    @Log(title = "用户密码修改", businessType = BusinessType.UPDATE, isSaveResponseData = true)
    public Result modifyPassword(@Validated @RequestBody final PasswordBody passwordBody) {
        return sysLoginService.modifyPassword(passwordBody);
    }

    @GetMapping("/get/financial_platform_ticket")
    @ApiOperation("获取绿色金融平台的免登票据")
    @Log(title = "跳转绿色金融平台", businessType = BusinessType.OTHER)
    public Result getFinancialPlatformTicket() {
        String result = thirdFinanceService.pushUserInfo();
        log.error("绿色金融平台请求返回：{}", result);
        if (result == null) {
            throw new BusinessException("获取绿色金融平台票据异常");
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getString("data") == null) {
            throw new BusinessException("获取绿色金融平台票据异常");
        }
        return ResultConvert.success(resultObj.getString("data"));
    }

    @GetMapping("/get/financial_platform_ticket_h5")
    @ApiOperation("获取绿色金融平台的免登票据_h5")
    @Log(title = "跳转绿色金融平台_h5", businessType = BusinessType.OTHER)
    public Result getFinancialPlatformTicketForH5() {
        String result = thirdFinanceService.pushUserInfo();
        log.error("绿色金融平台请求返回：{}", result);
        if (result == null) {
            throw new BusinessException("获取绿色金融平台票据异常");
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getString("data") == null) {
            throw new BusinessException("获取绿色金融平台票据异常");
        }
        JSONObject res = new JSONObject();
        res.put("ticket", resultObj.getString("data"));
        return ResultConvert.success(res);
    }


    @GetMapping("/loginByEBus")
    @ApiOperation("用户登录(易企办)")
    @Log(title = "用户-登录(易企办)")
    public JSONObject validLoginByEBus(HttpServletRequest request) {
        JSONObject result = new JSONObject();
        result.put("success", true);
        // 校验请求签名
        if (!EBusUtil.checkSign(request)) {
            result.put("code", 1002);
            result.put("errmsg", "接口签名验证失败");
            result.put("data", null);
            return result;
        }
        // 用户登录
        String ext = request.getHeader("x-tif-ext");
        SaTokenInfo saTokenInfo = sysLoginService.validLoginByEBusUserInfo(JSONObject.parseObject(Base64.getDecoder().decode(ext)));
        if (saTokenInfo == null){
            result.put("code", 5001);
            result.put("errmsg", "请选择关联企业进入办事");
            result.put("data", null);
        }else{
            result.put("code", 0);
            result.put("errmsg", null);
            result.put("data", saTokenInfo);
        }
        return result;
    }

    @ApiOperation("用户登录(闵政通)")
    @Log(title = "用户-登录(闵政通)")
    @GetMapping("/validLoginMZT")
    public Result<SaTokenInfo> validLoginMZT(@Validated @NotBlank String code, HttpServletRequest request) {
        return ResultConvert.success(sysLoginService.validLoginMZT(code, request));
    }


}
