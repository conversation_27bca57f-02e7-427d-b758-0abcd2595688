package com.quantchi.nanping.innovation.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.model.IndustryChain;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.model.vo.DmDivisionVO;
import com.quantchi.nanping.innovation.service.IDivisionService;
import com.quantchi.nanping.innovation.service.ISysUserChainScopeService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = "通用接口")
@Validated
@Metrics
public class CommonController {

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private IDivisionService divisionService;

    @Autowired
    private ICompanyNodeRelationService companyNodeRelationService;

    @Autowired
    private ISysUserChainScopeService chainScopeService;

    @ApiOperation(value = "产业领域分类", notes = "获取产业领域分类")
    @GetMapping("/industry/field")
    //@AntiReptile
    //@Log(title = "通用-产业领域分类")
    public Result<List<IndustryChain>> field(boolean all) {
        List<IndustryChain> chainList = industryChainService.listChain(true);
        if (!all){
            Set<String> permittedChainIds = chainScopeService.getCurrentUserPermittedChainIds();
            chainList.removeIf(c -> CollectionUtils.isNotEmpty(permittedChainIds) && !permittedChainIds.contains(c.getId()));
        }
        return ResultConvert.success(chainList);
    }

    @ApiOperation(value = "节点模糊搜索")
    @GetMapping("/industry/node")
    //@Log(title = "通用-节点模糊搜索")
    public Result<List<IndustryChainNode>> field(@NotBlank String chainId, @NotBlank String key) {
        return ResultConvert.success(industryChainService.findNodeByKey(chainId, key));
    }

    @ApiOperation(value = "省市县三级结构")
    @GetMapping("/division")
    //@Log(title = "通用-省市县三级结构")
    public Result<DmDivisionVO> division(String areaName) {
        return ResultConvert.success(divisionService.getDivision(areaName));
    }

    @ApiOperation(value = "获取地图json")
    @GetMapping("/geoJson")
    //@Log(title = "通用-获取地图json")
    public Result geoJson(String region) {
        return ResultConvert.success(divisionService.getDivisionMap(region));
    }

    @ApiOperation(value = "查询产业节点树", notes = "用于门户未登录时需求填报和门户要素库节点筛选")
    @GetMapping("/industry/tree")
    @AntiReptile
    public Result<List<IndustryChainNode>> getIndustryTree(String entityId, @RequestParam(defaultValue = "0", required = false) int userType) {
        List<IndustryChainNode> rootList = industryChainService.listAllTrees();
        if (StpUtil.isLogin() && StringUtils.isNotEmpty(entityId)){
            // 查询企业所在的链
            Pair<Set<String>, Set<String>> chainInfo = companyNodeRelationService.getRelatedNodeIdsByEntityId(entityId,
                    UserInfoEntity.USER_COMPANY == userType, UserInfoEntity.USER_EXPERT == userType);
            Set<String> chainIds = chainInfo.getLeft();
            if (CollectionUtils.isNotEmpty(chainIds)){
                for (IndustryChainNode root: rootList){
                    if (chainIds.contains(root.getChainId())){
                        Map<String, Object> propertyMap = new HashMap<>();
                        propertyMap.put("located", true);
                        root.setProperties(propertyMap);
                    }
                }
            }
        }
        return ResultConvert.success(rootList);
    }

    @ApiOperation("查询产业链根节点")
    @GetMapping("/industry/root")
    //@Log(title = "通用-查询产业链根节点")
    public Result<List<IndustryChainNode>> getIndustryRootList() {
        return ResultConvert.success(industryChainService.listRoots());
    }

}
