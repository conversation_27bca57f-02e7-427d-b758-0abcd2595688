package com.quantchi.nanping.innovation.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.model.TechPoint;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.demand.model.Demand;
import com.quantchi.nanping.innovation.demand.model.DemandConfig;
import com.quantchi.nanping.innovation.demand.model.DemandExpertConfig;
import com.quantchi.nanping.innovation.demand.model.enums.DemandTypeEnum;
import com.quantchi.nanping.innovation.demand.service.IDemandConfigService;
import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.insight.model.bo.PersonStatsBo;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.demand.model.bo.DemandBO;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.entity.DmDivisionEntity;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.IDivisionService;
import com.quantchi.nanping.innovation.service.ITechCommissionDemandService;
import com.quantchi.nanping.innovation.service.ITechCommissionerService;
import com.quantchi.nanping.innovation.service.ITechCommissionService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.DemandService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.nanping.innovation.utils.*;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/5/4 16:32
 */
@Slf4j
@RestController
@RequestMapping("/commission")
@Api(tags = "科特派3.0")
@PlatformAuthCheck(type = {"0"})
@Metrics
public class TechCommissionController {

    /**
     * 匹配尝试次数redis存放前缀
     */
    private final static String MATCH_REDIS_KEY = "commission:match:count:";

    /**
     * 匹配尝试次数限制
     */
    private final static Integer MATCH_COUNT_LIMIT = 10;

    @Value("${model.expertMatch}")
    private String expertMatchUrl;

    @Autowired
    private TalentService talentService;

    @Autowired
    private IDivisionService divisionService;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private DemandService demandService;

    @Autowired
    private ITechCommissionerService techCommissionerService;

    @Autowired
    private ITechCommissionDemandService techCommissionDemandService;

    @Autowired
    private ITechCommissionService techCommissionService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private IIndexFusionService indexFusionService;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private StringRedisCache redisCache;

    @Autowired
    private IDemandConfigService demandConfigService;

    @ApiOperation(value = "本地人才情况")
    @GetMapping("/local")
    @Log(title = "科特派")
    public Result<Map<String, Object>> getLocalDetail() {
        Map<String, Object> resultMap = new HashMap<>(4);
        PersonStatsBo bo = PersonStatsBo.build4Source(null, null, null, null, true, false);
        // 等级分布
        resultMap.put("level", talentService.getRankDistribution(bo, true));
        // 学历分布
        resultMap.put("degree", talentService.getDegreeDistribution(bo, true));
        // 职称分布
        resultMap.put("title", talentService.getProfTitleDistribution(bo, true));
        // 产业链分布
        resultMap.put("inChain", talentService.getDistributionInChain(bo, true));
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "科技特派员情况")
    @GetMapping("/commissioner")
    //@Log(title = "科特派-科技特派员情况")
    public Result<Map<String, Object>> getCommissionDetail() {
        Map<String, Object> resultMap = new HashMap<>(4);
        String cityId = null, areaId = RequestContext.getAreaId();
        // 服务情况
        // 1.累计科特派人数
        Long totalNum = techCommissionerService.countCommissionerNum(cityId, areaId);
        // 2.服务对象数量
        Long serviceOrgNum = techCommissionerService.countServiceOrgNum(cityId, areaId);
        // 3.对接需求数量
        Long demandNum = techCommissionDemandService.countNumExcludeUndocking(cityId, areaId);
        // 4.人均对接需求数量
        BigDecimal avgDockNum = BigDecimalUtil.divide(new BigDecimal(demandNum), new BigDecimal(totalNum), 1, RoundingMode.HALF_UP);
        Map<String, Object> overviewMap = new HashMap<>(4);
        overviewMap.put("totalNum", totalNum);
        overviewMap.put("serviceOrgNum", serviceOrgNum);
        overviewMap.put("demandNum", demandNum);
        overviewMap.put("avgDockNum", avgDockNum);
        resultMap.put("overview", overviewMap);
        // 学历分布
        resultMap.put("degree", techCommissionerService.getDegreeDistribution(cityId, areaId));
        // 职称分布
        resultMap.put("title", techCommissionerService.getProfTitleDistribution(cityId, areaId));
        // 科技特派人数与需求增长趋势
        int years = 5;
        // 1.科技特派人数增长趋势
        Map<Integer, Long> personMap = techCommissionerService.getGrowth(cityId, areaId, years);
        // 2.需求增长趋势
        Map<Integer, Long> demandMap = demandService.getDemandGrowth(years, null);
        List<String> interval = DateUtils.getRecentYears(years);
        Map<String, Map<String, Long>> tendMap = new LinkedHashMap<>();
        for (int i = years - 1; i >= 0; i--) {
            String year = interval.get(i);
            Map<String, Long> current = new HashMap<>();
            current.put("demand", demandMap.get(Integer.parseInt(year)));
            current.put("person", personMap.get(Integer.parseInt(year)));
            tendMap.put(year, current);
        }
        resultMap.put("tend", tendMap);
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "南平各区县人才情况")
    @GetMapping("/distribution")
    //@Log(title = "科特派-南平各区县人才情况")
    public Result<Map<String, Object>> getDistribution(String chainId) {
        List<DmDivisionEntity> areaList = divisionService.listByParentId(CommonConstant.DIVISION_NANPING.getId());
        Map<String, Object> resultMap = new HashMap<>(areaList.size());
        List<CompletableFuture<Void>> futureList = new ArrayList<>(areaList.size());
        for (DmDivisionEntity area : areaList) {
            final CompletableFuture<Void> areaFuture = CompletableFuture.runAsync(() -> {
                Map<String, Long> countMap = new HashMap<>();
                countMap.putAll(talentService.getRankDistribution(PersonStatsBo.build4Source(chainId, null, area.getParentId(), area.getId(),
                        true, false), false));
                countMap.put("masterPlus", talentService.countMasterPlusNum(chainId, null, area.getParentId(), area.getId(), false));
                resultMap.put(area.getName(), countMap);
            }, taskExecutor);
            futureList.add(areaFuture);
        }
        TaskUtil.get(futureList);
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "需求匹配列表")
    @PostMapping("/requirement_matching")
    public Result listRequirementMatching(@RequestBody DemandBO bo) {
        return ResultConvert.success(demandService.page(bo, true, false));
    }

    @ApiOperation(value = "需求匹配-发起匹配")
    @GetMapping("/pageMatchingExpert")
    public Result pageMatchingExpert(@Valid @NotBlank String demandId) {
        return ResultConvert.success(demandService.pageMatchingExpert(demandId));
    }

    @PostMapping(value = "/expert/match")
    @ApiOperation("轮询获取人才匹配结果")
    @Log(title = "获取技术人才匹配结果")
    @PlatformAuthCheck()
    public Result<List<Map<String, Object>>> match(@RequestBody Demand demand) {
        String demandId = demand.getId(), demandContent = demand.getContent(), chainName = demand.getChainName();
        if (StringUtils.isAnyEmpty(demandId, demandContent, chainName)){
            return ResultConvert.error(500, "缺少必要参数");
        }
        // 查询数据库是否存在配置
        DemandConfig demandConfig = demandConfigService.getByDemandId(demandId);
        if (demandConfig != null && StringUtils.isNotEmpty(demandConfig.getExpertIds())){
            // 返回专家列表
            log.error("获取需求{" + demandId + "}专家信息---数据库");
            return ResultConvert.success(talentService.recommendByIds(demandId, Arrays.asList(demandConfig.getExpertIds().split(","))));
        }
        // 查询大模型是否已返回结果
        Map<String, Object> matchResult = elasticsearchHelper.getDataById(EsIndexEnum.MODEL_RESULT_POOL.getEsIndex(), demandId, null, null);;
        String countKey = MATCH_REDIS_KEY + demandId;
        if (matchResult == null || !matchResult.containsKey("experts")
                || CollectionUtils.isEmpty((List<Map<String, Object>>) matchResult.get("experts"))) {
            String existedCount = redisCache.get(countKey);
            // 判断是否已经发起人才匹配
            if (existedCount == null){
                existedCount = "0";
                redisCache.put(countKey, existedCount, 1, TimeUnit.DAYS);
                taskExecutor.execute(() -> {
                    String response = HttpClientUtils.post(expertMatchUrl, new TechPoint(demandId, "在"+chainName+"产业链下,"+demand.getContent(), null));
                    log.error("发起人才匹配：{}", response);
                });
                return ResultConvert.error(1000, "暂未获取到人才匹配结果");
            }
            //计数，超过10次不再查询
            if (StringUtils.isNotEmpty(existedCount) && Integer.parseInt(existedCount) == MATCH_COUNT_LIMIT) {
                log.error("获取人才匹配尝试次数达到上限，技术点id：{}", demandId);
                redisCache.remove(countKey);
                return ResultConvert.error(1001, "获取人才匹配尝试次数达到上限");
            }
            redisCache.put(countKey, String.valueOf(Integer.parseInt(existedCount) + 1), 1, TimeUnit.DAYS);
            return ResultConvert.error(1000, "暂未获取到人才匹配结果");
        }
        redisCache.remove(countKey);
        elasticsearchHelper.delete(EsIndexEnum.MODEL_RESULT_POOL.getEsIndex(), demandId);
        log.error("获取需求{" + demandId + "}专家信息---ES");
        List<Map<String, Object>> matchExpertList = (List<Map<String, Object>>) matchResult.get("experts");
        List<String> expertIds = new ArrayList<>();
        List<DemandExpertConfig> expertConfigs = new ArrayList<>();
        for (Map<String, Object> expert : matchExpertList) {
            if (expertIds.size() > 5){
                continue;
            }
            String expertId = (String) expert.get("id");
            expertIds.add(expertId);
            DemandExpertConfig expertConfig = new DemandExpertConfig();
            expertConfig.setDemandId(demandId);
            expertConfig.setExpertId(expertId);
            Map<String, Object> matchScoreMap = (Map<String, Object>) expert.get("match_score");
            expertConfig.setAchievementScore(new BigDecimal(String.valueOf(matchScoreMap.get("achievement"))));
            expertConfig.setMatchedScore(new BigDecimal(String.valueOf(matchScoreMap.get("matched"))));
            expertConfig.setInfluenceScore(new BigDecimal(String.valueOf(matchScoreMap.get("influence"))));
            expertConfig.setCooperationScore(new BigDecimal(String.valueOf(matchScoreMap.get("cooperation"))));
            expertConfig.setPerformanceScore(new BigDecimal(String.valueOf(matchScoreMap.get("performance"))));
            expertConfig.setFinalScore(new BigDecimal(String.valueOf(matchScoreMap.get("final_score"))));
            expertConfigs.add(expertConfig);
        }
        // 更新专家分数配置 先删除候增加
        demandConfigService.saveBatchExpertConfig(demandId, expertConfigs);
        // 保存大模型返回结果
        if (demandConfig != null){
            demandConfig.setExpertIds(StringUtils.join(expertIds, ","));
            demandConfigService.update(Wrappers.lambdaUpdate(DemandConfig.class)
                    .set(DemandConfig::getExpertIds, demandConfig.getExpertIds())
                    .eq(DemandConfig::getDemandId, demandId));
        }else{
            demandConfig = new DemandConfig();
            demandConfig.setDemandId(demandId);
            demandConfig.setExpertIds(StringUtils.join(expertIds, ","));
            if (DemandTypeEnum.TALENT.equals(demand.getType())){
                demandConfig.setDisplayCompany(DemandConfig.HIDE);
            }
            demandConfigService.save(demandConfig);
        }
        // 返回专家列表
        return ResultConvert.success(talentService.recommendByIds(demandId, expertIds));
    }

    @ApiOperation("节点筛选树")
    @GetMapping("/tree")
    //@Log(title = "科特派-节点筛选树")
    public Result<List<IndustryChainNode>> getSelectedTree(String chainId) {
        return ResultConvert.success(industryChainService.getSelectedTreeIncludesTwoLevel(chainId));
    }

    @ApiOperation("人才链现状（新）")
    @GetMapping("/info")
    public Result<Map<String, Object>> getBaseInfo(@RequestParam @NotBlank String chainId) {
        // 本地人才
        Long localNum = talentService.countExpertNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(),
                null, false);
        // 硕士及以上人才
        Long masterNum = talentService.countMasterPlusNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(),
                null, false);
        // 评价
        IndexFusion fusion = indexFusionService.getOne(FusionIndexEnum.PERSON_CHAIN.getIndexId(), chainId, null,
                CommonConstant.DIVISION_NANPING.getId(), true);
        // 产业链名称
        String chainName = industryChainService.getChainNameById(chainId);
        // 本地科特派人才数量
//        Long localCommissionNum = talentService.countExpertNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(),
//                RequestContext.getAreaId(), true);
        // 本地E类以上人才数量
//        PersonStatsBo bo = PersonStatsBo.build4Level(chainId, null, CommonConstant.DIVISION_NANPING.getId(),
//                RequestContext.getAreaId(), "E", true);
//        Long localEPlusNum = talentService.countByLevel(bo, true);
        // 本地高职称人才
        Long highTitleNum = talentService.countSeniorNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(), null);
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put("localNum", localNum);
        infoMap.put("masterNum", masterNum);
        infoMap.put("evaluation", fusion.getDescription());
        infoMap.put("chainName", chainName);
        infoMap.put("highTitleNum", highTitleNum);
        return ResultConvert.success(infoMap);
    }

//    @ApiOperation("人才区县情况（新）")
//    @GetMapping("/area/distribution")
//    public Result<Map<String, Long>> getAreaDistribution(@RequestParam @NotBlank String chainId) {
//        return ResultConvert.success(talentService.countLocalNumByRegion(chainId));
//    }

    @ApiOperation("需求节点分布情况（新）")
    @GetMapping("/demand/node")
    public Result<PieVO> getDemandNodeDistribution(@RequestParam @NotBlank String chainId) {
        return ResultConvert.success(demandService.getDemandFirstNodeDistribution(chainId));
    }

    @ApiOperation("人才学历、职称分布（新）")
    @GetMapping("/degree_title/distribution")
    public Result<Map<String, Object>> getDegreeTitleDistribution(@RequestParam @NotBlank String chainId) {
        try {
            return ResultConvert.success(techCommissionService.getDegreeTitleDistribution(chainId));
        } catch (Exception e) {
            log.error("获取学历职称分布失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

    @ApiOperation("本地人才节点分布（新）")
    @GetMapping("/node/distribution")
    public Result<Map<String, Object>> getNodeDistribution(@RequestParam @NotBlank String chainId) {
        return ResultConvert.success(talentService.countLocalNumByNode(chainId));
    }

    @ApiOperation("外部人才节点分布（新）")
    @GetMapping("/external/node/distribution")
    public Result<PieVO> getExternalNodeDistribution(@RequestParam @NotBlank String chainId) {
        try {
            return ResultConvert.success(techCommissionService.getExternalNodeDistribution(chainId));
        } catch (Exception e) {
            log.error("获取外部人才节点分布失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

}
