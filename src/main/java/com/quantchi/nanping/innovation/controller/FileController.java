package com.quantchi.nanping.innovation.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.RateLimiter;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.model.enums.BusinessType;
import com.quantchi.nanping.innovation.service.IFileService;
import com.quantchi.nanping.innovation.utils.AESUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Base64Utils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/1 11:06
 */
@RestController
@RequestMapping("/file")
@Api(tags = "文件管理")
@Validated
public class FileController {

    /**
     * 文件大小限制
     */
    private static final Long FILE_SIZE_LIMIT = Long.valueOf(20 * 1048576);
    /**
     * 文件格式类型限制
     */
    private static final List<String> FILE_TYPE_LIMIT = Arrays.asList(".png", ".jpg", ".jpeg", ".bmp", ".gif", ".webp",
            ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf");

    @Autowired
    private IFileService fileService;

    @ApiOperation(value = "文件上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件内容", required = true, dataType = "MultipartFile")
    })
    @PostMapping("/upload")
    @Metrics
    @RateLimiter(key = "rate_limit:upload:")
    @Log(title = "文件管理-上传", businessType = BusinessType.IMPORT, isSaveRequestData = false)
    public Result<FileInfo> upload(@RequestParam @NotNull MultipartFile file, @RequestParam @NotNull String sign) {
        String decryptedSign = AESUtil.decrypt(sign);
        String[] signArray = decryptedSign.split("\\*");
        if (signArray.length < 2) {
            throw new BusinessException("接口签名校验不通过");
        }
        String fileName = signArray[0];
        if (!fileName.equals(file.getOriginalFilename())) {
            throw new BusinessException("接口签名校验不通过");
        }
        String timestamp = signArray[1];
        if (Long.parseLong(timestamp) + 120000 < System.currentTimeMillis()) {
            throw new BusinessException("接口签名已失效");
        }
        if (file.getSize() > FILE_SIZE_LIMIT) {
            throw new BusinessException("上传文件大小不得超过20MB，请拆分后上传");
        }
        if (StringUtils.isEmpty(file.getOriginalFilename()) || !file.getOriginalFilename().contains(".")) {
            throw new BusinessException("文件格式类型不支持，请更换成以下类型：" + StringUtils.join(FILE_TYPE_LIMIT, ","));
        }
        String fileSuffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        if (!FILE_TYPE_LIMIT.contains(fileSuffix)) {
            throw new BusinessException("文件格式类型不支持，请更换成以下类型：" + StringUtils.join(FILE_TYPE_LIMIT, ","));
        }
        return ResultConvert.success(fileService.upload(file));
    }

    @ApiOperation(value = "文件下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, dataType = "String")
    })
    @GetMapping("/download")
    @Log(title = "文件管理-下载", businessType = BusinessType.EXPORT)
    public void download(@RequestParam @NotBlank String fileId, boolean inline, HttpServletResponse response) {
        fileService.download(fileId, inline, response);
    }

    @ApiOperation(value = "文件删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, dataType = "String")
    })
    @GetMapping("/delete")
    @Metrics
    @Log(title = "文件管理-删除", businessType = BusinessType.DELETE)
    public Result delete(@RequestParam @NotBlank String fileId) {
        return ResultConvert.success(fileService.deleteByFileId(fileId));
    }

    @ApiOperation(value = "文件预览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "token", value = "验证参数", required = true, dataType = "String")
    })
    @GetMapping("/preview")
    @Log(title = "文件管理-预览", businessType = BusinessType.EXPORT)
    public void preview(@RequestParam @NotBlank String fileId, @RequestParam @NotBlank String auth, HttpServletResponse response) {
        String authParam = AESUtil.decrypt(auth);
        String[] paramArray = authParam.split(",");
        if (paramArray.length < 2) {
            throw new BusinessException("接口签名校验不通过");
        }
        String tokenValue = paramArray[0];
        if (StpUtil.getLoginIdByToken(tokenValue) == null){
            throw new BusinessException("无文件预览权限,请重新登录");
        }
        String timestamp = paramArray[1];
        if (Long.parseLong(timestamp) + 120000 < System.currentTimeMillis()) {
            throw new BusinessException("无文件预览权限，请重新打开");
        }
        fileService.download(fileId, true, response);
    }
}
