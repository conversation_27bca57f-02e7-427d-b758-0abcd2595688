package com.quantchi.nanping.innovation.controller;

import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.model.LocalCompanyInvested;
import com.quantchi.nanping.innovation.service.ILocalInvestmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/24 11:06
 */
@Slf4j
@RestController
@RequestMapping("/local_invest")
@Api(tags = "央企本地投资情况")
@Metrics
@Validated
public class LocalInvestmentController {

    @Autowired
    private ILocalInvestmentService localInvestmentService;

    @ApiOperation(value = "央企在本地的投资情况")
    @GetMapping("/tree")
    @Log(title = "央企在本地的投资情况")
    public Result<LocalCompanyInvested> getTree() {
        return ResultConvert.success(localInvestmentService.getTree());
    }
}
