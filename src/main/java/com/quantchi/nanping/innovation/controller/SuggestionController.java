package com.quantchi.nanping.innovation.controller;

import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.model.admin.SysUsageSuggestion;
import com.quantchi.nanping.innovation.service.IUsageSuggestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/4 14:05
 */
@Slf4j
@RestController
@RequestMapping("/suggest")
@Api(tags = "用户反馈")
@PlatformAuthCheck()
@Metrics
public class SuggestionController {

    @Autowired
    private IUsageSuggestionService suggestionService;

    @ApiOperation(value = "提交建议")
    @PostMapping("/submit")
    @Log(title = "用户反馈")
    public Result submit(@RequestBody @Validated SysUsageSuggestion suggestion) {
        return ResultConvert.success(suggestionService.add(suggestion));
    }
}
