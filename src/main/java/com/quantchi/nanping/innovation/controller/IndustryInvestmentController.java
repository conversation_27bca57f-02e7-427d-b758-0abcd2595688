package com.quantchi.nanping.innovation.controller;

import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.AreaNodeRelation;
import com.quantchi.nanping.innovation.model.IndustryChainNodeGroup;
import com.quantchi.nanping.innovation.model.IndustryChainNodeTarget;
import com.quantchi.nanping.innovation.model.IndustryChainNodeType;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.CompanyPageBo;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.CompanyTagEnum;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.IAreaNodeRelationService;
import com.quantchi.nanping.innovation.service.IChainNodeGroupService;
import com.quantchi.nanping.innovation.service.IIndustryChainNodeTargetService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/11 14:39
 */
@Slf4j
@RestController
@RequestMapping("/industry")
@Api(tags = "产业招商")
@PlatformAuthCheck(type = {"0"})
@Metrics
@Validated
public class IndustryInvestmentController {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private IIndexFusionService fusionService;

    @Autowired
    private IChainNodeGroupService chainNodeGroupService;

    @Autowired
    private IAreaNodeRelationService areaNodeRelationService;

    @Autowired
    private IIndustryChainNodeTargetService nodeTargetService;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @ApiOperation(value = "产业链概况")
    @GetMapping("/index")
    @Log(title = "产业招商")
    public Result<List<CommonIndexBO>> getIndexDetail(@NotBlank String chainId) {
        List<CommonIndexBO> indexBOList = new ArrayList<>();
        // 本地企业数
        Long localNum = companyService.countNumInChain(chainId, null, CommonConstant.DIVISION_NANPING.getId(), null, true);
        indexBOList.add(new CommonIndexBO("本地企业数", localNum, "家"));
        // 外部企业数
        Long externalNum = companyService.countExternalNumInChain(chainId, null);
        indexBOList.add(new CommonIndexBO("外部企业数", externalNum, "家"));
        // 强补固拓节点数
        indexBOList.addAll(industryChainService.countNodeTypeMapByChainId(chainId, null, true));
        // 产业链评价
        IndexFusion indexFusion = fusionService.getOne(FusionIndexEnum.INDUSTRY_CHAIN.getIndexId(), chainId, null, CommonConstant.DIVISION_NANPING.getId(), true);
        indexBOList.add(new CommonIndexBO("产业链评价", indexFusion.getDescription()));
        return ResultConvert.success(indexBOList);
    }

    @ApiOperation(value = "产业链评价指标")
    @GetMapping("/sub_index")
    public Result<Map<String, Object>> getSubIndexDetail(@NotBlank String chainId) {
        Map<String, Object> resultMap = new HashMap<>();
        // 产业链名称
        String chainName = industryChainService.getChainNameById(chainId);
        // 高新技术企业数量
        Long highTechNum = companyService.countCompanyNumByTag(chainId, null, CommonConstant.DIVISION_NANPING.getId(), null, false, CompanyTagEnum.HIGH_TECH.getName());
        // 分布节点数量和节点占比
        Long nodeNum = companyService.countNodeNumRelatedCompany(chainId, CommonConstant.DIVISION_NANPING.getId(), null, false);
        Long allNodeNum = industryChainService.countNodes(chainId);
        BigDecimal nodeProportion = new BigDecimal(nodeNum * 100).divide(new BigDecimal(allNodeNum),0, RoundingMode.HALF_UP);
        // 本地企业员工数
        Long employeeNum = companyService.countCompanyEmployeeNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(), null, false);
        // 本地企业数
        Long localNum = companyService.countNumInChain(chainId, null, CommonConstant.DIVISION_NANPING.getId(), null, false);
        resultMap.put("chainName", chainName);
        resultMap.put("highTechNum", highTechNum);
        resultMap.put("nodeNum", nodeNum);
        resultMap.put("nodeProportion", nodeProportion);
        resultMap.put("employeeNum", employeeNum);
        resultMap.put("localNum", localNum);
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "产业链图谱")
    @GetMapping("/node_tree")
    public Result<IndustryChainNodeGroup> getTree(@NotBlank String chainId) {
        return ResultConvert.success(chainNodeGroupService.getTreeByChainId(chainId));
    }

//    @ApiOperation(value = "节点区县分布")
//    @GetMapping("/node_map")
//    public Result<Map<String, List<AreaNodeRelation>>> getAreaNodeDistribution(@NotBlank String chainId) {
//        return ResultConvert.success(areaNodeRelationService.getMapByChainId(chainId));
//    }

    @ApiOperation(value = "企业总数")
    @GetMapping("/count")
    public Result<Long> getCompanyCount(@NotBlank String chainId, String chainNodeId) {
        Long localNum = companyService.countNumInChain(chainId, chainNodeId, null, null, true);
        Long externalNum = companyService.countExternalNumInChain(chainId, chainNodeId);
        return ResultConvert.success(localNum + externalNum);
    }

    @ApiOperation(value = "企业总数(详细)")
    @GetMapping("/count/detail")
    public Result<Map<String, Long>> getCompanyCountDetail(@NotBlank String chainId, String chainNodeId) {
        Long localNum = companyService.countNumInChain(chainId, chainNodeId, null, null, true);
        Long externalNum = companyService.countExternalNumInChain(chainId, chainNodeId);
        Map<String, Long> resultMap = new HashMap<>();
        resultMap.put("localNum", localNum);
        resultMap.put("externalNum", externalNum);
        resultMap.put("totalNum", localNum + externalNum);
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "产业链-靶向企业分析")
    @GetMapping("/target_analysis")
    public Result<List<CommonIndexBO>> getTargetAnalysis(@NotBlank String chainId, @NotBlank String chainNodeId) {
        // 判断是否指定了靶向企业
        List<IndustryChainNodeTarget> nodeTargetList = nodeTargetService.listByNodeIds(Arrays.asList(chainNodeId));
        List<String> companyIds = nodeTargetList.stream().map(IndustryChainNodeTarget::getCompanyId).collect(Collectors.toList());
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(companyIds)){
            boolQueryBuilder.filter(QueryBuilders.idsQuery().addIds(companyIds.toArray(new String[0])));
        }else{
            boolQueryBuilder = companyService.buildTargetCompanyQuery(chainId, chainNodeId);
        }
        Map<String, Long> tagMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.COMPANY, boolQueryBuilder, "tag.name");
        List<CommonIndexBO> indexBOList = EsAlterUtil.dealWithTopDistribution(tagMap, 5, "家");
        return ResultConvert.success(indexBOList.subList(0, indexBOList.size() - 1));
    }

    @ApiOperation(value = "产业链-强补固拓节点定义")
    @GetMapping("/node_type_define")
    public Result<Map<String, List<String>>> getTargetAnalysis(@NotBlank String chainId, @NotNull Integer nodeTypeId) {
        return ResultConvert.success(industryChainService.getNodeTypeDefine(chainId, nodeTypeId));
    }

}
