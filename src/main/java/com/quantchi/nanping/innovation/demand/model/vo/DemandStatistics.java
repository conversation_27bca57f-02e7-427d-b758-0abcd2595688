package com.quantchi.nanping.innovation.demand.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/29 16:44
 */
@Data
@ApiModel("需求统计")
public class DemandStatistics {

    @ApiModelProperty("今日新增需求数")
    private Long todayNum;

    @ApiModelProperty("累计需求数")
    private Long totalNum;

    @ApiModelProperty("未对接需求数")
    private Long notStartNum;

    @ApiModelProperty("对接中需求数")
    private Long processingNum;

    @ApiModelProperty("待评价需求数")
    private Long toEvaluateNum;

}
