package com.quantchi.nanping.innovation.demand.util;

import com.quantchi.nanping.innovation.utils.AESUtil;
import com.quantchi.nanping.innovation.utils.EncryptUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2023/7/19 15:12
 */
public class EncryptTypeHandler extends BaseTypeHandler<String> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, String t, JdbcType jdbcType) throws SQLException {
        //preparedStatement.setString(i, EncryptUtil.encryptSM4FixIndex(t));
        preparedStatement.setString(i, AESUtil.encrypt(t));
    }

    @Override
    public String getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String columnValue = resultSet.getString(s);
        //return StringUtils.isBlank(columnValue) ? columnValue : EncryptUtil.decryptSM4FixIndex(columnValue);
        return StringUtils.isBlank(columnValue) ? columnValue : AESUtil.decrypt(columnValue);
    }

    @Override
    public String getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String columnValue = resultSet.getString(i);
        //return StringUtils.isBlank(columnValue) ? columnValue : EncryptUtil.decryptSM4FixIndex(columnValue);
        return StringUtils.isBlank(columnValue) ? columnValue : AESUtil.decrypt(columnValue);
    }

    @Override
    public String getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String columnValue = callableStatement.getString(i);
        //return StringUtils.isBlank(columnValue) ? columnValue : EncryptUtil.decryptSM4FixIndex(columnValue);
        return StringUtils.isBlank(columnValue) ? columnValue : AESUtil.decrypt(columnValue);
    }
}
