package com.quantchi.nanping.innovation.demand.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.nanping.innovation.demand.model.Demand;
import com.quantchi.nanping.innovation.demand.model.DemandEvaluation;
import com.quantchi.nanping.innovation.demand.model.DemandProcess;
import com.quantchi.nanping.innovation.demand.model.bo.ProcessPageBO;
import com.quantchi.nanping.innovation.demand.model.vo.DemandStatistics;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.demand.model.bo.DemandBO;
import com.quantchi.nanping.innovation.model.vo.PieVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23 18:13
 */
public interface IDemandMngService {

    /**
     * 获得需求管理的统计面板
     *
     * @param creditCode
     * @return
     * @throws Exception
     */
    DemandStatistics getStatistics(String creditCode) throws Exception;

    /**
     * 按照产业链计数
     *
     * @return
     */
    List<CommonIndexBO> countByChain();

    /**
     * 分页
     *
     * @param bo
     * @return
     */
    Page<Demand> page(DemandBO bo);

    /**
     * 获得需求详情
     *
     * @param id
     * @return
     */
    Demand getById(String id);

    /**
     * 按照需求状态统计
     *
     * @return
     */
    PieVO analyzeByStatus();

    /**
     * 按照需求类型统计
     *
     * @return
     */
    PieVO analyzeByType();

    /**
     * 需求提交
     *
     * @param demand
     * @return
     */
    boolean submit(Demand demand);

    /**
     * 需求编辑
     *
     * @param demand
     * @return
     */
    boolean edit(Demand demand);

    /**
     * 需求受理
     *
     * @param process
     * @return
     */
    boolean accept(DemandProcess process);

    /**
     * 需求退回
     *
     * @param demand
     * @return
     */
    boolean reject(Demand demand);

    /**
     * 提交状态跟进
     *
     * @param process
     * @return
     */
    boolean follow(DemandProcess process);

    /**
     * 结束服务
     *
     * @param process
     * @return
     */
    boolean end(DemandProcess process);

    /**
     * 跟进记录列表
     *
     * @param processBO
     * @return
     */
    Page<DemandProcess> listProcess(ProcessPageBO processBO);

    /**
     * 评价
     *
     * @param evaluation
     * @return
     */
    boolean evaluate(DemandEvaluation evaluation);

    /**
     * 获取需求评价
     *
     * @param id
     * @return
     */
    List<DemandEvaluation> listEvaluationByDemandId(String id);

    /**
     * 需求催办
     *
     * @param id
     * @return
     */
    boolean urging(String id);

    /**
     * 企业门户需求列表
     *
     * @param bo
     * @return
     */
    Page<Demand> page4Portal(DemandBO bo);

    /**
     * 专家首页需求列表
     *
     * @param bo
     * @return
     */
    Page<Demand> page4ExpertPortal(DemandBO bo);

    /**
     * 需求揭榜
     *
     * @param id
     * @return
     */
    boolean selfRecommend(String id);

    /**
     * 按类型罗列需求
     *
     * @param chainId
     * @param type
     * @param minStatus 处理状态满足条件
     * @return
     */
    List<Demand> listByType(String chainId, String type, int minStatus);

    /**
     * 删除需求
     *
     * @param id
     * @return
     */
    boolean delete(String id);

    Demand findExistedDemand(String id);
}
