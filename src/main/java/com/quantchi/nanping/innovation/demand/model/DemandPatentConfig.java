package com.quantchi.nanping.innovation.demand.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/19 9:45
 */
@Data
@TableName("demand_patent_config")
public class DemandPatentConfig extends BaseTime {

    @ApiModelProperty("需求专家配置id")
    @TableId(value = "id")
    private String id;

    @ApiModelProperty("需求id")
    private String demandId;

    @ApiModelProperty("专利id")
    private String patentId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("关键词，用,分隔")
    private String keywords;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("摘要")
    @TableField(exist = false)
    private String abstractContent;

}
