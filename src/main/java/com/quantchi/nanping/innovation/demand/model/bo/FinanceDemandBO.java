package com.quantchi.nanping.innovation.demand.model.bo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.demand.condition.Submit;
import com.quantchi.nanping.innovation.demand.condition.ThirdSyn;
import com.quantchi.nanping.innovation.demand.condition.UserSubmit;
import com.quantchi.nanping.innovation.demand.model.Demand;
import com.quantchi.nanping.innovation.demand.model.DemandProcess;
import com.quantchi.nanping.innovation.demand.model.DemandUndertake;
import com.quantchi.nanping.innovation.demand.util.EncryptTypeHandler;
import com.quantchi.nanping.innovation.finance.model.FinanceLoanDetail;
import com.quantchi.nanping.innovation.model.BaseTime;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/29 14:51
 */
@Data
@ApiModel("金融平台需求")
@TableName(value = "demand", autoResultMap = true)
public class FinanceDemandBO extends Demand {

    @ApiModelProperty("放款情况")
    private FinanceLoanDetail loanDetail;

}
