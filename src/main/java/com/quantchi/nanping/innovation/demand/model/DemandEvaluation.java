package com.quantchi.nanping.innovation.demand.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import com.quantchi.nanping.innovation.model.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/2 18:00
 */
@ApiModel("需求评价")
@Data
@TableName("demand_evaluation")
public class DemandEvaluation extends BaseTime {

    /**
     * 评价对象类型：对接服务
     */
    public static final Integer TYPE_SERVICE = 0;
    /**
     * 评价对象类型：服务成果
     */
    public static final Integer TYPE_OUTCOME = 0;

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("需求id")
    @NotBlank
    private String demandId;

    @ApiModelProperty("评价类型 0：对接服务评价 1：服务成果评价")
    @NotNull
    private Integer type;

    @ApiModelProperty("得分")
    @NotNull
    private BigDecimal score;

    @ApiModelProperty("内容")
    @NotBlank
    private String content;

    @ApiModelProperty("文件附件")
    @TableField(exist = false)
    private List<FileInfo> relatedFiles;
}
