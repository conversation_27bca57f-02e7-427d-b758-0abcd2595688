package com.quantchi.nanping.innovation.demand.model.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.quantchi.nanping.innovation.model.bo.PageBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/2 10:14
 */
@Data
@ApiModel("跟进记录分页条件")
public class ProcessPageBO extends PageBO {

    @ApiModelProperty
    private String demandId;

    @ApiModelProperty("提出时间(起始)")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date commitDateFrom;

    @ApiModelProperty("提出时间(截至)")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date commitDateTo;

    @ApiModelProperty("上报人")
    private String reportUserName;

}
