package com.quantchi.nanping.innovation.demand.model.enums;

import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/5/29 15:50
 */
public enum ProcedureEnum {
    /**
     * 退回
     */
    REJECT(-1, "退回", new ArrayList<>(0)),
    /**
     * 待受理
     */
    NOT_START(0, "待受理", Arrays.asList(1)),
    /**
     * 需求受理
     */
    START(1, "需求受理", Arrays.asList(2, 3)),
    /**
     * 需求受理——持续跟进
     */
    START_NEXT(2, "持续跟进", Arrays.asList(2, 3)),
    /**
     * 需求分类
     */
    CLASSIFY(3, "需求分类", Arrays.asList(4, 5)),
    /**
     * 需求分类——持续跟进
     */
    CLASSIFY_NEXT(4, "持续跟进", Arrays.asList(4, 5)),
    /**
     * 需求对接
     */
    DOCK(5, "需求对接", Arrays.asList(6, 7)),
    /**
     * 持续跟进
     */
    DOCK_NEXT(6, "持续跟进", Arrays.asList(6, 7)),
    /**
     * 双方撮合
     */
    MATCH(7, "双方撮合", Arrays.asList(8, 9, 10)),
    /**
     * 双方撮合-持续跟进
     */
    MATCH_NEXT(8, "持续跟进", Arrays.asList(8, 9, 10)),
    /**
     * 撮合成功
     */
    END_SUCCESS(9, "对接成功", Arrays.asList(11)),
    //END_SUCCESS(9, "对接成功", Arrays.asList(11, 12)),
    /**
     * 撮合失败
     */
    END_FAIL(10, "对接失败", new ArrayList<>(0)),
    /**
     * 放款成功
     */
    //LENDING_SUCCESS(11, "放款成功", Arrays.asList(12)),
    /**
     * 用户评价
     */
    EVALUATE(11, "用户评价", Arrays.asList(0));
    //EVALUATE(12, "用户评价", new ArrayList<>(0));

    /**
     * 状态编号
     */
    private Integer id;
    /**
     * 状态名称
     */
    private String name;
    /**
     * 后续状态编号
     */
    private List<Integer> nextIds;

    ProcedureEnum(Integer id, String name, List<Integer> nextIds) {
        this.id = id;
        this.name = name;
        this.nextIds = nextIds;
    }

    /**
     * 查询后续状态
     *
     * @param currentStatus
     * @return
     */
    public static List<Map<String, Object>> getNextStatus(Integer currentStatus) {
        Map<Integer, ProcedureEnum> enumMap = new HashMap<>();
        ProcedureEnum currentEnum = null;
        for (ProcedureEnum p : ProcedureEnum.values()) {
            enumMap.put(p.getId(), p);
            if (p.getId().equals(currentStatus)) {
                currentEnum = p;
            }
        }
        if (currentEnum == null || CollectionUtils.isEmpty(currentEnum.getNextIds())) {
            return new ArrayList<>(0);
        }
        List<Map<String, Object>> nextStatus = new ArrayList<>();
        for (Integer nextId : currentEnum.getNextIds()) {
            Map<String, Object> statusMap = new HashMap<>();
            ProcedureEnum nextEnum = enumMap.get(nextId);
            statusMap.put("id", nextEnum.getId());
            statusMap.put("name", nextEnum.getName());
            nextStatus.add(statusMap);
        }
        return nextStatus;
    }

    /**
     * 获得标志性的阶段状态
     *
     * @param currentStatus
     * @return
     */
    public static ProcedureEnum getFlagStatus(Integer currentStatus) {
        ProcedureEnum beforeEnum = null;
        ProcedureEnum currentEnum = null;
        for (ProcedureEnum p : ProcedureEnum.values()) {
            if (p.getId().equals(currentStatus - 1)) {
                beforeEnum = p;
            }
            if (p.getId().equals(currentStatus)) {
                currentEnum = p;
                break;
            }
        }
        if (currentEnum.getNextIds().contains(currentStatus)) {
            return beforeEnum;
        } else {
            return currentEnum;
        }
    }

    /**
     * 查找对应状态名称
     *
     * @param id
     * @return
     */
    public static String getNameById(Integer id) {
        for (ProcedureEnum p : ProcedureEnum.values()) {
            if(p.getId().equals(id)){
                return p.getName();
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public List<Integer> getNextIds() {
        return nextIds;
    }
}
