package com.quantchi.nanping.innovation.demand.model.bo;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/19 16:36
 */
@Data
@ApiModel(value = "SortColumn", description = "排序字段")
public class SortColumn {

    public static final String ORDER_DESC = "desc";
    public static final String ORDER_ASC = "asc";

    @ApiModelProperty("排序名称")
    private String name;

    @ApiModelProperty("排序规则 升序：asc 降序：desc")
    private String order;

    public static String getSortRuleList(SortColumn sortColumn) {
        if(sortColumn == null){
            return "create_time desc";
        }
        return StringUtils.camelToUnderline(sortColumn.getName()) + " " + sortColumn.getOrder();
    }

}
