package com.quantchi.nanping.innovation.demand.model.enums;

import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/5/29 15:50
 */
public enum DemandTypeEnum {
    /**
     * 人才
     */
    TALENT(0, "人才"),
    /**
     * 资金
     */
    FUND(1, "资金"),
    /**
     * 技术
     */
    TECH(2, "技术"),
    /**
     * 其他
     */
    OTHER(3, "其他");

    /**
     * 类型编号
     */
    private Integer id;

    /**
     * 类型名称
     */
    private String name;

    DemandTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getName() {
        return name;
    }

}
