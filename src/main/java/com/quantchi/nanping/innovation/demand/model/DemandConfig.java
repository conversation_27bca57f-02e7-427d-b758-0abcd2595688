package com.quantchi.nanping.innovation.demand.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 需求配置
 *
 * <AUTHOR>
 * @date 2024/6/17 10:11
 */
@Data
public class DemandConfig extends BaseTime {

    public static final int DISPLAY = 1;
    public static final int HIDE = 0;

    @ApiModelProperty("需求配置id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty("创建用户id")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String createUserId;

    @ApiModelProperty("需求id")
    private String demandId;

    @ApiModelProperty("战略需求匹配得分")
    private Integer demandMatchScore;

    @ApiModelProperty("国外贸易管控得分")
    private Integer tradeControlScore;

    @ApiModelProperty("技术专利壁垒得分")
    private Integer patentBarriersScore;

    @ApiModelProperty("网络公开观点得分")
    private Integer openOpinionScore;

    @ApiModelProperty("区域攻关基础得分")
    private Integer developBaseScore;

    @ApiModelProperty("专利检索报告文件id")
    private String patentReportId;

    @ApiModelProperty("专利检索报告标题")
    private String patentReportTitle;

    @ApiModelProperty("专利检索报告摘要")
    private String patentReportAbstract;

    @ApiModelProperty("资金需求-总体评价")
    private String financeEvaluation;

    @ApiModelProperty("推荐专家ids，用','分割")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String expertIds;

    @ApiModelProperty("使用旧专家评分模型 0 否 1 是")
    private Integer useOldScoreModel = 0;

    /**
     * 展示控制
     */
    @ApiModelProperty("推荐专利")
    private Integer displayPatent = HIDE;

    @ApiModelProperty("专利检索报告")
    private Integer displayPatentReport = HIDE;

    @ApiModelProperty("推荐专家")
    private Integer displayExpert = DISPLAY;

    @ApiModelProperty("推荐企业")
    private Integer displayCompany = DISPLAY;

    @ApiModelProperty("自荐专家")
    private Integer displaySelfExpert = DISPLAY;

    @ApiModelProperty("附件")
    private Integer displayAttachment = DISPLAY;

    @ApiModelProperty("推荐专利列表")
    @TableField(exist = false)
    private List<DemandPatentConfig> recommendPatentList;

    @ApiModelProperty("资金需求-推荐产品")
    @TableField(exist = false)
    private List<DemandConfigFinanceProduct> financeProductList;

    @ApiModelProperty("专家推荐")
    @TableField(exist = false)
    private List<DemandExpertConfig> recommendExpertList;

    @ApiModelProperty("推荐企业列表")
    @TableField(exist = false)
    private List<Map<String, Object>> recommendCompanyList;

    /**
     * 判断自定义分数是否存在
     *
     * @return
     */
    public boolean customedScore() {
        return this.demandMatchScore != null && this.tradeControlScore != null && this.patentBarriersScore != null
                && this.openOpinionScore != null && this.developBaseScore != null;
    }

    /**
     * 获取总分
     *
     * @return
     */
    public Integer getTotalScore() {
        if (customedScore()){
            return this.demandMatchScore + this.tradeControlScore + this.patentBarriersScore
                    + this.openOpinionScore + this.developBaseScore;
        }
        return 0;
    }
}
