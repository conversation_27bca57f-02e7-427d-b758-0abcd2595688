package com.quantchi.nanping.innovation.demand.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/19 9:45
 */
@Data
@TableName("demand_config_finance_product")
public class DemandConfigFinanceProduct extends BaseTime {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer productId;

    @ApiModelProperty("需求id")
    private String demandId;

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("银行名称")
    private String bank;

    @ApiModelProperty("最高额度")
    private Integer maxAmount;

    @ApiModelProperty("最高期限")
    private Integer maxDeadline;

    @ApiModelProperty("最低利率")
    private String interestRate;

    @ApiModelProperty("融资方式")
    private String method;

    @ApiModelProperty("排序")
    private Integer sort;
}
