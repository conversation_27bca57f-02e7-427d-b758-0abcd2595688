package com.quantchi.nanping.innovation.demand.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 需求配置
 *
 * <AUTHOR>
 * @date 2024/6/17 10:11
 */
@Data
@TableName("demand_expert_config")
public class DemandExpertConfig {

    @ApiModelProperty("需求专家配置id")
    @TableId(value = "id")
    private String id;

    @ApiModelProperty("需求id")
    private String demandId;

    @ApiModelProperty("专家id")
    private String expertId;

    @ApiModelProperty("创新产出得分")
    private BigDecimal achievementScore;

    @ApiModelProperty("合作意向得分")
    private BigDecimal cooperationScore;

    @ApiModelProperty("影响能力得分")
    private BigDecimal influenceScore;

    @ApiModelProperty("专业能力得分")
    private BigDecimal performanceScore;

    @ApiModelProperty("契合度得分")
    private BigDecimal matchedScore;

    @ApiModelProperty("总得分")
    private BigDecimal finalScore;

}
