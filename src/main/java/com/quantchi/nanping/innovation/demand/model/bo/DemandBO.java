package com.quantchi.nanping.innovation.demand.model.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.quantchi.nanping.innovation.demand.model.bo.SortColumn;
import com.quantchi.nanping.innovation.model.bo.PageBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/5/9 13:52
 */
@Data
public class DemandBO extends PageBO {

    @ApiModelProperty("节点ids")
    private Set<String> nodeIds;

    @ApiModelProperty("需求标题")
    private String title;

    @ApiModelProperty("需求内容")
    private String content;

    @ApiModelProperty("提出人id")
    private String proposerId;

    @ApiModelProperty("提出人")
    private String proposer;

    @ApiModelProperty("所属产业id")
    private String chainId;

    @ApiModelProperty("提出时间(起始)")
    private String commitDateFrom;

    @ApiModelProperty("提出时间(截至)")
    private String commitDateTo;

    @ApiModelProperty("需求类型")
    private String type;

    @ApiModelProperty("需求状态id 退回：-1，待受理：0，对接中：1，撮合成功：9,撮合失败：10，待评价：11")
    private Integer statusId;

    @ApiModelProperty("需求ids")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<String> dataIds;

    @ApiModelProperty("排序")
    private SortColumn sort;

}
