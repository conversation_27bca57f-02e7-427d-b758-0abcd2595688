package com.quantchi.nanping.innovation.demand.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/14 11:17
 */
@Data
@ApiModel("需求样例，供驾驶舱轮播展示")
@TableName("demand_sample")
public class DemandSample extends BaseTime {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("需求id")
    private String demandId;
}
