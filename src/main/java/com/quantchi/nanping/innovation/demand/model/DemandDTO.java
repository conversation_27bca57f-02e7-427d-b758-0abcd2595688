package com.quantchi.nanping.innovation.demand.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.quantchi.nanping.innovation.demand.condition.Submit;
import com.quantchi.nanping.innovation.demand.condition.ThirdSyn;
import com.quantchi.nanping.innovation.demand.condition.UserSubmit;
import com.quantchi.nanping.innovation.demand.util.EncryptTypeHandler;
import com.quantchi.nanping.innovation.model.FileInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 企呼我应导入对象
 *
 * <AUTHOR>
 * @date 2024/6/11 16:29
 */
@Data
public class DemandDTO {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(index = 0)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelIgnore
    private LocalDateTime updateTime;

    @ApiModelProperty("所属产业链id")
    @ExcelIgnore
    private String chainId;

    @ApiModelProperty("所属产业链名称")
    @ExcelProperty(index = 1)
    private String chainName;

    @ApiModelProperty("所属产业链节点id")
    @ExcelIgnore
    private String chainNodeId;

    @ApiModelProperty("所属产业链节点名称")
    @ExcelProperty(index = 2)
    private String chainNodeName;

    @ApiModelProperty("需求类型：人才，资金，技术，其他")
    @ExcelProperty(index = 3)
    private String type;

    @ApiModelProperty("需求标题")
    @ExcelProperty(index = 4)
    private String title;

    @ApiModelProperty("需求内容")
    @ExcelProperty(index = 5)
    private String content;

    @ApiModelProperty("用户名称")
    @ExcelProperty(index = 6)
    private String userName;

    @ApiModelProperty("用户id")
    @ExcelProperty(index = 7)
    private String userId;

    @ApiModelProperty("文件地址")
    @ExcelProperty(index = 8)
    private String fileUrl;

    @ApiModelProperty("联系方式")
    @ExcelProperty(index = 9)
    private String link;

    @ApiModelProperty("申请金额")
    @ExcelProperty(index = 10)
    private String applyAmount;

    @ApiModelProperty("资金用途")
    @ExcelProperty(index = 11)
    private String fundPurpose;

    @ApiModelProperty("融资类型（债权融资/股权融资）")
    @ExcelProperty(index = 12)
    private String financeType;
}
