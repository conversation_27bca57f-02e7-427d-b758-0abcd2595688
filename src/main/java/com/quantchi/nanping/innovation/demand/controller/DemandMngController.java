package com.quantchi.nanping.innovation.demand.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.common.core.domain.ResultInfo;
import com.quantchi.nanping.innovation.auth.aop.CheckThirdAccess;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.config.aop.*;
import com.quantchi.nanping.innovation.demand.condition.*;
import com.quantchi.nanping.innovation.demand.model.Demand;
import com.quantchi.nanping.innovation.demand.model.DemandConfig;
import com.quantchi.nanping.innovation.demand.model.DemandEvaluation;
import com.quantchi.nanping.innovation.demand.model.DemandProcess;
import com.quantchi.nanping.innovation.demand.model.bo.DemandBO;
import com.quantchi.nanping.innovation.demand.model.bo.FinanceDemandBO;
import com.quantchi.nanping.innovation.demand.model.bo.ProcessPageBO;
import com.quantchi.nanping.innovation.demand.model.enums.DemandTypeEnum;
import com.quantchi.nanping.innovation.demand.model.enums.ProcedureEnum;
import com.quantchi.nanping.innovation.demand.model.vo.DemandStatistics;
import com.quantchi.nanping.innovation.demand.service.IDemandConfigService;
import com.quantchi.nanping.innovation.demand.service.IDemandMngService;
import com.quantchi.nanping.innovation.finance.model.FinanceLoanDetail;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.enums.BusinessType;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.IFileService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.utils.BigDecimalUtil;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/5/22 10:22
 */
@Slf4j
@RestController
@RequestMapping("/demand")
@Api(tags = "需求管理")
@Validated
@PlatformAuthCheck(type = {"0"})
@Metrics
public class DemandMngController {

    @Autowired
    private IDemandMngService demandMngService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private StringRedisCache stringRedisCache;

    @Autowired
    private IFileService fileService;

    @Autowired
    private IDemandConfigService configService;

    @ApiOperation(value = "统计")
    @GetMapping("/statistics")
    @Log(title = "需求管理")
    public Result<DemandStatistics> getStatistics() throws Exception {
        return ResultConvert.success(demandMngService.getStatistics(null));
    }

    @ApiOperation(value = "需求状态分析")
    @GetMapping("/analysisByStatus")
    //@Log(title = "需求-状态分析")
    public Result<PieVO> analyzeByStatus() {
        return ResultConvert.success(demandMngService.analyzeByStatus());
    }

    @ApiOperation(value = "需求类型分析")
    @GetMapping("/analysisByType")
    //@Log(title = "需求-类型分析")
    public Result<PieVO> analysisByType() {
        return ResultConvert.success(demandMngService.analyzeByType());
    }

    @ApiOperation(value = "需求产业链分布")
    @GetMapping("/analysis")
    //@Log(title = "需求-产业链分布")
    public Result<List<CommonIndexBO>> analyzeByCondition() {
        return ResultConvert.success(demandMngService.countByChain());
    }

    @ApiOperation(value = "需求列表")
    @PostMapping("/list")
    @PlatformAuthCheck(type = {"0", "1"})
    public Result<Page<Demand>> listRequirementMatching(@Validated @RequestBody DemandBO bo) {
        String socialCreditCode = sysLoginService.getUserInfo(false).getSocialCreditCode();
        bo.setProposerId(socialCreditCode);
        return ResultConvert.success(demandMngService.page(bo));
    }

    @ApiOperation(value = "需求列表(企业门户)")
    @PostMapping("/list/portal")
    @PlatformAuthCheck()
    @AntiReptile
    public Result<Page<Demand>> listRequirementMatching4Portal(@Validated @RequestBody DemandBO bo) {
        return ResultConvert.success(demandMngService.page4Portal(bo));
    }

    @ApiOperation(value = "需求列表(专家首页)")
    @PostMapping("/list/expertPortal")
    @PlatformAuthCheck(type = {"1"})
    public Result<Page<Demand>> listRequirementMatching4ExpertPortal(@Validated @RequestBody DemandBO bo) {
        return ResultConvert.success(demandMngService.page4ExpertPortal(bo));
    }

    @ApiOperation(value = "需求详情")
    @GetMapping("/detail")
    @PlatformAuthCheck(type = {"0", "1"})
    //@Log(title = "需求-详情")
    public Result<Demand> detail(@NotBlank String id) {
        return ResultConvert.success(demandMngService.getById(id));
    }

    @ApiOperation(value = "需求提交")
    @PostMapping("/submit")
    @PlatformAuthCheck(type = {"0", "1"})
    @RepeatSubmit
    @Log(title = "需求-提交", businessType = BusinessType.INSERT)
    public Result submit(@RequestBody @Validated({Submit.class}) Demand demand) {
        return ResultConvert.success(demandMngService.submit(demand));
    }

    @ApiOperation(value = "需求删除")
    @GetMapping("/delete")
    @PlatformAuthCheck(type = {"0"})
    @RepeatSubmit
    @Log(title = "需求-删除", businessType = BusinessType.DELETE)
    public Result delete(@NotBlank String id) {
        return ResultConvert.success(demandMngService.delete(id));
    }

    @ApiOperation(value = "需求提交(首页)")
    @PostMapping("/submit/portal")
    @PlatformAuthCheck()
    @RepeatSubmit
    @RateLimiter(key = "rate_limit:demand_submit:")
    @Log(title = "服务侧需求提交(门户)", businessType = BusinessType.INSERT)
    public Result submitInPortal(@RequestBody @Validated({UserSubmit.class}) Demand demand) {
        if (StringUtils.isEmpty(demand.getCaptcha()) || StringUtils.isEmpty(demand.getUid())) {
            throw new BusinessException("请输入验证码信息");
        }
        // 判断验证码是否正确
        final String code = stringRedisCache.get(demand.getUid());
        if (com.quantchi.common.core.utils.StringUtils.isEmpty(code)) {
            return ResultConvert.error(500, "验证码已过期，请重新输入");
        }
        if (!StrUtil.equalsIgnoreCase(code, demand.getCaptcha())) {
            return ResultConvert.error(500, "验证码不正确");
        }
        // 移除使用过的验证码
        stringRedisCache.remove(demand.getUid());
        return ResultConvert.success(demandMngService.submit(demand));
    }

    @ApiOperation(value = "需求受理")
    @PostMapping("/accept")
    @RepeatSubmit
    @Log(title = "需求-受理", businessType = BusinessType.UPDATE)
    public Result accept(@RequestBody @Validated({Accept.class}) DemandProcess process) {
        return ResultConvert.success(demandMngService.accept(process));
    }

    @ApiOperation(value = "需求修改")
    @PostMapping("/edit")
    @PlatformAuthCheck(type = {"0", "1"})
    @RepeatSubmit
    @Log(title = "需求-修改", businessType = BusinessType.UPDATE)
    public Result edit(@RequestBody Demand demand) {
        return ResultConvert.success(demandMngService.edit(demand));
    }

    @ApiOperation(value = "需求退回")
    @PostMapping("/reject")
    @RepeatSubmit
    @Log(title = "需求-退回", businessType = BusinessType.UPDATE)
    public Result reject(@RequestBody @Validated({Reject.class}) Demand demand) {
        return ResultConvert.success(demandMngService.reject(demand));
    }

    @ApiOperation(value = "需求跟进")
    @PostMapping("/follow")
    @RepeatSubmit
    @Log(title = "需求-跟进", businessType = BusinessType.INSERT)
    public Result follow(@RequestBody @Validated({Submit.class}) DemandProcess process) {
        return ResultConvert.success(demandMngService.follow(process));
    }

    @ApiOperation(value = "可选工作类型列表")
    @GetMapping("/process_status")
    //@Log(title = "需求-可选工作类型列表")
    public Result listProcessStatus(@NotNull Integer currentStatus, @NotBlank String type) {
        if (DemandTypeEnum.FUND.getName().equals(type) &&
                (ProcedureEnum.CLASSIFY.getId().equals(currentStatus) || ProcedureEnum.CLASSIFY_NEXT.getId().equals(currentStatus))) {
            // 跳过需求对接状态
            List<Map<String, Object>> nextStatus = new ArrayList<>();
            for (ProcedureEnum nextEnum : Arrays.asList(ProcedureEnum.CLASSIFY_NEXT, ProcedureEnum.MATCH)) {
                Map<String, Object> statusMap = new HashMap<>();
                statusMap.put("id", nextEnum.getId());
                statusMap.put("name", nextEnum.getName());
                nextStatus.add(statusMap);
            }
            return ResultConvert.success(nextStatus);
        }
        return ResultConvert.success(ProcedureEnum.getNextStatus(currentStatus));
    }

    @ApiOperation(value = "结束服务")
    @PostMapping("/end")
    @RepeatSubmit
    @Log(title = "需求-结束服务", businessType = BusinessType.UPDATE)
    public Result end(@RequestBody @Validated({End.class}) DemandProcess process) {
        return ResultConvert.success(demandMngService.end(process));
    }

    @ApiOperation(value = "跟进记录列表")
    @PostMapping("/process_list")
    @PlatformAuthCheck(type = {"0", "1"})
    //@Log(title = "需求-跟进记录列表")
    public Result getProcess(@Validated @RequestBody ProcessPageBO processBO) {
        return ResultConvert.success(demandMngService.listProcess(processBO));
    }

    @ApiOperation(value = "服务评价")
    @PostMapping("/evaluate")
    @PlatformAuthCheck(type = {"1"})
    @RepeatSubmit
    @Log(title = "需求-服务评价", businessType = BusinessType.INSERT)
    public Result evaluate(@RequestBody @Validated DemandEvaluation evaluation) {
        return ResultConvert.success(demandMngService.evaluate(evaluation));
    }

    @ApiOperation(value = "获取评价列表")
    @GetMapping("/evaluation_list")
    @PlatformAuthCheck(type = {"0", "1"})
    //@Log(title = "需求-获取评价列表")
    public Result listEvaluation(@NotBlank String id) {
        return ResultConvert.success(demandMngService.listEvaluationByDemandId(id));
    }

    @ApiOperation(value = "催办")
    @GetMapping("/urging")
    @PlatformAuthCheck(type = {"1"})
    @RepeatSubmit
    @Log(title = "需求-催办", businessType = BusinessType.UPDATE)
    public Result urging(@NotBlank String id) {
        return ResultConvert.success(demandMngService.urging(id));
    }

    @ApiOperation(value = "需求揭榜")
    @GetMapping("/selfRecommend")
    @PlatformAuthCheck(type = {"1"})
    @RepeatSubmit
    @Log(title = "需求-揭榜", businessType = BusinessType.INSERT)
    public Result selfRecommend(@NotBlank String id) {
        return ResultConvert.success(demandMngService.selfRecommend(id));
    }


    @ApiOperation(value = "企呼我应-需求同步")
    @PostMapping("/syn")
    @RateLimiter
    @Log(title = "企呼我应-需求同步", businessType = BusinessType.INSERT)
    @CheckThirdAccess
    public ResultInfo syn(@RequestBody @Validated({ThirdSyn.class}) Demand demand, HttpServletRequest request) {
        // 查询对应产业链id和节点id
        IndustryChainNode node = industryChainService.getNodeById(demand.getChainNodeId());
        if (node == null || !node.getChainId().equals(demand.getChainId())) {
            return com.quantchi.common.core.utils.ResultConvert.error(500, "产业链节点不存在");
        }
        demand.setChainNodeName(node.getName());
        // 单独设置需求id，加以区分
        demand.setId(request.getHeader("appId") + "_" + UUID.randomUUID());
        // 如果有附件，单独保存
        List<FileInfo> fileInfoList = demand.getRelatedFiles();
        if (CollectionUtils.isNotEmpty(fileInfoList)) {
            fileInfoList.forEach(file -> file.setRelatedId(demand.getId()));
            fileService.saveOrUpdateBatch(fileInfoList);
        }
        demand.setDemandSource(Demand.ENT_SOURCE);
        return com.quantchi.common.core.utils.ResultConvert.success(demandMngService.submit(demand));
    }

    @ApiOperation(value = "金融平台-需求同步")
    @PostMapping("/syn/finance")
    @RateLimiter
    @Log(title = "金融平台-需求同步", businessType = BusinessType.INSERT)
    @CheckThirdAccess
    public ResultInfo synFinance(@RequestBody @Validated({ThirdSyn.class}) FinanceDemandBO demandBO, HttpServletRequest request) {
        // 查询对应产业链id和节点id
        IndustryChainNode node = industryChainService.getNodeById(demandBO.getChainNodeId());
        if (node == null || !node.getChainId().equals(demandBO.getChainId())) {
            return com.quantchi.common.core.utils.ResultConvert.error(500, "产业链节点不存在");
        }
        demandBO.setChainNodeName(node.getName());
        DemandProcess process = new DemandProcess();
        process.setReportUserId("finance_admin");
        process.setReportUserName("绿色金融平台");
        process.setDemandId(demandBO.getId());
        if (demandBO.getStatus().equals(ProcedureEnum.CLASSIFY.getId())) {
            if (StringUtils.isEmpty(demandBO.getApplyAmount()) || Integer.parseInt(demandBO.getApplyAmount()) <= 0){
                throw new BusinessException("申请金额填写有误");
            }
            // 同步提交
            demandBO.setDemandSource(Demand.FINANCE_SOURCE);
            demandMngService.submit(demandBO);
            // 创建跟进记录，直至双方撮合状态前
            // 需求受理
            process.setCreateTime(demandBO.getCreateTime());
            process.setContent("需求受理");
            demandMngService.accept(process);
            // 需求分类
            process.setCreateTime(demandBO.getCreateTime().plus(1, ChronoUnit.MINUTES));
            process.setContent("确认为资金需求");
            process.setProcedureType(ProcedureEnum.CLASSIFY.getId());
            process.setProcedureTypeName(ProcedureEnum.CLASSIFY.getName());
            demandMngService.follow(process);
        } else if (demandBO.getStatus().equals(ProcedureEnum.END_SUCCESS.getId())) {
            // 创建跟进记录，直至对接成功
            FinanceLoanDetail loanDetail = demandBO.getLoanDetail();
            if (loanDetail != null){
                if (BigDecimal.ZERO.compareTo(loanDetail.getLoanAmount()) >= 0
                        || StringUtils.isEmpty(loanDetail.getLoanType())
                        || loanDetail.getLoanDate() == null){
                    throw new BusinessException("放款信息参数不正确");
                }
            }
            // 双方撮合
            process.setCreateTime(demandBO.getUpdateTime());
            process.setProcedureType(ProcedureEnum.MATCH.getId());
            process.setProcedureTypeName(ProcedureEnum.MATCH.getName());
            process.setContent("企业成功获得了" + loanDetail.getLoanType() + "的" + loanDetail.getLoanAmount() + "万元贷款金额。");
            demandMngService.follow(process);
            // 对接成功
            process.setCreateTime(demandBO.getUpdateTime().plus(1, ChronoUnit.MINUTES));
            process.setProcedureType(ProcedureEnum.END_SUCCESS.getId());
            process.setProcedureTypeName(ProcedureEnum.END_SUCCESS.getName());
            process.setLoanDetail(loanDetail);
            process.setContent("需求服务跟进结束");
            demandMngService.follow(process);
        }else{
            // 对接失败
            process.setCreateTime(demandBO.getUpdateTime());
            process.setProcedureType(ProcedureEnum.MATCH.getId());
            process.setProcedureTypeName(ProcedureEnum.MATCH.getName());
            process.setContent("审批中");
            demandMngService.follow(process);
            process.setCreateTime(demandBO.getUpdateTime().plus(1, ChronoUnit.MINUTES));
            process.setProcedureType(ProcedureEnum.END_FAIL.getId());
            process.setProcedureTypeName(ProcedureEnum.END_FAIL.getName());
            process.setContent("审批不通过");
            demandMngService.follow(process);
        }
        return com.quantchi.common.core.utils.ResultConvert.success(true);
    }

    @ApiOperation(value = "需求配置")
    @GetMapping("/config/detail")
    @PlatformAuthCheck(type = {"0", "1"})
    //@Log(title = "需求-详情")
    public Result<DemandConfig> getConfigDetail(@NotBlank String demandId) {
        return ResultConvert.success(configService.getFullByDemandId(demandId));
    }

}
