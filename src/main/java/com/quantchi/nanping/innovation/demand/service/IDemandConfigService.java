package com.quantchi.nanping.innovation.demand.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.demand.model.DemandConfig;
import com.quantchi.nanping.innovation.demand.model.DemandExpertConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/11 14:03
 */
public interface IDemandConfigService extends IService<DemandConfig> {

    /**
     * 获取需求配置详情
     *
     * @param demandId
     * @return
     */
    DemandConfig getFullByDemandId(String demandId);

    /**
     * 查询需求配置
     *
     * @param demandId
     * @return
     */
    DemandConfig getByDemandId(String demandId);

    /**
     * 删除需求配置
     *
     * @param demandId
     * @return
     */
    boolean deleteByDemandId(String demandId);

    /**
     * 重置专家
     *
     * @param demandId
     */
    boolean resetExpertByDemandId(String demandId);

    /**
     * 保存专家分数配置(先删除后增加)
     *
     * @param demandId
     * @param expertConfigs
     * @return
     */
    boolean saveBatchExpertConfig(String demandId, List<DemandExpertConfig> expertConfigs);

    /**
     * 查询专家分数配置
     *
     * @param demandId
     * @return
     */
    List<DemandExpertConfig> getExpertConfigByDemandId(String demandId);
}
