package com.quantchi.nanping.innovation.demand.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.demand.condition.Submit;
import com.quantchi.nanping.innovation.demand.condition.ThirdSyn;
import com.quantchi.nanping.innovation.demand.condition.UserSubmit;
import com.quantchi.nanping.innovation.demand.util.EncryptTypeHandler;
import com.quantchi.nanping.innovation.model.BaseTime;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/29 14:51
 */
@Data
@ApiModel("需求")
@TableName(value = "demand", autoResultMap = true)
public class Demand extends BaseTime {

    /**
     * es id前缀
     */
    public static final String ES_PREFIX = "server_";

    /**
     * 游客填充值
     */
    public static final String NONE_FILL = "0";

    /**
     * 需求来源:企呼我应
     */
    public static final int ENT_SOURCE = 1;

    /**
     * 需求来源:绿色金融
     */
    public static final int FINANCE_SOURCE = 2;

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("用户id")
    @NotBlank(groups = {Submit.class, ThirdSyn.class})
    private String userId;

    @ApiModelProperty("用户名称")
    @NotBlank(groups = {Submit.class, ThirdSyn.class})
    @Length(max = 100, groups = {Submit.class, ThirdSyn.class}, message = "提出人名称请控制在100字以内")
    private String userName;

    @ApiModelProperty("联系方式")
    @NotBlank(groups = {Submit.class, UserSubmit.class, ThirdSyn.class})
    @Length(max = 100, groups = {Submit.class, UserSubmit.class, ThirdSyn.class}, message = "联系方式请控制在100字以内")
    private String link;

    @ApiModelProperty("需求类型：人才，资金，技术，其他")
    @NotBlank(groups = {Submit.class, UserSubmit.class, ThirdSyn.class})
    private String type;

    @ApiModelProperty("所属产业链id")
    @NotBlank(groups = {Submit.class, UserSubmit.class, ThirdSyn.class})
    private String chainId;

    @ApiModelProperty("所属产业链名称")
    private String chainName;

    @ApiModelProperty("所属产业链节点id")
    @NotBlank(groups = {Submit.class, UserSubmit.class, ThirdSyn.class})
    private String chainNodeId;

    @ApiModelProperty("所属产业链节点名称")
    @NotBlank(groups = {Submit.class, UserSubmit.class})
    private String chainNodeName;

    @ApiModelProperty("申请金额")
    @TableField(typeHandler = EncryptTypeHandler.class)
    @NotBlank(message = "请填写申请金额")
    private String applyAmount;

    @ApiModelProperty("资金用途")
    @Length(max = 200, message = "资金用途请控制在200字以内")
    private String fundPurpose;

    @ApiModelProperty("融资类型（债权融资/股权融资）")
    @NotBlank(message = "请填写融资类型")
    private String financeType;

    @ApiModelProperty("催办状态：0：未催办 1：已催办")
    private Integer urgentStatus;

    @ApiModelProperty("处理状态：-1：退回 0：未受理")
    private Integer status;

    @ApiModelProperty("备注")
    @Length(max = 1000, message = "备注内容请控制在1000字以内")
    private String remark;

    @ApiModelProperty("排序")
    @TableField(value = "demand_sort")
    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelIgnore
    private Integer demandSort;

//    @ApiModelProperty("退回原因")
//    @NotBlank(groups = {Reject.class}, message = "请填写需求退回原因")
//    private String rejectReason;

    @ApiModelProperty("服务评价")
    private BigDecimal serviceEvaluation;

    @ApiModelProperty("成果评价")
    private BigDecimal outcomeEvaluation;

    @ApiModelProperty("对接结果")
    @TableField(exist = false)
    private String finalStatus;

    @ApiModelProperty("需求标题")
    @NotBlank(groups = {Submit.class, UserSubmit.class, ThirdSyn.class})
    @Length(max = 30, groups = {Submit.class, UserSubmit.class}, message = "需求标题请控制在30字以内")
    private String title;

    @ApiModelProperty("需求内容")
    @NotBlank(groups = {Submit.class, UserSubmit.class, ThirdSyn.class})
    @Length(max = 1000, groups = {Submit.class, UserSubmit.class}, message = "需求内容请控制在1000字以内")
    private String content;

    @ApiModelProperty("加密索引")
    private Integer encryptIndex;

    @ApiModelProperty("来源 0:绿创 1:企呼我应 2：绿色金融")
    private Integer demandSource;

    @ApiModelProperty("需求进展")
    @TableField(exist = false)
    private List<DemandProcess> processList;

    @ApiModelProperty("专家推荐")
    @TableField(exist = false)
    private List<Map<String, Object>> recommendExpertList;

    @ApiModelProperty("企业es实体id")
    @TableField(exist = false)
    private String companyId;

    @ApiModelProperty("文件附件")
    @TableField(exist = false)
    @Size(groups = {Submit.class, UserSubmit.class}, max = 9)
    private List<FileInfo> relatedFiles;

    @ApiModelProperty("自荐专家列表")
    @TableField(exist = false)
    private List<DemandUndertake> selfRecommendExpertList;

    @ApiModelProperty("推荐企业列表")
    @TableField(exist = false)
    private List<Map<String, Object>> recommendCompanyList;

    @ApiModelProperty("推荐专利列表")
    @TableField(exist = false)
    private List<Map<String, Object>> recommendPatentList;

    @ApiModelProperty("推荐金融产品列表")
    @TableField(exist = false)
    private List<Map<String, Object>> recommendProductList;

    @ApiModelProperty("验证码")
    @TableField(exist = false)
    private String captcha;

    @ApiModelProperty("验证码id")
    @TableField(exist = false)
    private String uid;

    @ApiModelProperty("需求评价报告id")
    @TableField(exist = false)
    private String reportId;

    /**
     * 转换为ES结构内容
     *
     * @param demand
     * @return
     */
    public static String toEsObject(Demand demand, List<IndustryChainNode> pathNodes) {
        JSONObject demandObj = new JSONObject();
        // id
        demandObj.put("id", ES_PREFIX + demand.getId());
        // chain
        JSONArray chainArray = new JSONArray();
        JSONObject chainObj = new JSONObject();
        chainObj.put("id", demand.getChainId());
        chainObj.put("name", demand.getChainName());
        chainArray.add(chainObj);
        demandObj.put("chain", chainArray);
        // chain_node
        JSONArray chainNodeArray = new JSONArray();
        for (IndustryChainNode node : pathNodes) {
            JSONObject chainNodeObj = new JSONObject();
            chainNodeObj.put("chain_id", node.getChainId());
            chainNodeObj.put("id", node.getId());
            chainNodeObj.put("name", node.getName());
            chainNodeArray.add(chainNodeObj);
        }
        demandObj.put("chain_node", chainNodeArray);
        // product_node
        JSONArray productNodeArray = new JSONArray();
        JSONObject productNodeObj = new JSONObject();
        productNodeObj.put("chain_id", demand.getChainId());
        productNodeObj.put("id", demand.getChainNodeId());
        productNodeObj.put("name", demand.getChainNodeName());
        productNodeArray.add(productNodeObj);
        demandObj.put("product_node", productNodeArray);
        // name
        demandObj.put("name", demand.getTitle());
        // research_content
        demandObj.put("research_content", demand.getContent());
        // commit_date
        DateTimeFormatter dfDate = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        demandObj.put("commit_date", dfDate.format(demand.getCreateTime()));
        // source_type
        demandObj.put("source_type", "服务侧需求");
        // type
        demandObj.put("type", demand.getType());
        // commit_info
        JSONObject commitInfoObj = new JSONObject();
        commitInfoObj.put("unit", demand.getUserName());
        demandObj.put("commit_info", commitInfoObj);
        return JSON.toJSONString(demandObj, SerializerFeature.DisableCircularReferenceDetect);
    }
}
