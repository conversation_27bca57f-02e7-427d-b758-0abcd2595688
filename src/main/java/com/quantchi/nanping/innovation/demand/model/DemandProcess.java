package com.quantchi.nanping.innovation.demand.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.demand.condition.Accept;
import com.quantchi.nanping.innovation.demand.condition.End;
import com.quantchi.nanping.innovation.demand.condition.Submit;
import com.quantchi.nanping.innovation.finance.model.FinanceLoanDetail;
import com.quantchi.nanping.innovation.model.BaseTime;
import com.quantchi.nanping.innovation.model.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23 18:15
 */
@Data
@ApiModel("需求进展")
@TableName("demand_process")
public class DemandProcess extends BaseTime {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("需求id")
    @NotBlank(groups = {Submit.class, End.class, Accept.class})
    private String demandId;

    @ApiModelProperty("上报用户名id")
    private String reportUserId;

    @ApiModelProperty("上报用户名称")
    private String reportUserName;

    @ApiModelProperty("工作类型")
    @NotNull(groups = {Submit.class})
    private Integer procedureType;

    @ApiModelProperty("工作类型名称")
    private String procedureTypeName;

    @ApiModelProperty("工作内容")
    @NotBlank(groups = {Submit.class, End.class, Accept.class})
    private String content;

    @ApiModelProperty("文件附件")
    @TableField(exist = false)
    private List<FileInfo> relatedFiles;

    @ApiModelProperty("资金需求对接成功时的放款信息")
    @TableField(exist = false)
    private FinanceLoanDetail loanDetail;

}
