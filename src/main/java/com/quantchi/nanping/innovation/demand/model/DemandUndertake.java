package com.quantchi.nanping.innovation.demand.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.demand.condition.Submit;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/7/20 13:36
 */
@Data
@ApiModel("需求揭榜信息")
@TableName(value = "demand_undertake", autoResultMap = true)
public class DemandUndertake extends BaseTime {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("需求id")
    @NotBlank(groups = {Submit.class})
    private String demandId;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("联系方式")
    @NotBlank(groups = {Submit.class})
    //@TableField(typeHandler = EncryptSMTypeHandler.class)
    private String link;

    @ApiModelProperty("加密索引")
    private Integer encryptIndex;

}
