package com.quantchi.nanping.innovation.demand.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.demand.dao.DemandConfigFinanceProductMapper;
import com.quantchi.nanping.innovation.demand.dao.DemandConfigMapper;
import com.quantchi.nanping.innovation.demand.dao.DemandPatentConfigMapper;
import com.quantchi.nanping.innovation.demand.dao.DemandUndertakeMapper;
import com.quantchi.nanping.innovation.demand.model.*;
import com.quantchi.nanping.innovation.demand.model.enums.DemandTypeEnum;
import com.quantchi.nanping.innovation.demand.service.IDemandConfigService;
import com.quantchi.nanping.innovation.demand.service.IDemandExpertConfigService;
import com.quantchi.nanping.innovation.demand.service.IDemandMngService;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.IFileService;
import com.quantchi.nanping.innovation.service.IThirdFinanceService;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.utils.EncryptUtil;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/11 14:04
 */
@Service
public class DemandConfigServiceImpl extends ServiceImpl<DemandConfigMapper, DemandConfig> implements IDemandConfigService {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IDemandExpertConfigService expertConfigService;

    @Autowired
    private DemandConfigFinanceProductMapper configFinanceProductMapper;

    @Autowired
    private DemandPatentConfigMapper patentConfigMapper;

    @Autowired
    private DemandUndertakeMapper undertakeMapper;

    @Autowired
    private IEs8Service es8Service;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private IDemandMngService demandMngService;

    @Autowired
    private IThirdFinanceService thirdFinanceService;

    @Autowired
    private IFileService fileService;

    @Autowired
    private SysLoginService sysLoginService;

    @Override
    public DemandConfig getFullByDemandId(String demandId) {
        Demand demand = demandMngService.findExistedDemand(demandId);
        if (demand == null){
            return new DemandConfig();
        }
        DemandConfig demandConfig = getByDemandId(demandId);
        if (demandConfig == null){
            demandConfig = new DemandConfig();
            demandConfig.setDemandId(demandId);
            // 设置默认展示开关
            if (DemandTypeEnum.FUND.getName().equals(demand.getType())){
                demandConfig.setDisplayCompany(DemandConfig.HIDE);
                demandConfig.setDisplayExpert(DemandConfig.HIDE);
                demandConfig.setDisplayPatent(DemandConfig.HIDE);
                demandConfig.setDisplaySelfExpert(DemandConfig.HIDE);
            }else if (DemandTypeEnum.TALENT.getName().equals(demand.getType())){
                demandConfig.setDisplayCompany(DemandConfig.HIDE);
            }
            this.save(demandConfig);
        }
        // 附件
        if (demandConfig.getDisplayAttachment() == DemandConfig.DISPLAY){
            UserInfoEntity currentUser = sysLoginService.getUserInfo(false);
            if (UserInfoEntity.USER_EXPERT != currentUser.getUserType()) {
                Map<String, List<FileInfo>> fileInfoList = fileService.listFileByRelatedIdsAndType(Arrays.asList(demandId), FileInfo.RELATED_TYPE_DEMAND);
                demand.setRelatedFiles(fileInfoList.get(demandId));
            }
        }
        // 金融产品
        if (DemandTypeEnum.FUND.getName().equals(demand.getType())){
            if (demand.getDemandSource() == Demand.FINANCE_SOURCE) {
                // 资金需求补充金融产品
                demand.setRecommendProductList(thirdFinanceService.getRecommendFinanceProductList(demand.getUserId()));
            }else {
                List<DemandConfigFinanceProduct> productConfigs = configFinanceProductMapper.selectList(Wrappers.lambdaQuery(DemandConfigFinanceProduct.class)
                        .eq(DemandConfigFinanceProduct::getDemandId, demandId)
                        .orderByAsc(DemandConfigFinanceProduct::getSort));
                demandConfig.setFinanceProductList(productConfigs);
            }
            return demandConfig;
        }
        // 推荐专利
        if (demandConfig.getDisplayPatent() == DemandConfig.DISPLAY){
            List<DemandPatentConfig> patentConfigs = patentConfigMapper.selectList(Wrappers.lambdaQuery(DemandPatentConfig.class)
                    .eq(DemandPatentConfig::getDemandId, demandId)
                    .orderByAsc(DemandPatentConfig::getSort));
            if (CollectionUtils.isNotEmpty(patentConfigs)){
                List<String> patentIdList = patentConfigs.stream().map(DemandPatentConfig::getPatentId).collect(Collectors.toList());
                BoolQueryBuilder patentQuery = QueryBuilders.boolQuery();
                patentQuery.filter(QueryBuilders.idsQuery().addIds(patentIdList.toArray(new String[0])));
                SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(patentQuery, 1, patentIdList.size(),
                        new String[]{"id", "abstract_cn"}, null, null);
                SearchResponse searchResponse = es8Service.request(searchSourceBuilder, EsIndexEnum.PATENT_VECTOR.getEsIndex(), null, null);
                EsPageResult patentPage = ElasticsearchBuilder.buildPageResult(searchResponse);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(patentPage.getList())){
                    List<Map<String, Object>> patentList = patentPage.getList();
                    Map<String, String> patentAbstractMap = new HashMap<>();
                    for (Map<String, Object> patent: patentList){
                        patentAbstractMap.put((String)patent.get("id"), String.valueOf(patent.get("abstract_cn")));
                    }
                    for (DemandPatentConfig pc: patentConfigs){
                        pc.setAbstractContent(patentAbstractMap.get(pc.getPatentId()));
                    }
                }
            }
            demandConfig.setRecommendPatentList(patentConfigs);
        }
        // 推荐企业
        if (demandConfig.getDisplayCompany() == DemandConfig.DISPLAY){
            if (DemandTypeEnum.TECH.getName().equals(demand.getType())) {
                // 补充推荐企业
                List<Map<String, Object>> recommendCompanyList = companyService.getCompanyList4Demand(demand.getChainNodeId());
                if (CollectionUtils.isNotEmpty(recommendCompanyList)) {
                    demandConfig.setRecommendCompanyList(recommendCompanyList);
                }
            }
        }
        // 自荐专家
        if (demandConfig.getDisplaySelfExpert() == DemandConfig.DISPLAY){
            // 补充自荐专家
            List<DemandUndertake> selfRecommendList = undertakeMapper.selectList(Wrappers.lambdaQuery(DemandUndertake.class)
                    .eq(DemandUndertake::getDemandId, demandId)
                    .orderByAsc(DemandUndertake::getCreateTime));
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(selfRecommendList)) {
                // 解密手机号
                for (DemandUndertake undertake : selfRecommendList) {
                    undertake.setLink(EncryptUtil.decryptSM4FixIndex(undertake.getLink(), undertake.getEncryptIndex()));
                }
                demand.setSelfRecommendExpertList(selfRecommendList);
            }
        }
        return demandConfig;
    }

    @Override
    public DemandConfig getByDemandId(String demandId) {
        return this.getOne(Wrappers.lambdaQuery(DemandConfig.class)
                .eq(DemandConfig::getDemandId, demandId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByDemandId(String demandId) {
        removeExpertConfigByDemandId(demandId);
        return this.remove(Wrappers.lambdaQuery(DemandConfig.class)
                .eq(DemandConfig::getDemandId, demandId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetExpertByDemandId(String demandId) {
        log.error("重置需求{" + demandId + "}专家信息");
        // 同时删除es
        elasticsearchHelper.delete(EsIndexEnum.MODEL_RESULT_POOL.getEsIndex(), demandId);
        removeExpertConfigByDemandId(demandId);
        return this.update(Wrappers.lambdaUpdate(DemandConfig.class)
                .set(DemandConfig::getExpertIds, null)
                .eq(DemandConfig::getDemandId, demandId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchExpertConfig(String demandId, List<DemandExpertConfig> expertConfigs) {
        removeExpertConfigByDemandId(demandId);
        if (CollectionUtils.isEmpty(expertConfigs)) {
            return true;
        }
        return expertConfigService.saveBatch(expertConfigs);
    }

    @Override
    public List<DemandExpertConfig> getExpertConfigByDemandId(String demandId) {
        return expertConfigService.list(Wrappers.lambdaQuery(DemandExpertConfig.class)
                .eq(DemandExpertConfig::getDemandId, demandId));
    }

    private boolean removeExpertConfigByDemandId(String demandId) {
        return expertConfigService.remove(Wrappers.lambdaQuery(DemandExpertConfig.class)
                .eq(DemandExpertConfig::getDemandId, demandId));
    }
}
