package com.quantchi.nanping.innovation.external.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 多维度查询条件封装对象
 * <AUTHOR>
 * @date 2022/4/21 17:06
 */
@Data
@ApiModel("基础第三方搜索条件")
public class BaseThirdQuery {

    @ApiModelProperty("登录认证key")
    private Long appKey;

    @ApiModelProperty("当前时间戳")
    private String requestTime;

    @ApiModelProperty("签名值")
    private String sign;

}
