package com.quantchi.nanping.innovation.external.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("项目补助查询条件")
public class SubsidyBO extends BaseThirdQuery{

    @ApiModelProperty("页码")
    private Integer pageNum;

    @ApiModelProperty("每页条数")
    private Integer pageSize;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("项目名称")
    private String projectName;

}
