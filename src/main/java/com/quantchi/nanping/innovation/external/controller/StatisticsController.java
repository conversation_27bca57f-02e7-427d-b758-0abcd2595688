package com.quantchi.nanping.innovation.external.controller;

import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.common.exception.MessageException;
import com.quantchi.nanping.innovation.company.service.ITechnicalContractService;
import com.quantchi.nanping.innovation.config.aop.RateLimiter;
import com.quantchi.nanping.innovation.external.model.bo.*;
import com.quantchi.nanping.innovation.insight.controller.OverViewController;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.knowledge.center.service.IKnowledgeCenterQueryService;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.service.ICockpitService;
import com.quantchi.nanping.innovation.service.ITechCommissionService;
import com.quantchi.nanping.innovation.service.ITechInvestAttractService;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.ResultCodeEnum;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.IndexService;
import com.quantchi.nanping.innovation.service.library.FinanceService;
import com.quantchi.nanping.innovation.service.library.ProjectService;
import com.quantchi.nanping.innovation.utils.RequestContext;
import com.quantchi.tianying.model.MultidimensionalQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@Api(tags = "统计信息")
@RequestMapping("/statistics")
public class StatisticsController {

    private final IKnowledgeCenterQueryService knowledgeCenterQueryService;
    private final ICockpitService cockpitService;
    private final ITechCommissionService techCommissionService;
    private final ITechInvestAttractService techInvestAttractService;
    private final ITechnicalContractService contractService;
    private final IIndexFusionService indexFusionService;
    private final FinanceService financeService;
    private final ProjectService projectService;
    private final IndexService indexService;


    @ApiOperation("查询产业专家人才")
    @PostMapping("/filtering")
    @RateLimiter(key = "rate_limit:expert_query:", count = 60)
    public Result queryExperts(@RequestBody final QueryExpertsBO bo) {
        calibratePermission(bo);
        MultidimensionalQuery mQuery = new MultidimensionalQuery();
        mQuery.setIndex(EsIndexEnum.EXPERT.getEsIndex());
        mQuery.setPageNum(bo.getPageNum());
        mQuery.setPageSize(bo.getPageSize());
        mQuery.setKeyword(bo.getKeyword());
        try {
            return ResultConvert.success(knowledgeCenterQueryService.queryByTermsAndKey(mQuery, false, bo.getNodePath()));
        } catch (Exception e) {
            log.error("查询专家失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "查询失败");
        }
    }

    @ApiOperation(value = "产业链-链上企业分布")
    @PostMapping("/industry/company")
    @RateLimiter(key = "rate_limit:industry_detail:", count = 60)
    public Result<Map<String, Object>> industryDetail(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        try {
            return ResultConvert.success(cockpitService.industryDetail(bo.getChainId(), ""));
        } catch (Exception e) {
            log.error("获取产业链企业分布失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

    @ApiOperation(value = "产业链-链节点分布-圆环图统计")
    @PostMapping("/industry/node/distribution")
    @RateLimiter(key = "rate_limit:industry_node_distribution:", count = 60)
    public Result<PieVO> getNodeDistribution(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        try {
            return ResultConvert.success(cockpitService.getNodeDistribution(bo.getChainId()));
        } catch (Exception e) {
            log.error("获取节点分布失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

    @ApiOperation("人才学历、职称分布")
    @PostMapping("/degree_title/distribution")
    @RateLimiter(key = "rate_limit:degree_title_distribution:", count = 60)
    public Result<Map<String, Object>> getDegreeTitleDistribution(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        try {
            return ResultConvert.success(techCommissionService.getDegreeTitleDistribution(bo.getChainId()));
        } catch (Exception e) {
            log.error("获取学历职称分布失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

    @ApiOperation("外部人才节点分布")
    @PostMapping("/external/node/distribution")
    @RateLimiter(key = "rate_limit:external_node_distribution:", count = 60)
    public Result<PieVO> getExternalNodeDistribution(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        try {
            return ResultConvert.success(techCommissionService.getExternalNodeDistribution(bo.getChainId()));
        } catch (Exception e) {
            log.error("获取外部人才节点分布失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }


    @PostMapping("/get/finance/info")
    @ApiOperation("投融资指标监测")
    @RateLimiter(key = "rate_limit:finance_info:", count = 60)
    public Result getFinanceInfo(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        try {
            return ResultConvert.success(financeService.getFinanceInfo(bo.getChainId(), null, null));
        } catch (Exception e) {
            log.error("获取投融资信息失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

    @PostMapping("/get/subsidy/list")
    @ApiOperation("项目补助列表")
    @RateLimiter(key = "rate_limit:subsidy_list:", count = 60)
    public Result getEventList(@RequestBody final SubsidyBO bo){
        calibratePermission(bo);
        try {
            return ResultConvert.success(projectService.page4Subsidy(bo.getPageNum(), bo.getPageSize(), bo.getChainId(),
                    null, CommonConstant.DIVISION_NANPING.getId(), null, bo.getCompanyName(), null, bo.getProjectName()));
        } catch (Exception e) {
            log.error("获取项目补助列表失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

    @PostMapping("/weak/type")
    @ApiOperation("关键核心技术分类统计")
    @RateLimiter(key = "rate_limit:weak_type:", count = 60)
    public Result<List<CommonIndexBO>> getRiskMap(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        try {
            return ResultConvert.success(techInvestAttractService.getRiskMap(bo.getChainId()));
        } catch (Exception e) {
            log.error("获取技术分类统计失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

    @ApiOperation("技术合同成果交易统计")
    @PostMapping("/ach_trade/count")
    @RateLimiter(key = "rate_limit:ach_trade_count:", count = 60)
    public Result count(@RequestBody final BaseThirdQuery bo) {
        calibratePermission(bo);
        return ResultConvert.success(contractService.countByType());
    }

    @ApiOperation(value = "创新链诊断")
    @PostMapping("/chain_analyze")
    @RateLimiter(key = "rate_limit:chain_analyze:", count = 60)
    public Result<Map<String, Object>> analyzeInnovationChain(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        try {
            return ResultConvert.success(techInvestAttractService.analyzeInnovationChain(bo.getChainId()));
        } catch (Exception e) {
            log.error("创新链诊断失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

    @PostMapping("/totalIndex")
    @ApiOperation("四链融合总览-指标")
    @RateLimiter(key = "rate_limit:total_index:", count = 60)
    public Result totalIndex(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return ResultConvert.success(indexService.totalIndex(bo.getChainId()));
    }

    @ApiOperation(value = "指标")
    @PostMapping("/index")
    @RateLimiter(key = "rate_limit:index:", count = 60)
    public Result<Map<String, Object>> indexDetail(@RequestBody final  ChainBO bo) {
        calibratePermission(bo);
        try {
            return ResultConvert.success(cockpitService.indexDetail(bo.getChainId(), null));
        } catch (Exception e) {
            log.error("获取指标详情失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "获取数据失败");
        }
    }

    @ApiOperation(value = "四链融合模型")
    @PostMapping("/fusion")
    @RateLimiter(key = "rate_limit:fusion:", count = 60)
    public Result<Map<String, Object>> fusionDetail(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return ResultConvert.success(indexFusionService.fusionDetail(bo.getChainId()));
    }



    public static void main(String[] args) {
        String requestTime = System.currentTimeMillis() + "";
        System.out.println(requestTime);
        System.out.println(DigestUtils.md5Hex(tempAppKey + appSecret + requestTime));
    }

    /**
     * 专用AppKey
     */
    public static final Long tempAppKey = 87965432301L;

    /**
     * 专用AppSecret
     */
    public static final String appSecret = "gO4qBTQSirpFXeU2Hth";

    /**
     * 校验权限
     */
    private void calibratePermission(final BaseThirdQuery baseThirdQuery) {
        // 校验用户token
        final Long appKey = baseThirdQuery.getAppKey();
        final String requestTime = baseThirdQuery.getRequestTime();
        final String sign = baseThirdQuery.getSign();

        if (!Objects.equals(appKey, tempAppKey)) {
            throw new MessageException(ResultCodeEnum.USER_ERROR, "appKey错误");
        }

        // 可以根据需要取消注释以下代码，增加时间戳验证
        if (System.currentTimeMillis() - Long.parseLong(requestTime) > 15 * 60 * 1000) {
            throw new MessageException(ResultCodeEnum.USER_ERROR, "时间戳已过期");
        }

        // IP白名单验证也可以根据需要添加
        // final String whiteIp = user.getPassword();
        // if (whiteIp == null || !whiteIp.contains(clientIP)) {
        //     throw new MessageException(ResultCodeEnum.USER_ERROR, "不在IP白名单中");
        // }

        if (!DigestUtils.md5Hex(appKey + appSecret + requestTime).equals(sign)) {
            throw new MessageException(ResultCodeEnum.USER_ERROR, "签名错误");
        }
    }
}
