package com.quantchi.nanping.innovation.insight.model.bo;

import com.quantchi.nanping.innovation.model.bo.PageBO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/29 10:53 上午
 * @description
 */
@Data
public class ExpertPageBo extends PageBO {

    @ApiModelProperty("人才类型：1 引进人才 2 本地人才")
    private Integer type;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("产业链id集合")
    private List<String> chainIds;

    @ApiModelProperty("产业链节点id集合")
    private List<String> nodeIds;

}
