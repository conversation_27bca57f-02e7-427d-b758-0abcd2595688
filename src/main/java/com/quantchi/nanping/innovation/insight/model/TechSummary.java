package com.quantchi.nanping.innovation.insight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class TechSummary {

    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("应用")
    private String application;

    @ApiModelProperty("参数")
    private String parameter;

    public static TechSummary convertFromTechAnalysis(TechAnalysis techAnalysis) {

        TechSummary techSummary = new TechSummary();

        BeanUtils.copyProperties(techAnalysis, techSummary);

        techSummary.setDesc(techAnalysis.getDescription());

        return techSummary;
    }

    public TechAnalysis convertToTechAnalysis() {

        TechAnalysis techAnalysis = new TechAnalysis();

        BeanUtils.copyProperties(this, techAnalysis);

        techAnalysis.setDescription(this.getDesc());

        return techAnalysis;
    }

}
