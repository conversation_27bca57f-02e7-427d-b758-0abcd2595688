package com.quantchi.nanping.innovation.insight.controller;

import com.quantchi.common.core.utils.StringUtils;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.insight.model.TechAnalysisVO;
import com.quantchi.nanping.innovation.insight.model.TechDescription;
import com.quantchi.nanping.innovation.insight.model.TechDescriptionVO;
import com.quantchi.nanping.innovation.insight.service.InsightService;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.admin.SysConstant;
import com.quantchi.nanping.innovation.model.bo.ChainNodeTreeBO;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.enums.BusinessType;
import com.quantchi.nanping.innovation.model.vo.ChainNodeTreeVO;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.IIndustryChainNodeWeakService;
import com.quantchi.nanping.innovation.service.INodeWeakProjectService;
import com.quantchi.nanping.innovation.service.ISysConstantService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.RiskService;
import com.quantchi.tianying.model.EsPageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.security.InvalidParameterException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/27 10:13 上午
 * @description
 */
@RestController
@RequestMapping("/insight")
@Api(tags = "链洞察")
@PlatformAuthCheck(type = {"0"})
@Metrics
public class InsightController {

    @Autowired
    private InsightService insightService;

    @Autowired
    private RiskService riskService;

    @Autowired
    private INodeWeakProjectService nodeWeakProjectService;

    @Autowired
    private ISysConstantService sysConstantService;

    @Autowired
    private IndustryChainService chainService;

    @Autowired
    private IIndustryChainNodeWeakService weakService;

    @PostMapping("/getTree")
    @ApiOperation("获取节点树")
    @Log(title = "链洞察")
    public Result<ChainNodeTreeVO> getTree(@RequestBody ChainNodeTreeBO chainNodeTreeBO) {
        return ResultConvert.success(insightService.getChainNodeTree(chainNodeTreeBO));
    }

    @PostMapping("/getIndex")
    @ApiOperation("获取指标")
    //@Log(title = "链洞察-获取指标")
    public Result<List<CommonIndexBO>> getIndex(@RequestBody ChainNodeTreeBO chainNodeTreeBO) {
        return ResultConvert.success(insightService.getIndex(chainNodeTreeBO));
    }

    @PostMapping("/getTypeDistribution")
    @ApiOperation("获取各要素类型分布")
    //@Log(title = "链洞察-获取各要素类型分布")
    public Result<PieVO> getTypeDistribution(@RequestBody EsSimpleQuery esSimpleQuery) {
        return ResultConvert.success(insightService.getTypeDistribution(esSimpleQuery));
    }

    @PostMapping("/listResource")
    @ApiOperation("获取各个要素列表")
    //@Log(title = "链洞察-获取各个要素列表")
    public Result<EsPageResult> listResource(@RequestBody EsSimpleQuery esSimpleQuery) {
        return ResultConvert.success(insightService.listResource(esSimpleQuery));
    }

    @GetMapping("/tech_detail")
    @ApiOperation("技术详情")
    //@Log(title = "链洞察-技术详情")
    public Result<Map<String, Object>> getTechDetail(@NotBlank @RequestParam String id) {
        Map<String, Object> techDetail = riskService.getById(id);
        List<Map<String, Object>> weakProjects = nodeWeakProjectService.listByWeakDataId(id);
        techDetail.put("weakProjects", weakProjects);
        // 补充本地调研观点
        SysConstant sysConstant = sysConstantService.getBySubjectIdAndConstantCode(id, "clue.local_overview");
        if (sysConstant != null){
            ((Map<String, Object>)techDetail.get("clue")).put("local_overview", sysConstant.getContent());
        }
        // 补充技术简介和原因分析
//        IndustryChainNodeWeakDetail weakDetail = weakService.getDetailById(id);
//        techDetail.put("description", weakDetail.getDescription());
//        techDetail.put("reason", weakDetail.getReasonAnalysis());
        IndustryChainNodeWeak weak = chainService.getWeakNodesByDataId(id);
        if (weak != null){
            techDetail.put("reason", weak.getDescription());
        }
        return ResultConvert.success(techDetail);
    }

    @GetMapping("/tech_analysis")
    @ApiOperation("ai分析关键技术")
    public Result<TechAnalysisVO> getTechAnalysis(@RequestParam String chainId, @RequestParam String chainName,
                                                  @RequestParam String dataId, @RequestParam String dataName) {
        if (StringUtils.isAnyBlank(chainId, chainName, dataId, dataName)) {
            throw new BusinessException("参数不能为空！");
        }
        return ResultConvert.success(insightService.getTechAnalysis(chainId, chainName, dataId, dataName));

    }

    @GetMapping("/tech_analysis_description")
    @ApiOperation("ai分析关键技术-描述")
    public Result<TechDescriptionVO> getTechDescription(@RequestParam String chainId, @RequestParam String chainName,
                                                        @RequestParam String dataId, @RequestParam String dataName) {
        if (StringUtils.isAnyBlank(chainId, chainName, dataId, dataName)) {
            throw new BusinessException("参数不能为空！");
        }
        return ResultConvert.success(insightService.getTechDescription(chainId, chainName, dataId, dataName));
    }

    @GetMapping("/tech_analysis/reset")
    @ApiOperation("重新生成关键技术分析")
    @Log(title = "重新生成关键技术分析", businessType = BusinessType.DELETE)
    public Result<Boolean> getTechAnalysis(@RequestParam String analysisId) {
        return ResultConvert.success(insightService.removeTechAnalysis(analysisId));

    }

    @GetMapping("/tech_analysis/localization")
    @ApiOperation("大模型数据全部入库到数据库")
    public Result<Boolean> localization() {
        return ResultConvert.success(insightService.localization());
    }

}
