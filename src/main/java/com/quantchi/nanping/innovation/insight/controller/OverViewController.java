package com.quantchi.nanping.innovation.insight.controller;

import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.insight.model.bo.ExpertPageBo;
import com.quantchi.nanping.innovation.insight.model.bo.PersonStatsBo;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.IndustryChainNodeType;
import com.quantchi.nanping.innovation.model.bo.AreaRankBo;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.CompanyAnalyzeBo;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.service.IndexService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.*;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.RequestContext;
import com.quantchi.tianying.model.EsPageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/10 4:41 下午
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/overview")
@Api(tags = "总览")
@PlatformAuthCheck(type = {"0"})
@Metrics
public class OverViewController {

    @Resource
    private CompanyService companyService;

    @Resource
    private FinanceService financeService;

    @Resource
    private PatentService patentService;

    @Resource
    private TalentService talentService;

    @Autowired
    private IndexService indexService;

    @Resource
    private IIndexFusionService indexFusionService;

    @Autowired
    private AchievementService achievementService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private IndustryChainService chainService;

    @GetMapping("/all")
    @ApiOperation("总览接口")
    @Log(title = "四链融合总览")
    public Result getAll(String chainId) {
        Map<String, Object> resultMap = new HashMap<>();
        //企业分析
        Map<String, Object> companyMap = new HashMap<>();
        //企业分析-企业类型分析
        Map<String, CompanyAnalyzeBo> typeMap = new HashMap<>();
        typeMap.put("typeInfo", companyService.getCompanyTypeAnalyze(chainId, null, null, null, true));
        companyMap.put("typeMap", typeMap);
        //企业分析-科技型企业数量变化趋势
        Map<String, CompanyAnalyzeBo> companyTendInfo = companyService.getCompanyTendInfo(chainId);
        companyMap.put("tendMap", companyTendInfo);
        resultMap.put("companyMap", companyMap);

        //技术分析
        Map<String, Object> techMap = new HashMap<>();
        //技术分析-科研成果分类
        techMap.put("achievementType", achievementService.getAchievementTypeMap(chainId, null));
        //技术分析-专利增长趋势（高被引）
        techMap.put("patentTend", patentService.getPatentReferenceTend(chainId));
        // 技术分析-专利热词
        techMap.put("wordRank", patentService.getWordCloud(chainId, chainId, "chain.id", null,null,true, false));
        // 技术分析-引用最多专利分析
        techMap.put("topFamily", patentService.getTopReferencedPatents(chainId, 10, false));
        // 技术分析-专利构成旭日图
        techMap.put("sunriseChart", patentService.getPatentSunriseChart(chainId, false));
        resultMap.put("techMap", techMap);

        //人才分析
        Map<String, Object> talentMap = new HashMap<>();
        //人才分析-可引进人才统计
        List<CommonIndexBO> introduceTalentMap = talentService.getDegreeDistribution(PersonStatsBo.build4Source(chainId, null, null, null, false, false), true).getIndexList();
        talentMap.put("introduceTalentMap", introduceTalentMap);
        //人才分析-本地人才及科特派统计
        List<CommonIndexBO> schoolTalentMap = talentService.getDegreeDistribution(PersonStatsBo.build4Source(chainId, null, null, null, true, false), true).getIndexList();
        talentMap.put("schoolTalentMap", schoolTalentMap);
        resultMap.put("talentMap", talentMap);

        //资金分析
        Map<String, Object> financeMap = new HashMap<>();
        // 资金分析-企业项目补助资金规模分析
        financeMap.put("subsidyScale", projectService.getInvestmentAndSubsidyTend(chainId, null, RequestContext.getCityId(), RequestContext.getAreaId()));
        // 资金分析-产业投融资金额分析
        financeMap.put("financeScale", financeService.getFinanceScaleTendIncludeProvince(chainId, RequestContext.getCityId(), RequestContext.getAreaId()));
        // 资金分析-产业投融资金额趋势
        financeMap.put("financeTend", financeService.getFinanceScaleTend(chainId, null, null));
        // 资金分析-投资机构
        financeMap.put("orgList", financeService.getInvestmentFund(chainId));
        resultMap.put("financeMap", financeMap);
        return ResultConvert.success(resultMap);
    }

    @GetMapping("/talent/page")
    @ApiOperation("人才列表接口")
    //@Log(title = "四链融合总览-人才列表接口")
    public Result getTalentPage(@Validated ExpertPageBo expertPageBo) {
        EsPageResult talentPage = talentService.page(expertPageBo);
        EsAlterUtil.filterChainByChainId(talentPage.getList(), expertPageBo.getChainId());
        return ResultConvert.success(talentPage);
    }

    @GetMapping("/totalIndex")
    //@Log(title = "四链融合总览-指标")
    public Result totalIndex(@NotBlank @RequestParam String chainId) {
        Map<String, Object> resultMap = indexService.totalIndex(chainId);
        return ResultConvert.success(resultMap);
    }

    @GetMapping("/rankSearch")
    @ApiOperation("综合指数排名")
    public Result areaRank(@NotBlank @RequestParam String chainId) {
        List<AreaRankBo> areaList = indexFusionService.getAreaRank(chainId);
        return ResultConvert.success(areaList);
    }

    @GetMapping("/node/score")
    @ApiOperation("强补固拓节点详细分数")
    public Result nodeScore(@NotBlank @RequestParam String nodeId) {
        List<IndustryChainNodeType> typeList = chainService.listNodeTypeByNodeId(nodeId);
        return ResultConvert.success(typeList);
    }

}
