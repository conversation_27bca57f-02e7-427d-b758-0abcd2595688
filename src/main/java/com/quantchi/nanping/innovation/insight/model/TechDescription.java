package com.quantchi.nanping.innovation.insight.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("industry_chain_node_weak_description")
public class TechDescription {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("es实体id")
    private String dataId;

    @ApiModelProperty("薄弱环节名称")
    private String dataName;

    @ApiModelProperty("推荐名称")
    private String recommendName;

    @ApiModelProperty("推荐类型")
    private String recommendClass;

    @ApiModelProperty("推荐理由")
    private String recommendReason;

    @ApiModelProperty("esId")
    private String esId;

    @ApiModelProperty("es索引库")
    private String esIndex;
}
