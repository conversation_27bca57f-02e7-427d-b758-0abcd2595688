package com.quantchi.nanping.innovation.insight.service;

import com.quantchi.nanping.innovation.insight.model.TechAnalysisVO;
import com.quantchi.nanping.innovation.insight.model.TechDescription;
import com.quantchi.nanping.innovation.insight.model.TechDescriptionVO;
import com.quantchi.nanping.innovation.model.bo.ChainNodeTreeBO;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.vo.ChainNodeTreeVO;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.tianying.model.EsPageResult;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/12/27 14:19
 */
public interface InsightService {


    /**
     * 获取节点树
     *
     * @param chainNodeTreeBO
     * @return
     */
    ChainNodeTreeVO getChainNodeTree(ChainNodeTreeBO chainNodeTreeBO);

    /**
     * 获取链的指标
     *
     * @param chainNodeTreeBO
     * @return
     */
    List<CommonIndexBO> getIndex(ChainNodeTreeBO chainNodeTreeBO);

    /**
     * 查询资源
     *
     * @param esSimpleQuery
     * @return
     */
    EsPageResult listResource(EsSimpleQuery esSimpleQuery);

    /**
     * 各要素类型数量分布
     *
     * @param esSimpleQuery
     * @return
     */
    PieVO getTypeDistribution(EsSimpleQuery esSimpleQuery);

    TechAnalysisVO getTechAnalysis(String chainId, String chainName, String dataId, String dataName);

    /**
     * 删除已经生成的关键技术分析
     *
     * @param analysisId
     * @return
     */
    boolean removeTechAnalysis(String analysisId);

    Boolean localization();

    /**
     * ai分析关键技术-描述
     */
    TechDescriptionVO getTechDescription(String chainId, String chainName, String dataId, String dataName);

}
