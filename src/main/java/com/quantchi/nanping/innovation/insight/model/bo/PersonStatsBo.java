package com.quantchi.nanping.innovation.insight.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/3/17 3:26 下午
 * @description
 */
@Data
public class PersonStatsBo {

    @ApiModelProperty("产业链节点Id")
    private String nodeId;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("人才类型: A、B、C、D、E、F")
    private String type;

    @ApiModelProperty("是否为以上")
    private boolean isAbove;

    @ApiModelProperty("城市id")
    private String cityId;

    @ApiModelProperty("区县id")
    private String areaId;

    @ApiModelProperty("是否为本地人才")
    private boolean isLocal;

    @ApiModelProperty("是否为科技特派员")
    private boolean isTechCommissioner;

    public PersonStatsBo() {
    }

    public PersonStatsBo(String chainId, String nodeId, String cityId, String areaId, String type, Boolean isAbove) {
        this.chainId = chainId;
        this.nodeId = nodeId;
        this.type = type;
        this.isAbove = isAbove;
        this.cityId = cityId;
        this.areaId = areaId;
    }

    /**
     * 构建人才等级的筛选条件对象
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param type
     * @param isAbove
     * @return
     */
    public static PersonStatsBo build4Level(String chainId, String nodeId, String cityId, String areaId, String type, Boolean isAbove) {
        PersonStatsBo bo = new PersonStatsBo();
        bo.setChainId(chainId);
        bo.setNodeId(nodeId);
        bo.setCityId(cityId);
        bo.setAreaId(areaId);
        bo.setType(type);
        bo.setAbove(isAbove);
        return bo;
    }

    /**
     * 构建人才来源的筛选对象
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param isLocal
     * @param isTechCommissioner
     * @return
     */
    public static PersonStatsBo build4Source(String chainId, String nodeId, String cityId, String areaId, boolean isLocal, boolean isTechCommissioner) {
        PersonStatsBo bo = new PersonStatsBo();
        bo.setChainId(chainId);
        bo.setNodeId(nodeId);
        bo.setCityId(cityId);
        bo.setAreaId(areaId);
        bo.setLocal(isLocal);
        bo.setTechCommissioner(isTechCommissioner);
        return bo;
    }
}
