package com.quantchi.nanping.innovation.insight.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2022/12/29 20:33
 */
@Data
@TableName("index_fusion")
public class IndexFusion {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("节点id")
    private String chainNodeId;

    @ApiModelProperty("区域id")
    private String regionId;

    @ApiModelProperty("指标id")
    private String indexId;

    @ApiModelProperty("指标名称")
    @TableField(exist = false)
    private String indexName;

    @ApiModelProperty("时间")
    private String statTime;

    @ApiModelProperty("指标数值")
    private BigDecimal data;

    @ApiModelProperty("指标分析建议")
    private String description;
}
