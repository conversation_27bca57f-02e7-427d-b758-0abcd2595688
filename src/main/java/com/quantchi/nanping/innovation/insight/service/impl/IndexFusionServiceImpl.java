package com.quantchi.nanping.innovation.insight.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.insight.dao.IndexFusionDAO;
import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.bo.AreaRankBo;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.IndexQuery;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.constant.Constant;
import com.quantchi.nanping.innovation.model.entity.DmDivisionEntity;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.model.vo.CommonIndexVO;
import com.quantchi.nanping.innovation.service.IDivisionService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.utils.BigDecimalUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/6 9:39
 */
@Service
public class IndexFusionServiceImpl extends ServiceImpl<IndexFusionDAO, IndexFusion> implements IIndexFusionService {

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private IDivisionService divisionService;

    @Override
    public List<IndexFusion> list(List<String> listIndexIds, String chainId, String chainNodeId, String regionId, boolean isNode, boolean isLatest) {
        // 设置默认值
        if (StringUtils.isEmpty(chainNodeId)) {
            if (StringUtils.isNotEmpty(chainId) && isNode) {
                // 设置根节点
                chainNodeId = industryChainService.getParentNodeIdByChainId(chainId);
            } else {
                chainNodeId = Constant.INDEX_EMPTY_CONDITION;
            }
        }
        if (StringUtils.isEmpty(chainId)) {
            chainId = Constant.INDEX_EMPTY_CONDITION;
        }
        if (StringUtils.isEmpty(regionId)) {
            regionId = Constant.INDEX_EMPTY_CONDITION;
        }
        List<IndexFusion> fusionList = this.list(Wrappers.lambdaQuery(IndexFusion.class)
                .in(IndexFusion::getIndexId, listIndexIds)
                .eq(IndexFusion::getChainId, chainId)
                .eq(IndexFusion::getRegionId, regionId)
                .eq(IndexFusion::getChainNodeId, chainNodeId)
                .orderByAsc(IndexFusion::getStatTime));
        return isLatest ? selectLatestData(fusionList) : fusionList;
    }

    @Override
    public List<IndexFusion> list(String chainId, String indexId, List<String> regionIds) {
        return this.list(Wrappers.lambdaQuery(IndexFusion.class)
                .eq(IndexFusion::getIndexId, indexId)
                .eq(IndexFusion::getChainId, chainId)
                .in(IndexFusion::getRegionId, regionIds)
                .eq(IndexFusion::getChainNodeId, Constant.INDEX_EMPTY_CONDITION));
    }

    @Override
    public List<AreaRankBo> getAreaRank(String chainId) {
        List<IndexFusion> fusionList = this.list(Wrappers.lambdaQuery(IndexFusion.class)
                .eq(IndexFusion::getIndexId, FusionIndexEnum.COMPOSITE.getIndexId())
                .eq(IndexFusion::getChainNodeId, industryChainService.getParentNodeIdByChainId(chainId))
                .orderByAsc(IndexFusion::getStatTime));
        List<IndexFusion> latestFusionList = selectLatestData(fusionList);
        if (CollectionUtils.isEmpty(latestFusionList)){
            return new ArrayList<>();
        }
        List<DmDivisionEntity> regionList = divisionService.listByIds(latestFusionList.stream().map(IndexFusion::getRegionId).collect(Collectors.toList()));
        Map<String, BigDecimal> indexMap = latestFusionList.stream().collect(Collectors.toMap(IndexFusion::getRegionId, IndexFusion::getData));
        List<AreaRankBo> boList = new ArrayList<>(regionList.size());
        for (DmDivisionEntity region : regionList) {
            AreaRankBo bo = new AreaRankBo();
            bo.setName(region.getName());
            bo.setData(indexMap.get(region.getId()));
            boList.add(bo);
        }
        Collections.sort(boList, (b1, b2) -> BigDecimal.ZERO.compareTo(BigDecimalUtil.subtract(b1.getData(), b2.getData())));
        return boList;
    }

    @Override
    public Map<String, Object> getIndexFusionTrend(List<String> listIndexIds, String chainId, String chainNodeId, String regionId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        List<IndexFusion> fusionList = list(listIndexIds, chainId, chainNodeId, regionId, true, false);
        if (fusionList.size() > 12){
            fusionList = fusionList.subList(fusionList.size() - 12, fusionList.size());
        }
        fusionList.forEach(e -> {
            Map<String, Object> map = new HashMap<>();
            map.put("value", e.getData());
            resultMap.put(e.getStatTime().substring(0, 7), map);
        });
        return resultMap;
    }

    @Override
    public List<IndexFusion> listByIndexIdAndChainIdAndNodeIds(String indexId, String chainId, List<String> nodeIds, String regionId) {
        List<IndexFusion> fusionList = this.list(Wrappers.lambdaQuery(IndexFusion.class)
                .eq(IndexFusion::getIndexId, indexId)
                .eq(IndexFusion::getChainId, chainId)
                .in(IndexFusion::getChainNodeId, nodeIds)
                .eq(StringUtils.isNotEmpty(regionId), IndexFusion::getRegionId, regionId)
                .eq(StringUtils.isEmpty(regionId), IndexFusion::getRegionId, CommonConstant.DIVISION_NANPING.getId())
                .orderByAsc(IndexFusion::getStatTime));
        return selectLatestData(fusionList);
    }

    @Override
    public IndexFusion getCompositeFusionIndex(String chainId, String nodeId, String regionId) {
        return getOne(FusionIndexEnum.COMPOSITE.getIndexId(), chainId, nodeId, regionId, true);
    }

    /**
     * 筛选最新统计日期的数据
     *
     * @param targetList 必须按统计时间升序排列
     * @return
     */
    private List<IndexFusion> selectLatestData(List<IndexFusion> targetList) {
        // 筛选最新日期
        Map<String, IndexFusion> resultMap = new HashMap<>();
        targetList.forEach(e -> {
            String key = e.getIndexId() + e.getChainId() + e.getChainNodeId() + e.getRegionId();
            resultMap.put(key, e);
        });
        return new ArrayList<>(resultMap.values());
    }

    @Override
    public IndexFusion getOne(String indexId, String chainId, String nodeId, String regionId, boolean isNode) {
        // 设置默认值
        if (StringUtils.isEmpty(nodeId)) {
            if (StringUtils.isNotEmpty(chainId) && isNode) {
                // 设置根节点
                nodeId = industryChainService.getParentNodeIdByChainId(chainId);
            } else {
                nodeId = Constant.INDEX_EMPTY_CONDITION;
            }
        }
        if (StringUtils.isEmpty(chainId)) {
            chainId = Constant.INDEX_EMPTY_CONDITION;
        }
        if (StringUtils.isEmpty(regionId)) {
            regionId = Constant.INDEX_EMPTY_CONDITION;
        }
        IndexFusion fusion = this.getOne(Wrappers.lambdaQuery(IndexFusion.class)
                .eq(IndexFusion::getIndexId, indexId)
                .eq(IndexFusion::getChainId, chainId)
                .eq(IndexFusion::getRegionId, regionId)
                .eq(IndexFusion::getChainNodeId, nodeId)
                .orderByDesc(IndexFusion::getStatTime)
                .last("limit 1"));
        return fusion == null? new IndexFusion(): fusion;
    }

    @Override
    public Map<String, Object> fusionDetail(String chainId) {
        List<IndexFusion> fusionList = list(FusionIndexEnum.listIndexIds(), chainId, null,
                CommonConstant.DIVISION_NANPING.getId(), true, true);
        Map<String, IndexFusion> fusionMap = fusionList.stream().collect(Collectors.toMap(IndexFusion::getIndexId, Function.identity()));
        Map<String, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("综合指数", fusionMap.get(FusionIndexEnum.COMPOSITE.getIndexId()));
        resultMap.put("创新链", fusionMap.get(FusionIndexEnum.INNOVATION_CHAIN.getIndexId()));
        resultMap.put("资金链", fusionMap.get(FusionIndexEnum.FUND_CHAIN.getIndexId()));
        resultMap.put("人才链", fusionMap.get(FusionIndexEnum.PERSON_CHAIN.getIndexId()));
        resultMap.put("产业链", fusionMap.get(FusionIndexEnum.INDUSTRY_CHAIN.getIndexId()));
        return resultMap;
    }

}
