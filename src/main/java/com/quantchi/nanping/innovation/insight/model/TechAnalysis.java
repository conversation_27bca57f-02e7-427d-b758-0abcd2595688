package com.quantchi.nanping.innovation.insight.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("industry_chain_node_weak_analysis")
public class TechAnalysis {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("产业链id")
    private String chainId;

    @ApiModelProperty("es实体id")
    private String dataId;

    @ApiModelProperty("薄弱环节名称")
    private String dataName;

    @ApiModelProperty("技术简介-描述")
    private String description;

    @ApiModelProperty("技术简介-应用")
    private String application;

    @ApiModelProperty("技术简介-参数")
    private String parameter;

    @ApiModelProperty("原因分析")
    private String reasonExt;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
