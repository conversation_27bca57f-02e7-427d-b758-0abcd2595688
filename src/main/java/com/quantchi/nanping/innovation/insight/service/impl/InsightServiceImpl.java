package com.quantchi.nanping.innovation.insight.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.nanping.innovation.common.exception.MessageException;
import com.quantchi.nanping.innovation.component.LocalModelComponent;
import com.quantchi.nanping.innovation.insight.dao.TechAnalysisDAO;
import com.quantchi.nanping.innovation.insight.dao.TechDescriptionDAO;
import com.quantchi.nanping.innovation.insight.model.*;
import com.quantchi.nanping.innovation.insight.model.bo.PersonStatsBo;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.insight.service.InsightService;
import com.quantchi.nanping.innovation.model.IndustryChain;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.bo.ChainNodeTreeBO;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.*;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.model.vo.ChainNodeTreeVO;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.impl.IndustryChainNodeWeakServiceImpl;
import com.quantchi.nanping.innovation.service.library.*;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.RequestContext;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/12/27 14:19
 */
@Slf4j
@Service
public class InsightServiceImpl implements InsightService {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private IIndexFusionService indexFusionService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private AchievementService achievementService;

    @Autowired
    private RiskService riskService;

    @Autowired
    private DemandService demandService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private IndustryChainService chainService;

    @Autowired
    private LocalModelComponent localModelComponent;

    @Autowired
    private TechAnalysisDAO techAnalysisDAO;

    @Autowired
    private TechDescriptionDAO techDescriptionDAO;

    @Autowired
    private IndustryChainNodeWeakServiceImpl industryChainNodeWeakService;

    @Value("${local.model.workflowApi}")
    private String workflowApi;

    @Value("${local.model.analysis.apiKey}")
    private String analysisApiKey;

    @Override
    public ChainNodeTreeVO getChainNodeTree(ChainNodeTreeBO chainNodeTreeBO) {
        chainNodeTreeBO.setRegionId(RequestContext.getRegionId());
        List<ChainNodeTreeVO> list = industryChainService.getTreeNodesByChainId(chainNodeTreeBO.getChainId());
        //根据不同的链类型，注入不同的参数
        ChainTypeEnum chainTypeEnum = ChainTypeEnum.toEnum(chainNodeTreeBO.getChainType());
        if (chainTypeEnum == null) {
            throw new MessageException(ResultCodeEnum.INPUT_ERROR);
        }
        List<String> nodeIds = list.stream().map(ChainNodeTreeVO::getId).collect(Collectors.toList());
        switch (chainTypeEnum) {
            case ALL:
                List<IndexFusion> fusionList = indexFusionService.listByIndexIdAndChainIdAndNodeIds(FusionIndexEnum.COMPOSITE.getIndexId(),
                        chainNodeTreeBO.getChainId(), nodeIds, chainNodeTreeBO.getRegionId());
                Map<String, BigDecimal> nodeDataMap = fusionList.stream().collect(Collectors.toMap(IndexFusion::getChainNodeId, IndexFusion::getData));
                for (ChainNodeTreeVO temp : list) {
                    temp.setFusion(nodeDataMap.containsKey(temp.getId()) ? nodeDataMap.get(temp.getId()) : BigDecimal.ZERO);
                }
                break;
            case INNOVATION:
                // 关键技术节点判别
                List<String> keyTechNodeIds = riskService.getRiskNodeIds(chainNodeTreeBO.getChainId());
                for (ChainNodeTreeVO temp : list) {
                    temp.setKeyTech(keyTechNodeIds.contains(temp.getId()));
                }
                break;
            case INDUSTRY:
                BoolQueryBuilder externalCompanyQuery = companyService.buildExternalCompanyQuery(null, null);
                BoolQueryBuilder companyQuery = new BoolQueryBuilder();
                if (StringUtils.isNotEmpty(chainNodeTreeBO.getChainId())) {
                    companyQuery.filter(QueryBuilders.termQuery("chain.id", chainNodeTreeBO.getChainId()));
                    externalCompanyQuery.filter(QueryBuilders.termQuery("chain.id", chainNodeTreeBO.getChainId()));
                }
                Map<String, Long> externalCompanyMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.COMPANY, externalCompanyQuery, "chain_node.id");
                if (StringUtils.isNotBlank(chainNodeTreeBO.getRegionId())) {
                    EsAlterUtil.keywordsForMutiFields(companyQuery, chainNodeTreeBO.getRegionId(), "province.id,city.id,area.id".split(","));
                }
                Map<String, Long> LocalCompanyMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.COMPANY, companyQuery, "chain_node.id");
                companyQuery.filter(QueryBuilders.termsQuery("tag.name", CompanyTagEnum.HIGH_TECH.getName()));
                Map<String, List<String>> nodeTypeMap = industryChainService.getNodeTypeMapByChainId(chainNodeTreeBO.getChainId());
                //BoolQueryBuilder nationCompanyQuery = EsAlterUtil.buildAuthQuery(chainNodeTreeBO.getChainId(), null, null, null, false);
                //Map<String, Long> nationCompanyMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.COMPANY, nationCompanyQuery, "chain_node.id");
                for (ChainNodeTreeVO temp : list) {
                    temp.setExternalCompanyCount(externalCompanyMap.containsKey(temp.getId()) ? externalCompanyMap.get(temp.getId()) : 0L);
                    temp.setTopCompanyCount(LocalCompanyMap.containsKey(temp.getId()) ? LocalCompanyMap.get(temp.getId()) : 0L);
                    temp.setChainIndustryType(nodeTypeMap.get(temp.getId()));
                    //temp.setNationCompanyCount(nationCompanyMap.containsKey(temp.getId()) ? nationCompanyMap.get(temp.getId()) : 0L);
                }
                break;
            case EXPERT:
                Map<String, Long> localPersonNumMap = talentService.getNodeNumMap(chainNodeTreeBO.getChainId(), nodeIds, true);
                Map<String, Long> externalPersonNumMap = talentService.getNodeNumMap(chainNodeTreeBO.getChainId(), nodeIds, false);
                for (ChainNodeTreeVO temp : list) {
                    temp.setLocalExpertCount(localPersonNumMap.containsKey(temp.getId()) ? Math.toIntExact(localPersonNumMap.get(temp.getId())) : 0);
                    temp.setExternalExpertCount(externalPersonNumMap.containsKey(temp.getId()) ? Math.toIntExact(externalPersonNumMap.get(temp.getId())) : 0);
                }
                break;
            case FINANCE:
                //1.投融资
                BoolQueryBuilder financingQuery = new BoolQueryBuilder();
                if ("1009".equals(chainNodeTreeBO.getChainId())) {
                    financingQuery = financeService.buildBaseQueryTimeLimit(chainNodeTreeBO.getChainId(), chainNodeTreeBO.getChainNodeId(), null,
                            null, null);
                } else {
                    financingQuery = financeService.buildBaseQuery(chainNodeTreeBO.getChainId(), chainNodeTreeBO.getChainNodeId(), null,
                            RequestContext.getCityId(), RequestContext.getAreaId());
                }
                if (StringUtils.isNotEmpty(chainNodeTreeBO.getChainId())) {
                    financingQuery.filter(QueryBuilders.termQuery("chain.id", chainNodeTreeBO.getChainId()));
                }
                financingQuery.filter(QueryBuilders.existsQuery("financing_amount_cal"));
                Map<String, BigDecimal> financingMap = EsAlterUtil.getSumAggregation(elasticsearchHelper, EsIndexEnum.FINANCING, financingQuery, "chain_node.id", "financing_amount_cal");
                //2.项目补助金额
                BoolQueryBuilder subsidyQuery = projectService.buildSubsidyProjectQuery(chainNodeTreeBO.getChainId(), chainNodeTreeBO.getChainNodeId(),
                        RequestContext.getCityId(), RequestContext.getAreaId());
                Map<String, BigDecimal> subsidyMap = EsAlterUtil.getSumAggregation(elasticsearchHelper, EsIndexEnum.PROJECT, subsidyQuery, "chain_node.id", "apply_expense");
                //3.股权融资
                financingQuery.must(QueryBuilders.termsQuery("financing_round", "股权融资"));
                Map<String, Long> stockMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.FINANCING, financingQuery, "product_node.id");
                for (ChainNodeTreeVO temp : list) {
                    temp.setFinancing(BigDecimal.ZERO);
                    //如果sum有值，则取对应的值
                    if (financingMap.containsKey(temp.getId())) {
                        temp.setFinancing(financingMap.get(temp.getId()).divide(new BigDecimal(10000)));
                    }
                    if (subsidyMap != null && subsidyMap.containsKey(temp.getId())) {
                        temp.setSubsidy(subsidyMap.get(temp.getId()));
                    }
                    if (stockMap != null && stockMap.containsKey(temp.getId())) {
                        temp.setFinancingSuccess(true);
                    }
                }
                break;
            default:
                break;
        }
        //list组装成树
        return buildTree(list, list.get(0));
    }

    @Override
    public List<CommonIndexBO> getIndex(ChainNodeTreeBO chainNodeTreeBO) {
        ChainTypeEnum chainTypeEnum = ChainTypeEnum.toEnum(chainNodeTreeBO.getChainType());
        if (chainTypeEnum == null) {
            throw new MessageException(ResultCodeEnum.INPUT_ERROR);
        }
        List<CommonIndexBO> res = new ArrayList<>();
        String chainId = chainNodeTreeBO.getChainId(), nodeId = chainNodeTreeBO.getChainNodeId(),
                cityId = RequestContext.getCityId(), areaId = RequestContext.getAreaId(), regionId = RequestContext.getRegionId();
        switch (chainTypeEnum) {
            case ALL:
                // 1.绿色创新指数
                IndexFusion fusion = indexFusionService.getCompositeFusionIndex(chainId, nodeId, regionId);
                res.add(new CommonIndexBO("绿色创新指数", fusion.getData(), ""));
                // 2.企业总数&高新企业数
                Long companyNum = companyService.getTotalCountByRegionId(chainId, nodeId, cityId, areaId, null);
                res.add(new CommonIndexBO("企业总数", companyNum, "家"));
                Long highTechCompanyNum = companyService.getTotalCountByRegionId(chainId, nodeId, cityId, areaId, CompanyTagEnum.HIGH_TECH.getName());
                res.add(new CommonIndexBO("高新企业数", highTechCompanyNum, "家"));
                // 3.E类以上高级人才数量
                Long expertTotal = talentService.countByLevel(PersonStatsBo.build4Level(chainId, nodeId, cityId, areaId, "E", true), false);
                res.add(new CommonIndexBO("E类以上人才", expertTotal, "人"));
                // 4.专利数
                Long patentNum = patentService.countPatentNumByChainIdAndRegionId(chainId, nodeId, cityId, areaId);
                res.add(new CommonIndexBO("专利数", patentNum, "个"));
                // 5.关键技术
                res.add(new CommonIndexBO("关键技术", riskService.count(chainId, nodeId), "项"));
                // 6.投融资总额 & 贷款总额
                BigDecimal financeAmount = financeService.sumByChainAndRegion(chainId, nodeId, cityId, areaId, null);
                res.add(new CommonIndexBO("投融资金额", financeAmount, "万"));
                break;
            case INNOVATION:
                // 1.关键技术
                res.add(new CommonIndexBO("关键技术", riskService.count(chainId, nodeId), "项"));
                // 2.需求
                res.add(new CommonIndexBO("需求", demandService.count(chainId, nodeId), "项"));
                // 3.项目
                res.add(new CommonIndexBO("项目", projectService.count(chainId, nodeId, cityId, areaId), "项"));
                // 4.成果
                res.add(new CommonIndexBO("成果", achievementService.countByChain(chainId, nodeId), "项"));
                break;
            case INDUSTRY:
                // 1.强/补/固/拓节点数
                res.addAll(industryChainService.countNodeTypeMapByChainId(chainId, nodeId, false));
                // 2.本地企业数量&外部企业数量
                res.add(new CommonIndexBO("本地企业数量", companyService.getTotalCountByRegionId(chainId, nodeId, cityId, areaId, null), "家"));
                res.add(new CommonIndexBO("外部企业数量", elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(),
                        companyService.buildExternalCompanyQuery(chainId, nodeId)), "家"));
                break;
            case EXPERT:
                res.addAll(talentService.getExpertIndex4Insight(chainId, nodeId, cityId, areaId));
                break;
            case FINANCE:
                if ("1009".equals(chainId)) {
                    // 1.投融资金额
                    res.add(new CommonIndexBO("投融资金额", financeService.sumByChainAndRegionTimeLimit(chainId, nodeId, null, null, null), "万"));
                    // 3.投融资企业
                    res.add(new CommonIndexBO("投融资企业", financeService.countCompanyByChainAndRegionTimeLimit(chainId, nodeId, null, null, null), "家"));
                } else {
                    // 1.投融资金额
                    res.add(new CommonIndexBO("投融资金额", financeService.sumByChainAndRegion(chainId, nodeId, cityId, areaId, null), "万"));
                    // 3.投融资企业
                    res.add(new CommonIndexBO("投融资企业", financeService.countCompanyByChainAndRegion(chainId, nodeId, cityId, areaId, null), "家"));
                }
                // 1.投融资金额
//                res.add(new CommonIndexBO("投融资金额", financeService.sumByChainAndRegion(chainId, nodeId, cityId, areaId, null), "万"));
                // 2.项目补助金额
                res.add(new CommonIndexBO("项目补助金额", projectService.countSubsidy(chainId, nodeId, cityId, areaId, null), "万"));
                // 3.投融资企业
//                res.add(new CommonIndexBO("投融资企业", financeService.countCompanyByChainAndRegion(chainId, nodeId, cityId, areaId, null), "家"));
                // 4.项目补助企业
                res.add(new CommonIndexBO("项目补助单位", projectService.countSubsidyCompanyNum(chainId, nodeId, cityId, areaId), "家"));
                break;
            default:
                break;
        }
        return res;
    }

    /**
     * 查询资源
     *
     * @param esSimpleQuery
     * @return
     */
    @Override
    public EsPageResult listResource(EsSimpleQuery esSimpleQuery) {
        if (StringUtils.isBlank(esSimpleQuery.getResourceType())) {
            throw new MessageException(ResultCodeEnum.IMPORT_ERROR);
        }
        if (InsightTypeEnum.CHAIN_NODE.getType().equals(esSimpleQuery.getResourceType())) {
            // 链节点单独走表查询
            EsPageResult pageResult = new EsPageResult();
            Page<Map<String, Object>> page = industryChainService.page4NodeType(esSimpleQuery.getPageNum(), esSimpleQuery.getPageSize(), esSimpleQuery.getChainId(), esSimpleQuery.getChainNodeId(),
                    ChainNodeTypeEnum.getTypeByName(esSimpleQuery.getCurrentCategory()), esSimpleQuery.getKeywords());
            for (Map<String, Object> nodeType : page.getRecords()) {
                if (nodeType.get("node_type") != null) {
                    continue;
                }
                nodeType.put("node_type", "普通节点");
            }
            pageResult.setList(page.getRecords());
            pageResult.setPageSize((int) page.getSize());
            pageResult.setTotal(page.getTotal());
            return pageResult;
        }

        InsightTypeEnum insightTypeEnum = InsightTypeEnum.getEsIndexEnumByType(esSimpleQuery.getResourceType());
        esSimpleQuery.setResourceType(insightTypeEnum.getEsType());
        // 查询条件
        esSimpleQuery.setQueryBuilder(buildQueryBuilder(insightTypeEnum, esSimpleQuery));
        // 自定义排序
        String currentNodeId = esSimpleQuery.getChainNodeId();
        if (StringUtils.isEmpty(esSimpleQuery.getChainNodeId()) && StringUtils.isNotEmpty(esSimpleQuery.getChainId())){
            currentNodeId = industryChainService.getParentNodeByChainId(esSimpleQuery.getChainId()).getId();
        }
        esSimpleQuery.setFilterFunctionBuilders(buildFilterFunctionBuilders(insightTypeEnum, currentNodeId));
        EsPageResult pageResult = resourceService.listResource(esSimpleQuery);
        if (InsightTypeEnum.FINANCING.getType().equals(esSimpleQuery.getResourceType())) {
            // 额外换算下融资金额，由元换成万
            for (Map<String, Object> detail : pageResult.getList()) {
                String fieldName = "financing_amount";
                if (detail.containsKey(fieldName) && detail.get(fieldName) != null) {
                    BigDecimal financeAmount = new BigDecimal((Double) detail.get(fieldName)).divide(new BigDecimal(10000), 0, RoundingMode.HALF_UP);
                    detail.put(fieldName, financeAmount + "万元");
                } else {
                    detail.put(fieldName, "未披露");
                }

            }
        } else if (InsightTypeEnum.ACHIEVEMENT.getType().equals(esSimpleQuery.getResourceType())) {
            // 区分来源，全国科技奖励
            for (Map<String, Object> detail : pageResult.getList()) {
                detail.put("source", "全国科技奖励".equals(detail.get("source")) ? "非本地" : "本地");
            }
        }
        return pageResult;
    }

    /**
     * 自定义得分权重，用于复杂排序
     *
     * @param insightTypeEnum
     * @return
     */
    private List<FunctionScoreQueryBuilder.FilterFunctionBuilder> buildFilterFunctionBuilders(InsightTypeEnum insightTypeEnum, String chainNodeId) {
        List<FunctionScoreQueryBuilder.FilterFunctionBuilder> builders = new ArrayList<>();
        switch (insightTypeEnum) {
            case COMPANY:
            case COMPANY_IN:
            case COMPANY_TARGET:
            case COMPANY_EXTERNAL:
                builders.addAll(EsAlterUtil.buildCustomSortRule(EsIndexEnum.COMPANY.getEsIndex(), chainNodeId));
                break;
            case EXPERT_IN:
            case EXPERT_OUT:
                builders.addAll(EsAlterUtil.buildCustomSortRule(EsIndexEnum.EXPERT.getEsIndex(), null));
                break;
            case BOTTLENECK:
//                builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.termQuery("research_status", "有可能抢占技术制高点"), ScoreFunctionBuilders.weightFactorFunction(6)));
//                builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.termQuery("research_status", "已实现国产化替代"), ScoreFunctionBuilders.weightFactorFunction(5)));
//                builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.termQuery("research_status", "市内有基础实现攻关突破"), ScoreFunctionBuilders.weightFactorFunction(4)));
//                builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.termQuery("research_status", "省内有基础实现攻关突破"), ScoreFunctionBuilders.weightFactorFunction(3)));
//                builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.termQuery("research_status", "省外有基础实现攻关突破"), ScoreFunctionBuilders.weightFactorFunction(2)));
//                builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.termQuery("research_status", "尚无国产替代方案"), ScoreFunctionBuilders.weightFactorFunction(1)));
                break;
            case ACHIEVEMENT:
                builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.termQuery("source", "全国科技奖励"), ScoreFunctionBuilders.weightFactorFunction(0.1f)));
                break;
            default:
                break;
        }
        return builders;
    }

    @Override
    public PieVO getTypeDistribution(EsSimpleQuery esSimpleQuery) {
        if (StringUtils.isBlank(esSimpleQuery.getResourceType())) {
            throw new MessageException(ResultCodeEnum.IMPORT_ERROR);
        }
        String resourceType = esSimpleQuery.getResourceType();
        PieVO resultVO = new PieVO();
        if (InsightTypeEnum.CHAIN_NODE.getType().equals(resourceType)) {
            // 链节点单独走表查询
            resultVO.setIndexList(industryChainService.countNodeTypeMapByChainId(esSimpleQuery.getChainId(), esSimpleQuery.getChainNodeId(), true));
            return resultVO;
        }
        InsightTypeEnum insightTypeEnum = InsightTypeEnum.getEsIndexEnumByType(esSimpleQuery.getResourceType());
        esSimpleQuery.setResourceType(insightTypeEnum.getEsType());
        // 查询条件
        BoolQueryBuilder boolQueryBuilder = buildQueryBuilder(insightTypeEnum, esSimpleQuery);
        esSimpleQuery.setQueryBuilder(boolQueryBuilder);
        // 聚合条件
        Pair<String, List<String>> aggregationCondition = getAggregationCondition(insightTypeEnum);
        List<CommonIndexBO> indexList = new ArrayList<>(0);
        if (Arrays.asList(InsightTypeEnum.COMPANY.getType(), InsightTypeEnum.COMPANY_IN.getType()).contains(resourceType)) {
            // 饼图只计算本地企业
            boolQueryBuilder.filter(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
            resultVO.setTotal(elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder));
            indexList = companyService.getCompanyCountByType(boolQueryBuilder, aggregationCondition.getRight());
        } else if (InsightTypeEnum.COMPANY_EXTERNAL.getType().equals(resourceType)) {
            resultVO.setTotal(elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder));
            indexList = companyService.getCompanyCountByProvince(boolQueryBuilder);
        } else if (InsightTypeEnum.FINANCING_STOCK.getType().equals(resourceType)) {
            indexList = financeService.getCompanyCountByAmount(boolQueryBuilder);
        } else if (Arrays.asList(InsightTypeEnum.EXPERT.getType(), InsightTypeEnum.EXPERT_IN.getType(), InsightTypeEnum.EXPERT_OUT.getType()).contains(resourceType)) {
            indexList = talentService.getDegreeDistribution(PersonStatsBo.build4Source(esSimpleQuery.getChainId(), esSimpleQuery.getChainNodeId(), null, null,
                    !InsightTypeEnum.EXPERT_OUT.getType().equals(resourceType), false), true).getIndexList();
        } else {
            indexList = resourceService.countByType(esSimpleQuery, aggregationCondition.getLeft(), aggregationCondition.getRight());
        }
        if (InsightTypeEnum.BOTTLENECK.getType().equals(resourceType)) {
            Map<String, CommonIndexBO> boMap = indexList.stream().collect(Collectors.toMap(CommonIndexBO::getName, Function.identity()));
            List<String> techTypeList = Arrays.asList("有可能抢占技术制高点", "已实现国产化替代", "市内有基础实现攻关突破", "省内有基础实现攻关突破", "省外有基础实现攻关突破", "尚无国产替代方案");
            List<CommonIndexBO> sortedBoList = new ArrayList<>();
            for (String type : techTypeList) {
                if (boMap.containsKey(type)) {
                    sortedBoList.add(boMap.get(type));
                }
            }
            indexList.clear();
            indexList.addAll(sortedBoList);
        }
        resultVO.setIndexList(indexList);
        return resultVO;
    }

    @Override
    public TechAnalysisVO getTechAnalysis(String chainId, String chainName, String dataId, String dataName) {

        TechAnalysisVO vo = new TechAnalysisVO();

        //查数据库，无结果则调用大模型分析
        LambdaQueryWrapper<TechAnalysis> anaWrapper = new LambdaQueryWrapper<>();
        anaWrapper.eq(TechAnalysis::getChainId, chainId).eq(TechAnalysis::getDataId, dataId).last("limit 1");
        TechAnalysis techAnalysis = techAnalysisDAO.selectOne(anaWrapper);
        String analysisId = null;
        if (techAnalysis != null) {
            analysisId = techAnalysis.getId();
            TechSummary summary = TechSummary.convertFromTechAnalysis(techAnalysis);
            vo.setTechSummary(summary);
            vo.setReasonExt(techAnalysis.getReasonExt());
        } else {
            analysisId = getTechAnalysisFromLocalModel(chainId, chainName, dataId, dataName, vo);
        }
        vo.setAnalysisId(analysisId);

        return vo;
    }

    @Override
    public boolean removeTechAnalysis(String analysisId) {
        if (StringUtils.isEmpty(analysisId)){
            return true;
        }
        techAnalysisDAO.deleteById(analysisId);
        return true;
    }

    @Override
    public Boolean localization() {
        try {
            Map<String, String> chainMap = industryChainService.list(Wrappers.lambdaQuery(IndustryChain.class)
                            .select(IndustryChain::getId, IndustryChain::getName))
                    .stream().collect(Collectors.toMap(IndustryChain::getId, IndustryChain::getName));

            List<IndustryChainNodeWeak> list = industryChainNodeWeakService.list(Wrappers.lambdaQuery(IndustryChainNodeWeak.class)
                    .select(IndustryChainNodeWeak::getChainId, IndustryChainNodeWeak::getDataId, IndustryChainNodeWeak::getName)
                    .groupBy(IndustryChainNodeWeak::getChainId, IndustryChainNodeWeak::getDataId));

            List<String> exists = techAnalysisDAO.selectList(Wrappers.lambdaQuery(TechAnalysis.class)
                    .select(TechAnalysis::getChainId, TechAnalysis::getDataId))
                    .stream().map(TechAnalysis::getDataId).collect(Collectors.toList());

            list.forEach(item -> {
                if (exists.contains(item.getDataId())) {
                    return;
                }
                String chainName = chainMap.getOrDefault(item.getChainId(), "").toString();
                getTechAnalysisFromLocalModel(item.getChainId(), chainName, item.getDataId(), item.getName(), new TechAnalysisVO());
            });

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    @Override
    public TechDescriptionVO getTechDescription(String chainId, String chainName, String dataId, String dataName) {

        TechDescriptionVO vo = new TechDescriptionVO();

        LambdaQueryWrapper<TechDescription> descWrapper = new LambdaQueryWrapper<>();
        descWrapper.eq(TechDescription::getDataId, dataId);
        List<TechDescription> descriptions = techDescriptionDAO.selectList(descWrapper);

        if (CollectionUtils.isEmpty(descriptions)) {
            return vo;
        }

        Map<String, List<TechDescription>> recommendMap = descriptions.stream()
                .collect(Collectors.groupingBy(TechDescription::getRecommendClass));

        LinkedHashMap<String, List<TechDescription>> desc = new LinkedHashMap<>();

        String[] includes = recommendMap.keySet().toArray(new String[0]);
        Map<String, Object> descMap = elasticsearchHelper.getDataById(EsIndexEnum.BOTTLENECK.getEsIndex(), dataId, includes, null);

        descMap.forEach((k, v) -> {

            Optional.ofNullable(v).ifPresent(descValue -> {

                List<TechDescription> itemList = new ArrayList<>();

                Map<String, List<TechDescription>> recommendGroupMap = recommendMap.getOrDefault(k, Collections.emptyList())
                        .stream()
                        .collect(Collectors.groupingBy(TechDescription::getRecommendName));

                JSONArray descArr = JSONArray.parseArray(JSONObject.toJSONString(descValue));

                descArr.forEach(obj -> {
                    String name = JSONObject.parseObject(JSONObject.toJSONString(obj)).getString("name");
                    recommendGroupMap.getOrDefault(name, Collections.emptyList())
                            .stream()
                            .findFirst()
                            .ifPresent(itemList::add);
                });

                desc.put(k, itemList);
            });
        });

        vo.setDescriptionExt(desc);

        return vo;
    }

    /**
     * 通过大模型查询关键技术相关描述
     */
    @Transactional(rollbackFor = Exception.class)
    public String getTechAnalysisFromLocalModel(String chainId, String chainName, String dataId,
                                               String dataName, TechAnalysisVO vo) {

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("chain", chainName);
        paramMap.put("technology", dataName);

        JSONObject requestBody = localModelComponent.buildRequestParam(paramMap, "blocking");
        JSONObject response = localModelComponent.request(workflowApi, analysisApiKey, requestBody);

        if (response == null){
            return StringUtils.EMPTY;
        }

        TechSummary summary = new TechSummary();
        summary.setDesc(response.getOrDefault("desc", "").toString());
        summary.setApplication(response.getOrDefault("application", "").toString());
        summary.setParameter(response.getOrDefault("parameter", "").toString());

        vo.setTechSummary(summary);

        vo.setReasonExt(response.getOrDefault("reason", "").toString());

        TechAnalysis analysis = summary.convertToTechAnalysis();
        analysis.setId(UUID.randomUUID().toString().replace("-", ""));
        analysis.setChainId(chainId);
        analysis.setDataId(dataId);
        analysis.setDataName(dataName);
        analysis.setReasonExt(vo.getReasonExt());
        analysis.setCreateTime(LocalDateTime.now());
        analysis.setUpdateTime(LocalDateTime.now());
        techAnalysisDAO.insert(analysis);
        return analysis.getId();
    }


    private ChainNodeTreeVO buildTree(List<ChainNodeTreeVO> nodes, ChainNodeTreeVO root) {
        for (ChainNodeTreeVO node : nodes) {
            if (node.getParentId() != null && node.getParentId().equals(root.getId())) {
                root.getChildList().add(node);
                buildTree(nodes, node);
            }
        }
        return root;
    }

    /**
     * 定义查询条件
     *
     * @param insightTypeEnum
     * @param simpleQuery
     * @return
     */
    private BoolQueryBuilder buildQueryBuilder(InsightTypeEnum insightTypeEnum, EsSimpleQuery simpleQuery) {
        String chainId = simpleQuery.getChainId(), nodeId = simpleQuery.getChainNodeId();
        Integer regionType = simpleQuery.getRegionType();
        String currentCategory = simpleQuery.getCurrentCategory();
        List<String> allCategories = simpleQuery.getAllCategories();
        BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, true);
        switch (insightTypeEnum) {
            case COMPANY:
                queryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, false);
                if (regionType != null) {
                    if (regionType == 0) {
                        queryBuilder.filter(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
                        if (StringUtils.isNotEmpty(RequestContext.getAreaId())) {
                            queryBuilder.filter(QueryBuilders.termQuery("area.id", RequestContext.getAreaId()));
                        }
                    } else if (regionType == 1) {
                        queryBuilder.filter(QueryBuilders.termQuery("province.id", CommonConstant.DIVISION_FUJIAN.getId()));
                    }
                }
                setCategoryQuery(queryBuilder, "tag.name", currentCategory, allCategories);
                break;
            case COMPANY_IN:
                setCategoryQuery(queryBuilder, "tag.name", currentCategory, allCategories);
                break;
            case COMPANY_TARGET:
                queryBuilder = companyService.buildTargetCompanyQuery(chainId, nodeId);
                setCategoryQuery(queryBuilder, "province.name", currentCategory, allCategories);
                break;
            case COMPANY_EXTERNAL:
                queryBuilder = companyService.buildExternalCompanyQuery(chainId, nodeId);
                setCategoryQuery(queryBuilder, "province.name", currentCategory, allCategories);
                break;
            case EXPERT:
                setCategoryQuery(queryBuilder, "final_edu_degree", currentCategory, allCategories);
                break;
            case EXPERT_IN:
                setCategoryQuery(queryBuilder, "final_edu_degree", currentCategory, allCategories);
                break;
            case EXPERT_OUT:
                queryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, false);
                //queryBuilder.filter(QueryBuilders.existsQuery("city.id"));
                queryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
                if (CollectionUtils.isNotEmpty(allCategories)) {
                    allCategories.addAll(Arrays.asList("大学本科", "大学", "学士"));
                }
                setCategoryQuery(queryBuilder, "final_edu_degree", currentCategory, allCategories);
                break;
            case FINANCING:
                if ("1009".equals(chainId)) {
                    queryBuilder = financeService.buildBaseQueryTimeLimit(chainId, nodeId, null, null, null);
                } else {
                    queryBuilder = financeService.buildBaseQuery(chainId, nodeId, null, RequestContext.getCityId(), RequestContext.getAreaId());
                }
                setCategoryQuery(queryBuilder, "financing_round", currentCategory, allCategories);
                break;
            case FINANCING_STOCK:
                queryBuilder = financeService.buildBaseQuery(chainId, nodeId, null, RequestContext.getCityId(), RequestContext.getAreaId());
                queryBuilder.filter(QueryBuilders.termQuery("financing_round", "股权融资"));
                break;
            case BOTTLENECK:
                queryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, false);
                if (!"1003".equals(chainId)){
                    List<IndustryChainNodeWeak> weakList = chainService.listWeakNodesByChainId(chainId);
                    if (CollectionUtils.isNotEmpty(weakList)){
                        Set<String> dataIds = weakList.stream().map(IndustryChainNodeWeak::getDataId).collect(Collectors.toSet());
                        queryBuilder.filter(QueryBuilders.idsQuery().addIds(dataIds.toArray(new String[0])));
                    }
                }
                setCategoryQuery(queryBuilder, "research_status", currentCategory, allCategories);
                break;
            case DEMAND:
                queryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, false);
                setCategoryQuery(queryBuilder, "source_type", currentCategory, allCategories);
                break;
            case PROJECT:
                queryBuilder.mustNot(QueryBuilders.termQuery("project_type", "招商项目"));
                queryBuilder.mustNot(QueryBuilders.termQuery("project_type", "工信局项目"));
                if (simpleQuery.getKeywordMap() != null) {
                    Map<String, String> keywordMap = simpleQuery.getKeywordMap();
                    for (Map.Entry<String, String> keywordPair : keywordMap.entrySet()) {
                        if (StringUtils.isEmpty(keywordPair.getValue())) {
                            continue;
                        }
                        if ("undertaking_unit.name".equals(keywordPair.getKey())) {
                            queryBuilder.filter(QueryBuilders.wildcardQuery(keywordPair.getKey(), "*" + keywordPair.getValue() + "*"));
                        } else {
                            queryBuilder.filter(QueryBuilders.matchPhraseQuery(keywordPair.getKey(), keywordPair.getValue()));
                        }
                    }
                }
                setCategoryQuery(queryBuilder, "project_type", currentCategory, allCategories);
                break;
            case ACHIEVEMENT:
//                queryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, false);
                setCategoryQuery(queryBuilder, "type", currentCategory, allCategories);
                break;
            default:
                break;
        }
        return queryBuilder;
    }

    /**
     * 设置类别查询参数
     *
     * @param boolQueryBuilder
     * @param currentCategory
     */
    private void setCategoryQuery(BoolQueryBuilder boolQueryBuilder, String field, String currentCategory, List<String> allCategories) {
        if (StringUtils.isEmpty(currentCategory)) {
            return;
        }
        if ("其他".equals(currentCategory)) {
            allCategories.removeIf(c -> "其他".equals(c));
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery(field, allCategories));
            if (!"tag.name".equals(field)) {
                boolQueryBuilder.must(QueryBuilders.existsQuery(field));
            }
        } else {
            if ("本科".equals(currentCategory)) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery(field, Arrays.asList("本科", "大学本科", "大学", "学士")));
            } else {
                boolQueryBuilder.filter(QueryBuilders.termQuery(field, currentCategory));
            }
        }
    }

    /**
     * 定义聚合条件
     *
     * @param insightTypeEnum
     * @return
     */
    private Pair<String, List<String>> getAggregationCondition(InsightTypeEnum insightTypeEnum) {
        String aggregationField = null;
        List<String> fieldValues = new ArrayList<>();
        switch (insightTypeEnum) {
            case COMPANY:
            case COMPANY_IN:
                aggregationField = "tag.name";
                fieldValues.addAll(Arrays.asList(CompanyTagEnum.TECH_GIANT.getName(), CompanyTagEnum.HIGH_TECH.getName(), CompanyTagEnum.TECH_SMES.getName()));
                break;
            case COMPANY_TARGET:
                aggregationField = "province.name";
                break;
            case EXPERT:
            case EXPERT_IN:
            case EXPERT_OUT:
                aggregationField = "final_edu_degree";
                break;
            case FINANCING:
                aggregationField = "financing_round";
                fieldValues.addAll(Arrays.asList("种子轮", "天使轮", "A轮", "B轮", "C轮"));
                break;
            case FINANCING_STOCK:
                aggregationField = "financing_amount";
                break;
            case BOTTLENECK:
                aggregationField = "research_status";
//                fieldValues.addAll(Arrays.asList("省内有基础实现攻关突破", "尚无国产替代方案", "省外有基础实现攻关突破", "市内有基础实现攻关突破", "已实现国产化替代", "有可能抢占技术制高点"));
                break;
            case DEMAND:
                aggregationField = "source_type";
                break;
            case PROJECT:
                aggregationField = "project_type";
                break;
            case ACHIEVEMENT:
                aggregationField = "type";
                break;
            default:
                break;
        }
        return new ImmutablePair<>(aggregationField, fieldValues);
    }
}
