package com.quantchi.nanping.innovation.insight.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.model.bo.AreaRankBo;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.IndexQuery;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/6 9:38
 */
public interface IIndexFusionService extends IService<IndexFusion> {

    /**
     * 条件筛选指标
     *
     * @param listIndexIds
     * @param chainId
     * @param chainNodeId
     * @param regionId
     * @param isNode
     * @param isLatest     是否选取最新的数据
     * @return
     */
    List<IndexFusion> list(List<String> listIndexIds, String chainId, String chainNodeId, String regionId, boolean isNode, boolean isLatest);

    /**
     * 条件筛选指标
     *
     * @param chainId
     * @param indexId
     * @param regionIds
     * @return
     */
    List<IndexFusion> list(String chainId, String indexId, List<String> regionIds);

    /**
     * 获取区域排行信息
     * @param chainId
     * @return
     */
    List<AreaRankBo> getAreaRank(String chainId);

    /**
     * 获取指数变化趋势
     *
     * @return
     */
    Map<String, Object> getIndexFusionTrend(List<String> listIndexIds, String chainId, String chainNodeId, String regionId);

    /**
     * 条件查询融合指标
     *
     * @param indexId
     * @param chainId
     * @param nodeIds
     * @param regionId
     * @return
     */
    List<IndexFusion> listByIndexIdAndChainIdAndNodeIds(String indexId, String chainId, List<String> nodeIds, String regionId);

    /**
     * 查询综合指数
     *
     * @param chainId
     * @param nodeId
     * @param regionId
     * @return
     */
    IndexFusion getCompositeFusionIndex(String chainId, String nodeId, String regionId);

    /**
     * 查询单个指标
     *
     * @param indexId
     * @param chainId
     * @param nodeId
     * @param regionId
     * @param isNode
     * @return
     */
    IndexFusion getOne(String indexId, String chainId, String nodeId, String regionId, boolean isNode);

    Map<String, Object> fusionDetail(String chainId);
}
