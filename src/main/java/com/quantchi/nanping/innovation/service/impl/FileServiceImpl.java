package com.quantchi.nanping.innovation.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inspurcloud.oss.client.impl.OSSClientImpl;
import com.inspurcloud.oss.model.entity.OSSObject;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.config.FileConfig;
import com.quantchi.nanping.innovation.dao.mapper.FileInfoMapper;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.service.IFileService;
import com.quantchi.nanping.innovation.utils.SpringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/1 11:24
 */
@Service
public class FileServiceImpl extends ServiceImpl<FileInfoMapper, FileInfo> implements IFileService {

    /**
     * 使用oss的环境
     */
    private static final String PROFILE_PROD = "prod";
    private static final String PROFILE_PROD_SERVER = "prod-server";

    @Value("${self.address}")
    private String downloadAddress;

    @Autowired
    private FileConfig fileConfig;

    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${inspur.oss.bucketName}")
    private String bucketName;

    @Override
    public FileInfo upload(MultipartFile multipartFile) {
        // 获取文件的名称
        String originalFileName = multipartFile.getOriginalFilename();
        // 文件后缀 例如：.png
        String fileSuffix = originalFileName.substring(originalFileName.lastIndexOf("."));
        // uuid 生成文件名
        String uuid = String.valueOf(UUID.randomUUID());
        // 新的文件名，使用uuid生成文件名
        String fileNameAlias = uuid + fileSuffix;
        boolean isOss = PROFILE_PROD.equals(profile) || PROFILE_PROD_SERVER.equals(profile);
        try {
            if (isOss) {
                putOssObject(fileNameAlias, multipartFile.getInputStream());
            } else {
                // 根路径
                String basePath = fileConfig.getUploadDirectory();
                // 创建新的文件
                File fileExist = new File(basePath);
                // 文件夹不存在，则新建
                if (!fileExist.exists()) {
                    fileExist.mkdirs();
                }
                // 获取文件对象
                File file = new File(basePath, fileNameAlias);
                // 完成文件的上传
                multipartFile.transferTo(file);
            }
        } catch (Exception e) {
            throw new BusinessException("上传文件失败，异常：", e);
        }
        return saveFileInfo(uuid, originalFileName, fileNameAlias, isOss);
    }

    @Override
    public FileInfo upload(Workbook workbook, String fileName) {
        // 文件后缀 例如：.png
        String fileSuffix = fileName.substring(fileName.lastIndexOf("."));
        // uuid 生成文件名
        String uuid = String.valueOf(UUID.randomUUID());
        // 新的文件名，使用uuid生成文件名
        String fileNameAlias = uuid + fileSuffix;
        boolean isOss = PROFILE_PROD.equals(profile);
        try {
            if (isOss) {
                putOssObject(fileNameAlias, transferWorkbook2InputStream(workbook));
            } else {
                String basePath = ResourceUtils.getURL("classpath:").getPath() + fileConfig.getUploadDirectory();
                // 创建新的文件
                File fileExist = new File(basePath);
                // 文件夹不存在，则新建
                if (!fileExist.exists()) {
                    fileExist.mkdirs();
                }
                // 获取文件对象
                FileOutputStream fos = new FileOutputStream(basePath + File.separator + fileNameAlias);
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new BusinessException("上传文件失败，异常：", e);
        }
        return saveFileInfo(uuid, fileName, fileNameAlias, isOss);
    }

    private FileInfo saveFileInfo(String fileId, String fileName, String fileNameAlias, boolean isOss) {
        // 保存文件信息
        FileInfo fileInfo = new FileInfo();
        if (StpUtil.isLogin()) {
            fileInfo.setCreateUserId(StpUtil.getLoginIdAsString());
        }
        fileInfo.setFileId(fileId);
        fileInfo.setOriginalFileName(fileName);
        fileInfo.setFileNameAlias(fileNameAlias);
        fileInfo.setDownloadUrl(downloadAddress + "/api/file/download?fileId=" + fileId);
        if (!isOss) {
            fileInfo.setRelativeDownloadUrl(fileConfig.getUploadDirectory() + File.separator + fileNameAlias);
        }
        this.save(fileInfo);
        return fileInfo;
    }

    @Override
    public void download(String fileId, boolean inline, HttpServletResponse response) {
        FileInfo fileInfo = this.getById(fileId);
        if (fileInfo == null) {
            throw new BusinessException("文件已被删除，无法下载");
        }
        // 获取文件原始名称
        String fileName = fileInfo.getOriginalFileName();
        // 获取文件后缀名
        String fileSuffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        InputStream inputStream = null;
        try {
            response.setContentType("application/" + fileSuffix);
            response.addHeader("Content-Disposition", (inline ? "inline" : "attachment") + ";filename=" + URLEncoder.encode(fileName, "UTF-8"));
            if (StringUtils.isEmpty(fileInfo.getRelativeDownloadUrl())) {
                // 从oss下载
                OSSObject ossObject = getOssObject(fileInfo.getFileNameAlias());
                IOUtils.copy(ossObject.getObjectContent(), response.getOutputStream());
            } else {
                Path path = Paths.get(fileInfo.getRelativeDownloadUrl());
                log.error("下载路径" + path);
                if (Files.exists(path)) {
                    log.error("该文件路径：" + path);
                    Files.copy(path, response.getOutputStream());
                }
            }
        } catch (Exception e) {
            log.error("文件下载失败，异常：", e);
            throw new BusinessException("文件下载失败，异常：", e);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public boolean deleteByFileId(String fileId) {
        FileInfo fileInfo = this.getById(fileId);
        if (fileInfo == null) {
            return true;
        }
        try {
            if (StringUtils.isEmpty(fileInfo.getRelativeDownloadUrl())) {
                deleteOssObject(fileInfo.getFileNameAlias());
            } else {
                Path path = Paths.get(ResourceUtils.getURL("classpath:").getPath().substring(1), fileInfo.getRelativeDownloadUrl());
                if (Files.exists(path)) {
                    Files.delete(path);
                }
            }
        } catch (Exception e) {
            throw new BusinessException("文件删除失败，异常：", e);
        }
        this.removeById(fileId);
        return true;
    }

    @Override
    public List<FileInfo> listFileByRelatedIdAndType(String relatedId, String relatedType) {
        return this.list(Wrappers.<FileInfo>lambdaQuery()
                .eq(StringUtils.isNotEmpty(relatedId), FileInfo::getRelatedId, relatedId)
                .eq(StringUtils.isNotEmpty(relatedType), FileInfo::getRelatedType, relatedType));
    }

    @Override
    public Map<String, List<FileInfo>> listFileByRelatedIdsAndType(List<String> relatedIds, String relatedType) {
        if (CollectionUtils.isEmpty(relatedIds) || StringUtils.isEmpty(relatedType)) {
            return new HashMap<>(0);
        }
        List<FileInfo> fileInfoList = this.list(Wrappers.<FileInfo>lambdaQuery()
                .in(FileInfo::getRelatedId, relatedIds)
                .eq(FileInfo::getRelatedType, relatedType)
                .orderByAsc(FileInfo::getCreateTime));
        return fileInfoList.stream().collect(Collectors.groupingBy(FileInfo::getRelatedId));
    }

    @Override
    public boolean updateRelation(String relatedId, String relatedType, List<String> fileIds) {
        return this.update(Wrappers.<FileInfo>lambdaUpdate()
                .set(FileInfo::getRelatedId, relatedId)
                .set(FileInfo::getRelatedType, relatedType)
                .in(FileInfo::getFileId, fileIds));
    }

    @Override
    public boolean deleteByRelatedIds(List<String> relatedIds) {
        if (CollectionUtils.isEmpty(relatedIds)) {
            return true;
        }
        List<FileInfo> fileInfoList = this.list(Wrappers.<FileInfo>lambdaQuery()
                .in(FileInfo::getRelatedId, relatedIds));
        for (FileInfo fileInfo : fileInfoList) {
            deleteByFileId(fileInfo.getFileId());
        }
        return true;
    }

    /**
     * 转换workbook为inputstream
     *
     * @param workbook
     * @return
     */
    private InputStream transferWorkbook2InputStream(Workbook workbook) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            workbook.write(bos);
        } catch (IOException e) {
        } finally {
            try {
                bos.close();
            } catch (IOException e) {
            }
        }
        byte[] bArray = bos.toByteArray();
        return new ByteArrayInputStream(bArray);
    }

    private void putOssObject(String fileName, InputStream inputStream) {
        OSSClientImpl ossClient = SpringUtils.getBean(OSSClientImpl.class);
        ossClient.putObject(bucketName, fileName, inputStream);
    }

    private void deleteOssObject(String fileName) {
        OSSClientImpl ossClient = SpringUtils.getBean(OSSClientImpl.class);
        ossClient.deleteObject(bucketName, fileName);
    }

    private OSSObject getOssObject(String fileName) {
        OSSClientImpl ossClient = SpringUtils.getBean(OSSClientImpl.class);
        return ossClient.getObject(bucketName, fileName);
    }
}
