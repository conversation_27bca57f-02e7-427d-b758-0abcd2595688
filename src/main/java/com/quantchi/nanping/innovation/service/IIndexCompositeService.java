package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.IndexComposite;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/2/24 14:42
 */
public interface IIndexCompositeService extends IService<IndexComposite> {

    /**
     * 根据父指标id罗列子指标
     *
     * @param pid
     * @return
     */
    List<IndexComposite> listByPid(String pid);

    /**
     * 根据父指标id罗列子指标
     * @param pids
     * @return
     */
    List<IndexComposite> listByPids(Set<String> pids);
}
