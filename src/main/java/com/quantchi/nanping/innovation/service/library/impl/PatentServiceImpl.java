package com.quantchi.nanping.innovation.service.library.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import com.quantchi.nanping.innovation.company.model.bo.PatentVectorMatchBO;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.knowledge.center.utils.ElasticsearchBuilder;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.TwoLevelIndex;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.IDivisionService;
import com.quantchi.nanping.innovation.service.IWordVectorService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.utils.DateUtils;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.RequestContext;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.model.SearchSourceQuery;
import com.quantchi.tianying.utils.AggregationBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.range.ParsedRange;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.range.RangeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/1/10 3:57 下午
 * @description
 */
@Slf4j
@Service
public class PatentServiceImpl implements PatentService {

    /**
     * 高被引次数标准
     */
    private final Integer HIGH_REFERED_STANDARD = 1;

    private final String[] PATENT_VECTOR_EXCLUDES = new String[]{"ti_vector", "ab_vector"};

    @Resource
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private IEs8Service es8Service;

    @Autowired
    private IWordVectorService wordVectorService;

    @Autowired
    private IDivisionService divisionService;

    @Override
    public Map<String, Object> getPatentGrowthTend(String chainId, String areaId) {
        final BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, CommonConstant.DIVISION_NANPING.getId(), areaId, false);
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("chain.id", ""));
        boolQueryBuilder.must(QueryBuilders.existsQuery("chain.id"));
        // 年份分组
        final String yearAggregationKey = TERMS_BUCKET_PREFIX + "publish_year";
        RangeAggregationBuilder yearAggregationBuilder = AggregationBuilders.range(yearAggregationKey).field("publish_year");
        List<String> years = DateUtils.getRecentYears(6);
        for (String year : years) {
            yearAggregationBuilder.addUnboundedTo(Integer.parseInt(year) + 1);
        }
        final AggregationPageResult result = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PATENT.getEsIndex(), boolQueryBuilder, yearAggregationBuilder);
        ParsedRange ranges = (ParsedRange) result.getSearchResponse().getAggregations().asMap().get(yearAggregationKey);
        Map<String, Long> yearCountMap = new HashMap<>(years.size());
        for (Range.Bucket bucket : ranges.getBuckets()) {
            yearCountMap.put(String.valueOf(Math.round(Double.valueOf(bucket.getToAsString())) - 1), bucket.getDocCount());
        }
        Map<String, Object> resultMap = new LinkedHashMap<>(years.size());
        for (int i = years.size() - 2; i >= 0; i--) {
            String year = years.get(i), lastYear = years.get(i + 1);
            Map<String, Long> group = new HashMap();
            group.put("existEventNum", yearCountMap.containsKey(year) ? yearCountMap.get(year) : 0L);
            group.put("newlyEventNum", (yearCountMap.containsKey(year) ? yearCountMap.get(year) : 0L)
                    - (yearCountMap.containsKey(lastYear) ? yearCountMap.get(lastYear) : 0L));
            resultMap.put(year, group);
        }
        return resultMap;
    }

    @Override
    public Map<String, Long> getPatentDomain(String industry) {
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 所属产业
        if (StringUtils.isNotBlank(industry)) {
            String chainId = industryChainService.getChainIdByName(industry);
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.id", chainId));
        }
        boolQueryBuilder.filter(QueryBuilders.termsQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("chain_node.name", ""));
        //专利领域
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "chain_node.name").field("chain_node.name");
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PATENT.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        return parsePatentStatisticAggregationFromResponse(pageResult.getSearchResponse(), TERMS_BUCKET_PREFIX + "chain_node.name");
    }


    public static Map<String, Long> parsePatentStatisticAggregationFromResponse(final SearchResponse searchResponse, final String aggregationKey) {
        Map<String, Long> talentMap = new LinkedHashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(aggregationKey);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            talentMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return talentMap;
    }

    @Override
    public Map<String, Object> page(Integer pageNum, Integer pageSize) {
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.from((pageNum - 1) * pageSize);
        searchSourceBuilder.size(pageSize);
        searchSourceBuilder.sort("public_date", SortOrder.DESC);
        searchSourceBuilder.fetchSource(new String[]{"id", "applicants.name", "public_date"}, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.PATENT.getEsIndex());
        return ElasticsearchBuilder.buildPageResult(searchResponse);

    }

    @Override
    public List<TwoLevelIndex> getPatentPublishTrendRecentYears(String value, String field) {
        final int pastYears = 6;
        final String pastFiveYearStr = DateUtil.beginOfYear(
                        DateUtil.offset(new Date(), DateField.YEAR, -pastYears + 1))
                .toString("yyyy-MM-dd");
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.matchPhraseQuery(field, value));
        boolQuery.filter(QueryBuilders.rangeQuery("public_date").gte(pastFiveYearStr));
        final DateHistogramAggregationBuilder aggregation = AggregationBuilderUtil.buildYearAggregation("public_date");
        aggregation.order(BucketOrder.key(true));
        aggregation.subAggregation(AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "type").field("patent_type"));
        AggregationPageResult aggregationPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PATENT.getEsIndex(), boolQuery, aggregation);
        ParsedDateHistogram dateHistogram = (ParsedDateHistogram) aggregationPageResult.getSearchResponse().getAggregations().getAsMap().get(TERMS_BUCKET_PREFIX + "public_date");
        List<TwoLevelIndex> indexList = new ArrayList<>(pastYears);
        for (Histogram.Bucket bucket : dateHistogram.getBuckets()) {
            final ParsedStringTerms subTerms = (ParsedStringTerms) bucket.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "type");
            TwoLevelIndex index = new TwoLevelIndex(bucket.getKeyAsString());
            for (Terms.Bucket subBucket : subTerms.getBuckets()) {
                index.getChildList().add(new CommonIndexBO(subBucket.getKeyAsString(), subBucket.getDocCount(), null));
            }
            indexList.add(index);
        }
        return indexList;
    }

    @Override
    public Map<String, Long> getPatentIsOrNotValidRatio(String value, String field) {
        final Map<String, Long> result = new HashMap(2);
        result.put("有效", 0L);
        result.put("失效", 0L);
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery(field, value));
        log.debug("打印boolQuery:{}", JSON.toJSONString(boolQuery));
        final Map<String, Long> countMap = elasticsearchHelper
                .getTermBucketsAggregation(EsIndexEnum.PATENT.getEsIndex(), "is_valid", boolQuery, 1000);
        for (final Map.Entry<String, Long> entry : countMap.entrySet()) {
            if ("1".equals(entry.getKey())) {
                result.put("有效", entry.getValue());
            }
            if ("0".equals(entry.getKey())) {
                result.put("失效", entry.getValue());
            }
        }
        return result;
    }

    @Override
    public List<CommonIndexBO> getWordCloud(String chainId, String value, String field, String cityId, String areaId, boolean auth, boolean isGlobal) {
        BoolQueryBuilder queryBuilders = EsAlterUtil.buildAuthQuery(chainId, null, cityId, areaId, auth);
        if (StringUtils.isNotEmpty(value)) {
            queryBuilders.filter(QueryBuilders.matchPhraseQuery(field, value));
        }
        queryBuilders.mustNot(QueryBuilders.termQuery("chain.id", ""));
        queryBuilders.must(QueryBuilders.existsQuery("chain.id"));
        final String termKey = TERMS_BUCKET_PREFIX + "tag";
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(termKey)
                .field("product_node.name").order(BucketOrder.count(false)).size(1000);
        AggregationPageResult aggregationResult = elasticsearchHelper.getBucketsAggregationPageResult(isGlobal ? EsIndexEnum.GLOBAL_PATENT.getEsIndex() : EsIndexEnum.PATENT.getEsIndex(),
                queryBuilders, aggregationBuilder);
        final ParsedStringTerms stringTerms = (ParsedStringTerms) aggregationResult.getSearchResponse().getAggregations().asMap().get(termKey);
        List<CommonIndexBO> wordRank = new ArrayList<>();
        stringTerms.getBuckets().forEach(bucket -> {
            wordRank.add(new CommonIndexBO(bucket.getKeyAsString(), Math.toIntExact(bucket.getDocCount())));
        });
        return wordRank;
    }

    @Override
    public Map<String, Long> getPatentApplyTrendRecentYears(String chainId, String chainNodeId, String cityId, String areaId, boolean auth, int recentYears) {
        LocalDate pastYear = new LocalDate();
        pastYear = pastYear.minusYears(recentYears);
        final String pastYearStr = pastYear.getYear() + "-01-01";
        final BoolQueryBuilder boolQuery = EsAlterUtil.buildAuthQuery(chainId, chainNodeId, cityId, areaId, auth);
        boolQuery.filter(QueryBuilders.rangeQuery("public_date").gte(pastYearStr));
        return elasticsearchHelper.getTimeOfYearAggregations(EsIndexEnum.PATENT.getEsIndex(), "public_date", boolQuery);
    }

    @Override
    public List<IndustryChainNode> getPatentSunriseChart(String chainId, boolean auth) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, auth);
        // 罗列所有三级和四级节点
        List<IndustryChainNode> nodeList = industryChainService.getNodesByChainIdAndLevels(chainId, 2, 3);
        Map<String, IndustryChainNode> nodeMap = nodeList.stream().collect(Collectors.toMap(IndustryChainNode::getId, Function.identity()));
        boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", nodeMap.keySet()));
        final String termKey = TERMS_BUCKET_PREFIX + "chain_node.id";
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(termKey).field("chain_node.id")
                .size(nodeMap.size())
                .includeExclude(new IncludeExclude(nodeMap.keySet().toArray(new String[nodeMap.size()]), null));
        AggregationPageResult aggregationResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.GLOBAL_PATENT.getEsIndex(),
                boolQueryBuilder, aggregationBuilder);
        final ParsedStringTerms stringTerms = (ParsedStringTerms) aggregationResult.getSearchResponse().getAggregations().asMap().get(termKey);
        stringTerms.getBuckets().forEach(bucket -> {
            nodeMap.get(bucket.getKeyAsString()).getProperties().put("docCount", bucket.getDocCount());
        });
        return industryChainService.buildTrees(nodeList);
    }

    @Override
    public List<Map<String, Object>> getTopReferencedPatents(String chainId, int topNum, boolean auth) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, auth);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(topNum);
        searchSourceBuilder.sort("ct_times", SortOrder.DESC);
        searchSourceBuilder.query(boolQueryBuilder);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.GLOBAL_PATENT.getEsIndex());
        SearchHits hits = searchResponse.getHits();
        List<Map<String, Object>> topReferencedPatents = new ArrayList<>();
        for (SearchHit hit : hits) {
            topReferencedPatents.add(hit.getSourceAsMap());
        }
        return topReferencedPatents;
    }

    @Override
    public Long countPatentNumByChainIdAndRegionId(String chainId, String nodeId, String cityId, String areaId) {
        final BoolQueryBuilder boolQueryBuilder = buildAuthQuery(chainId, nodeId, cityId, areaId, false);
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("chain.id", ""));
        boolQueryBuilder.must(QueryBuilders.existsQuery("chain.id"));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.fetchSource(null, new String[]{"name_vector"});
        return es8Service.countRequest(searchSourceBuilder, EsIndexEnum.PATENT_VECTOR.getEsIndex());
    }

    private BoolQueryBuilder buildAuthQuery(String chainId, String chainNodeId, String cityId, String areaId, boolean auth) {
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(chainId)) {
            boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
        }
        if (StringUtils.isNotBlank(chainNodeId)) {
            boolQuery.filter(QueryBuilders.termQuery("chain_node.id", chainNodeId));
        }
        final String targetCityId = auth ? RequestContext.getCityId() : cityId,
                targetAreaId = auth ? RequestContext.getAreaId() : areaId;
        if (StringUtils.isNotBlank(targetCityId)) {
            boolQuery.filter(QueryBuilders.termQuery("city", divisionService.getById(targetCityId).getName()));
        }
        if (StringUtils.isNotBlank(targetAreaId)) {
            boolQuery.filter(QueryBuilders.termQuery("area", divisionService.getById(targetAreaId).getName()));
        }
        return boolQuery;
    }

    @Override
    public Map<String, Object> getPatentReferenceTend(String chainId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, true);
        // 年份分组
        final String yearAggregationKey = TERMS_BUCKET_PREFIX + "publish_year";
        RangeAggregationBuilder yearAggregationBuilder = AggregationBuilders.range(yearAggregationKey).field("publish_year");
        List<String> years = DateUtils.getRecentYears(5);
        for (String year : years) {
            yearAggregationBuilder.addUnboundedTo(Integer.parseInt(year) + 1);
        }

        // 引用次数分组
        final String referAggregationKey = TERMS_BUCKET_PREFIX + "ct_times";
        RangeAggregationBuilder referAggregationBuilder = AggregationBuilders.range(referAggregationKey).field("ct_times")
                .addUnboundedFrom(HIGH_REFERED_STANDARD);
        yearAggregationBuilder.subAggregation(referAggregationBuilder);
        final AggregationPageResult result = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PATENT.getEsIndex(), boolQueryBuilder, yearAggregationBuilder);
        ParsedRange ranges = (ParsedRange) result.getSearchResponse().getAggregations().asMap().get(yearAggregationKey);
        Map<String, Object> resultMap = new LinkedHashMap<>(years.size());
        for (Range.Bucket bucket : ranges.getBuckets()) {
            Map<String, Long> yearCount = new HashMap<>(2);
            yearCount.put("existNum", bucket.getDocCount());
            ParsedRange subRanges = (ParsedRange) bucket.getAggregations().asMap().get(referAggregationKey);
            yearCount.put("highReferNum", subRanges.getBuckets().get(0).getDocCount());
            resultMap.put(String.valueOf(Math.round(Double.valueOf(bucket.getToAsString())) - 1), yearCount);
        }
        return resultMap;
    }

    @Override
    public Long countPatentTypeNumByChainIdAndRegion(String chainId, String cityId, String areaId) {
        final BoolQueryBuilder boolQuery = buildAuthQuery(chainId, null, cityId, areaId, false);
        boolQuery.filter(QueryBuilders.termQuery("patent_type", "发明专利"));
        return es8Service.countRequest(EsAlterUtil.buildSearchSource(boolQuery, 1, 1, null, new String[]{"name_vector"}, null), EsIndexEnum.PATENT_VECTOR.getEsIndex());
    }

    @Override
    public List<Map<String, Object>> getByIds(String[] ids, List<String> allKeywords, String[] includes, String[] excludes) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.idsQuery().addIds(ids));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(ids.length);
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.fetchSource(includes, excludes);
        SearchResponse searchResponse = es8Service.request(searchSourceBuilder, EsIndexEnum.PATENT_VECTOR.getEsIndex(), null, null);
        SearchHits hits = searchResponse.getHits();
//        List<Map<String, Object>> patents = new ArrayList<>();
//        for (SearchHit hit : hits) {
//            patents.add(hit.getSourceAsMap());
//        }
        final EsPageResult libraryList = com.quantchi.tianying.utils.ElasticsearchBuilder.buildPageResult(searchResponse);
        // 手动高亮处理
        List<Map<String, Object>> list = libraryList.getList();
        highLight(list, searchResponse, allKeywords);
        return list;
    }

    @Override
    public List<Map<String, Object>> vectorSearch(PatentVectorMatchBO matchBO, int pageNo, int pageSize) {
        BoolQueryBuilder preQueryBuilder = QueryBuilders.boolQuery();
        preQueryBuilder.filter(QueryBuilders.termsQuery("patent_type", "发明专利", "实用新型", "发明申请"));
        preQueryBuilder.filter(QueryBuilders.termsQuery("status", "有效", "授权", "实质审查的生效", "公布","审中"));
        preQueryBuilder.filter(QueryBuilders.existsQuery("chain.id"));
        if (CollectionUtils.isNotEmpty(matchBO.getApplicants())) {
            preQueryBuilder.should(QueryBuilders.termsQuery("applicants.name", matchBO.getApplicants()));
            preQueryBuilder.should(QueryBuilders.termsQuery("inventors.name", matchBO.getApplicants()));
            preQueryBuilder.minimumShouldMatch(1);
        }
        if (StringUtils.isNotEmpty(matchBO.getChainId())){
            preQueryBuilder.filter(QueryBuilders.termQuery("chain.id", matchBO.getChainId()));
        }
        String sort = EsIndexEnum.PATENT_VECTOR.getSort();
        String vectorColumn = null, keywords = null;
        if (CollectionUtils.isNotEmpty(matchBO.getKeywords())) {
            sort = null;
            vectorColumn = "ti_vector";
            keywords = StringUtils.join(matchBO.getKeywords());
        }
        SearchSourceBuilder sourceBuilder = null;
        if (StringUtils.isEmpty(vectorColumn)) {
            sourceBuilder = EsAlterUtil.buildSearchSource(preQueryBuilder, pageNo, pageSize,
                    new String[]{"id", "public_code", "name", "title", "abstract", "abstract_cn", "patent_type", "status", "domain", "applicants", "inventors", "apply_date", "public_date"},
                    null, sort);
        } else {
            sourceBuilder = new SearchSourceBuilder();
            preQueryBuilder.filter(QueryBuilders.existsQuery(vectorColumn));
            sourceBuilder.query(preQueryBuilder);
        }
        final SearchResponse searchResponse = es8Service.request(sourceBuilder, EsIndexEnum.PATENT_VECTOR.getEsIndex(), vectorColumn, keywords);
        final EsPageResult libraryList = com.quantchi.tianying.utils.ElasticsearchBuilder.buildPageResult(searchResponse);
        // 手动高亮处理
        List<Map<String, Object>> list = libraryList.getList();
        highLight(list, searchResponse, matchBO.getHighlightKeywords());
        return list;
    }

    private void highLight(List<Map<String, Object>> list, SearchResponse searchResponse, List<String> highLightKeywords) {
        // 手动放入最大分数，方便前端判断文案
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.get(0).put("score", searchResponse.getHits().getMaxScore());
        List<Term> terms = CollectionUtils.isEmpty(highLightKeywords) ? new ArrayList<>(0) :
                HanLP.segment(StringUtils.join(highLightKeywords, ""));
        if (CollectionUtils.isNotEmpty(terms)) {
            for (Map<String, Object> patent : list) {
                if (patent.containsKey("abstract_cn") && StringUtils.isNotEmpty((String)patent.get("abstract_cn"))) {
                    patent.put("abstract",
                            ElasticsearchBuilder.replacementInfo((String) patent.get("abstract_cn"), terms, "<span style=\"color:rgb(30, 132, 246)\">", "</span>"));
                }else{
                    patent.put("abstract",
                            ElasticsearchBuilder.replacementInfo((String) patent.get("abstract"), terms, "<span style=\"color:rgb(30, 132, 246)\">", "</span>"));
                }
                if (patent.containsKey("title")) {
                    patent.put("title",
                            ElasticsearchBuilder.replacementInfo((String) patent.get("title"), terms, "<span style=\"color:rgb(30, 132, 246)\">", "</span>"));
                }
                if (patent.containsKey("name")) {
                    patent.put("name",
                            ElasticsearchBuilder.replacementInfo((String) patent.get("name"), terms, "<span style=\"color:rgb(30, 132, 246)\">", "</span>"));
                }
            }
        } else {
            for (Map<String, Object> patent : list) {
                if (patent.containsKey("abstract_cn")) {
                    patent.put("abstract", patent.get("abstract_cn"));
                }
            }
        }
    }

    @Override
    public Map<String, Object> pageByCompanyId(String companyId, Integer pageNum, Integer pageSize) {
        QueryBuilder patentQuery = QueryBuilders.termQuery("applicants.id", companyId);
        SearchSourceBuilder searchBuilder = EsAlterUtil.buildSearchSource(patentQuery, pageNum, pageSize, new String[]{"id","title", "public_code", "public_date", "patent_type"}, PATENT_VECTOR_EXCLUDES,
                EsIndexEnum.PATENT_VECTOR.getSort());
        SearchResponse searchResponse = es8Service.request(searchBuilder, EsIndexEnum.PATENT_VECTOR.getEsIndex(), null, null);
        return ElasticsearchBuilder.buildPageResult(searchResponse);
    }

    public static List<Map<String, Object>> parseInventorAggregationFromResponse(
            final SearchResponse searchResponse) {
        final List<Map<String, Object>> resultMap = new ArrayList<>();
        final ParsedStringTerms stringTerms = (ParsedStringTerms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "inventors");
        List<? extends Terms.Bucket> buckets = stringTerms.getBuckets();
        buckets.forEach(bucket -> {
            final ParsedStringTerms subTerms = (ParsedStringTerms) bucket.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "chain_name");
            Set<String> domainSet = new HashSet<>();
            List<? extends Terms.Bucket> subBuckets = subTerms.getBuckets();
            subBuckets.forEach(sub -> {
                domainSet.add(sub.getKeyAsString());
            });
            Map<String, Object> map = new HashMap<>();
            map.put("name", bucket.getKeyAsString());
            map.put("patentNum", bucket.getDocCount());
            map.put("domain", domainSet);
            resultMap.add(map);
        });
        return resultMap.stream().sorted(
                        Comparator.comparing((Map<String, Object> h) -> ((Long) h.get("patentNum"))).reversed())
                .collect(Collectors.toList());
    }

    private Integer countPatentWithRange(final String value, final String field, final String beginDate, final String endDate) {
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.matchPhraseQuery(field, value));
        boolQuery.filter(QueryBuilders.rangeQuery("public_date").gte(beginDate).lt(endDate));
        final SearchSourceQuery sourceQuery = new SearchSourceQuery();
        sourceQuery.setIncludes(new String[]{"inventors"});
        sourceQuery.setQueryBuilder(boolQuery);
        final SearchSourceBuilder searchSource = com.quantchi.tianying.utils.ElasticsearchBuilder.buildSearchSource(sourceQuery);
        // 获取结果
        final SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSource, EsIndexEnum.PATENT.getEsIndex());
        final EsPageResult resultMap = com.quantchi.tianying.utils.ElasticsearchBuilder.buildPageResult(searchResponse);
        final List<Map<String, Object>> list = resultMap.getList();
        Set<String> inventorNameSet = new HashSet<>();
        list.forEach(map -> {
            final Object inventors = map.get("inventors");
            if (inventors == null) {
                return;
            }
            for (Map<String, Object> inventor : (List<Map<String, Object>>) inventors) {
                inventorNameSet.add((String) inventor.get("name"));
            }
        });
        return inventorNameSet.size();
    }
}
