package com.quantchi.nanping.innovation.service;

import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;

import java.util.List;
import java.util.Map;

/**
 * 科技招商服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-18
 */
public interface ITechInvestAttractService {

    /**
     * 创新链诊断
     *
     * @param chainId 产业链ID
     * @return 创新链诊断数据
     */
    Map<String, Object> analyzeInnovationChain(String chainId);

    /**
     * 关键核心技术分类统计
     *
     * @param chainId 产业链ID
     * @return 技术分类统计数据
     */
    List<CommonIndexBO> getRiskMap(String chainId);
}
