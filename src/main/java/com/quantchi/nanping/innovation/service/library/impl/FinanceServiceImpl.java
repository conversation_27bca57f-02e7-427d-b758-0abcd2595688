package com.quantchi.nanping.innovation.service.library.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.nanping.innovation.finance.dao.FinanceLoanDetailMapper;
import com.quantchi.nanping.innovation.finance.dao.FinanceLoanMapper;
import com.quantchi.nanping.innovation.finance.dao.FinanceNeedMapper;
import com.quantchi.nanping.innovation.finance.model.*;
import com.quantchi.nanping.innovation.finance.service.IFinanceLocalOrgService;
import com.quantchi.nanping.innovation.finance.service.IFinanceProductService;
import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.IndustryChain;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.*;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.CompanyTagEnum;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.service.IThirdFinanceService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.impl.CommonUseExtendsService;
import com.quantchi.nanping.innovation.service.library.FinanceService;
import com.quantchi.nanping.innovation.service.library.ProjectService;
import com.quantchi.nanping.innovation.utils.BigDecimalUtil;
import com.quantchi.nanping.innovation.utils.DateUtils;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.RequestContext;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.model.MultidimensionalQuery;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.range.*;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2022/12/27 10:24 上午
 * @description
 */
@Slf4j
@Service
public class FinanceServiceImpl implements FinanceService {

    /**
     * 投融资
     */
    private static final String TYPE_FINANCE = "financing";
    /**
     * 贷款
     */
    private static final String TYPE_SUBSIDY = "subsidy";

    @Resource
    private CommonUseExtendsService commonUseExtendsService;

    @Resource
    private ElasticsearchHelper elasticsearchHelper;

    @Resource
    private IndustryChainService chainService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private FinanceLoanMapper loanMapper;

    @Autowired
    private FinanceLoanDetailMapper loanDetailMapper;

    @Autowired
    private IFinanceProductService productService;

    @Autowired
    private IFinanceLocalOrgService localOrgService;

    @Autowired
    private FinanceNeedMapper needMapper;

    @Autowired
    private IIndexFusionService fusionService;

    @Autowired
    private IThirdFinanceService thirdFinanceService;

    @Override
    public EsPageResult getEventList(FinanceEventBo financeEventBo) {
        final MultidimensionalQuery query = new MultidimensionalQuery();
        final String esIndex = EsIndexEnum.FINANCING.getEsIndex();
        query.setIndex(esIndex);
        final Map<String, List<String>> termQueries = new HashMap<>(1);
        if (StringUtils.isNotBlank(financeEventBo.getCityId())) {//市内
            termQueries.put("financing_company.city_code", Collections.singletonList(financeEventBo.getCityId()));
            if (StringUtils.isNotBlank(financeEventBo.getAreaId())) {
                termQueries.put("financing_company.area_code", Collections.singletonList(financeEventBo.getAreaId()));
            }
        } else if (StringUtils.isNotBlank(financeEventBo.getProvinceId())) {//省内
            termQueries.put("financing_company.province_code", Collections.singletonList(financeEventBo.getProvinceId()));
        }
        //公司名称（模糊）
        if (StringUtils.isNotBlank(financeEventBo.getCompanyName())) {
            termQueries.put("financing_company.name", Collections.singletonList(financeEventBo.getCompanyName()));
        }
        //投资人（模糊）
        if (StringUtils.isNotBlank(financeEventBo.getInvestor())) {
            termQueries.put("investment_agency", Collections.singletonList(financeEventBo.getInvestor()));
        }
        //投资时间
        if (StringUtils.isNotBlank(financeEventBo.getFinanceDate())) {
            termQueries.put("financing_time", Collections.singletonList(financeEventBo.getFinanceDate()));
        }
        // 所属产业
        if (StringUtils.isNotBlank(financeEventBo.getChainId())) {
            termQueries.put("chain.id", Collections.singletonList(financeEventBo.getChainId()));
        }

        query.setTermQueries(termQueries);
        query.setRequiredList(Arrays.asList("id", "financing_company.name", "chain.name", "financing_round", "financing_scale", "financing_amount_cal", "investment_agency", "financing_time"));
        query.setPageNum(financeEventBo.getPageNum());
        query.setPageSize(financeEventBo.getPageSize());
        query.setSort(EsIndexEnum.FINANCING.getSort());
        final EsPageResult libraryList = commonUseExtendsService.getLibraryList(query);
        libraryList.setPageSize(financeEventBo.getPageSize());
        // 列表为空时返回null，便于前端判断
        if (CollUtil.isEmpty(libraryList.getList())) {
            return null;
        }
        for (Map<String, Object> detail : libraryList.getList()) {
            String fieldName = "financing_amount_cal";
            if (detail.containsKey(fieldName) && detail.get(fieldName) != null) {
                BigDecimal actualAmount = new BigDecimal((Double) detail.get(fieldName)).divide(new BigDecimal(*********), 2, RoundingMode.HALF_UP);
                detail.put("financing_scale", actualAmount.stripTrailingZeros() + "亿元人民币");
            } else {
                detail.put(fieldName, "未披露");
            }
            Map<String, Object> financeCompany = (Map<String, Object>) detail.get("financing_company");
            String companyName = (String) financeCompany.get("name");
            financeCompany.put("name", desensitizeCompanyName(companyName));
        }
        return libraryList;
    }

    @Override
    public EsPageResult getEventListTimeLimit(FinanceEventBo financeEventBo) {
        final MultidimensionalQuery query = new MultidimensionalQuery();
        final String esIndex = EsIndexEnum.FINANCING.getEsIndex();
        query.setIndex(esIndex);
        final Map<String, List<String>> termQueries = new HashMap<>(1);
        if (StringUtils.isNotBlank(financeEventBo.getCityId())) {//市内
            termQueries.put("financing_company.city_code", Collections.singletonList(financeEventBo.getCityId()));
            if (StringUtils.isNotBlank(financeEventBo.getAreaId())) {
                termQueries.put("financing_company.area_code", Collections.singletonList(financeEventBo.getAreaId()));
            }
        } else if (StringUtils.isNotBlank(financeEventBo.getProvinceId())) {//省内
            termQueries.put("financing_company.province_code", Collections.singletonList(financeEventBo.getProvinceId()));
        }
        //公司名称（模糊）
        if (StringUtils.isNotBlank(financeEventBo.getCompanyName())) {
            termQueries.put("financing_company.name", Collections.singletonList(financeEventBo.getCompanyName()));
        }
        //投资人（模糊）
        if (StringUtils.isNotBlank(financeEventBo.getInvestor())) {
            termQueries.put("investment_agency", Collections.singletonList(financeEventBo.getInvestor()));
        }
        //投资时间
        if (StringUtils.isNotBlank(financeEventBo.getFinanceDate())) {
            termQueries.put("financing_time", Collections.singletonList(financeEventBo.getFinanceDate()));
        }
        // 所属产业
        if (StringUtils.isNotBlank(financeEventBo.getChainId())) {
            termQueries.put("chain.id", Collections.singletonList(financeEventBo.getChainId()));
        }

        query.setTermQueries(termQueries);
        query.setRequiredList(Arrays.asList("id", "financing_company.name", "chain.name", "financing_round", "financing_scale", "financing_amount_cal", "investment_agency", "financing_time"));
        query.setPageNum(financeEventBo.getPageNum());
        query.setPageSize(financeEventBo.getPageSize());
        query.setSort(EsIndexEnum.FINANCING.getSort());
        final EsPageResult libraryList = commonUseExtendsService.getLibraryRangeList(query, "financing_time", "2023-01-01", null);
        libraryList.setPageSize(financeEventBo.getPageSize());
        // 列表为空时返回null，便于前端判断
        if (CollUtil.isEmpty(libraryList.getList())) {
            return null;
        }
        for (Map<String, Object> detail : libraryList.getList()) {
            String fieldName = "financing_amount_cal";
            if (detail.containsKey(fieldName) && detail.get(fieldName) != null) {
                BigDecimal actualAmount = new BigDecimal((Double) detail.get(fieldName)).divide(new BigDecimal(*********), 2, RoundingMode.HALF_UP);
                detail.put("financing_scale", actualAmount.stripTrailingZeros() + "亿元人民币");
            } else {
                detail.put(fieldName, "未披露");
            }
            Map<String, Object> financeCompany = (Map<String, Object>) detail.get("financing_company");
            String companyName = (String) financeCompany.get("name");
            financeCompany.put("name", desensitizeCompanyName(companyName));
        }
        return libraryList;
    }

    @Override
    public List<FinanceCountBo> getInvestmentFund(String chainId) {
        final BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.boolQuery().mustNot(QueryBuilders.termsQuery("investment_agency", "")));
        if (StringUtils.isNotEmpty(chainId)) {
            queryBuilder.filter(QueryBuilders.termsQuery("chain.id", chainId));
        }
        //queryBuilder.filter(QueryBuilders.rangeQuery("financing_time"));

        // 统计投资金额
        String agencyAggKey = TERMS_BUCKET_PREFIX + "investors", provinceAggKey = TERMS_BUCKET_PREFIX + "province",
                amountAggKey = TERMS_BUCKET_PREFIX + "totalAmount";
        TermsAggregationBuilder agencyAggregation = AggregationBuilders.terms(agencyAggKey).field("investment_agency").size(10000);
        TermsAggregationBuilder provinceAggregation = AggregationBuilders.terms(provinceAggKey).field("financing_company.province_code").size(50);
        SumAggregationBuilder amountAggregation = AggregationBuilders.sum(amountAggKey).field("financing_amount_cal");
        agencyAggregation.subAggregation(provinceAggregation.subAggregation(amountAggregation));
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), queryBuilder, agencyAggregation);
        Terms terms = (Terms) pageResult.getSearchResponse().getAggregations().asMap().get(agencyAggKey);
        List<FinanceCountBo> financeCountBoList = new ArrayList<>();
        for (Terms.Bucket bucket : terms.getBuckets()) {
            if ("未披露".equals(bucket.getKeyAsString())) {
                continue;
            }
            FinanceCountBo countBo = new FinanceCountBo();
            Terms subTerms = (Terms) bucket.getAggregations().asMap().get(provinceAggKey);
            BigDecimal countryCount = BigDecimal.ZERO;
            Long countryStrokeCount = 0L;
            for (Terms.Bucket subBucket : subTerms.getBuckets()) {
                ParsedSum inventorNum = (ParsedSum) subBucket.getAggregations().asMap().get(amountAggKey);
                if (CommonConstant.DIVISION_FUJIAN.getId().equals(subBucket.getKeyAsString())) {
                    countBo.setTotalAmountInProvince(BigDecimal.valueOf(inventorNum.getValue()).divide(new BigDecimal(*********), 2, RoundingMode.HALF_UP));
                    countBo.setTotalStrokeCountInProvince(subBucket.getDocCount());
                }
                countryStrokeCount += subBucket.getDocCount();
                countryCount = countryCount.add(BigDecimal.valueOf(inventorNum.getValue()).divide(new BigDecimal(*********), 2, RoundingMode.HALF_UP));
            }
            countBo.setTotalAmountInCountry(countryCount);
            countBo.setTotalStrokeCountInCountry(countryStrokeCount);
            countBo.setOrgName(bucket.getKeyAsString().trim());
            financeCountBoList.add(countBo);
        }
        Collections.sort(financeCountBoList, (b1, b2) -> BigDecimal.ZERO.compareTo(BigDecimalUtil.subtract(b1.getTotalAmountInCountry(), b2.getTotalAmountInCountry())));
        if (StringUtils.isNotEmpty(chainId)) {
            IndustryChain chain = chainService.getById(chainId);
            for (FinanceCountBo bo : financeCountBoList) {
                bo.setInvestment(Arrays.asList(chain.getName()));
            }
            return financeCountBoList;
        }
        // 统计投资领域
        String chainAggKey = TERMS_BUCKET_PREFIX + "chain";
        TermsAggregationBuilder agencyAggregation4Chain = AggregationBuilders.terms(agencyAggKey).field("investment_agency").size(10000);
        TermsAggregationBuilder chainAggregation = AggregationBuilders.terms(chainAggKey).field("chain.name");
        agencyAggregation4Chain.subAggregation(chainAggregation);
        final AggregationPageResult chainPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), queryBuilder, agencyAggregation4Chain);
        Map<String, List<String>> agencyChainMap = new HashMap<>();
        Terms chainTerms = (Terms) chainPageResult.getSearchResponse().getAggregations().asMap().get(agencyAggKey);
        for (Terms.Bucket bucket : chainTerms.getBuckets()) {
            agencyChainMap.computeIfAbsent(bucket.getKeyAsString(), k -> new ArrayList<>());
            Terms chainDetail = (Terms) bucket.getAggregations().asMap().get(chainAggKey);
            for (Terms.Bucket subBucket : chainDetail.getBuckets()) {
                agencyChainMap.get(bucket.getKeyAsString()).add(subBucket.getKeyAsString());
            }
        }
        for (FinanceCountBo bo : financeCountBoList) {
            bo.setInvestment(agencyChainMap.get(bo.getOrgName()));
        }
        return financeCountBoList;
    }

    @Override
    public Map<String, FinanceAreaCountBo> getAreaFund() {
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.province_code", CommonConstant.DIVISION_FUJIAN.getId()));
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "city").field("financing_company.city_name").size(100);
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum("totalAmount").field("financing_amount_cal");
        termsAggregationBuilder.subAggregation(sumAggregationBuilder);
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), boolQueryBuilder, termsAggregationBuilder);
        return parseAreaAggregationFromResponse(pageResult.getSearchResponse());
    }

    @Override
    public Map<String, Map<String, FinanceCompareBo>> getFundStatistic(String indexType) {
        Map<String, Map<String, FinanceCompareBo>> resultMap = new HashMap<>();
        String cityId = RequestContext.getCityId(), areaId = RequestContext.getAreaId();
        Map<String, BigDecimal> techCompanyMap = indexType.equals(TYPE_FINANCE) ? getFinanceTendGroupByTag(cityId, areaId, true) : projectService.getSubsidyTendGroupByTag(cityId, areaId, true);
        Map<String, BigDecimal> otherCompanyMap = indexType.equals(TYPE_FINANCE) ? getFinanceTendGroupByTag(cityId, areaId, false) : projectService.getSubsidyTendGroupByTag(cityId, areaId, false);
        resultMap.put("highAndNewCompany", buildFinanceCompare(techCompanyMap));
        resultMap.put("industrialCompany", buildFinanceCompare(otherCompanyMap));
        return resultMap;
    }

    private Map<String, FinanceCompareBo> buildFinanceCompare(Map<String, BigDecimal> amountMap) {
        Map<String, FinanceCompareBo> resultMap = new LinkedHashMap<>();
        FinanceCompareBo.initByYear(resultMap, 5);
        Map<String, BigDecimal> rateMap = getIncreaseRate(amountMap);
        for (Map.Entry<String, BigDecimal> entry : amountMap.entrySet()) {
            if (!resultMap.containsKey(entry.getKey())) {
                continue;
            }
            FinanceCompareBo financeCompareBo = new FinanceCompareBo();
            financeCompareBo.setTotalAmount(entry.getValue());
            financeCompareBo.setRate(rateMap.get(entry.getKey()));
            resultMap.put(entry.getKey(), financeCompareBo);
        }
        return resultMap;
    }

    /**
     * 科技企业、传统企业投融资历年资金对比
     *
     * @param cityId
     * @param areaId
     * @param isTechCompany
     * @return
     */
    private Map<String, BigDecimal> getFinanceTendGroupByTag(String cityId, String areaId, boolean isTechCompany) {
        // 初始化
        int pastYears = 6;
        LocalDate startDate = new LocalDate().minusYears(pastYears);
        final BoolQueryBuilder queryBuilder = buildBaseQuery(null, null, null, cityId, areaId);
        queryBuilder.filter(QueryBuilders.rangeQuery("financing_time").gte(startDate.toString("yyyy-MM-dd")));
        List<String> highTechTag = CompanyTagEnum.listCompanyTags();
        highTechTag.add("高技术型企业");
        if (isTechCompany) {
            queryBuilder.filter(QueryBuilders.termsQuery("financing_company.tag_name", highTechTag));
        } else {
            queryBuilder.mustNot(QueryBuilders.termsQuery("financing_company.tag_name", highTechTag));
        }

        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "year").field("year").size(pastYears);
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum("totalAmount").field("financing_amount_cal");
        termsAggregationBuilder.subAggregation(sumAggregationBuilder);
        final AggregationPageResult newPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), queryBuilder, termsAggregationBuilder);
        return parseCompareAggregationFromResponse(newPageResult.getSearchResponse());
    }

    @Override
    public YearFinanceBo getYearFinanceInfo(String year, String cityId, String areaId) {
        YearFinanceBo bo = new YearFinanceBo();
        // 总计
        BoolQueryBuilder totalQuery = buildBaseQuery(null, null, null, cityId, areaId);
        // 按今年和到今年为止分组
        String yearAggregationKey = TERMS_BUCKET_PREFIX + "year";
        DateRangeAggregationBuilder yearAggregationBuilder = AggregationBuilders.dateRange(yearAggregationKey).field("financing_time").format("yyyy")
                .addRange(Double.parseDouble(year), Double.parseDouble(year) + 1)
                .addUnboundedTo(Double.parseDouble(year) + 1);
        // 投融资金额
        String amountAggregationKey = TERMS_BUCKET_PREFIX + "amount";
        SumAggregationBuilder amountAggregationBuilder = AggregationBuilders.sum(amountAggregationKey).field("financing_amount_cal");
        yearAggregationBuilder.subAggregation(amountAggregationBuilder);
        AggregationPageResult amountPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), totalQuery, yearAggregationBuilder);
        ParsedDateRange amountRange = (ParsedDateRange) amountPageResult.getSearchResponse().getAggregations().asMap().get(yearAggregationKey);
        for (Range.Bucket bucket : amountRange.getBuckets()) {
            ParsedSum sum = (ParsedSum) bucket.getAggregations().asMap().get(amountAggregationKey);
            if (bucket.getFrom() != null) {
                bo.setYearAmount(BigDecimal.valueOf(sum.getValue()).divide(new BigDecimal(*********), 2, RoundingMode.HALF_UP));
                bo.setYearFinanceNum(bucket.getDocCount());
            } else {
                bo.setTotalAmount(BigDecimal.valueOf(sum.getValue()).divide(new BigDecimal(*********), 2, RoundingMode.HALF_UP));
                bo.setTotalFinanceNum(bucket.getDocCount());
            }
        }
        // 投融资企业数
        String companyAggregationKey = TERMS_BUCKET_PREFIX + "company";
        CardinalityAggregationBuilder companyAggregationBuilder = AggregationBuilders.cardinality(companyAggregationKey).field("financing_company.id");
        yearAggregationBuilder.subAggregation(companyAggregationBuilder);
        AggregationPageResult companyPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), totalQuery, yearAggregationBuilder);
        ParsedDateRange companyRange = (ParsedDateRange) companyPageResult.getSearchResponse().getAggregations().asMap().get(yearAggregationKey);
        for (Range.Bucket bucket : companyRange.getBuckets()) {
            ParsedCardinality result = bucket.getAggregations().get(companyAggregationKey);
            if (bucket.getFrom() != null) {
                bo.setYearCompanyNum(result.getValue());
            } else {
                bo.setTotalCompanyNum(result.getValue());
            }
        }
        bo.setAvgFinanceAmount(BigDecimalUtil.divide(bo.getTotalAmount(), BigDecimal.valueOf(bo.getTotalFinanceNum()), 2, RoundingMode.HALF_UP));
        bo.setYearAvgFinanceAmount(BigDecimalUtil.divide(bo.getYearAmount(), BigDecimal.valueOf(bo.getYearFinanceNum()), 2, RoundingMode.HALF_UP));
        return bo;
    }

    @Override
    public Map<String, FinanceAreaCountBo> getFinanceEvent(String industry, String cityCode, String areaCode) {
        LocalDate localDate = new LocalDate();
        String year = localDate.minusYears(6).toString("yyyy");
        String chainId = chainService.getChainIdByName(industry);

        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.id", chainId));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("year").gte(year));
        if (StringUtils.isNotBlank(cityCode)) {//市级
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.city_code", cityCode));
            if (StringUtils.isNotBlank(areaCode)) {//区级
                boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.area_code", areaCode));
            }
        }

        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "year").field("year").size(6);
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum("totalAmount").field("financing_amount_cal");
        termsAggregationBuilder.subAggregation(sumAggregationBuilder);
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), boolQueryBuilder, termsAggregationBuilder);
        final Map<String, FinanceAreaCountBo> areaMap = parseYearAggregationFromResponse(pageResult.getSearchResponse());
        return areaMap;
    }

    @Override
    public Map<String, Long> getAnalyzeInfos(String chainId, String cityCode, String areaCode, String field) {
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.id", chainId));
        if (StringUtils.isNotBlank(cityCode)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.city_code", cityCode));
        }
        if (StringUtils.isNotBlank(areaCode)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.area_code", areaCode));
        }
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + field).field(field).size(20);
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), boolQueryBuilder, termsAggregationBuilder);
        return parseCommonAggregationFromResponse(pageResult.getSearchResponse(), field);
    }

    @Override
    public Map<String, BigDecimal> getStatisticByChain(String chainId, String cityCode, String areaCode) {
        Map<String, BigDecimal> statisticMap = new HashMap<>();
        // 投融资
        final BoolQueryBuilder boolQueryBuilder = buildBaseQuery(chainId, null, null, cityCode, areaCode);
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum(TERMS_BUCKET_PREFIX + "totalAmount").field("financing_amount_cal");
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), boolQueryBuilder, sumAggregationBuilder);
        statisticMap.put("financeAmount", parseFinanceSumAggregationFromResponse(pageResult.getSearchResponse()));
        // 股权融资
        boolQueryBuilder.must(QueryBuilders.termsQuery("financing_round", "股权融资"));
        SumAggregationBuilder stockSumAggregationBuilder = AggregationBuilders.sum(TERMS_BUCKET_PREFIX + "totalAmount").field("financing_amount_cal");
        final AggregationPageResult stockPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), boolQueryBuilder, stockSumAggregationBuilder);
        statisticMap.put("stockFinanceAmount", parseFinanceSumAggregationFromResponse(stockPageResult.getSearchResponse()));
        return statisticMap;
    }

    /**
     * 获得近几年的股权融资总额
     *
     * @param chainId
     * @param cityCode
     * @param areaCode
     * @param recentYears
     * @return
     */
    private Map<String, BigDecimal> getEquityFinancingRecently(String chainId, String cityCode, String areaCode, Integer recentYears) {
        return getFinancingRecently(chainId, null, cityCode, areaCode, recentYears, new String[]{"股权融资"});
    }

    /**
     * 获得近几年的融资总额
     *
     * @param chainId
     * @param cityCode
     * @param areaCode
     * @param recentYears
     * @param roundTypes
     * @return
     */
    private Map<String, BigDecimal> getFinancingRecently(String chainId, String provinceId, String cityCode, String areaCode, Integer recentYears, String... roundTypes) {
        //近X年数据
        LocalDate localDate = new LocalDate();
        LocalDate date = localDate.minusYears(recentYears);
        String afterYear = date.toString("yyyy-MM-dd");
        //股权融资金额
        final BoolQueryBuilder boolQueryBuilder = buildBaseQuery(chainId, null, provinceId, cityCode, areaCode);
        if (roundTypes != null && roundTypes.length > 0) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_round", roundTypes));
        }
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("financing_time").gte(afterYear));
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "year").field("year").size(recentYears);
        SumAggregationBuilder newSumBuilder = AggregationBuilders.sum("totalAmount").field("financing_amount_cal");
        termsAggregationBuilder.subAggregation(newSumBuilder);
        final AggregationPageResult newPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), boolQueryBuilder, termsAggregationBuilder);
        return parseCompareAggregationFromResponse(newPageResult.getSearchResponse());
    }

    @Override
    public BigDecimal sumByChainAndRegion(String chainId, String nodeId, String cityId, String areaId, String roundType) {
        BoolQueryBuilder financingAllQuery = buildBaseQuery(chainId, nodeId, null, cityId, areaId);
        if (StringUtils.isNotEmpty(roundType)) {
            financingAllQuery.must(QueryBuilders.termQuery("financing_round", roundType));
        }
        Map<String, BigDecimal> financingAllMap = EsAlterUtil.getSumAggregation(elasticsearchHelper, EsIndexEnum.FINANCING, financingAllQuery, "chain.id", "financing_amount_cal");
        return financingAllMap.isEmpty() ? new BigDecimal(0) : BigDecimalUtil.divide(financingAllMap.get(chainId), new BigDecimal(10000), 2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal sumByChainAndRegionTimeLimit(String chainId, String nodeId, String cityId, String areaId, String roundType) {
        BoolQueryBuilder financingAllQuery = buildBaseQuery(chainId, nodeId, null, cityId, areaId);
        if (StringUtils.isNotEmpty(roundType)) {
            financingAllQuery.must(QueryBuilders.termQuery("financing_round", roundType));
        }
        financingAllQuery.must(QueryBuilders.rangeQuery("financing_time").gte("2023-01-01"));
        Map<String, BigDecimal> financingAllMap = EsAlterUtil.getSumAggregation(elasticsearchHelper, EsIndexEnum.FINANCING, financingAllQuery, "chain.id", "financing_amount_cal");
        return financingAllMap.isEmpty() ? new BigDecimal(0) : BigDecimalUtil.divide(financingAllMap.get(chainId), new BigDecimal(10000), 2, RoundingMode.HALF_UP);
    }

    @Override
    public Map<String, Object> getFinanceScaleTend(String chainId, String cityCode, String areaCode) {
        List<String> years = DateUtils.getRecentYears(5);
        // 投融资
        Map<String, BigDecimal> financeMap = getFinancingRecently(chainId, null, cityCode, areaCode, years.size(), null);
        // 股权融资
        Map<String, BigDecimal> equityFinanceMap = getEquityFinancingRecently(chainId, cityCode, areaCode, years.size());
        Map<String, Object> resultMap = new HashMap<>();
        for (int i = years.size() - 1; i >= 0; i--) {
            String year = years.get(i);
            Map<String, Object> subMap = new HashMap<>();
            subMap.put("total", financeMap.get(year));
            subMap.put("equity", equityFinanceMap.get(year));
            resultMap.put(year, subMap);
        }
        return resultMap;
    }

    @Override
    public Integer countCompanyByChainAndRegion(String chainId, String nodeId, String cityId, String areaId, String roundType) {
        BoolQueryBuilder financingAllQuery = buildBaseQuery(chainId, nodeId, null, cityId, areaId);
        if (StringUtils.isNotEmpty(roundType)) {
            financingAllQuery.must(QueryBuilders.termQuery("financing_round", roundType));
        }
        Map<String, Long> financingCompanyMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.FINANCING, financingAllQuery, "financing_company.id");
        return financingCompanyMap.size();
    }

    @Override
    public Integer countCompanyByChainAndRegionTimeLimit(String chainId, String nodeId, String cityId, String areaId, String roundType) {
        BoolQueryBuilder financingAllQuery = buildBaseQuery(chainId, nodeId, null, cityId, areaId);
        if (StringUtils.isNotEmpty(roundType)) {
            financingAllQuery.must(QueryBuilders.termQuery("financing_round", roundType));
        }
        financingAllQuery.must(QueryBuilders.rangeQuery("financing_time").gte("2023-01-01"));
        Map<String, Long> financingCompanyMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.FINANCING, financingAllQuery, "financing_company.id");
        return financingCompanyMap.size();
    }

    public static BigDecimal parseFinanceSumAggregationFromResponse(final SearchResponse searchResponse) {
        ParsedSum inventorNum = (ParsedSum) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "totalAmount");
        double value = inventorNum.getValue();
        return new BigDecimal(String.valueOf(value)).divide(new BigDecimal("*********"), 2, RoundingMode.HALF_UP);
    }

    private static Map<String, BigDecimal> getIncreaseRate(Map<String, BigDecimal> map) {
        Map<String, BigDecimal> resultMap = new HashMap<>();
        for (Map.Entry<String, BigDecimal> yearCount : map.entrySet()) {
            String currentYear = yearCount.getKey();
            String lastYear = String.valueOf(Integer.parseInt(currentYear) - 1);
            if (!map.containsKey(lastYear)) {
                continue;
            }
            BigDecimal subtract = BigDecimalUtil.subtract(yearCount.getValue(), map.get(lastYear));
            BigDecimal increaseRate = null;
            if (map.get(lastYear).compareTo(BigDecimal.ZERO) != 0) {
                increaseRate = BigDecimalUtil.divide(subtract, map.get(lastYear), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"))
                        .setScale(2, RoundingMode.HALF_UP);
            }
            resultMap.put(currentYear, increaseRate);
        }
        return resultMap;
    }


    public static Map<String, BigDecimal> parseCompareAggregationFromResponse(final SearchResponse searchResponse) {
        Map<String, BigDecimal> compareMap = new LinkedHashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "year");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            ParsedSum inventorNum = (ParsedSum) bucket.getAggregations().asMap().get("totalAmount");
            double value = inventorNum.getValue();
            BigDecimal result = new BigDecimal(String.valueOf(value)).divide(new BigDecimal("*********"), 2, RoundingMode.HALF_UP);
            compareMap.put(bucket.getKeyAsString(), result);
        }
        return compareMap;
    }

    public static Map<String, Long> parseCommonAggregationFromResponse(final SearchResponse searchResponse, String field) {
        Map<String, Long> commonMap = new LinkedHashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + field);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            commonMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return commonMap;
    }


    public static Map<String, FinanceAreaCountBo> parseYearAggregationFromResponse(final SearchResponse searchResponse) {
        Map<String, FinanceAreaCountBo> financeMap = new HashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "year");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            FinanceAreaCountBo financeAreaCountBo = new FinanceAreaCountBo();
            financeAreaCountBo.setTotalStrokeCount(bucket.getDocCount());
            ParsedSum inventorNum = (ParsedSum) bucket.getAggregations().asMap().get("totalAmount");
            double value = inventorNum.getValue();
            BigDecimal result = new BigDecimal(String.valueOf(value)).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
            financeAreaCountBo.setTotalAmount(result);
            financeMap.put(bucket.getKeyAsString(), financeAreaCountBo);
        }
        return financeMap;
    }

    public static Map<String, FinanceAreaCountBo> parseAreaAggregationFromResponse(final SearchResponse searchResponse) {
        Map<String, FinanceAreaCountBo> financeMap = new LinkedHashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "city");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        List<FinanceAreaCountBo> areaCountBoList = new ArrayList<>();
        for (Terms.Bucket bucket : buckets) {
            FinanceAreaCountBo financeAreaCountBo = new FinanceAreaCountBo();
            financeAreaCountBo.setTotalStrokeCount(bucket.getDocCount());
            ParsedSum inventorNum = (ParsedSum) bucket.getAggregations().asMap().get("totalAmount");
            double value = inventorNum.getValue();
            BigDecimal result = new BigDecimal(String.valueOf(value)).divide(new BigDecimal("*********"), 2, RoundingMode.HALF_UP);
            financeAreaCountBo.setTotalAmount(result);
            financeAreaCountBo.setAreaName(bucket.getKeyAsString());
            areaCountBoList.add(financeAreaCountBo);
        }
        Collections.sort(areaCountBoList, (a1, a2) -> BigDecimal.ZERO.compareTo(BigDecimalUtil.subtract(a1.getTotalAmount(), a2.getTotalAmount())));
        for (FinanceAreaCountBo bo : areaCountBoList) {
            financeMap.put(bo.getAreaName(), bo);
        }
        return financeMap;
    }

    public static Map<String, FinanceCountBo> parseAggregationFromResponse(final SearchResponse searchResponse, Integer isProvince) {

        Map<String, FinanceCountBo> financeMap = new HashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "investors");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        if (isProvince != 1) {
            for (Terms.Bucket bucket : buckets) {
                FinanceCountBo financeCountBo = new FinanceCountBo();
                financeCountBo.setTotalStrokeCountInCountry(bucket.getDocCount());
                ParsedSum inventorNum = (ParsedSum) bucket.getAggregations().asMap().get("totalAmount");
                financeCountBo.setTotalAmountInProvince(new BigDecimal(inventorNum.getValue()));
                financeMap.put(bucket.getKeyAsString(), financeCountBo);
            }
        } else {
            for (Terms.Bucket bucket : buckets) {
                FinanceCountBo financeCountBo = new FinanceCountBo();
                financeCountBo.setTotalStrokeCountInProvince(bucket.getDocCount());
                ParsedSum inventorNum = (ParsedSum) bucket.getAggregations().asMap().get("totalAmount");
                financeCountBo.setTotalAmountInProvince(new BigDecimal(inventorNum.getValue()));
                financeMap.put(bucket.getKeyAsString(), financeCountBo);
            }
        }
        return financeMap;
    }

    @Override
    public BoolQueryBuilder buildBaseQuery(String chainId, String nodeId, String provinceId, String cityId, String areaId) {
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(chainId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.id", chainId));
        }
        if (StringUtils.isNotEmpty(nodeId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", nodeId));
        }
        if (StringUtils.isNotEmpty(cityId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.city_code", cityId));
        }
        if (StringUtils.isNotEmpty(areaId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.area_code", areaId));
        }
        if (StringUtils.isNotEmpty(provinceId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.province_code", provinceId));
        }
        return boolQueryBuilder;
    }

    @Override
    public BoolQueryBuilder buildBaseQueryTimeLimit(String chainId, String nodeId, String provinceId, String cityId, String areaId) {
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(chainId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.id", chainId));
        }
        if (StringUtils.isNotEmpty(nodeId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", nodeId));
        }
        if (StringUtils.isNotEmpty(cityId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.city_code", cityId));
        }
        if (StringUtils.isNotEmpty(areaId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.area_code", areaId));
        }
        if (StringUtils.isNotEmpty(provinceId)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.province_code", provinceId));
        }
        boolQueryBuilder.must(QueryBuilders.rangeQuery("financing_time").gte("2023-01-01"));
        return boolQueryBuilder;
    }

    @Override
    public List<CommonIndexBO> getCompanyCountByAmount(BoolQueryBuilder boolQueryBuilder) {
        String amountAggregationKey = TERMS_BUCKET_PREFIX + "amount";
        RangeAggregationBuilder rangeAggregationBuilder = AggregationBuilders.range(amountAggregationKey).field("financing_amount_cal")
                .addUnboundedTo(500)
                .addRange(500, 2000)
                .addRange(2000, 10000)
                .addUnboundedFrom(10000);
        AggregationPageResult aggregationResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.FINANCING.getEsIndex(), boolQueryBuilder, rangeAggregationBuilder);
        ParsedRange ranges = (ParsedRange) aggregationResult.getSearchResponse().getAggregations().asMap().get(amountAggregationKey);
        List<CommonIndexBO> resultList = new ArrayList<>();
        for (Range.Bucket bucket : ranges.getBuckets()) {
            String keyName = null;
            if (500 - Math.floor((Double) bucket.getTo()) == 0) {
                keyName = "500万以下";
            } else if (500 - Math.floor((Double) bucket.getFrom()) == 0) {
                keyName = "500-2000万";
            } else if (2000 - Math.floor((Double) bucket.getFrom()) == 0) {
                keyName = "2000万-1亿";
            } else {
                keyName = "1亿以上";
            }
            resultList.add(new CommonIndexBO(keyName, bucket.getDocCount(), null));
        }
        return resultList;
    }

    @Override
    public Map<String, BigDecimal> countAmountByRound(String chainId, String cityId, String areaId) {
        final BoolQueryBuilder boolQueryBuilder = buildBaseQuery(chainId, null, null, cityId, areaId);
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("financing_round", Arrays.asList("战略融资", "股权投资", "股权转让", "股权投资")));
        Map<String, BigDecimal> roundAmountMap = EsAlterUtil.getSumAggregation(elasticsearchHelper, EsIndexEnum.FINANCING, boolQueryBuilder,
                "financing_round", "financing_amount_cal");
        if (roundAmountMap.isEmpty()) {
            List<String> rounds = Arrays.asList("种子轮", "天使轮", "A轮", "B轮", "C轮");
            for (String round : rounds) {
                roundAmountMap.put(round, new BigDecimal(0));
            }
        }
        return roundAmountMap;
    }

    @Override
    public Map<String, BigDecimal> getFundDistributionInChain(String indexType) {
        BoolQueryBuilder queryBuilder = TYPE_SUBSIDY.equals(indexType) ? EsAlterUtil.buildAuthQuery(null, null, null, null, true) :
                buildBaseQuery(null, null, null, RequestContext.getCityId(), RequestContext.getAreaId());
        EsIndexEnum indexEnum = TYPE_SUBSIDY.equals(indexType) ? EsIndexEnum.PROJECT : EsIndexEnum.FINANCING;
        String amountColumn = TYPE_SUBSIDY.equals(indexType) ? "apply_expense" : "financing_amount_cal";
        Map<String, BigDecimal> finalMap = new LinkedHashMap<>();
        Map<String, BigDecimal> resultMap = EsAlterUtil.getSumAggregation(elasticsearchHelper, indexEnum, queryBuilder, "chain.name", amountColumn);
        //List<IndustryChain> chainList = chainService.listChain(false);
        // 山 、叶、鸡、竹、水、氟新材料、es纤维、物联网电池
        List<String> chainSort = Arrays.asList("山", "茶", "白羽鸡", "竹", "水", "氟新材料", "ES纤维", "物联网电池");
        for (String chain : chainSort) {
            if (!resultMap.containsKey(chain)) {
                continue;
            }
            BigDecimal amount = resultMap.get(chain);
            if (TYPE_FINANCE.equals(indexType)) {
                amount = amount.divide(new BigDecimal(*********), 2, RoundingMode.HALF_UP);
            }
            if (amount.compareTo(BigDecimal.ZERO) > 0) {
                finalMap.put(chain, amount);
            }
        }
        return finalMap;
    }

    @Override
    public Map<String, Object> getFinanceScaleTendIncludeProvince(String chainId, String cityId, String areaId) {
        List<String> years = DateUtils.getRecentYears(5);
        // 目标地区投融资
        Map<String, BigDecimal> financeMap = getFinancingRecently(chainId, null, cityId, areaId, years.size(), null);
        // 福建省
        Map<String, BigDecimal> provinceFinanceMap = getFinancingRecently(chainId, CommonConstant.DIVISION_FUJIAN.getId(), null, null, years.size(), null);
        Map<String, Object> resultMap = new LinkedHashMap<>();
        for (int i = years.size() - 1; i >= 0; i--) {
            String year = years.get(i);
            Map<String, Object> subMap = new HashMap<>();
            subMap.put("local", financeMap.get(year));
            subMap.put("province", provinceFinanceMap.get(year));
            resultMap.put(year, subMap);
        }
        return resultMap;
    }

    @Override
    public List<String> listFundAcquisition(String chainId, boolean fromCockpit) {
        Set<String> acquisitionSet = new HashSet<>();
        String companyStyle = fromCockpit ? "<company>" : StringUtils.EMPTY;
        String style = "<number>";
        // 放贷
        List<FinanceLoan> loanList = loanMapper.selectList(Wrappers.lambdaQuery(FinanceLoan.class));
        for (FinanceLoan loan : loanList) {
            StringBuilder desc = new StringBuilder();
            desc.append(loan.getLoanType()).append(companyStyle).append("发放户数").append(style).append(loan.getLoanNum()).append(style)
                    .append("户；发放金额").append(style).append(loan.getLoanAmount().setScale(0, RoundingMode.HALF_UP)).append(style).append("万元");
            acquisitionSet.add(desc.toString());
        }
        // 融资
        Integer pageNum = 1, pageSize = 100;
        FinanceEventBo eventBo = new FinanceEventBo();
        if (!chainId.equals("1009")) {
            eventBo.setCityId(CommonConstant.DIVISION_NANPING.getId());
        }
        eventBo.setPageNum(pageNum);
        eventBo.setPageSize(pageSize);
        eventBo.setChainId(chainId);
        EsPageResult financePage = getEventList(eventBo);
        if (financePage != null && CollectionUtils.isNotEmpty(financePage.getList())) {
            List<Map<String, Object>> financeList = financePage.getList();
            for (Map<String, Object> finance : financeList) {
                StringBuilder desc = new StringBuilder();
                desc.append(((Map<String, Object>) finance.get("financing_company")).get("name")).append(companyStyle).append("获得风投");
                if (finance.get("financing_amount_cal") == null) {
                    BigDecimal amount = new BigDecimal(String.valueOf(finance.get("financing_amount_cal")))
                            .divide(new BigDecimal("10000"), 0, RoundingMode.HALF_UP);
                    desc.append(style).append(amount).append(style).append("万元");
                } else {
                    desc.append("，金额未披露");
                }
                acquisitionSet.add(desc.toString());
            }
        }
        // 项目补助
        EsPageResult subsidyPage = new EsPageResult();
        if (!chainId.equals("1009")) {
            subsidyPage = projectService.page4Subsidy(pageNum, pageSize, chainId, null, CommonConstant.DIVISION_NANPING.getId(), null, null, null, null);
        } else {
            subsidyPage = projectService.page4Subsidy(pageNum, pageSize, chainId, null, null, null, null, null, null);
        }
        if (subsidyPage != null && CollectionUtils.isNotEmpty(subsidyPage.getList())) {
            List<Map<String, Object>> subsidyList = subsidyPage.getList();
            for (Map<String, Object> subsidy : subsidyList) {
                StringBuilder desc = new StringBuilder();
                String companyName = (String) ((Map<String, Object>) subsidy.get("undertaking_unit")).get("name");
                desc.append(desensitizeCompanyName(companyName)).append(companyStyle).append("获得项目补助")
                        .append(style).append(new BigDecimal(String.valueOf(subsidy.get("apply_expense"))).setScale(0, RoundingMode.HALF_UP)).append(style).append("万元");
                acquisitionSet.add(desc.toString());
            }
        }
        return new ArrayList<>(acquisitionSet);
    }

    @Override
    public List<Map<String, Object>> listFundAcquisitionObject(String chainId) {
        List<Map<String, Object>> acquisitionList = new ArrayList<>();
        // 放贷
//        List<FinanceLoan> loanList = loanMapper.selectList(Wrappers.lambdaQuery(FinanceLoan.class));
//        for (FinanceLoan loan : loanList) {
//            Map<String, Object> acquisitionMap = new LinkedHashMap<>();
//            acquisitionMap.put("name", loan.getLoanType());
//            acquisitionMap.put("amount", loan.getLoanAmount().setScale(0, RoundingMode.HALF_UP) + "万元");
//            acquisitionMap.put("type", "债权融资");
//            acquisitionSet.add(acquisitionMap);
//        }
        Integer pageNum = 1, pageSize = 1000;
        // 项目补助
        EsPageResult subsidyPage = projectService.page4Subsidy(pageNum, pageSize, chainId, null, CommonConstant.DIVISION_NANPING.getId(), RequestContext.getAreaId(), null, null, null);
        if (subsidyPage != null && CollectionUtils.isNotEmpty(subsidyPage.getList())) {
            List<Map<String, Object>> subsidyList = subsidyPage.getList();
            for (Map<String, Object> subsidy : subsidyList) {
                Map<String, Object> acquisitionMap = new LinkedHashMap<>();
                String companyName = (String) ((Map<String, Object>) subsidy.get("undertaking_unit")).get("name");
                acquisitionMap.put("name", desensitizeCompanyName(companyName));
                acquisitionMap.put("amount", new BigDecimal(String.valueOf(subsidy.get("apply_expense"))).setScale(0, RoundingMode.HALF_UP) + "万元");
                acquisitionMap.put("type", "项目补助");
                acquisitionMap.put("start_date", subsidy.get("start_date"));
                acquisitionList.add(acquisitionMap);
            }
        }
        // 融资
        FinanceEventBo eventBo = new FinanceEventBo();
        if (!"1009".equals(chainId)) {
            eventBo.setCityId(CommonConstant.DIVISION_NANPING.getId());
        }
        eventBo.setPageNum(pageNum);
        eventBo.setPageSize(pageSize);
        eventBo.setChainId(chainId);
        EsPageResult financePage = getEventList(eventBo);
        if (financePage != null && CollectionUtils.isNotEmpty(financePage.getList())) {
            List<Map<String, Object>> financeList = financePage.getList();
            for (Map<String, Object> finance : financeList) {
                Map<String, Object> acquisitionMap = new LinkedHashMap<>();
                // 脱敏企业名称
                String companyName = (String) ((Map<String, Object>) finance.get("financing_company")).get("name");
                acquisitionMap.put("name", desensitizeCompanyName(companyName));
                if (finance.get("financing_amount_cal") == null) {
                    BigDecimal amount = new BigDecimal(String.valueOf(finance.get("financing_amount_cal")))
                            .divide(new BigDecimal("10000"), 0, RoundingMode.HALF_UP);
                    acquisitionMap.put("amount", amount + "万元");
                } else {
                    acquisitionMap.put("amount", "金额未披露");
                }
                acquisitionMap.put("type", "股权融资");
                acquisitionMap.put("start_date", finance.get("financing_time"));
                acquisitionList.add(acquisitionMap);
            }
        }
        return acquisitionList;
    }

    @Override
    public List<Map<String, Object>> listProductAndOrg(String chainId) {
        List<Map<String, Object>> mergeList = new ArrayList<>();
        // 投资机构
        List<Map<String, Object>> orgList = localOrgService.listMaps(Wrappers.lambdaQuery(FinanceLocalOrg.class).orderByAsc(FinanceLocalOrg::getId));
        orgList.forEach(o -> {
            o.put("type", "org");
            Map<String, Object> newO = new HashMap<>();
            o.forEach((k, v) -> newO.put(StrUtil.toCamelCase(k), v));
            mergeList.add(newO);
        });
        // 金融产品
//        List<Map<String, Object>> productList = productService.listMaps(Wrappers.lambdaQuery(FinanceProduct.class)
//                .eq(StringUtils.isNotEmpty(chainId), FinanceProduct::getChainId, chainId).orderByAsc(FinanceProduct::getId));
//        productList.forEach(p -> {
//            p.put("type", "product");
//            Map<String, Object> newP = new HashMap<>();
//            p.forEach((k, v) -> newP.put(StrUtil.toCamelCase(k), v));
//            mergeList.add(newP);
//        });
        mergeList.addAll(thirdFinanceService.getFinanceProductList());
        return mergeList;
    }

    @Override
    public List<FinanceNeed> getFinancingNeeds(String chainId) {
        List<FinanceNeed> needs = needMapper.selectList(Wrappers.lambdaQuery(FinanceNeed.class)
                .eq(StringUtils.isNotEmpty(chainId), FinanceNeed::getChainId, chainId));
        Map<String, FinanceNeed> needMap = new HashMap<>();
        for (FinanceNeed need : needs) {
            String key = need.getCompanyName() + need.getFinanceType();
            // 脱敏企业名称
            need.setCompanyName(desensitizeCompanyName(need.getCompanyName()));
            needMap.put(key, need);
        }
        return new ArrayList<>(needMap.values());
    }

    @Override
    public Long countFinanceNumByChain(String chainId, String cityId, String areaId, String roundType) {
        final BoolQueryBuilder boolQueryBuilder = buildBaseQuery(chainId, null, null, cityId, areaId);
        if (StringUtils.isNotEmpty(roundType)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("financing_round", roundType));
        }
        return elasticsearchHelper.countRequest(EsIndexEnum.FINANCING.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public Long countFinanceNumByChainTimeLimit(String chainId, String cityId, String areaId, String roundType) {
        final BoolQueryBuilder boolQueryBuilder = buildBaseQuery(chainId, null, null, cityId, areaId);
        if (StringUtils.isNotEmpty(roundType)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("financing_round", roundType));
        }
        boolQueryBuilder.must(QueryBuilders.rangeQuery("financing_time").gte("2023-01-01"));
        return elasticsearchHelper.countRequest(EsIndexEnum.FINANCING.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public Map<String, Object> getFinanceInfo(String chainId, String cityId, String areaId) {
        // 项目补助资金
        BigDecimal subsidyAmount = projectService.countSubsidy(chainId, null, cityId, areaId, null);
        // 股权融资金额
        BigDecimal stockFinanceAmount = sumByChainAndRegion(chainId, null, cityId, areaId, "股权融资");
        // 项目补助笔数
        Long subsidyNum = projectService.countSubsidyNum(chainId, null, cityId, areaId);
        // 股权融资笔数
        Long financeNum = null;
        if ("1009".equals(chainId)) {
            financeNum = countFinanceNumByChainTimeLimit(chainId, null, null, null);
        } else {
            financeNum = countFinanceNumByChain(chainId, cityId, areaId, null);
        }
        // 风险资金
        BigDecimal LocalTotalFinanceAmount = sumByChainAndRegion(chainId, null, cityId, areaId, null);
        BigDecimal totalFinanceAmount = null;
        if ("1009".equals(chainId)) {
            totalFinanceAmount = sumByChainAndRegionTimeLimit(chainId, null, null, null, null);
        } else {
            totalFinanceAmount = sumByChainAndRegion(chainId, null, cityId, areaId, null);
        }
        // 资金链评价
        IndexFusion fusion = fusionService.getOne(FusionIndexEnum.FUND_CHAIN.getIndexId(), chainId, null, CommonConstant.DIVISION_NANPING.getId(), true);
        // 产业链名称
        String chainName = chainService.getChainNameById(chainId);
        // 项目补助增幅
        BigDecimal currentSubsidyAmount = projectService.countSubsidy(chainId, null, cityId, areaId, new LocalDate().toString("yyyy"));
        LocalDate lastYear = new LocalDate().minusYears(1);
        BigDecimal lastSubsidyAmount = projectService.countSubsidy(chainId, null, cityId, areaId, lastYear.toString("yyyy"));
        BigDecimal subsidyIncreaseRate = null;
        if (lastSubsidyAmount.compareTo(BigDecimal.ZERO) > 0) {
            subsidyIncreaseRate = currentSubsidyAmount.subtract(lastSubsidyAmount)
                    .divide(lastSubsidyAmount, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        }
        // 贷款企业数量和贷款金额
        BigDecimal loanAmount = BigDecimal.ZERO;
        Set<String> loanedCompanySet = new HashSet<>();
        List<FinanceLoanDetail> loanDetails = loanDetailMapper.selectList(Wrappers.lambdaQuery(FinanceLoanDetail.class)
                .eq(FinanceLoanDetail::getChainId, chainId));
        for (FinanceLoanDetail detail: loanDetails){
            loanedCompanySet.add(detail.getCreditObject());
            loanAmount = loanAmount.add(detail.getLoanAmount());
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("subsidyAmount", subsidyAmount);
        // 风险资金
        resultMap.put("financeAmount", LocalTotalFinanceAmount);
        resultMap.put("subsidyNum", subsidyNum);
        resultMap.put("financeNum", financeNum + loanDetails.size());
        resultMap.put("totalFinanceAmount", totalFinanceAmount.add(loanAmount));
        resultMap.put("evaluation", fusion.getDescription());
        resultMap.put("chainName", chainName);
        resultMap.put("subsidyIncreaseRate", subsidyIncreaseRate);
        resultMap.put("loanNum", loanedCompanySet.size());
        resultMap.put("loanAmount", loanAmount);
        return resultMap;
    }



    @Override
    public Map<String, Object> getFundDistributionInNode(String indexType, String chainId) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();

        if ("1009".equals(chainId)) {
            queryBuilder = TYPE_FINANCE.equals(indexType) ? EsAlterUtil.buildAuthQueryTimeLimit(null, null, null, null, false)
                    : EsAlterUtil.buildAuthQuery(null, null, null, null, true) ;
        } else {
            queryBuilder = TYPE_SUBSIDY.equals(indexType) ? EsAlterUtil.buildAuthQuery(null, null, null, null, true) :
                    buildBaseQuery(null, null, null, RequestContext.getCityId(), RequestContext.getAreaId());
        }

        EsIndexEnum indexEnum = TYPE_SUBSIDY.equals(indexType) ? EsIndexEnum.PROJECT : EsIndexEnum.FINANCING;
        String amountColumn = TYPE_SUBSIDY.equals(indexType) ? "apply_expense" : "financing_amount_cal";
        // 查询一级节点
        List<IndustryChainNode> nodeList = chainService.getNodesByChainIdAndLevels(chainId, 2, 2);
        List<String> nodeNames = nodeList.stream().map(IndustryChainNode::getName).collect(Collectors.toList());
        TermsAggregationBuilder countryAggregation = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "sum")
                .field("chain_node.name").size(100).order(BucketOrder.aggregation("totalAmount", false))
                .includeExclude(new IncludeExclude(nodeNames.toArray(new String[0]), null));
        SumAggregationBuilder countrySum = AggregationBuilders.sum("totalAmount").field(amountColumn);
        countryAggregation.subAggregation(countrySum);
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(indexEnum.getEsIndex(), queryBuilder, countryAggregation);
        Terms terms = (Terms) pageResult.getSearchResponse().getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "sum");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        Map<String, BigDecimal> amountMap = new LinkedHashMap<>();
        for (Terms.Bucket bucket : buckets) {
            ParsedSum inventorNum = (ParsedSum) bucket.getAggregations().asMap().get("totalAmount");
            BigDecimal val = new BigDecimal(String.valueOf(inventorNum.getValue()));
            if (TYPE_FINANCE.equals(indexType)) {
                val = val.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP);
            }
            if (val.compareTo(BigDecimal.ZERO) > 0) {
                amountMap.put(bucket.getKeyAsString(), val);
            }
        }
        BigDecimal loanAmount = BigDecimal.ZERO;
        if (TYPE_FINANCE.equals(indexType)){
            // 合并银行贷款计算
            List<FinanceLoanDetail> details = loanDetailMapper.selectList(Wrappers.lambdaQuery(FinanceLoanDetail.class)
                    .eq(FinanceLoanDetail::getChainId, chainId)
                    .isNotNull(FinanceLoanDetail::getChainNodeName));
            for (FinanceLoanDetail detail: details){
                loanAmount = loanAmount.add(detail.getLoanAmount());
                String chainNodeName = detail.getChainNodeName();
                if (!amountMap.containsKey(chainNodeName)){
                    amountMap.put(chainNodeName, detail.getLoanAmount());
                    continue;
                }
                amountMap.put(chainNodeName, amountMap.get(chainNodeName).add(detail.getLoanAmount()));
            }
        }
        BigDecimal amount = null;
        if (TYPE_SUBSIDY.equals(indexType)) {
            amount = projectService.countSubsidy(chainId, null, RequestContext.getCityId(), RequestContext.getAreaId(), null);
        } else {
            if ("1009".equals(chainId)) {
                amount = sumByChainAndRegionTimeLimit(chainId, null, null, null, null);
            } else {
                amount = sumByChainAndRegion(chainId, null, RequestContext.getCityId(), RequestContext.getAreaId(), null);
            }
            amount = amount.add(loanAmount);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", amount);
        resultMap.put("detail", amountMap);
        return resultMap;
    }

    @Override
    public Page<Map<String, Object>> page4Loan(Integer pageNum, Integer pageSize, String chainId) {
        return loanDetailMapper.selectMapsPage(new Page<>(pageNum, pageSize), Wrappers.lambdaQuery(FinanceLoanDetail.class)
                .eq(FinanceLoanDetail::getChainId, chainId));
    }

//    @Override
//    public EsPageResult listSubsidyFundAcquisition(String chainId,Integer pageNum, Integer pageSize) {
//        EsPageResult subsidyPage = projectService.page4Subsidy(pageNum, pageSize, chainId, null, CommonConstant.DIVISION_NANPING.getId(), RequestContext.getAreaId());
//        if (subsidyPage != null && CollectionUtils.isNotEmpty(subsidyPage.getList())) {
//            List<Map<String, Object>> subsidyList = subsidyPage.getList();
//            for (Map<String, Object> subsidy : subsidyList) {
//                Map<String, Object> acquisitionMap = new LinkedHashMap<>();
//                String companyName = (String) ((Map<String, Object>) subsidy.get("undertaking_unit")).get("name");
//                ((Map<String, Object>) subsidy.get("undertaking_unit")).put("name", desensitizeCompanyName(companyName));
//
//            }
//        }
//        return subsidyPage;
//    }

    @Override
    public String desensitizeCompanyName(String companyName) {
        List<String> divisionNames = Arrays.asList("福建省", "福建", "南平市", "南平", "延平区", "建阳区", "顺昌县", "浦城县", "光泽县", "松溪县", "政和县", "邵武市", "武夷山市", "建瓯市",
                "延平", "建阳", "顺昌", "浦城", "光泽", "松溪", "政和", "邵武", "武夷山", "建瓯");
        for (String divisionName : divisionNames) {
            if (companyName.startsWith(divisionName)) {
                companyName = companyName.replace(divisionName, "");
            }
        }
        if (companyName.contains("公司")){
            if (companyName.length() >= 7){
                // 第三个字明文展示
                String firstChar = String.valueOf(companyName.charAt(0));
                String thirdChar = String.valueOf(companyName.charAt(2));
                String safeNamePrefix = firstChar + "*" + thirdChar;
                companyName = safeNamePrefix + companyName.substring(3, companyName.length());
                return StringUtils.rightPad(companyName.substring(0, 3), companyName.length() - 4, '*') + "有限公司";
            }
            return StringUtils.rightPad(companyName.substring(0, 1), companyName.length() - 4, '*') + "有限公司";
        }
//        if (companyName.contains("学院")){
//            if (companyName.length() >= 5){
//                // 第三个字明文展示
//                String firstChar = String.valueOf(companyName.charAt(0));
//                String thirdChar = String.valueOf(companyName.charAt(2));
//                String safeNamePrefix = firstChar + "*" + thirdChar;
//                companyName = safeNamePrefix + companyName.substring(3, companyName.length());
//                return StringUtils.rightPad(companyName.substring(0, 3), companyName.length() - 4, '*') + "学院";
//            }
//            return StringUtils.rightPad(companyName.substring(0, 1), companyName.length() - 4, '*') + "学院";
//        }
        if (companyName.contains("合作社")){
            return StringUtils.rightPad(companyName.substring(0, 2), companyName.length() - 3, '*') + "合作社";
        }
        return StringUtils.rightPad(companyName.substring(0, 2), companyName.length() - 2, '*')
                + companyName.substring(companyName.length() - 2, companyName.length());
    }

    @Override
    public EsPageResult pageByCompanyId(String id, int pageNum, int pageSize) {
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("financing_company.id", id));
        SearchSourceBuilder searchBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, pageNum, pageSize, null, null,
                EsIndexEnum.FINANCING.getSort());
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchBuilder, EsIndexEnum.FINANCING.getEsIndex());
        return ElasticsearchBuilder.buildPageResult(searchResponse);
    }

//    public static void main(String[] args){
//        String companyName = "邵武永太高新材料有限公司";
//        List<String> divisionNames = Arrays.asList("福建省", "福建", "南平市", "南平", "延平区", "建阳区", "顺昌县", "浦城县", "光泽县", "松溪县", "政和县", "邵武市", "武夷山市", "建瓯市",
//                "延平", "建阳", "顺昌", "浦城", "光泽", "松溪", "政和", "邵武", "武夷山", "建瓯");
//        for (String divisionName : divisionNames) {
//            if (companyName.startsWith(divisionName)) {
//                companyName = companyName.replace(divisionName, "");
//            }
//        }
//        // 第三个字明文展示
//        String firstChar = String.valueOf(companyName.charAt(0));
//        String thirdChar = String.valueOf(companyName.charAt(2));
//        String safeNamePrefix = firstChar + "*" + thirdChar;
//        companyName = safeNamePrefix + companyName.substring(3, companyName.length());
//        System.out.println(StringUtils.rightPad(companyName.substring(0, 3), companyName.length() - 4, '*') + "有限公司");
//    }

}
