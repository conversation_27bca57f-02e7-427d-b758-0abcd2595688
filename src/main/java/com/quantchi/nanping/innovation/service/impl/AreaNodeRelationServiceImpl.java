package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.AreaNodeRelationDAO;
import com.quantchi.nanping.innovation.model.AreaNodeRelation;
import com.quantchi.nanping.innovation.service.IAreaNodeRelationService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/11 16:17
 */
@Service
public class AreaNodeRelationServiceImpl extends ServiceImpl<AreaNodeRelationDAO, AreaNodeRelation> implements IAreaNodeRelationService {
    @Override
    public Map<String, List<AreaNodeRelation>> getMapByChainId(String chainId) {
        List<AreaNodeRelation> areaNodeRelations = this.list(Wrappers.lambdaQuery(AreaNodeRelation.class)
                .eq(AreaNodeRelation::getChainId, chainId)
                .orderByAsc(AreaNodeRelation::getSort));
        return areaNodeRelations.stream().collect(Collectors.groupingBy(AreaNodeRelation::getAreaId));

    }
}
