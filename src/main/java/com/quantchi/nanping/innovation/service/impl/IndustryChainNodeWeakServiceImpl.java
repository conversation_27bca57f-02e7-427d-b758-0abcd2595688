package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDAO;
import com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakDetailMapper;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeakDetail;
import com.quantchi.nanping.innovation.service.IIndustryChainNodeWeakService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/12 14:02
 */
@Service
public class IndustryChainNodeWeakServiceImpl extends ServiceImpl<IndustryChainNodeWeakDAO, IndustryChainNodeWeak> implements IIndustryChainNodeWeakService {

    @Autowired
    private IndustryChainNodeWeakDetailMapper detailMapper;

    @Override
    public IndustryChainNodeWeakDetail getDetailById(String id) {
        IndustryChainNodeWeakDetail existedDetail = detailMapper.selectOne(Wrappers.lambdaQuery(IndustryChainNodeWeakDetail.class)
                .eq(IndustryChainNodeWeakDetail::getDataId, id));
        if (existedDetail != null){
            return existedDetail;
        }
        existedDetail = new IndustryChainNodeWeakDetail();
        existedDetail.setDataId(id);
        // TODO: 补充原因和分析
        detailMapper.insert(existedDetail);
        return existedDetail;
    }
}
