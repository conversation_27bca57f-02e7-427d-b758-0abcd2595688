package com.quantchi.nanping.innovation.service.library;

import com.quantchi.nanping.innovation.insight.model.bo.ExpertPageBo;
import com.quantchi.nanping.innovation.insight.model.bo.PersonStatsBo;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.tianying.model.EsPageResult;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/15 5:00 下午
 * @description
 */
public interface TalentService {

    /**
     * 获取人才列表
     *
     * @return
     */
    EsPageResult page(ExpertPageBo expertPageBo);

    /**
     * 获取人才列表(不区分本地，外部)
     *
     * @return
     */
    EsPageResult pageAll(ExpertPageBo expertPageBo);

    /**
     * 分页查询
     *
     * @param boolQueryBuilder
     * @param pageNum
     * @param pageSize
     * @return
     */
    EsPageResult pageQuery(BoolQueryBuilder boolQueryBuilder, int pageNum, int pageSize);

    /**
     * 按(职称维度)统计人数
     *
     * @param personStatsBo
     * @param auth
     * @return
     */
    PieVO getProfTitleDistribution(PersonStatsBo personStatsBo, boolean auth);

    /**
     * 按(人才等级)统计人数
     *
     * @param statsBo
     * @param auth
     * @return
     */
    Map<String, Long> getRankDistribution(PersonStatsBo statsBo, boolean auth);

    Long countByLevel(PersonStatsBo statsBo, boolean auth);

    /**
     * 按产业链统计人才数量（包含本地和市外）
     *
     * @param chainId
     * @param nodeIds
     * @param local
     * @return
     */
    Map<String, Long> getNodeNumMap(String chainId, List<String> nodeIds, boolean local);

    /**
     * 链洞察（人才链）-统计人员等级和职称分布（包含本地和科特派）
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    List<CommonIndexBO> getExpertIndex4Insight(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 统计人才数量
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param onlyTechCommissioner 是否仅统计科特派
     * @return
     */
    Long countExpertNum(String chainId, String nodeId, String cityId, String areaId, boolean onlyTechCommissioner);

    /**
     * 统计硕士以上学历人数
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param onlyTechCommissioner 是否仅统计科特派人员数量
     * @return
     */
    Long countMasterPlusNum(String chainId, String nodeId, String cityId, String areaId, boolean onlyTechCommissioner);

    /**
     * 统计正高人数
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    Long countSeniorNum(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 按学历统计人数
     *
     * @param personStatsBo
     * @param auth
     * @return
     */
    PieVO getDegreeDistribution(PersonStatsBo personStatsBo, boolean auth);

    /**
     * 驾驶舱各产业链人才分布（人才数量，硕士以上， C类以上）
     *
     * @param regionId
     * @return
     */
    Map<String, Map<String, Long>> getPortalIndustryDistribution(String regionId);

    /**
     * 统计本科以上人数
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    Long undergraduatePlusNum(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 按产业链统计专家人数
     *
     * @param personStatsBo
     * @param auth
     * @return
     */
    List<CommonIndexBO> getDistributionInChain(PersonStatsBo personStatsBo, boolean auth);

    /**
     * 分页查询链上专家，按职称从高到底排序
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    EsPageResult pageOrderByTitle(int pageNum, int pageSize);

    /**
     * 按不同人才类型统计各省份数量（前五详细，其余计入其他）
     *
     * @param type
     * @return
     */
    List<CommonIndexBO> getProvinceDistribution(Integer type);

    /**
     * 根据人才id查询
     *
     * @param ids
     * @param includes
     * @return
     */
    List<Map<String, Object>> listExpertByIds(List<String> ids, String... includes);

    /**
     * 服务侧专家用户详情
     *
     * @param expertId
     * @return
     */
    Map<String, Object> getInfoById(String expertId);

    /**
     * 判断科特派专家是否在链上
     *
     * @param id
     * @return
     */
    boolean isCommissionerInChain(String id);

    /**
     * 按区县统计人才数量
     *
     * @param chainId
     * @return
     */
    Map<String, Long> countLocalNumByRegion(String chainId);

    /**
     * 统计本地人才在节点上的分布
     *
     * @param chainId
     * @return
     */
    Map<String, Object> countLocalNumByNode(String chainId);

    /**
     * 统计外部人才在节点上的分布（硕士以上，E类以上，高职称）
     *
     * @param chainId
     * @return
     */
    Map<String, Long> countExternalNumByNode(String chainId);

    /**
     * 统计目标产业链外部专家数
     *
     * @param chainId
     * @return
     */
    Long countExternalNumByChainId(String chainId);

    /**
     * 按指定的id推荐专家，并按分数排序
     *
     * @param demandId
     * @param ids
     * @return
     */
    List<Map<String, Object>> recommendByIds(String demandId, List<String> ids);
}
