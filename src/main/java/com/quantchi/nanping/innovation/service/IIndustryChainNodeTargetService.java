package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.IndustryChainNodeTarget;
import com.quantchi.nanping.innovation.model.bo.CompanyPageBo;
import com.quantchi.tianying.model.EsPageResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/11 14:46
 */
public interface IIndustryChainNodeTargetService extends IService<IndustryChainNodeTarget> {
    /**
     * 查询指定节点id对应的靶向企业
     *
     * @param nodeIds
     * @return
     */
    List<IndustryChainNodeTarget> listByNodeIds(List<String> nodeIds);

    /**
     * 查询指定节点id对应的靶向企业
     *
     * @param pageBo
     * @param nodeIds
     * @return
     */
    EsPageResult pageByNodeIds(CompanyPageBo pageBo, List<String> nodeIds);
}
