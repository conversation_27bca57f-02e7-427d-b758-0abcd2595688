package com.quantchi.nanping.innovation.service.impl;

import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.overcome.service.OvercomeService;
import com.quantchi.nanping.innovation.service.*;
import com.quantchi.nanping.innovation.service.library.AchievementService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.nanping.innovation.utils.RequestContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 科技招商服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TechInvestAttractServiceImpl implements ITechInvestAttractService {

    private final IIndexFusionService indexFusionService;
    private final IndustryChainService industryChainService;
    private final CompanyService companyService;
    private final TalentService talentService;
    private final AchievementService achievementService;
    private final PatentService patentService;
    private final OvercomeService overcomeService;

    @Override
    public Map<String, Object> analyzeInnovationChain(String chainId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        String cityId = RequestContext.getCityId(), areaId = RequestContext.getAreaId();
        // 整体情况
        List<CommonIndexBO> overviewBoList = new ArrayList<>();
        // 1.创新得分
        overviewBoList.add(new CommonIndexBO("创新链指数", indexFusionService.getOne(FusionIndexEnum.INNOVATION_CHAIN.getIndexId(), chainId,
                null, RequestContext.getRegionId(), true).getData(), "分"));
        // 2.链上节点
        overviewBoList.add(new CommonIndexBO("链上节点", industryChainService.countNodes(chainId), "个"));
        // 3.链上企业
        overviewBoList.add(new CommonIndexBO("链上企业", companyService.countNumInChain(chainId, null, cityId, areaId, false), "家"));
        // 4.链上人才
        overviewBoList.add(new CommonIndexBO("链上人才", talentService.countExpertNum(chainId, null, cityId, areaId, false), "人"));
        // 5.链上成果
        overviewBoList.add(new CommonIndexBO("链上成果", achievementService.countByChain(chainId, null), "项"));
        // 6.链上专利
        overviewBoList.add(new CommonIndexBO("链上专利", patentService.countPatentNumByChainIdAndRegionId(chainId, null, cityId, areaId), "项"));
        resultMap.put("overview", overviewBoList);
        // 薄弱节点分析
        resultMap.put("weaknesses", industryChainService.listWeakNodesAnalysisByChainId(chainId));
        return resultMap;
    }

    @Override
    public List<CommonIndexBO> getRiskMap(String chainId) {
        Set<String> riskIdSet = null;
        if (!"1003".equals(chainId)){
            List<IndustryChainNodeWeak> weakList = industryChainService.listWeakNodesByChainId(chainId);
            riskIdSet = weakList.stream().map(IndustryChainNodeWeak::getDataId).collect(Collectors.toSet());
        }
        return overcomeService.getRiskMap(chainId, riskIdSet);
    }
}
