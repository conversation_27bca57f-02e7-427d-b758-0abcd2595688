package com.quantchi.nanping.innovation.service;

import com.quantchi.nanping.innovation.model.UserInfoEntity;

/**
 * 掌上南平相关接口服务
 *
 * <AUTHOR>
 * @date 2023/7/3 15:11
 */
public interface IZSNanPingService {

    /**
     * 根据授权码获得access_token
     *
     * @param code
     * @param isPC 是否为PC端
     * @return
     */
    String getAccessTokenByCode(String code, boolean isPC);

    /**
     * 根据授权码获得access_token
     *
     * @param accessToken
     * @return
     */
    UserInfoEntity getUserInfoByAccessToken(String accessToken);

}
