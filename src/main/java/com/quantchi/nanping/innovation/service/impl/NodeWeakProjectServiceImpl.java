package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeWeakProjectDAO;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeakProject;
import com.quantchi.nanping.innovation.service.INodeWeakProjectService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/13 9:27
 */
@Service
public class NodeWeakProjectServiceImpl extends ServiceImpl<IndustryChainNodeWeakProjectDAO, IndustryChainNodeWeakProject> implements INodeWeakProjectService {
    @Override
    public List<Map<String, Object>> listByWeakDataId(String weakId) {
        // 相关项目模块低于二十万的不展示，排序按照资金额度从大到小排序
        return this.listMaps(Wrappers.lambdaQuery(IndustryChainNodeWeakProject.class)
                .eq(IndustryChainNodeWeakProject::getWeakDataId, weakId)
                .ge(IndustryChainNodeWeakProject::getFundLimitAmount, 20)
                .orderByDesc(IndustryChainNodeWeakProject::getFundLimitAmount));
    }
}
