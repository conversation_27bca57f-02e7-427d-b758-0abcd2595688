package com.quantchi.nanping.innovation.service;

import com.quantchi.nanping.innovation.model.vo.PieVO;

import java.util.Map;

/**
 * 驾驶舱服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-18
 */
public interface ICockpitService {

    /**
     * 产业链-链上企业分布
     *
     * @param chainId 产业链ID
     * @param regionId 区域ID
     * @return 企业分布数据
     */
    Map<String, Object> industryDetail(String chainId, String regionId);

    /**
     * 产业链-链节点分布-圆环图统计
     *
     * @param chainId 产业链ID
     * @return 节点分布数据
     */
    PieVO getNodeDistribution(String chainId);

    /**
     * 指标详情
     *
     * @param chainId 产业链ID
     * @param regionId 区域ID
     * @return 指标详情数据
     */
    Map<String, Object> indexDetail(String chainId, String regionId);
}
