package com.quantchi.nanping.innovation.service.impl;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kind.crypto.common.ConfigBean;
import com.quantchi.common.core.utils.StringUtils;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.dao.mapper.LoginLogMapper;
import com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO;
import com.quantchi.nanping.innovation.dao.mapper.UserInfoDAO;
import com.quantchi.nanping.innovation.dao.mapper.admin.SysMenuMapper;
import com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMapper;
import com.quantchi.nanping.innovation.dao.mapper.admin.SysRoleMenuMapper;
import com.quantchi.nanping.innovation.dao.mapper.admin.SysUserRoleMapper;
import com.quantchi.nanping.innovation.model.*;
import com.quantchi.nanping.innovation.model.admin.SysMenu;
import com.quantchi.nanping.innovation.model.admin.SysRole;
import com.quantchi.nanping.innovation.model.admin.SysRoleMenu;
import com.quantchi.nanping.innovation.model.admin.SysUserRole;
import com.quantchi.nanping.innovation.model.bo.CompanyPageBo;
import com.quantchi.nanping.innovation.model.bo.UserPageBo;
import com.quantchi.nanping.innovation.model.vo.EnterInfoVO;
import com.quantchi.nanping.innovation.service.ITechCommissionerService;
import com.quantchi.nanping.innovation.service.IZSNanPingService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.nanping.innovation.utils.*;
import com.quantchi.tianying.model.EsPageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;


/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class SysLoginService {

    @Resource
    private final UserInfoDAO userInfoDAO;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private StringRedisCache stringRedisCache;

    @Autowired
    private IZSNanPingService zsNanPingService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private ITechCommissionerService techCommissionerService;

    @Autowired
    private LoginLogMapper loginLogMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private ICompanyNodeRelationService companyNodeRelationService;

    @Autowired
    private TestCompanyMappingDAO testCompanyMappingDAO;

    @Autowired
    private MZTServiceImpl mztService;

    @Value("${user.password.valid_days}")
    private Integer passwordValidDays;

    @Value("${user.never_logout.id}")
    private String neverLogoutUserId;

    @Value("${user.admin_id}")
    private String adminUserId;

    private static final String FREEZE_PREFIX = "freeze_";
    private static final String FAIL_PREFIX = "fail_";

    /**
     * 密码类型
     */
    private static final String TYPE_LOGIN_PASSWORD = "登录密码";
    private static final String TYPE_OPERATE_PASSWORD = "操作密码";

    /**
     * 登录
     *
     * @param loginBody
     * @return
     * @throws Exception
     */
    public Result validLogin(LoginBody loginBody) throws Exception {
        // 判断账号是否处于登录冻结中
        String freezeKey = FREEZE_PREFIX + loginBody.getAccount();
        final String freezeInfo = stringRedisCache.get(freezeKey);
        if (freezeInfo != null) {
            return ResultConvert.error(500, "账户冻结中，请10分钟后重试");
        }
        // 判断验证码是否正确
        final String code = stringRedisCache.get(loginBody.getUid());
        if (StringUtils.isEmpty(code)) {
            return ResultConvert.error(500, "验证码已过期，请重新输入");
        }
        if (!StrUtil.equalsIgnoreCase(code, loginBody.getCaptcha())) {
            return ResultConvert.error(500, "验证码不正确");
        }
        // 判断账户名是否存在
        final UserInfoEntity accountInfo =
                userInfoDAO.selectOne(Wrappers.lambdaQuery(UserInfoEntity.class)
                        .eq(UserInfoEntity::getAccount, AESUtil.decrypt(loginBody.getAccount()))
                        .eq(UserInfoEntity::getPlatformType, loginBody.getPlatformType()));
        if (accountInfo == null) {
            return ResultConvert.error(500, "当前账号不存在");
        }
        if (UserInfoEntity.DISABLE.equals(accountInfo.getStatus())) {
            return ResultConvert.error(500, "当前账号已被停用，请联系管理员");
        }
        final UserInfoEntity loginInfo;
        if (SpringUtils.getActiveProfile().startsWith("prod")) {
            loginInfo = userInfoDAO.getUserInfoByAccountAndPassword(AESUtil.decrypt(loginBody.getAccount()),
                    EncryptUtil.encryptSM4FixIndex(SecureUtil.md5(AESUtil.decrypt(loginBody.getPassword())),
                            accountInfo.getEncryptIndex()), loginBody.getPlatformType());
        } else {
            loginInfo = accountInfo;
        }
        String failKey = FAIL_PREFIX + loginBody.getAccount();
        if (null != loginInfo) {
            stringRedisCache.remove(failKey);
            StpUtil.login(loginInfo.getId());
            recordLoginLog(loginInfo.getId(), "登录成功");
            return ResultConvert.success(StpUtil.getTokenInfo());
        }
        // 账户密码错误
        // 记录用户密码输入错误次数
        return recordFailLogin(accountInfo.getId(), accountInfo.getAccount(), failKey, freezeKey, TYPE_LOGIN_PASSWORD);
    }

    private Result recordFailLogin(String userId, String accountName, String failKey, String freezeKey, String passwordType) {
        String recordKey = "record_" + accountName;
        if (!stringRedisCache.lock(recordKey, "1")) {
            return ResultConvert.error(500, "登录太过频繁，请稍后重试");
        }
        try {
            int failTimes = Integer.parseInt(stringRedisCache.get(failKey) == null ? "0" : stringRedisCache.get(failKey)) + 1;
            if (failTimes >= 5) {
                // 次数达到上线，冻结账户
                stringRedisCache.put(freezeKey, "freeze", 10, TimeUnit.MINUTES);
                stringRedisCache.remove(failKey);
                recordLoginLog(userId, "登录失败，" + passwordType + "输入错误次数达到上限");
                return ResultConvert.error(500, passwordType + "输入错误次数达到上限，账号被冻结，请10分钟后重试");
            } else {
                // 记录累计错误次数
                stringRedisCache.put(failKey, String.valueOf(failTimes));
                recordLoginLog(userId, "登录失败，" + passwordType + "输入错误" + failTimes + "次");
                return ResultConvert.error(500, passwordType + "输入错误" + failTimes + "次，请重试");
            }
        } catch (Exception e) {
            log.error("登录异常：", e);
            recordLoginLog(userId, "服务异常");
            return ResultConvert.error(500, "服务异常，请重新登录");
        } finally {
            stringRedisCache.unLock(recordKey, "1");
        }
    }

    /**
     * 登录
     *
     * @param loginBody
     * @return
     * @throws Exception
     */
    public Result validLogin1(LoginBody loginBody) throws Exception {
        // 判断账号是否处于登录冻结中
        String freezeKey = FREEZE_PREFIX + loginBody.getAccount();
        final String freezeInfo = stringRedisCache.get(freezeKey);
        if (freezeInfo != null) {
            return ResultConvert.error(500, "账户冻结中，请10分钟后重试");
        }
        // 判断验证码是否正确
        final String code = stringRedisCache.get(loginBody.getUid());
        if (StringUtils.isEmpty(code)) {
            return ResultConvert.error(500, "验证码已过期，请重新输入");
        }
        if (!StrUtil.equalsIgnoreCase(code, loginBody.getCaptcha())) {
            return ResultConvert.error(500, "验证码不正确");
        } else {
            stringRedisCache.remove(loginBody.getUid());
        }
        // 判断账户名是否存在
        final UserInfoEntity accountInfo = findUser(AESUtil.decrypt(loginBody.getAccount()), null);
        if (accountInfo == null) {
            return ResultConvert.error(500, "当前账号不存在");
        }
        if (UserInfoEntity.DISABLE.equals(accountInfo.getStatus())) {
            return ResultConvert.error(500, "当前账号已被停用，请联系管理员");
        }
        // 判断网络地址是否合法
        if (StringUtils.isNotEmpty(accountInfo.getLimitedAddress())
                && !accountInfo.getLimitedAddress().equals(loginBody.getIp())) {
            return ResultConvert.error(500, "当前账号登录IP异常，禁止登录");
        }
        final UserInfoEntity loginInfo =
                userInfoDAO.getUserInfoByAccountAndPassword(AESUtil.decrypt(loginBody.getAccount()),
                        EncryptUtil.encryptSM4FixIndex(SecureUtil.md5(AESUtil.decrypt(loginBody.getPassword())), accountInfo.getEncryptIndex()), loginBody.getPlatformType());
        String failKey = FAIL_PREFIX + loginBody.getAccount();
        if (null != loginInfo) {
            // 判断密码是否过期
            if (isPasswordExpire(loginInfo)) {
                return ResultConvert.error(500, "当前账号密码已过期，请立即修改");
            }
            stringRedisCache.remove(failKey);
            String firstValidKey = UUID.randomUUID().toString();
            stringRedisCache.put(firstValidKey, loginInfo.getId(), 10, TimeUnit.MINUTES);
            recordLoginLog(loginInfo.getId(), "登录密码验证成功");
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("validKey", firstValidKey);
            return ResultConvert.success(resultMap);
        }
        if (loginInfo == null && SpringUtils.getActiveProfile().equals("testk8s")) {
            stringRedisCache.remove(failKey);
            String firstValidKey = UUID.randomUUID().toString();
            stringRedisCache.put(firstValidKey, accountInfo.getId(), 10, TimeUnit.MINUTES);
            recordLoginLog(accountInfo.getId(), "测试环境登录密码验证成功");
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("validKey", firstValidKey);
            return ResultConvert.success(resultMap);
        }
        // 账户密码错误
        // 记录用户密码输入错误次数
        return recordFailLogin(accountInfo.getId(), accountInfo.getAccount(), failKey, freezeKey, TYPE_LOGIN_PASSWORD);
    }

    /**
     * CA认证登录
     *
     * @param loginBody
     * @return
     */
    public Result validLogin(CALoginBody loginBody) {
        // 计算证书序列号
        String serialNum = SignUtil.verifySignedDataByWeb(loginBody.getRandom(), loginBody.getSign(), loginBody.getCert());
        if (StringUtils.isEmpty(serialNum)) {
            return ResultConvert.error(500, "认证失败，请更换证书");
        }
        // 根据序列号查询用户
        final UserInfoEntity loginInfo = userInfoDAO.selectOne(Wrappers.lambdaQuery(UserInfoEntity.class)
                .eq(UserInfoEntity::getCaNo, serialNum));
        if (loginInfo == null) {
            return ResultConvert.error(500, "认证失败，请联系管理员绑定证书");
        }
        // 验签
        if (SpringUtils.getActiveProfile().startsWith("prod") && !SignUtil.verifySignedData(loginInfo.getSign(), loginInfo.generateData2Sign(), loginInfo.getSignCert())) {
            return ResultConvert.error(500, "当前账号信息被篡改，禁止登录");
        }
        // 校验权限完整性
        getPermittedMenus(loginInfo.getId(), true);
        StpUtil.login(loginInfo.getId());
        recordLoginLog(loginInfo.getId(), "CA登录成功");
        return ResultConvert.success(StpUtil.getTokenInfo());
    }

    public UserInfoEntity getUserById(String userId, boolean usePhone) {
        UserInfoEntity userInfo = userInfoDAO.selectById(userId);
        if (usePhone) {
            // 解密手机号
            userInfo.setPhone(EncryptUtil.decryptSM4FixIndex(userInfo.getPhone(), userInfo.getEncryptIndex()));
        }
        return userInfo;
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    public UserInfoEntity getUserInfo(boolean usePhone) {
        if (StpUtil.isLogin()) {
            if (StpUtil.getLoginIdAsString().startsWith("wechat")) {
                UserInfoEntity userInfo = new UserInfoEntity();
                userInfo.setUsername("微信小程序用户");
                userInfo.setPlatformType(UserInfoEntity.PLATFORM_USER);
                userInfo.setUserType(UserInfoEntity.USER_PERSON);
                userInfo.setId(StpUtil.getLoginIdAsString());
                return userInfo;
            }
            final String userId = StpUtil.getLoginIdAsString();
            UserInfoEntity userInfo = getUserById(userId, usePhone);
            // 解决加密后access失效问题
            userInfo.setPassword(null);
            //userInfo.setPhone(null);
            if (1 == userInfo.getPlatformType()) {
                String entityId = null;
                if (UserInfoEntity.USER_COMPANY == userInfo.getUserType()) {
                    String companyId = companyService.getCompanyIdByCreditCode(userInfo.getSocialCreditCode());
                    entityId = companyId;
                    userInfo.setInChain(StringUtils.isNotBlank(companyId));
                    userInfo.setCompanyId(StringUtils.isBlank(companyId) ? userInfo.getSocialCreditCode() : companyId);
                } else {
                    String expertId = techCommissionerService.getIdByPhone(userInfo.getPhone());
                    entityId = expertId;
                    if (StringUtils.isEmpty(expertId)) {
                        userInfo.setInChain(false);
                        userInfo.setExpertId(userId);
                    } else {
                        userInfo.setExpertId(expertId);
                        userInfo.setInChain(talentService.isCommissionerInChain(expertId));
                    }
                }
                userInfo.setUsername(userInfo.getAccount());
                // 获取服务侧用户所在的链
                // 查询企业所在的链
                Pair<Set<String>, Set<String>> chainInfo = companyNodeRelationService.getRelatedNodeIdsByEntityId(entityId,
                        UserInfoEntity.USER_COMPANY == userInfo.getUserType(), UserInfoEntity.USER_EXPERT == userInfo.getUserType());
                Set<String> chainIds = chainInfo.getLeft();
                userInfo.setInChainIds(new ArrayList<>(chainIds));
            } else {
                userInfo.setInChain(true);
                // 计算菜单权限
                userInfo.setMenuMap(getPermittedMenus(userInfo.getId(), false));
            }
            return userInfo;
        }
        throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
    }

    public Map<String, Boolean> getPermittedMenus(final String userId, boolean authSign) {
        final List<SysMenu> sysMenus = menuMapper.selectList(Wrappers.lambdaQuery(SysMenu.class)
                .eq(SysMenu::getStatus, 0)
                .isNotNull(SysMenu::getParentId));
        // 收集现有数据的签名
        Map<String, Pair<String, String>> existedSignMap = new HashMap<>();
        // 菜单收集签名
        for (SysMenu sysMenu : sysMenus) {
            existedSignMap.put(sysMenu.getSign(), new ImmutablePair<>(sysMenu.generateData2Sign(), sysMenu.getSignCert()));
        }
        List<Long> menuIdsPermitted = new ArrayList<>();
        final List<SysUserRole> sysUserRoles = userRoleMapper.selectList(Wrappers.<SysUserRole>lambdaQuery()
                .eq(SysUserRole::getUserId, userId));
        // 用户角色收集签名
        for (SysUserRole userRole : sysUserRoles) {
            existedSignMap.put(userRole.getSign(), new ImmutablePair<>(userRole.generateData2Sign(), userRole.getSignCert()));
        }
        // 获取角色对应的菜单id，检查每个菜单id是否有父级菜单，不在其中的话需要一并返回
        if (CollUtil.isNotEmpty(sysUserRoles)) {
            List<Long> roleIds = sysUserRoles.stream().map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());
            // 检查是否有禁用的角色
            final List<SysRole> sysRoles = roleMapper.selectBatchIds(roleIds);
            roleIds = sysRoles.stream().filter(item -> Objects.equals(item.getStatus(), "0"))
                    .map(SysRole::getRoleId).collect(Collectors.toList());
            if (CollUtil.isEmpty(roleIds)) {
                return new HashMap<>(0);
            }
            final List<SysRoleMenu> sysRoleMenus = roleMenuMapper.selectList(Wrappers.<SysRoleMenu>lambdaQuery()
                    .in(SysRoleMenu::getRoleId, roleIds));
            // 角色菜单关系收集签名
            for (SysRoleMenu roleMenu : sysRoleMenus) {
                existedSignMap.put(roleMenu.getSign(), new ImmutablePair<>(roleMenu.generateData2Sign(), roleMenu.getSignCert()));
            }

            final Set<Long> menuIds = sysRoleMenus.stream().map(SysRoleMenu::getMenuId).collect(Collectors.toSet());
            // 构建菜单id与父级菜单id的映射
            final Map<Long, Long> parentIdMap = sysMenus.stream().collect(Collectors.toMap(SysMenu::getMenuId, SysMenu::getParentId));
            final Set<Long> parentMenuIds = new HashSet<>();
            menuIds.forEach(menuId -> {
                Long parentId = parentIdMap.get(menuId);
                while (parentId != null && !menuIds.contains(parentId) && !parentMenuIds.contains(parentId)) {
                    parentMenuIds.add(parentId);
                }
            });
            menuIdsPermitted.addAll(parentMenuIds);
            menuIdsPermitted.addAll(menuIds);
        }
        // 验签
        if (authSign && SpringUtils.getActiveProfile().startsWith("prod") && !SignUtil.batchVerifySignedData(existedSignMap)) {
            log.error("该用户：{}数据被篡改，不允许登录", StpUtil.getLoginIdAsString());
            throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
        }
        Map<String, Boolean> menuMap = new HashMap<>();
        for (SysMenu menu : sysMenus) {
            menuMap.put(menu.getPath(), menuIdsPermitted.contains(menu.getMenuId()));
        }
        return menuMap;
    }

    /**
     * 注销登录
     */
    public void logout() {
        try {
            StpUtil.logout();
        } catch (NotLoginException e) {
        }
    }

    public Page<EnterInfoVO> page4Enterprise(UserPageBo bo) {
        CompanyPageBo companyBo = new CompanyPageBo();
        companyBo.setPageNum(bo.getPageNum());
        companyBo.setPageSize(bo.getPageSize());
        companyBo.setCompanyName(bo.getKey());
        EsPageResult pageResult = companyService.getCompanyList(companyBo);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
            return new Page<>();
        }
        Page<EnterInfoVO> page = new Page<>();
        List<EnterInfoVO> enterList = new ArrayList<>();
        for (Map<String, Object> enterInfo : pageResult.getList()) {
            EnterInfoVO entity = new EnterInfoVO();
            entity.setUserName((String) enterInfo.get("name"));
            entity.setId((String) enterInfo.get("social_credit_code"));
            entity.setCompanyId((String) enterInfo.get("id"));
            enterList.add(entity);
        }
        page.setRecords(enterList);
        page.setTotal(pageResult.getTotal());
        page.setCurrent(companyBo.getPageNum());
        page.setPages(page.getTotal() / companyBo.getPageSize() + 1);
        return page;
    }

    public Result<SaTokenInfo> validLoginByCode(String code, boolean isPC) {
        log.error("掌上南平参数code:{},isPc:{}", code, isPC);
        String accessToken = zsNanPingService.getAccessTokenByCode(code, isPC);
        UserInfoEntity userInfo = zsNanPingService.getUserInfoByAccessToken(accessToken);

        userInfo.setPlatform("zsnp");

        saveUserInfo(userInfo, accessToken);

        recordLoginLog(userInfo.getId(), "掌上南平登录成功");
        return ResultConvert.success(StpUtil.getTokenInfo());
    }

    /**
     * 保存用户信息
     */
    private void saveUserInfo(UserInfoEntity userInfo, String accessToken) {
        // 保存用户基本信息
        UserInfoEntity existed = userInfoDAO.selectById(userInfo.getId());
        if (existed == null) {
            // 加密手机号
            userInfo.setEncryptIndex(ConfigBean.getIntVal("kind.common.encrypt.keyIndex"));
            userInfo.setPhone(EncryptUtil.encryptSM4FixIndex(userInfo.getPhone(), userInfo.getEncryptIndex()));
            // 重新生成签名
            Pair<String, String> signResult = SignUtil.signData(userInfo.generateData2Sign());
            userInfo.setSign(signResult.getLeft());
            userInfo.setSignCert(signResult.getRight());
            userInfoDAO.insert(userInfo);
        } else {
            // 验签
//            if (SpringUtils.getActiveProfile().startsWith("prod") && !SignUtil.verifySignedData(existed.getSign(), existed.generateData2Sign(), existed.getSignCert())) {
//                return ResultConvert.error(500, "当前账号信息被篡改，禁止登录");
//            }
            existed.setAccount(userInfo.getAccount());
            existed.setSocialCreditCode(userInfo.getSocialCreditCode());
            existed.setUserType(userInfo.getUserType());
            // 加密手机号
            existed.setEncryptIndex(ConfigBean.getIntVal("kind.common.encrypt.keyIndex"));
            existed.setPhone(EncryptUtil.encryptSM4FixIndex(userInfo.getPhone(), existed.getEncryptIndex()));
            // 重新生成签名
            Pair<String, String> signResult = SignUtil.signData(existed.generateData2Sign());
            existed.setSign(signResult.getLeft());
            existed.setSignCert(signResult.getRight());
            userInfoDAO.updateById(existed);
        }
        // 测试账号增加临时有效期
        if ("913507845673247430".equals(userInfo.getSocialCreditCode())) {
            StpUtil.login(existed == null ? userInfo.getId() : existed.getId(),
                    new SaLoginModel().setToken(accessToken).setTimeout(86400).setActiveTimeout(7200));
        } else {
            // 有效期为1天
            StpUtil.login(existed == null ? userInfo.getId() : existed.getId(),
                    new SaLoginModel().setToken(accessToken).setTimeout(60 * 60 * 24));
        }
    }

    /**
     * 查询当前用户的唯一标识
     *
     * @return
     */
    public String findCollectEntityId() {
        UserInfoEntity userInfo = getUserInfo(false);
        return UserInfoEntity.USER_COMPANY == userInfo.getUserType() ?
                userInfo.getSocialCreditCode() : userInfo.getId();
    }

    private void recordLoginLog(String userId, String message) {
        LoginLog log = new LoginLog();
        log.setUserId(userId);
        log.setMessage(message);
        log.setLoginIp(ServletUtils.getClientIP());
        Pair<String, String> signResult = SignUtil.signData(log.generateData2Sign());
        log.setSign(signResult.getLeft());
        log.setSignCert(signResult.getRight());
        loginLogMapper.insert(log);
    }

    /**
     * 二次验证
     *
     * @param form
     * @return
     */
    public Result validLogin2(LoginBody form) {
        // 判断验证码是否正确
        final String code = stringRedisCache.get(form.getUid());
        if (StringUtils.isEmpty(code)) {
            return ResultConvert.error(500, "验证码已过期，请重新输入");
        }
        if (!StrUtil.equalsIgnoreCase(code, form.getCaptcha())) {
            return ResultConvert.error(500, "验证码不正确");
        } else {
            stringRedisCache.remove(form.getUid());
        }
        // 判断首次是否验证成功
        String loginId = stringRedisCache.get(form.getFirstValidKey());
        if (loginId == null) {
            return ResultConvert.error(500, "首次验证已失效，请重新登录");
        }
        // 判断账户是否存在
        final UserInfoEntity accountInfo = findUser(AESUtil.decrypt(form.getAccount()), loginId);
        // 输入账户名和loginid是否一致
        if (accountInfo == null) {
            return ResultConvert.error(500, "用户验证信息不正确");
        }
        // 判断是否冻结
        String freezeKey = FREEZE_PREFIX + accountInfo.getAccount();
        final String freezeInfo = stringRedisCache.get(freezeKey);
        if (freezeInfo != null) {
            return ResultConvert.error(500, "账户冻结中，请10分钟后重试");
        }
        if (UserInfoEntity.DISABLE.equals(accountInfo.getStatus())) {
            return ResultConvert.error(500, "当前账号已被停用，请联系管理员");
        }
        String currentPassword = EncryptUtil.encryptSM4FixIndex(AESUtil.decrypt(form.getPassword()), accountInfo.getEncryptIndex());
        String failKey = FAIL_PREFIX + accountInfo.getAccount();
        if (currentPassword.equals(accountInfo.getOperatePassword()) || SpringUtils.getActiveProfile().equals("testk8s")) {
            stringRedisCache.remove(failKey);
            if (StringUtils.isNotEmpty(accountInfo.getLoginTimeOut())) {
                String[] timeOutParams = accountInfo.getLoginTimeOut().split(",");
                StpUtil.login(loginId, new SaLoginModel().setTimeout(Long.parseLong(timeOutParams[0])).setActiveTimeout(Long.parseLong(timeOutParams[1])));
            } else if (neverLogoutUserId.equals(loginId)) {
                StpUtil.login(loginId, new SaLoginModel().setTimeout(-1).setActiveTimeout(-1));
            } else if (adminUserId.equals(loginId)) {
                StpUtil.login(loginId, new SaLoginModel().setTimeout(86400).setActiveTimeout(7200));
            } else if ("1800367682904944641".equals(loginId)) {
                StpUtil.login(loginId, new SaLoginModel().setTimeout(86400).setActiveTimeout(86400));
            } else {
                StpUtil.login(loginId);
            }
            recordLoginLog(loginId, "操作密码验证成功");
            return ResultConvert.success(StpUtil.getTokenInfo());
        }
        // 账户密码错误
        // 记录用户密码输入错误次数
        return recordFailLogin(accountInfo.getId(), accountInfo.getAccount(), failKey, freezeKey, TYPE_OPERATE_PASSWORD);
    }

    private UserInfoEntity findUser(String account, String loginId) {
        if (StringUtils.isEmpty(account) && StringUtils.isEmpty(loginId)) {
            return null;
        }
        return userInfoDAO.selectOne(Wrappers.lambdaQuery(UserInfoEntity.class)
                .eq(StringUtils.isNotEmpty(loginId), UserInfoEntity::getId, loginId)
                .eq(StringUtils.isNotEmpty(account), UserInfoEntity::getAccount, account)
                .eq(UserInfoEntity::getPlatformType, UserInfoEntity.PLATFORM_GOV));
    }

    /**
     * 判断密码是否过期
     *
     * @return
     */
    private boolean isPasswordExpire(UserInfoEntity userInfo) {
        if (userInfo.getUpdatePasswordTime() == null) {
            return false;
        }
        LocalDate lastUpdateDate = userInfo.getUpdatePasswordTime().toLocalDate();
        LocalDate nowDate = LocalDate.now();
        if (lastUpdateDate.plusDays(passwordValidDays).compareTo(nowDate) < 0) {
            return true;
        }
        return false;
    }

    /**
     * 修改密码
     *
     * @param passwordBody
     * @return
     */
    public Result modifyPassword(PasswordBody passwordBody) {
        // 验证账户是否存在
        String userName = AESUtil.decrypt(passwordBody.getUsername());
        final UserInfoEntity user = findUser(userName, null);
        if (user == null) {
            return ResultConvert.error(500, "当前账号不存在");
        }
        if (UserInfoEntity.DISABLE.equals(user.getStatus())) {
            return ResultConvert.error(500, "当前账号已被停用，请联系管理员");
        }
        if (passwordBody.getProcedureNum() >= 2) {
            String oldPassword = AESUtil.decrypt(passwordBody.getOldPassword()), newPassword = AESUtil.decrypt(passwordBody.getNewPassword());
            // 验证登录密码是否正确
            checkModifyPassword(userName,
                    () -> !SecureUtil.md5(oldPassword).equals(EncryptUtil.decryptSM4FixIndex(user.getPassword(), user.getEncryptIndex())), TYPE_LOGIN_PASSWORD);
            // 验证新旧密码是否一致
            if (StringUtils.equals(oldPassword, newPassword)) {
                return ResultConvert.error(500, "新旧密码不能相同");
            }
            if (passwordBody.getProcedureNum() >= 3) {
                String oldOperatePassword = AESUtil.decrypt(passwordBody.getOldOperatePassword()),
                        newOperatePassword = AESUtil.decrypt(passwordBody.getNewOperatePassword());
                // 验证操作密码是否正确，若正确，则修改密码
                checkModifyPassword(userName,
                        () -> !oldOperatePassword.equals(EncryptUtil.decryptSM4FixIndex(user.getOperatePassword(), user.getEncryptIndex())), TYPE_OPERATE_PASSWORD);
                // 验证新旧密码是否一致
                if (StringUtils.equals(oldOperatePassword, newOperatePassword)) {
                    return ResultConvert.error(500, "新旧操作密码不能相同");
                }
                user.setPassword(EncryptUtil.encryptSM4FixIndex(SecureUtil.md5(newPassword), user.getEncryptIndex()));
                user.setOperatePassword(EncryptUtil.encryptSM4FixIndex(newOperatePassword, user.getEncryptIndex()));
                user.setUpdatePasswordTime(LocalDateTime.now());
                userInfoDAO.updateById(user);
                StpUtil.logout(user.getId());
                return ResultConvert.success("密码修改成功");
            } else {
                return ResultConvert.success("登录密码验证成功");
            }
        } else {
            return ResultConvert.success("账号验证成功");
        }
    }

    /**
     * 修改密码，校验原密码
     */
    private void checkModifyPassword(final String username, final Supplier<Boolean> supplier, final String passwordType) {
        final String errorKey = "pwd_err_modify_cnt:" + username;
        // 获取用户错误次数
        String errorNumberInRedis = stringRedisCache.get(errorKey);
        Integer errorNumber = StringUtils.isEmpty(errorNumberInRedis) ? null : Integer.parseInt(errorNumberInRedis);
        final Integer retryCountLimit = 5;
        // 锁定时间内登录 则踢出
        if (ObjectUtil.isNotNull(errorNumber) && errorNumber.equals(5)) {
            throw new BusinessException("原" + passwordType + "输入错误次数达到上限，请1天后重试，或联系管理员进行重置");
        }
        if (supplier.get()) {
            // 是否第一次
            errorNumber = ObjectUtil.isNull(errorNumber) ? 1 : errorNumber + 1;
            // 达到规定错误次数 则锁定修改
            if (errorNumber.equals(5)) {
                stringRedisCache.put(errorKey, String.valueOf(errorNumber), 1, TimeUnit.DAYS);
                throw new BusinessException("原" + passwordType + "输入错误次数达到" + retryCountLimit + "次，请1天后重试，或联系管理员进行重置");
            } else {
                // 未达到规定错误次数 则递增
                stringRedisCache.put(errorKey, String.valueOf(errorNumber));
                throw new BusinessException("原" + passwordType + "第" + errorNumber + "次输入错误,请重试");
            }
        }
        // 登录成功 清空错误次数
        stringRedisCache.remove(errorKey);
    }

    /**
     * 解析易企办用户信息
     *
     * @param eBusUserInfo
     * @return
     */
    public SaTokenInfo validLoginByEBusUserInfo(JSONObject eBusUserInfo) {
        UserInfoEntity eBusUser = new UserInfoEntity();
        eBusUser.setPlatform("mzt");
        JSONObject cropInfo = eBusUserInfo.getJSONObject("corp");
        if (cropInfo == null){

            eBusUser.setId(eBusUserInfo.getString("uid"));
            eBusUser.setAccount(eBusUserInfo.getString("account"));
            eBusUser.setUsername(eBusUserInfo.getString("name"));
            eBusUser.setSocialCreditCode(eBusUserInfo.getString("cid"));
            eBusUser.setPhone(eBusUserInfo.getString("mobile"));
            eBusUser.setPlatformType(UserInfoEntity.PLATFORM_USER);
            eBusUser.setUserType(UserInfoEntity.USER_PERSON);

        } else {
            eBusUser.setId(cropInfo.getString("uid"));
            eBusUser.setAccount(cropInfo.getString("name"));
            eBusUser.setUsername(cropInfo.getString("name"));
            eBusUser.setSocialCreditCode(cropInfo.getString("cid"));
            eBusUser.setPhone(cropInfo.getString("mobile"));
            eBusUser.setPlatformType(UserInfoEntity.PLATFORM_USER);
            eBusUser.setUserType(UserInfoEntity.USER_COMPANY);
            // 测试账号统一社会信用代码/企业名称替换
            TestCompanyMapping mapping = testCompanyMappingDAO.selectOne(Wrappers.lambdaQuery(TestCompanyMapping.class)
                    .eq(TestCompanyMapping::getOriginalCode, eBusUser.getSocialCreditCode()));
            if (mapping != null) {
                eBusUser.setAccount(mapping.getReplacedName());
                eBusUser.setSocialCreditCode(mapping.getReplacedCode());
            }
        }
        // 保存掌上南平用户基本信息
        UserInfoEntity existed = userInfoDAO.selectById(eBusUser.getId());
        if (existed == null) {
            // 加密手机号
            eBusUser.setEncryptIndex(ConfigBean.getIntVal("kind.common.encrypt.keyIndex"));
            eBusUser.setPhone(EncryptUtil.encryptSM4FixIndex(eBusUser.getPhone(), eBusUser.getEncryptIndex()));
            // 重新生成签名
            Pair<String, String> signResult = SignUtil.signData(eBusUser.generateData2Sign());
            eBusUser.setSign(signResult.getLeft());
            eBusUser.setSignCert(signResult.getRight());
            userInfoDAO.insert(eBusUser);
        } else {
            // 加密手机号
            existed.setEncryptIndex(ConfigBean.getIntVal("kind.common.encrypt.keyIndex"));
            existed.setPhone(EncryptUtil.encryptSM4FixIndex(eBusUser.getPhone(), existed.getEncryptIndex()));
            // 重新生成签名
            Pair<String, String> signResult = SignUtil.signData(existed.generateData2Sign());
            existed.setSign(signResult.getLeft());
            existed.setSignCert(signResult.getRight());
            existed.setPlatform("mzt");
            userInfoDAO.updateById(existed);
        }
        StpUtil.login(existed == null ? eBusUser.getId() : existed.getId());
        recordLoginLog(eBusUser.getId(), "易企办登录成功");
        return StpUtil.getTokenInfo();
    }

    /**
     * 用户登录(闵政通)
     */
    public SaTokenInfo validLoginMZT(String code, HttpServletRequest request) {

        log.info("闵政通参数code: {}", code);

        String accessToken = mztService.getAccessToken(code);
        UserInfoEntity userInfo = mztService.getUserInfo(accessToken);

        userInfo.setPlatform("mzt");

        saveUserInfo(userInfo, accessToken);

        recordLoginLog(userInfo.getId(), "闵政通登录成功");
        return StpUtil.getTokenInfo();

    }
}
