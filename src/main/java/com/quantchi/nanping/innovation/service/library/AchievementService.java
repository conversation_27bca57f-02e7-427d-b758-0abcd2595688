package com.quantchi.nanping.innovation.service.library;

import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;

import java.util.List;

public interface AchievementService {

    /**
     * 按产业链+成果类型统计
     *
     * @param chainId
     * @param nodeId
     * @return
     */
    List<CommonIndexBO> getAchievementTypeMap(String chainId, String nodeId);

    /**
     * 统计产业链的成果数
     *
     * @param chainId
     * @param nodeId
     * @return
     */
    Long countByChain(String chainId, String nodeId);
}
