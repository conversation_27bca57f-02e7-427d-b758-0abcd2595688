package com.quantchi.nanping.innovation.service.impl;

import com.quantchi.nanping.innovation.insight.model.bo.PersonStatsBo;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.ITechCommissionService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 科技特派员服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TechCommissionServiceImpl implements ITechCommissionService {

    private final TalentService talentService;

    @Override
    public Map<String, Object> getDegreeTitleDistribution(String chainId) {
        Map<String, Object> resultMap = new HashMap<>(4);
        PersonStatsBo bo = PersonStatsBo.build4Source(chainId, null, null, null, true, false);
        // 学历分布
        resultMap.put("degree", talentService.getDegreeDistribution(bo, true));
        // 职称分布
        resultMap.put("title", talentService.getProfTitleDistribution(bo, true));
        return resultMap;
    }

    @Override
    public PieVO getExternalNodeDistribution(String chainId) {
        Map<String, Long> nodeMap = talentService.countExternalNumByNode(chainId);
        List<CommonIndexBO> boList = EsAlterUtil.dealWithTopDistribution(nodeMap, null, "人");
        Long externalNum = talentService.countExternalNumByChainId(chainId);
        PieVO vo = new PieVO();
        vo.setIndexList(boList);
        vo.setTotal(externalNum);
        return vo;
    }
}
