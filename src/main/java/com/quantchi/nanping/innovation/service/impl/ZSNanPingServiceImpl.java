package com.quantchi.nanping.innovation.service.impl;

import cn.dev33.satoken.exception.NotLoginException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.dao.mapper.TestCompanyMappingDAO;
import com.quantchi.nanping.innovation.model.TestCompanyMapping;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.ITechCommissionerService;
import com.quantchi.nanping.innovation.service.IZSNanPingService;
import com.quantchi.nanping.innovation.utils.HttpClientUtils;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/3 15:11
 */
@Service
@Slf4j
public class ZSNanPingServiceImpl implements IZSNanPingService {

    private static final String PARAM_ACCESS_TOKEN = "access_token";

    @Value("${zs-nanping.client_id.pc}")
    private String clientId;

    @Value("${zs-nanping.client_secret.pc}")
    private String clientSecret;

    @Value("${zs-nanping.client_id.mobile}")
    private String mobileClientId;

    @Value("${zs-nanping.client_secret.mobile}")
    private String mobileClientSecret;

    @Value("${zs-nanping.login.url}")
    private String loginApiHost;

    @Value("${zs-nanping.user.url}")
    private String userApiHost;

    @Value("${zs-nanping.login.redirect.pc}")
    private String redirectUrl;

    @Value("${zs-nanping.login.redirect.mobile}")
    private String mobileRedirectUrl;

    @Autowired
    private TestCompanyMappingDAO testCompanyMappingDAO;

    @Autowired
    private ITechCommissionerService commissionerService;

    @Override
    public String getAccessTokenByCode(String code, boolean isPC) {
        String url = loginApiHost + "/oauth/token";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        paramMap.put("client_id", isPC ? clientId : mobileClientId);
        paramMap.put("client_secret", isPC ? clientSecret : mobileClientSecret);
        paramMap.put("grant_type", "authorization_code");
        paramMap.put("redirect_uri", isPC ? redirectUrl: mobileRedirectUrl);
        String result = HttpClientUtils.postParams(url, paramMap);
        log.error("url:{}, paramMap:{},result:{} 掌上南平验证结果：", url, paramMap, result);
        if (StringUtils.isEmpty(result)) {
            log.error("获取access_token异常,掌上南平验证无结果返回");
            throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (!resultObj.containsKey(PARAM_ACCESS_TOKEN)) {
            log.error("获取access_token异常，请求token返回结果为：" + result);
            throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
        }
        return resultObj.getString(PARAM_ACCESS_TOKEN);
    }

    @Override
    public UserInfoEntity getUserInfoByAccessToken(String accessToken) {
        String url = userApiHost + "/resource/user/userinfo";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(PARAM_ACCESS_TOKEN, accessToken);
        String result = HttpClientUtils.postParams(url, paramMap);
        if (StringUtils.isEmpty(result)) {
            log.error("请求掌上南平用户信息无返回结果");
            throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
        }
        log.error("掌上南平返回结果：{}", result);
        JSONObject userObject = JSONObject.parseObject(result).getJSONObject("content");
        UserInfoEntity userInfo = new UserInfoEntity();
        userInfo.setId(userObject.getString("id"));
        userInfo.setPhone(userObject.getString("phone"));
        if (!userObject.containsKey("enterAuth") || userObject.getJSONObject("enterAuth") == null) {
            // 普通用户
            if (userObject.containsKey("userAuth") && userObject.getJSONObject("userAuth") != null){
                JSONObject userAuthObject = userObject.getJSONObject("userAuth");
                userInfo.setAccount(userAuthObject.getString("realName"));
            }else{
                userInfo.setAccount(userObject.getString("nickname"));
            }
            userInfo.setPlatformType(UserInfoEntity.PLATFORM_USER);
            // 区分科特派专家
            if (commissionerService.exist(userInfo.getPhone())){
                userInfo.setUserType(UserInfoEntity.USER_EXPERT);
            }else{
                userInfo.setUserType(UserInfoEntity.USER_PERSON);
            }
        }else{
            // 企业用户
            JSONObject enterObject = userObject.getJSONObject("enterAuth");
            userInfo.setSocialCreditCode(enterObject.getString("orgId"));
            userInfo.setAccount(enterObject.getString("enterName"));
            userInfo.setUserType(UserInfoEntity.USER_COMPANY);
            userInfo.setPlatformType(UserInfoEntity.PLATFORM_USER);
            userInfo.setCityName(enterObject.getString("licenseCity"));
            userInfo.setAreaName(enterObject.getString("licenseCounty"));
            // 测试账号统一社会信用代码/企业名称替换
            TestCompanyMapping mapping = testCompanyMappingDAO.selectOne(Wrappers.lambdaQuery(TestCompanyMapping.class)
                    .eq(TestCompanyMapping::getOriginalCode, userInfo.getSocialCreditCode()));
            if (mapping != null) {
                userInfo.setAccount(mapping.getReplacedName());
                userInfo.setSocialCreditCode(mapping.getReplacedCode());
            }
        }
        return userInfo;
    }
}
