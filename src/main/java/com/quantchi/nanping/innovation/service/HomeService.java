package com.quantchi.nanping.innovation.service;

import com.quantchi.nanping.innovation.model.bo.CockpitGeoBO;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/11 14:27.
 */
public interface HomeService {

    /**
     * 首页区县指标（概览）
     *
     * @param chainId
     * @return
     */
    List<CockpitGeoBO> listRegion(String chainId);

    /**
     * 按首页区域详情查询指标
     *
     * @param chainId
     * @param regionId
     * @return
     */
    List<CommonIndexBO> list4RegionDetail(String chainId, String regionId);
}
