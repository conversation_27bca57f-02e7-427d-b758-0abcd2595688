package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeakProject;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/13 9:26
 */
public interface INodeWeakProjectService extends IService<IndustryChainNodeWeakProject> {

    /**
     * 根据关键核心技术实体id查询
     * @param weakId
     * @return
     */
    List<Map<String, Object>> listByWeakDataId(String weakId);
}
