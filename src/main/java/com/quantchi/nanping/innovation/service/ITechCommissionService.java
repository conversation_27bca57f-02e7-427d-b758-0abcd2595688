package com.quantchi.nanping.innovation.service;

import com.quantchi.nanping.innovation.model.vo.PieVO;

import java.util.Map;

/**
 * 科技特派员服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-18
 */
public interface ITechCommissionService {

    /**
     * 人才学历、职称分布
     *
     * @param chainId 产业链ID
     * @return 学历职称分布数据
     */
    Map<String, Object> getDegreeTitleDistribution(String chainId);

    /**
     * 外部人才节点分布
     *
     * @param chainId 产业链ID
     * @return 外部人才节点分布数据
     */
    PieVO getExternalNodeDistribution(String chainId);
}
