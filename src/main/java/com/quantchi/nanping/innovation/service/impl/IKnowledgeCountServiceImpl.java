package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.knowledge.center.dao.KnowledgeCountConfigMapper;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.KnowledgeCountConfig;
import com.quantchi.nanping.innovation.service.IKnowledgeCountService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class IKnowledgeCountServiceImpl extends ServiceImpl<KnowledgeCountConfigMapper, KnowledgeCountConfig> implements IKnowledgeCountService {

    @Override
    public Long getKnowledgeCount() {
        KnowledgeCountConfig entity = this.getOne(Wrappers.lambdaQuery(KnowledgeCountConfig.class).last("limit 1"));
        return entity !=null ? entity.getKnowledgeSum() : 0L;
    }
}
