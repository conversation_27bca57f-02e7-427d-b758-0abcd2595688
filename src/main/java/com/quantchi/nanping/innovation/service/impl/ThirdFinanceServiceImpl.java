package com.quantchi.nanping.innovation.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.common.core.exception.base.BusinessException;
import com.quantchi.common.core.utils.security.Md5Utils;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.service.IThirdFinanceService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.utils.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static cn.dev33.satoken.sign.SaSignTemplate.sign;

/**
 * <AUTHOR>
 * @date 2024/10/10 11:27
 */
@Service
@Slf4j
public class ThirdFinanceServiceImpl implements IThirdFinanceService {

    /**
     * 推送用户登录信息
     */
    private static final String API_TOKEN = "/pushAccessToken";

    /**
     * 获取产品信息
     */
    private static final String API_PRODUCT = "/productList";

    /**
     * 获取推荐企业的产品信息
     */
    private static final String API_PRODUCT_RECOMMEND = "/recommendedProductByCompanyCode";

    @Value("${nanping-lsjr.url}")
    private String lsjrUrl;

    @Value("${nanping-lsjr.appId}")
    private String appId;

    @Value("${nanping-lsjr.appSecret}")
    private String appSecret;

    @Autowired
    private ICompanyNodeRelationService nodeRelationService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private IndustryChainService chainService;

    @Autowired
    private SysLoginService sysLoginService;

    @Override
    public String pushUserInfo() {
        String token = StpUtil.getTokenValue();
        Map<String, String> params = new HashMap<>();
        // 传递企业的节点信息
        // 获得企业信息
        String creditCode = sysLoginService.getUserInfo(false).getSocialCreditCode();
        Map<String, Object> companyInfo = companyService.findCompanyByCreditCode(creditCode);
        String companyId = (String) companyInfo.get("id");
        if (com.quantchi.common.core.utils.StringUtils.isEmpty(companyId)) {
            // 非链上企业
            companyId = creditCode;
        }
        Pair<Set<String>, Set<String>> relateInfo = nodeRelationService.getRelatedNodeIdsByEntityId(companyId, true, false);
        Set<String> relateNodeIds = relateInfo.getRight();
        List<IndustryChainNode> nodeList = CollectionUtils.isEmpty(relateNodeIds) ? new ArrayList<>(0) : chainService.getNodesByNodeIds(relateNodeIds);
        params.put("nodeRelations", JSONObject.toJSONString(nodeList));
        params.put("platform", sysLoginService.getUserInfo(false).getPlatform());
        params.put("companyName", sysLoginService.getUserInfo(false).getAccount());
        params.put("companyCode", creditCode);
        params.put("address", companyInfo.getOrDefault("address", "").toString());
        return HttpClientUtils.postParams(lsjrUrl + API_TOKEN + "?accessToken=" + token, params);
    }

    @Override
    public List<Map<String, Object>> getFinanceProductList() {
        String result = HttpClientUtils.get(lsjrUrl + API_PRODUCT + "?" + generateAuthParam4Get(), null);
        log.error("绿色金融平台getFinanceProductList请求返回：{}", result);
        if (result == null) {
            throw new BusinessException("获取绿色金融平台产品异常");
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getString("data") == null) {
            throw new BusinessException("获取绿色金融平台产品异常");
        }
        return convert2FinanceProduct(resultObj.getJSONArray("data"));
    }

    @Override
    public List<Map<String, Object>> getRecommendFinanceProductList(String socialCreditCode) {
        String params = generateAuthParam4Get() + "companyCode=" + socialCreditCode;
        String result = HttpClientUtils.get(lsjrUrl + API_PRODUCT_RECOMMEND + "?" + params, null);
        log.error("绿色金融平台getRecommendFinanceProductList请求返回：{}", result);
        if (result == null) {
            throw new BusinessException("获取绿色金融平台推荐产品异常");
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getString("data") == null) {
            throw new BusinessException("获取绿色金融平台推荐产品异常");
        }
        return convert2FinanceProduct(resultObj.getJSONArray("data"));
    }

    /**
     * 生成签名
     *
     * @return
     */
    private String generateAuthParam4Get() {
        Long timestamp = System.currentTimeMillis();
        String sign = Md5Utils.hash(appId + appSecret + timestamp);
        return new StringBuilder().append("appId=").append(appId).append("&")
                .append("timestamp=").append(timestamp).append("&")
                .append("sign=").append(sign).append("&").toString();
    }

    /**
     * 字段转换
     *
     * @param productArray
     * @return
     */
    private List<Map<String, Object>> convert2FinanceProduct(JSONArray productArray) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (int i = 0; i < productArray.size(); i++) {
            JSONObject productObj = productArray.getJSONObject(i);
            Map<String, Object> product = new HashMap<>();
            product.put("type", "product");
            product.put("bank", productObj.getString("bank"));
            product.put("name", productObj.getString("productName"));
            product.put("interestRate", productObj.getString("minRate"));
            product.put("maxAmount", productObj.getIntValue("maxQuota"));
            product.put("maxDeadline", productObj.getIntValue("maxPeriod"));
            product.put("method", getGuaranteeMethodName(productObj.getString("guaranteeMethod")));
            resultList.add(product);
        }
        return resultList;
    }

    /**
     * 获取担保方式名称
     *
     * @param guaranteeMethodCode
     * @return
     */
    private String getGuaranteeMethodName(String guaranteeMethodCode) {
        switch (guaranteeMethodCode) {
            case "credit-guarantee":
                return "信用担保";
            case "mortgage-guarantee":
                return "抵押担保";
            case "pledge-guarantee":
                return "质押担保";
            case "guarantee-guarantee":
                return "担保";
            default:
                return StringUtils.EMPTY;
        }
    }
}
