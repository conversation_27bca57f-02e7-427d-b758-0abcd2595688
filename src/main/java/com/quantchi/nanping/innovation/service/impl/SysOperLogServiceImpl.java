package com.quantchi.nanping.innovation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.dao.mapper.SysOperLogDAO;
import com.quantchi.nanping.innovation.model.SysOperLog;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.model.event.OperLogEvent;
import com.quantchi.nanping.innovation.service.ISysOperLogService;
import com.quantchi.nanping.innovation.utils.SignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SysOperLogServiceImpl implements ISysOperLogService {

    private final SysOperLogDAO logDAO;

    @Autowired
    private SysLoginService sysLoginService;

    /**
     * 操作日志记录
     *
     * @param operLogEvent 操作日志事件
     */
    @Async
    @EventListener
    public void recordOper(final OperLogEvent operLogEvent) {
        final SysOperLog operLog = BeanUtil.toBean(operLogEvent, SysOperLog.class);
        if (StringUtils.isNotEmpty(operLogEvent.getOperUserId())){
            UserInfoEntity userInfo = sysLoginService.getUserById(operLog.getOperUserId(), false);
            if (userInfo == null){
                operLog.setOperName("微信小程序用户");
            }else{
                operLog.setOperName(UserInfoEntity.PLATFORM_GOV == userInfo.getPlatformType()? userInfo.getUsername(): userInfo.getAccount());
            }
        }else{
            operLog.setOperName("游客");
        }
        operLog.setOperTime(new Date());
        Pair<String, String> signResult = SignUtil.signData(operLog.generateData2Sign());
        operLog.setSign(signResult.getLeft());
        operLog.setSignCert(signResult.getRight());
        logDAO.insert(operLog);
    }
}
