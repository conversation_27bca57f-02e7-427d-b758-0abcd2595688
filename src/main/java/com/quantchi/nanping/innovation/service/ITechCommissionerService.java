package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.TechCommissioner;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.vo.PieVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/19 13:42
 */
public interface ITechCommissionerService extends IService<TechCommissioner> {
    /**
     * 统计科特派数量
     *
     * @param cityId
     * @param areaId
     * @return
     */
    Long countCommissionerNum(String cityId, String areaId);

    /**
     * 统计服务单位数量
     *
     * @param cityId
     * @param areaId
     * @return
     */
    Long countServiceOrgNum(String cityId, String areaId);

    /**
     * 获得历年科特派人数
     *
     * @param cityId
     * @param areaId
     * @param years
     * @return
     */
    Map<Integer, Long> getGrowth(String cityId, String areaId, int years);

    /**
     * 学历分布统计
     *
     * @param cityId
     * @param areaId
     * @return
     */
    PieVO getDegreeDistribution(String cityId, String areaId);

    /**
     * 职称分布统计
     *
     * @param cityId
     * @param areaId
     * @return
     */
    PieVO getProfTitleDistribution(String cityId, String areaId);

    /**
     * 通过手机号判断是否是科特派专家
     *
     * @param phone
     * @return
     */
    boolean exist(String phone);

    /**
     * 通过手机号查找专家id
     *
     * @param phone
     * @return
     */
    String getIdByPhone(String phone);

    /**
     * 通过手机号查找专家
     *
     * @param phone
     * @return
     */
    TechCommissioner getByPhone(String phone);

    /**
     * 查询服务经历
     *
     * @param dataId
     * @return
     */
    List<TechCommissioner> listExperienceByDataId(String dataId);

}
