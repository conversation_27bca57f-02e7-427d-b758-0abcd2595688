package com.quantchi.nanping.innovation.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.quantchi.nanping.innovation.service.IWordVectorService;
import com.quantchi.nanping.innovation.utils.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/7/5 16:46
 */
@Service
@Slf4j
public class WordVectorServiceImpl implements IWordVectorService {

    @Value("${model.vector.url}")
    private String vectorUrl;

    @Value("${model.vector.authorization}")
    private String vectorAuthorization;

    @Override
    public List<Double> calculateVector(String text, boolean isLarge) {
        if (StringUtils.isEmpty(text)){
            return new ArrayList<>(0);
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("X-API-Key", vectorAuthorization);
        JSONObject paramJson = new JSONObject();
        paramJson.put("input", Arrays.asList(text));
        paramJson.put("model", "embedding-bge-small-zh-v1.5@本地");
        String result = HttpClientUtils.postForJson(vectorUrl, paramJson, headers);
        if (StringUtils.isEmpty(result)){
            log.error("向量接口无返回");
            return new ArrayList<>(0);
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.containsKey("data") && resultObj.getJSONArray("data") != null){
            JSONArray data = resultObj.getJSONArray("data");
            if (CollectionUtils.isNotEmpty(data)){
                return data.getJSONObject(0).getJSONArray("embedding").toJavaList(Double.class);
            }
        }
        return new ArrayList<>(0);
    }
}
