package com.quantchi.nanping.innovation.service;

import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.model.entity.DmDivisionEntity;
import com.quantchi.nanping.innovation.model.vo.DmDivisionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/13 9:57
 */
public interface IDivisionService {

    /**
     * 查询子区域
     *
     * @param parentId
     * @return
     */
    List<DmDivisionEntity> listByParentId(String parentId);

    /**
     * 获取省市县三级结构
     *
     * @return
     */
    DmDivisionVO getDivision(String divisionId);

    DmDivisionEntity getByName(String name);

    /**
     * 获取省市县三级结构
     *
     * @return
     */
    JSONObject getDivisionMap(String region);

    /**
     * 按id查找区域
     *
     * @param ids
     * @return
     */
    List<DmDivisionEntity> listByIds(List<String> ids);

    DmDivisionEntity getById(String id);
}
