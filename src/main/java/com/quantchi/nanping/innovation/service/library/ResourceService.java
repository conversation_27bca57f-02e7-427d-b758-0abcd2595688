package com.quantchi.nanping.innovation.service.library;

import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.tianying.model.EsPageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/1/15 0:46
 */
public interface ResourceService {

    /**
     * 查询资源
     *
     * @param esSimpleQuery
     * @return
     */
    EsPageResult listResource(EsSimpleQuery esSimpleQuery);

    /**
     * 按指定字段分组统计数量
     *
     * @param esSimpleQuery
     * @param aggregationField
     * @param fieldValue
     * @return
     */
    List<CommonIndexBO> countByType(EsSimpleQuery esSimpleQuery, String aggregationField, List<String> fieldValue);
}
