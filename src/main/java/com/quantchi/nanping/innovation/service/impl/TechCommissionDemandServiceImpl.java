package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.TechCommissionDemandDAO;
import com.quantchi.nanping.innovation.model.TechCommissionDemand;
import com.quantchi.nanping.innovation.service.ITechCommissionDemandService;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/5/19 14:34
 */
@Service
public class TechCommissionDemandServiceImpl extends ServiceImpl<TechCommissionDemandDAO, TechCommissionDemand> implements ITechCommissionDemandService {
    @Override
    public Long countNumExcludeUndocking(String cityId, String areaId) {
        return this.count(Wrappers.lambdaQuery(TechCommissionDemand.class)
                .in(TechCommissionDemand::getStatus, Arrays.asList("对接中", "对接成功")));
    }
}
