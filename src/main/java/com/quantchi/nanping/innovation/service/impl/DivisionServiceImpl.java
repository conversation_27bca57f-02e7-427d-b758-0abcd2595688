package com.quantchi.nanping.innovation.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.nanping.innovation.dao.mapper.DmDivisionDAO;
import com.quantchi.nanping.innovation.dao.mapper.DmDivisionMapDAO;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.entity.DmDivisionEntity;
import com.quantchi.nanping.innovation.model.entity.DmDivisionMapEntity;
import com.quantchi.nanping.innovation.model.vo.DmDivisionVO;
import com.quantchi.nanping.innovation.service.IDivisionService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/4/13 9:57
 */
@Service
public class DivisionServiceImpl implements IDivisionService {

    @Autowired
    private DmDivisionDAO divisionDAO;

    @Resource
    private DmDivisionMapDAO divisionMapDAO;

    @Override
    public List<DmDivisionEntity> listByParentId(String parentId) {
        return divisionDAO.selectList(Wrappers.lambdaQuery(DmDivisionEntity.class)
                .eq(DmDivisionEntity::getParentId, parentId));
    }

    private DmDivisionVO buildClassicTree(List<DmDivisionEntity> nodeList, String divisionId) {
        Map<String, DmDivisionVO> nodeMap = new HashMap<>();
        String rootId = null;
        nodeList.forEach(temp -> nodeMap.put(temp.getId(), new DmDivisionVO(temp)));
        for (DmDivisionEntity node : nodeList) {
            DmDivisionVO industryChainTree = nodeMap.get(node.getId());
            if (!StringUtils.isEmpty(node.getParentId())) {
                if (nodeMap.get(node.getParentId()) != null) {
                    nodeMap.get(node.getParentId()).getChildren().add(industryChainTree);
                }
            } else {
                rootId = node.getId();
            }
        }
        if (StringUtils.isNotBlank(divisionId)) {
            return nodeMap.get(divisionId);
        }
        return nodeMap.get(rootId);
    }

    @Override
    public DmDivisionVO getDivision(String areaName) {
        String divisionId = null;
        if (StringUtils.isNotBlank(areaName)) {
            DmDivisionEntity entity = getByName(areaName);
            divisionId = entity.getId();
        }
        List<DmDivisionEntity> oriList = new ArrayList<>();
        oriList.add(CommonConstant.DIVISION_CHINA);
        oriList.addAll(listByLevels(Arrays.asList(1,2,3)));
        return buildClassicTree(oriList, divisionId);
    }

    @Override
    public DmDivisionEntity getByName(String name){
        return divisionDAO.selectOne(Wrappers.lambdaQuery(DmDivisionEntity.class).eq(DmDivisionEntity::getName, name));
    }

    private List<DmDivisionEntity> listByLevels(List<Integer> levels){
        return divisionDAO.selectList(Wrappers.lambdaQuery(DmDivisionEntity.class).in(DmDivisionEntity::getLevel, levels));
    }

    @Override
    public JSONObject getDivisionMap(String region) {
        QueryWrapper<DmDivisionMapEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("id", region).or().like("name", region);
        DmDivisionMapEntity entity = divisionMapDAO.selectOne(queryWrapper);
        if (entity != null) {
            return JSONObject.parseObject(entity.getJson());
        }
        return null;
    }

    @Override
    public List<DmDivisionEntity> listByIds(List<String> ids) {
        return divisionDAO.selectList(Wrappers.lambdaQuery(DmDivisionEntity.class)
                .in(DmDivisionEntity::getId, ids));

    }

    @Override
    public DmDivisionEntity getById(String id) {
        return divisionDAO.selectOne(Wrappers.lambdaQuery(DmDivisionEntity.class)
                .eq(DmDivisionEntity::getId, id));
    }
}
