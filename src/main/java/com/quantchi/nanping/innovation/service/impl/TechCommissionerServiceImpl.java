package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.TechCommissionerDAO;
import com.quantchi.nanping.innovation.model.TechCommissioner;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.ITechCommissionerService;
import com.quantchi.nanping.innovation.utils.AESUtil;
import com.quantchi.nanping.innovation.utils.EncryptUtil;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/19 13:42
 */
@Service
public class TechCommissionerServiceImpl extends ServiceImpl<TechCommissionerDAO, TechCommissioner> implements ITechCommissionerService {
    @Override
    public Long countCommissionerNum(String cityId, String areaId) {
        List<TechCommissioner> commissioners = this.list(Wrappers.lambdaQuery(TechCommissioner.class)
                .select(TechCommissioner::getId)
                .eq(StringUtils.isNotEmpty(cityId), TechCommissioner::getCityId, cityId)
                .eq(StringUtils.isNotEmpty(areaId), TechCommissioner::getAreaId, areaId)
                .groupBy(TechCommissioner::getDataId, TechCommissioner::getName));
        return commissioners.stream().count();
    }

    @Override
    public Long countServiceOrgNum(String cityId, String areaId) {
        List<TechCommissioner> commissioners = this.list(Wrappers.lambdaQuery(TechCommissioner.class)
                .select(TechCommissioner::getId)
                .eq(StringUtils.isNotEmpty(cityId), TechCommissioner::getCityId, cityId)
                .eq(StringUtils.isNotEmpty(areaId), TechCommissioner::getAreaId, areaId)
                .groupBy(TechCommissioner::getServiceObj));
        return commissioners.stream().count();
    }

    @Override
    public Map<Integer, Long> getGrowth(String cityId, String areaId, int years) {
        LocalDate startDate = new LocalDate().minusYears(years);
        List<TechCommissioner> commissioners = this.list(Wrappers.lambdaQuery(TechCommissioner.class)
                .select(TechCommissioner::getId, TechCommissioner::getYear)
                .eq(StringUtils.isNotEmpty(cityId), TechCommissioner::getCityId, cityId)
                .eq(StringUtils.isNotEmpty(areaId), TechCommissioner::getAreaId, areaId)
                .ge(TechCommissioner::getYear, Integer.parseInt(startDate.toString("yyyy"))));
        return commissioners.stream().collect(Collectors.groupingBy(TechCommissioner::getYear, Collectors.counting()));
    }

    @Override
    public PieVO getDegreeDistribution(String cityId, String areaId) {
        List<TechCommissioner> commissioners = this.list(Wrappers.lambdaQuery(TechCommissioner.class)
                .select(TechCommissioner::getId, TechCommissioner::getDegree)
                .eq(StringUtils.isNotEmpty(cityId), TechCommissioner::getCityId, cityId)
                .eq(StringUtils.isNotEmpty(areaId), TechCommissioner::getAreaId, areaId)
                .groupBy(TechCommissioner::getDataId, TechCommissioner::getName));
        Long totalNum = commissioners.stream().count();
        commissioners.removeIf(c -> StringUtils.isBlank(c.getDegree()));
        Map<String, Long> degreeMap = commissioners.stream().collect(Collectors.groupingBy(TechCommissioner::getDegree, Collectors.counting()));
        Map<String, Long> sortedDegreeMap = new LinkedHashMap<>();
        sortedDegreeMap.put("博士", degreeMap.get("博士"));
        sortedDegreeMap.put("硕士", degreeMap.get("硕士"));
        sortedDegreeMap.put("学士", degreeMap.get("学士"));
        fillMap(totalNum, commissioners.stream().count(), sortedDegreeMap);
        PieVO pie = new PieVO();
        pie.setTotal(totalNum);
        pie.setIndexList(EsAlterUtil.dealWithTopDistribution(sortedDegreeMap, null, "人"));
        return pie;
    }

    @Override
    public PieVO getProfTitleDistribution(String cityId, String areaId) {
        List<TechCommissioner> commissioners = this.list(Wrappers.lambdaQuery(TechCommissioner.class)
                .select(TechCommissioner::getId, TechCommissioner::getTitle)
                .eq(StringUtils.isNotEmpty(cityId), TechCommissioner::getCityId, cityId)
                .eq(StringUtils.isNotEmpty(areaId), TechCommissioner::getAreaId, areaId)
                .groupBy(TechCommissioner::getDataId, TechCommissioner::getName));
        Long totalNum = commissioners.stream().count();
        commissioners.removeIf(c -> StringUtils.isBlank(c.getTitle()));
        Map<String, Long> titleMap = commissioners.stream().collect(Collectors.groupingBy(TechCommissioner::getTitle, Collectors.counting()));
        Map<String, Long> sortedTitleMap = sortMap(titleMap);
        fillMap(totalNum, commissioners.stream().count(), sortedTitleMap);
        PieVO pie = new PieVO();
        pie.setTotal(totalNum);
        pie.setIndexList(EsAlterUtil.dealWithTopDistribution(sortedTitleMap, null, "人"));
        return pie;
    }

    @Override
    public boolean exist(String phone) {
        TechCommissioner commissioner = this.getOne(Wrappers.lambdaQuery(TechCommissioner.class)
                .eq(TechCommissioner::getPhone, EncryptUtil.encryptSM4FixIndex(phone, null))
                //.eq(TechCommissioner::getPhone, AESUtil.encrypt(phone))
                .eq(TechCommissioner::getType, 0));
        return commissioner != null;
    }

    @Override
    public String getIdByPhone(String phone) {
        TechCommissioner commissioner = getByPhone(phone);
        return commissioner == null ? null : commissioner.getDataId();
    }

    @Override
    public TechCommissioner getByPhone(String phone) {
        return this.getOne(Wrappers.lambdaQuery(TechCommissioner.class)
                .eq(TechCommissioner::getPhone, EncryptUtil.encryptSM4FixIndex(phone, null))
                //.eq(TechCommissioner::getPhone, AESUtil.encrypt(phone))
                .eq(TechCommissioner::getType, 0));
    }

    @Override
    public List<TechCommissioner> listExperienceByDataId(String dataId) {
        return this.list(Wrappers.lambdaQuery(TechCommissioner.class)
                .select(TechCommissioner::getLevel, TechCommissioner::getYear, TechCommissioner::getServiceObj)
                .eq(TechCommissioner::getDataId, dataId)
                .groupBy(TechCommissioner::getLevel, TechCommissioner::getYear, TechCommissioner::getServiceObj)
                .orderByAsc(TechCommissioner::getYear));
    }

    private void fillMap(Long totalNum, Long newNum, Map<String, Long> countMap) {
        Long subtract = totalNum - newNum;
        boolean filled = subtract == 0;
        if (!filled) {
            Long otherNum = subtract;
            if (countMap.containsKey("其他")) {
                otherNum += countMap.get("其他");
            }
            countMap.put("其他", otherNum);
        }
    }

    private Map<String, Long> sortMap(Map<String, Long> map) {
        List<Map.Entry<String, Long>> entryList = new ArrayList<>(map.entrySet());
        Collections.sort(entryList, new Comparator<Map.Entry<String, Long>>() {
            @Override
            public int compare(Map.Entry<String, Long> o1, Map.Entry<String, Long> o2) {
                return o2.getValue().compareTo(o1.getValue());
            }
        });
        LinkedHashMap<String, Long> linkedHashMap = new LinkedHashMap<>();
        for (Map.Entry<String, Long> e : entryList) {
            linkedHashMap.put(e.getKey(), e.getValue());
        }
        return linkedHashMap;
    }

}
