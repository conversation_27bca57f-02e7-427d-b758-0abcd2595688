package com.quantchi.nanping.innovation.service.library;


import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.tianying.model.EsPageResult;

import java.util.List;
import java.util.Map;

public interface PlatformService {

    /**
     * 获取平台载体列表
     *
     * @param industry
     * @param pageNum
     * @param pageSize
     * @return
     */
    EsPageResult getPlatformList(String industry, Integer pageNum, Integer pageSize);

    /**
     * 按产业链+城市获得平台类型统计
     *
     * @param chainId
     * @param cityId
     * @return
     */
    Map<String, Long> getPlatformTypeMap(String chainId, String cityId, String areaId);

    /**
     * 统计国家重点实验室省份分布
     *
     * @param chainId
     * @return
     */
    List<CommonIndexBO> countNationalGroupByProvince(String chainId);

    /**
     * 分页查询国家级重点实验室
     *
     * @param pageNum
     * @param pageSize
     * @param chainId
     * @return
     */
    EsPageResult page4National(Integer pageNum, Integer pageSize, String chainId);
}
