package com.quantchi.nanping.innovation.service.library;

import com.quantchi.nanping.innovation.company.model.bo.StandardVectorMatchBO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/12 17:33
 */
public interface StandardService {
    /**
     * 向量搜索
     *
     * @param bo
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<Map<String, Object>> vectorSearch(StandardVectorMatchBO bo, int pageNo, int pageSize);

    /**
     * 根据id查询
     *
     * @param ids
     * @param includes
     * @param excludes
     * @return
     */
    List<Map<String, Object>> getByIds(String[] ids, String[] includes, String[] excludes);
}
