package com.quantchi.nanping.innovation.service.library.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.nanping.innovation.demand.dao.DemandSampleMapper;
import com.quantchi.nanping.innovation.demand.model.DemandConfig;
import com.quantchi.nanping.innovation.demand.model.DemandSample;
import com.quantchi.nanping.innovation.demand.model.bo.DemandBO;
import com.quantchi.nanping.innovation.demand.model.enums.DemandTypeEnum;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.DemandService;
import com.quantchi.nanping.innovation.service.library.ResourceService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.TaskUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/4/20 13:44
 */
@Service
public class DemandServiceImpl implements DemandService {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private TalentService talentService;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private DemandSampleMapper sampleMapper;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private IndustryChainService industryChainService;

    @Override
    public Long count(String chainId, String nodeId) {
        final BoolQueryBuilder boolQuery = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, false);
        boolQuery.filter(QueryBuilders.existsQuery("chain.id"));
        boolQuery.mustNot(QueryBuilders.termQuery("chain.id", StringUtils.EMPTY));
        return elasticsearchHelper.countRequest(EsIndexEnum.DEMAND.getEsIndex(), boolQuery);
    }

    @Override
    public EsPageResult listSample(String chainId) {
        List<DemandSample> samples = sampleMapper.selectList(Wrappers.lambdaQuery(DemandSample.class)
                .eq(DemandSample::getChainId, chainId));
        List<String> demandIds = samples.stream().map(DemandSample::getDemandId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(demandIds)) {
            return new EsPageResult();
        }
        EsSimpleQuery esSimpleQuery = new EsSimpleQuery();
        esSimpleQuery.setPageNum(1);
        esSimpleQuery.setPageSize(demandIds.size());
        esSimpleQuery.setResourceType(EsIndexEnum.DEMAND.getType());
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        // new String[]{"15941ad77b98925a7e8f7e4255a4c31b", "73f13e4e49a8f9ba6dceb85aec5c0c50"}
        queryBuilder.filter(QueryBuilders.termsQuery("id", demandIds.toArray(new String[0])));
        EsPageResult pageResult = EsAlterUtil.esPageSearch(elasticsearchHelper, esSimpleQuery, EsIndexEnum.DEMAND, queryBuilder, null);
        fillMatchedExpertNew(pageResult.getList());
        return pageResult;
    }

    /**
     * 按照需求匹配专家，需求已在es库中
     *
     * @param demandList
     */
    private void fillMatchedExpertNew(List<Map<String, Object>> demandList) {
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (Map<String, Object> demand : demandList) {
            final CompletableFuture<Void> matchFuture = CompletableFuture.runAsync(() -> {
                EsPageResult pageResult = findExpert4Demand(demand, 1, 6);
                if (pageResult == null) {
                    return;
                }
                demand.put("lz_recommend_expert", pageResult.getList());
            }, taskExecutor);
            futureList.add(matchFuture);
        }
        TaskUtil.get(CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])));
    }

    EsPageResult findExpert4Demand(Map<String, Object> demand, int pageNum, int pageSize) {
        Set<String> chainNodeSet = new HashSet<>();
        if (demand.containsKey("product_node") && CollectionUtils.isNotEmpty((List<Map<String, Object>>) demand.get("product_node"))) {
            Map<String, Object> nodeInfo = ((List<Map<String, Object>>) demand.get("product_node")).get(0);
            chainNodeSet.add((String) nodeInfo.get("id"));
        }
        if (CollectionUtils.isEmpty(chainNodeSet)) {
            return null;
        }
        EsSimpleQuery esSimpleQuery = new EsSimpleQuery();
        esSimpleQuery.setPageNum(pageNum);
        esSimpleQuery.setPageSize(pageSize);
        esSimpleQuery.setResourceType(EsIndexEnum.EXPERT.getType());
        // 查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", chainNodeSet));
        boolQueryBuilder.filter(QueryBuilders.existsQuery("score_model"));
        esSimpleQuery.setQueryBuilder(boolQueryBuilder);
        // 自定义排序
        List<FunctionScoreQueryBuilder.FilterFunctionBuilder> builders = new ArrayList<>();
        builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.termsQuery("product_node.id", chainNodeSet), ScoreFunctionBuilders.weightFactorFunction(100)));
        builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(ScoreFunctionBuilders.fieldValueFactorFunction("score_model.final_score")));
        esSimpleQuery.setFilterFunctionBuilders(builders);
        return resourceService.listResource(esSimpleQuery);
    }

    /**
     * 汇报需求案例固定专家推荐
     *
     * @return
     */
    private Map<String, List<String>> getCustomRecommendExpert(DemandConfig demandConfig) {
        Map<String, List<String>> customRecommendExpert = new HashMap<>();
        customRecommendExpert.put("5a2a2d29-2b95-472d-8846-a182208ddb32", Arrays.asList("instance_entity_expert-536d7f8f8b360ad90b83cd55c961b100",
                "instance_entity_expert-aaca2248f5c9df3339065495f8aec78d", "instance_entity_expert-f364c0d6c4cff70b4d982930c07ce07a",
                "instance_entity_expert-32763c97b346cd48d5b4123958b6585f", "instance_entity_expert-013e1b168f155b35c139e4a06ab3c54d",
                "instance_entity_expert-e29c49a725926bdafb160adbfeb5686b"));
        if (demandConfig != null && StringUtils.isNotEmpty(demandConfig.getExpertIds())) {
            customRecommendExpert.put(demandConfig.getDemandId(), Arrays.asList(demandConfig.getExpertIds().split(",")));
        }
        return customRecommendExpert;
    }

    @Override
    public Map<Integer, Long> getDemandGrowth(int years, List<String> statusList) {
        String startYear = new LocalDate().minusYears(years).toString("yyyy");
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.rangeQuery("commit_date").format("yyyy").from(startYear, false));
        if (CollectionUtils.isNotEmpty(statusList)) {
            queryBuilder.filter(QueryBuilders.termsQuery("status", statusList));
        }
//        queryBuilder.filter(QueryBuilders.existsQuery("chain.id"));
//        queryBuilder.mustNot(QueryBuilders.termQuery("chain.id", StringUtils.EMPTY));
        // 年份分组
        final String yearAggregationKey = TERMS_BUCKET_PREFIX + "commit_date";
        DateHistogramAggregationBuilder yearAggregationBuilder = AggregationBuilders.dateHistogram(yearAggregationKey).field("commit_date")
                .format("yyyy").calendarInterval(DateHistogramInterval.YEAR).minDocCount(1);
        AggregationPageResult aggregationPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.DEMAND.getEsIndex(), queryBuilder, yearAggregationBuilder);
        ParsedDateHistogram dateHistogram = (ParsedDateHistogram) aggregationPageResult.getSearchResponse().getAggregations().getAsMap().get(yearAggregationKey);
        Map<Integer, Long> resultMap = new HashMap<>();
        for (Histogram.Bucket bucket : dateHistogram.getBuckets()) {
            resultMap.put(Integer.parseInt(bucket.getKeyAsString()), bucket.getDocCount());
        }
        return resultMap;
    }

    @Override
    public EsPageResult page(DemandBO bo, boolean matchExpert, boolean onlyCompany) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        String sort = EsIndexEnum.DEMAND.getSort();
        if (CollectionUtils.isNotEmpty(bo.getNodeIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", bo.getNodeIds()));
        }
        if (StringUtils.isNotEmpty(bo.getTitle())) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("name", bo.getTitle()));
            sort = null;
        }
        if (StringUtils.isNotEmpty(bo.getContent())) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("research_content", bo.getContent()));
            sort = null;
        }
        if (StringUtils.isNotEmpty(bo.getProposer())) {
            boolQueryBuilder.must(QueryBuilders.multiMatchQuery(bo.getProposer(), new String[]{"commit_info.unit", "commit_info.person"}));
            sort = null;
        }
        if (StringUtils.isNotEmpty(bo.getCommitDateFrom())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("commit_date").gte(bo.getCommitDateFrom()));
        }
        if (StringUtils.isNotEmpty(bo.getCommitDateTo())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("commit_date").lte(bo.getCommitDateTo()));
        }
        if (StringUtils.isNotEmpty(bo.getChainId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("chain.id", bo.getChainId()));
        }
        if (StringUtils.isNotEmpty(bo.getType())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("type", bo.getType()));
        }
        if (CollectionUtils.isNotEmpty(bo.getDataIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("id", bo.getDataIds()));
        }
        if (matchExpert) {
            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
            shouldQuery.should(QueryBuilders.termQuery("type", DemandTypeEnum.TALENT.getName()));
            shouldQuery.should(QueryBuilders.termQuery("source_type", "科特派需求"));
            shouldQuery.minimumShouldMatch(1);
            boolQueryBuilder.filter(shouldQuery);
        }
        if (onlyCompany) {
            boolQueryBuilder.filter(QueryBuilders.matchPhraseQuery("commit_info.unit.text", "公司"));
        }
        boolQueryBuilder.filter(QueryBuilders.existsQuery("chain.id"));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("chain.id", StringUtils.EMPTY));
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, bo.getPageNum(), bo.getPageSize(), null, null, sort);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.DEMAND.getEsIndex());
        // TODO: 使用向量后需要恢复
        //        return ElasticsearchBuilder.buildPageResult(searchResponse);
        EsPageResult pageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        if (matchExpert) {
            fillMatchedExpertNew(pageResult.getList());
        }
        return pageResult;
    }

    @Override
    public Pair<Long, BigDecimal> countMatchedDemand(String chainId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, false);
        boolQueryBuilder.filter(QueryBuilders.existsQuery("chain.id"));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("chain.id", StringUtils.EMPTY));
        BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
        shouldQuery.should(QueryBuilders.termQuery("type", DemandTypeEnum.TALENT.getName()));
        shouldQuery.should(QueryBuilders.termQuery("source_type", "科特派需求"));
        shouldQuery.minimumShouldMatch(1);
        boolQueryBuilder.filter(shouldQuery);
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, 10000, null, null, null);
        SearchResponse response = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.DEMAND.getEsIndex());
        EsPageResult pageResult = ElasticsearchBuilder.buildPageResult(response);
        List<Map<String, Object>> demandList = pageResult.getList();
        if (CollectionUtils.isEmpty(demandList)) {
            return new ImmutablePair<>(0L, BigDecimal.ZERO);
        }
        Long totalNum = demandList.stream().count();
        Long matchedNum = 0L;
        Map<String, String> demandNodeMap = new HashMap<>();
        Set<String> chainNodeSet = new HashSet<>();
        for (Map<String, Object> demand : demandList) {
            if (demand.containsKey("product_node")) {
                Map<String, Object> nodeInfo = ((List<Map<String, Object>>) demand.get("product_node")).get(0);
                chainNodeSet.add((String) nodeInfo.get("id"));
                demandNodeMap.put((String) demand.get("id"), (String) nodeInfo.get("id"));
            }
        }
        if (CollectionUtils.isNotEmpty(chainNodeSet)) {
            // 查询条件
            BoolQueryBuilder expertBoolQueryBuilder = QueryBuilders.boolQuery();
            expertBoolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", chainNodeSet));
            expertBoolQueryBuilder.filter(QueryBuilders.existsQuery("score_model"));
            Map<String, Long> nodeExpertCountMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.EXPERT, expertBoolQueryBuilder, "chain_node.id");
            for (Map.Entry<String, String> demandNode : demandNodeMap.entrySet()) {
                if (nodeExpertCountMap.containsKey(demandNode.getValue())) {
                    matchedNum += 1L;
                }
            }
        }
        BigDecimal matchRatio = new BigDecimal(matchedNum).divide(new BigDecimal(totalNum), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        return new ImmutablePair<>(matchedNum, matchRatio);
    }

    @Override
    public Long countToday() {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("commit_date").format("yyyy-mm-dd").gte(new LocalDate().toString("yyyy-mm-dd")));
        return elasticsearchHelper.countRequest(EsIndexEnum.DEMAND.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public Pair<Long, Map<String, Long>> countByStatus() {
        String statusAggregationKey = TERMS_BUCKET_PREFIX + "status";
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(statusAggregationKey).field("status");
        AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.DEMAND.getEsIndex(), QueryBuilders.boolQuery(), aggregationBuilder);
        Terms term = (Terms) pageResult.getSearchResponse().getAggregations().getAsMap().get(statusAggregationKey);
        Map<String, Long> statusMap = new HashMap<>();
        for (Terms.Bucket bucket : term.getBuckets()) {
            statusMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return new ImmutablePair<>(pageResult.getTotal(), statusMap);
    }

    @Override
    public Map<String, Long> countByChain() {
        return EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.DEMAND, QueryBuilders.boolQuery(), "chain.name");
    }

    @Override
    public List<Map<String, Object>> getRecommendExpertById(String id) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("id", id));
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, 1, null, null, null);
        SearchResponse response = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.DEMAND.getEsIndex());
        EsPageResult pageResult = ElasticsearchBuilder.buildPageResult(response);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
            return new ArrayList<>(0);
        }
        Map<String, Object> demand = pageResult.getList().get(0);
        fillMatchedExpertNew(Arrays.asList(demand));
        List<Map<String, Object>> recommendExperts = (List<Map<String, Object>>) demand.get("lz_recommend_expert");
        return CollectionUtils.isEmpty(recommendExperts) ? new ArrayList<>(0) : recommendExperts;
    }

    @Override
    public List<Map<String, Object>> listDemandByChainId(String chainId, String type) {
        DemandBO demandBO = new DemandBO();
        demandBO.setChainId(chainId);
        demandBO.setPageNum(1);
        demandBO.setPageSize(100);
        EsPageResult pageResult = page(demandBO, true, true);
        List<Map<String, Object>> demandList = pageResult.getList();
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> demand : demandList) {
            List<Map<String, Object>> productNodeList = (List<Map<String, Object>>) demand.get("product_node");
            if (CollectionUtils.isEmpty(productNodeList)) {
                continue;
            }
            Map<String, Object> demandInfo = new HashMap<>();
            List<String> nodeNameList = new ArrayList<>(productNodeList.size());
            for (Map<String, Object> productNode : productNodeList) {
                nodeNameList.add((String) productNode.get("name"));
            }
            demandInfo.put("demandDomainType", StringUtils.join(nodeNameList, "、") + "领域人才需求");
            demandInfo.put("research_content", demand.get("research_content"));
            demandInfo.put("commit_info", demand.get("commit_info"));
            resultList.add(demandInfo);
        }
        return resultList;
    }

    @Override
    public PieVO getDemandFirstNodeDistribution(String chainId) {
        // 查询一级节点
        List<IndustryChainNode> nodeList = industryChainService.getNodesByChainIdAndLevels(chainId, 2, 2);
        List<String> nodeNames = nodeList.stream().map(IndustryChainNode::getName).collect(Collectors.toList());
        TermsAggregationBuilder demandAggregation = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "node")
                .field("chain_node.name").size(100).order(BucketOrder.count(false))
                .includeExclude(new IncludeExclude(nodeNames.toArray(new String[0]), null));
        AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.DEMAND.getEsIndex(), QueryBuilders.boolQuery(), demandAggregation);
        Terms term = (Terms) pageResult.getSearchResponse().getAggregations().getAsMap().get(TERMS_BUCKET_PREFIX + "node");
        Map<String, Long> countMap = new LinkedHashMap<>();
        for (Terms.Bucket bucket : term.getBuckets()) {
            countMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        for (String nodeName : nodeNames) {
            if (countMap.containsKey(nodeName)) {
                continue;
            }
            countMap.put(nodeName, 0L);
        }
        PieVO pie = new PieVO();
        pie.setTotal(pageResult.getTotal());
        pie.setIndexList(CommonIndexBO.buildList(countMap));
        return pie;
    }

    @Override
    public List<Map<String, Object>> pageMatchingExpert(String demandId) {
        Map<String, Object> demand = elasticsearchHelper.getDataById(EsIndexEnum.DEMAND.getEsIndex(), demandId, null, null);
        EsPageResult expertPage = findExpert4Demand(demand, 1, 6);
        return expertPage == null? new ArrayList<>(0): expertPage.getList();
//        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//        boolQueryBuilder.filter(QueryBuilders.termQuery("id", demandId));
//        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1,1,null,null, null);
//        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.DEMAND.getEsIndex());
//        EsPageResult pageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
//        Map<String, Object> demand = pageResult.getList().get(0);
//        String chainId = (String)((List<Map<String, Object>>)demand.get("chain")).get(0).get("id");
//        List<String> nodeIds = new ArrayList<>();
//        for (Map<String, Object> chainNode: (List<Map<String, Object>>)demand.get("product_node")){
//            nodeIds.add(String.valueOf(chainNode.get("id")));
//        }
//        return talentService.recommendByDemand(chainId, nodeIds, (String)demand.get("research_content"));
    }
}
