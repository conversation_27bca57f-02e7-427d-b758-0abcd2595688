package com.quantchi.nanping.innovation.service.library.impl;

import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.bo.FinanceCompareBo;
import com.quantchi.nanping.innovation.model.enums.CompanyTagEnum;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.library.ProjectService;
import com.quantchi.nanping.innovation.utils.BigDecimalUtil;
import com.quantchi.nanping.innovation.utils.DateUtils;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.range.*;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.SumAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/4/20 14:33
 */
@Service
public class ProjectServiceImpl implements ProjectService {

    /**
     * 项目类型：招商项目
     */
    private static final String INVESTMENT_ATTRACT_PROJECT = "招商项目";

    /**
     * 项目类型字段
     */
    private static final String COLUMN_PROJECT_TYPE = "project_type";

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Override
    public Long count(String chainId, String nodeId, String cityId, String areaId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, cityId, areaId, false);
        boolQueryBuilder.mustNot(QueryBuilders.termQuery(COLUMN_PROJECT_TYPE, INVESTMENT_ATTRACT_PROJECT));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery(COLUMN_PROJECT_TYPE, "工信局项目"));
        return elasticsearchHelper.countRequest(EsIndexEnum.PROJECT.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public Long countInvestmentAttract(String chainId, String nodeId, String cityId, String areaId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, cityId, areaId, false);
        boolQueryBuilder.filter(QueryBuilders.termQuery(COLUMN_PROJECT_TYPE, INVESTMENT_ATTRACT_PROJECT));
        return elasticsearchHelper.countRequest(EsIndexEnum.PROJECT.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public List<CommonIndexBO> countInvestmentAttractGroupByStatus(String chainId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, true);
        boolQueryBuilder.filter(QueryBuilders.termQuery(COLUMN_PROJECT_TYPE, INVESTMENT_ATTRACT_PROJECT));
        Map<String, Long> countMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.PROJECT, boolQueryBuilder, "project_status");
        return EsAlterUtil.dealWithTopDistribution(countMap, null, "个");
    }

    @Override
    public EsPageResult page4InvestmentAttract(Integer pageNum, Integer pageSize, String chainId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, true);
        boolQueryBuilder.filter(QueryBuilders.termQuery(COLUMN_PROJECT_TYPE, INVESTMENT_ATTRACT_PROJECT));
        EsSimpleQuery esSimpleQuery = new EsSimpleQuery();
        esSimpleQuery.setPageNum(pageNum);
        esSimpleQuery.setPageSize(pageSize);
        EsPageResult pageResult = EsAlterUtil.esPageSearch(elasticsearchHelper, esSimpleQuery, EsIndexEnum.PROJECT, boolQueryBuilder, null);
        for (Map<String, Object> project: pageResult.getList()){
            if (!project.containsKey("amount")){
                continue;
            }
            BigDecimal amount = BigDecimal.valueOf((double)project.get("amount"));
            amount = amount.divide(new BigDecimal(10000), 0, RoundingMode.HALF_UP);
            project.put("amount", amount);
        }
        return pageResult;
    }

    @Override
    public BigDecimal countSubsidy(String chainId, String nodeId, String cityId, String areaId, String year) {
        BoolQueryBuilder boolQueryBuilder = buildSubsidyProjectQuery(chainId, nodeId, cityId, areaId);
        if (StringUtils.isNotEmpty(year)){
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("start_date").format("yyyy").gte(year).lte(year));
        }
        String sumAggregationKey = TERMS_BUCKET_PREFIX + "apply_expense";
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum(sumAggregationKey).field("apply_expense");
        AggregationPageResult aggregationPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PROJECT.getEsIndex(), boolQueryBuilder, sumAggregationBuilder);
        ParsedSum parsedSum = (ParsedSum) aggregationPageResult.getSearchResponse().getAggregations().asMap().get(sumAggregationKey);
        return BigDecimal.valueOf(parsedSum.getValue()).setScale(0, RoundingMode.HALF_UP);
    }

    @Override
    public Map<String, Long> getProjectCountMap(String chainId, String nodeId, String cityId, String areaId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, cityId, areaId, false);
        boolQueryBuilder.mustNot(QueryBuilders.termQuery(COLUMN_PROJECT_TYPE, INVESTMENT_ATTRACT_PROJECT));
        return EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.PROJECT, boolQueryBuilder, "project_type");
    }

    @Override
    public Long countSubsidyCompanyNum(String chainId, String nodeId, String cityId, String areaId) {
        BoolQueryBuilder boolQueryBuilder = buildSubsidyProjectQuery(chainId, nodeId, cityId, areaId);
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("apply_expense").gt(0));
        Map<String, Long> unitMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.PROJECT, boolQueryBuilder, "undertaking_unit.name");
        return unitMap.keySet().stream().count();
    }

    @Override
    public Map<String, Object> getInvestmentAndSubsidyTend(String chainId, String nodeId, String cityId, String areaId) {
        String yearAggregationKey = TERMS_BUCKET_PREFIX + "acceptance_date";
        DateRangeAggregationBuilder yearAggregationBuilder = AggregationBuilders.dateRange(yearAggregationKey).format("yyyy").field("acceptance_date");
        List<String> years = DateUtils.getRecentYears(5);
        for (String year : years) {
            yearAggregationBuilder.addRange(Integer.parseInt(year), Integer.parseInt(year) + 1);
        }
        BoolQueryBuilder boolQueryBuilder = buildSubsidyProjectQuery(chainId, nodeId, cityId, areaId);
        String subsidyAggregationKey = TERMS_BUCKET_PREFIX + "apply_expense";
        SumAggregationBuilder subisidyAggregationBuilder = AggregationBuilders.sum(subsidyAggregationKey).field("apply_expense");
        String investAggregationKey = TERMS_BUCKET_PREFIX + "amount";
        SumAggregationBuilder investAggregationBuilder = AggregationBuilders.sum(investAggregationKey).field("amount");
        yearAggregationBuilder.subAggregation(subisidyAggregationBuilder);
        yearAggregationBuilder.subAggregation(investAggregationBuilder);

        AggregationPageResult aggregationPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PROJECT.getEsIndex(), boolQueryBuilder, yearAggregationBuilder);
        ParsedDateRange ranges = (ParsedDateRange) aggregationPageResult.getSearchResponse().getAggregations().asMap().get(yearAggregationKey);
        Map<String, Object> indexList = new LinkedHashMap<>();
        for (Range.Bucket bucket : ranges.getBuckets()) {
            ParsedSum subsidySum = (ParsedSum) bucket.getAggregations().asMap().get(subsidyAggregationKey);
            ParsedSum investSum = (ParsedSum) bucket.getAggregations().asMap().get(investAggregationKey);
            Map<String, BigDecimal> yearMap = new HashMap<>();
            yearMap.put("subsidy", BigDecimal.valueOf(subsidySum.getValue()).setScale(0, RoundingMode.HALF_UP));
            yearMap.put("invest", BigDecimal.valueOf(investSum.getValue()).setScale(0, RoundingMode.HALF_UP));
            indexList.put(String.valueOf(Math.round(Double.valueOf(bucket.getToAsString())) - 1), yearMap);
        }
        return indexList;
    }

    @Override
    public BoolQueryBuilder buildSubsidyProjectQuery(String chainId, String nodeId, String cityId, String areaId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, cityId, areaId, false);
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery(COLUMN_PROJECT_TYPE, Arrays.asList("国家自然科学基金项目", INVESTMENT_ATTRACT_PROJECT)));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("apply_expense").gt(0));
        return boolQueryBuilder;
    }

    @Override
    public Map<String, BigDecimal> getSubsidyTendGroupByTag(String cityId, String areaId, boolean isTechCompany) {
        // 初始化
        int pastYears = 6;
        LocalDate startDate = new LocalDate().minusYears(pastYears);
        final BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(null, null, cityId, areaId, false);
        queryBuilder.filter(QueryBuilders.rangeQuery("acceptance_date").gte(startDate.toString("yyyy") + "-01-01"));
        List<String> highTechTag = CompanyTagEnum.listCompanyTags();
        highTechTag.add("高技术型企业");
        String tagFiled = "undertaking_unit.tag.name";
        if (isTechCompany) {
            queryBuilder.filter(QueryBuilders.termsQuery(tagFiled, highTechTag));
        } else {
            queryBuilder.mustNot(QueryBuilders.termsQuery(tagFiled, highTechTag));
        }
        String yearAggregationKey = TERMS_BUCKET_PREFIX + "acceptance_date";
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(yearAggregationKey).field("acceptance_date").format("yyyy").size(10000);
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum("amount").field("apply_expense");
        termsAggregationBuilder.subAggregation(sumAggregationBuilder);
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PROJECT.getEsIndex(), queryBuilder, termsAggregationBuilder);
        Map<String, BigDecimal> subsidyMap = new LinkedHashMap<>();
        Terms terms = (Terms) pageResult.getSearchResponse().getAggregations().asMap().get(yearAggregationKey);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            ParsedSum inventorNum = (ParsedSum) bucket.getAggregations().asMap().get("amount");
            String year = bucket.getKeyAsString();
            if (subsidyMap.containsKey(year)){
                subsidyMap.put(year, BigDecimal.valueOf(inventorNum.getValue()).add(subsidyMap.get(year)));
            }else{
                subsidyMap.put(year, BigDecimal.valueOf(inventorNum.getValue()));
            }
        }
        return subsidyMap;
    }

    @Override
    public EsPageResult page4Subsidy(Integer pageNum, Integer pageSize, String chainId, String nodeId, String cityId, String areaId,
                                     String companyName, String projectType, String projectName) {
        BoolQueryBuilder boolQueryBuilder = buildSubsidyProjectQuery(chainId, nodeId, cityId, areaId);
        if (StringUtils.isNotEmpty(companyName)){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("undertaking_unit.name", "*" + companyName + "*"));
        }
        if (StringUtils.isNotEmpty(projectType)){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("project_type", "*" + projectType + "*"));
        }
        if (StringUtils.isNotEmpty(projectName)){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("name.term", "*" + projectName + "*"));
        }
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, pageNum, pageSize,
                Arrays.asList("undertaking_unit","apply_expense","acceptance_date", "project_type", "name").toArray(new String[0]), null, "acceptance_date:desc");
        SearchResponse response = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.PROJECT.getEsIndex());
        EsPageResult pageResult = ElasticsearchBuilder.buildPageResult(response);
        List<Map<String, Object>> records = pageResult.getList();
        if (CollectionUtils.isNotEmpty(records)){
            for (Map<String, Object> record: records){
                if (!record.containsKey("acceptance_date") || record.get("acceptance_date") == null){
                    continue;
                }
                String acceptanceDate = (String) record.get("acceptance_date");
                record.put("start_date", acceptanceDate.substring(0, 4));
            }
        }
        return pageResult;
    }

    @Override
    public Long countSubsidyNum(String chainId, String nodeId, String cityId, String areaId) {
        BoolQueryBuilder boolQueryBuilder = buildSubsidyProjectQuery(chainId, nodeId, cityId, areaId);
        return elasticsearchHelper.countRequest(EsIndexEnum.PROJECT.getEsIndex(), boolQueryBuilder);
    }
}
