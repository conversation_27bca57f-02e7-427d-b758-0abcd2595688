package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeakDetail;

/**
 * <AUTHOR>
 * @date 2024/12/12 14:01
 */
public interface IIndustryChainNodeWeakService extends IService<IndustryChainNodeWeak> {

    /**
     * 查询关键核心技术的技术简介和原因分析
     *
     * @param id
     * @return
     */
    IndustryChainNodeWeakDetail getDetailById(String id);
}
