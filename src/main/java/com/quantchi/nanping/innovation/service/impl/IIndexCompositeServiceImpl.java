package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDAO;
import com.quantchi.nanping.innovation.model.IndexComposite;
import com.quantchi.nanping.innovation.service.IIndexCompositeService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/2/24 14:43
 */
@Service
public class IIndexCompositeServiceImpl extends ServiceImpl<IndexCompositeDAO, IndexComposite> implements IIndexCompositeService {
    @Override
    public List<IndexComposite> listByPid(String pid) {
        return this.list(Wrappers.lambdaQuery(IndexComposite.class).eq(IndexComposite::getPid, pid)
                .orderByAsc(IndexComposite::getId));
    }

    @Override
    public List<IndexComposite> listByPids(Set<String> pids) {
        return this.list(Wrappers.lambdaQuery(IndexComposite.class).in(IndexComposite::getPid, pids)
                .orderByAsc(IndexComposite::getId));
    }
}
