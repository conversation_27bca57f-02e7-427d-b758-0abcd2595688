package com.quantchi.nanping.innovation.service.impl;

import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.*;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.service.library.FinanceService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 驾驶舱服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CockpitServiceImpl implements ICockpitService {

    private final CompanyService companyService;
    private final IndustryChainService industryChainService;
    private final IndexService indexService;
    private final PatentService patentService;
    private final FinanceService financeService;
    private final TalentService talentService;

    @Override
    public Map<String, Object> industryDetail(String chainId, String regionId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        // 链上企业分布
        // 链上企业分布-链上企业数量
        resultMap.put("companyNum", companyService.countNumInChain(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
        // 链上企业分布-布局产业链节点（挂载企业的节点）
        resultMap.put("nodeNum", companyService.countNodeNumRelatedCompany(chainId, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
        // 链上企业分布-企业类型统计
        resultMap.put("typeCount", companyService.getCompanyTypeAnalyze(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
        return resultMap;
    }

    @Override
    public PieVO getNodeDistribution(String chainId) {
        PieVO result = new PieVO();
        List<CommonIndexBO> indexBOList = industryChainService.countNodeTypeMapByChainId(chainId, null, false);
        result.setIndexList(indexBOList);
        Long totalNum = 0L;
        for (CommonIndexBO bo : indexBOList) {
            totalNum += Long.parseLong(String.valueOf(bo.getData()));
        }
        result.setTotal(totalNum);
        return result;
    }

    @Override
    public Map<String, Object> indexDetail(String chainId, String regionId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        // 绿色创新指数 环比
        Pair<BigDecimal, BigDecimal> fusionPair = indexService.getFusionIndexByChainIdAndRegionId(chainId,
                StringUtils.isEmpty(regionId) ? CommonConstant.DIVISION_NANPING.getId() : regionId);
        resultMap.put("techPoint", fusionPair.getLeft());
        resultMap.put("increase", fusionPair.getRight());
        // 专利数量
        resultMap.put("patentNum", patentService.countPatentNumByChainIdAndRegionId(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId));
        // 链上企业
        resultMap.put("companyNum", companyService.countNumInChain(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
        // 投融资规模
        Map<String, BigDecimal> financeStatistic = financeService.getStatisticByChain(chainId, CommonConstant.DIVISION_NANPING.getId(), regionId);
        BigDecimal financeAmount = financeStatistic.get("financeAmount");
        if (financeAmount == null || financeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            financeAmount = null;
        }
        resultMap.put("financeAmount", financeAmount);
        // 投融资笔数
        resultMap.put("financeNum", financeService.countFinanceNumByChain(chainId, CommonConstant.DIVISION_NANPING.getId(), regionId, null));
        // 本地人才
        resultMap.put("localNum", talentService.countExpertNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
        return resultMap;
    }
}
