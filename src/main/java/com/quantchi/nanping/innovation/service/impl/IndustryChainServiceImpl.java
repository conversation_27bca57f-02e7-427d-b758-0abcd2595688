package com.quantchi.nanping.innovation.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.db.PageResult;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.*;
import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.*;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.model.vo.ChainNodeTreeVO;
import com.quantchi.nanping.innovation.model.vo.WeakNodeAnalysisVO;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.service.library.RiskService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.SpringUtils;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-04
 */
@Service
public class IndustryChainServiceImpl extends ServiceImpl<IndustryChainDAO, IndustryChain> implements IndustryChainService {

    private static final String ORDINARY_NODE_TYPE = "其他";

    @Autowired
    private IndustryChainNodeDAO industryChainNodeDAO;

    @Autowired
    private IndustryChainNodeTypeDAO chainNodeTypeDAO;

    @Autowired
    private IIndexFusionService indexFusionService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private RiskService riskService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IndustryChainNodeWeakDAO weakDAO;

    @Autowired
    private IndustryChainNodeTypeDefineMapper defineMapper;

    @Autowired
    private IndustryChainNodeWeakProjectDAO weakProjectDAO;

    @Override
    public String getParentNodeIdByChainId(String chainId) {
        return getParentNodeByChainId(chainId).getId();
    }

    @Override
    public List<IndustryChainNode> getNodesByChainNameAndLevel(String chainName, int level) {
        IndustryChain chain = this.getOne(Wrappers.lambdaQuery(IndustryChain.class)
                .eq(IndustryChain::getName, chainName));
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .eq(IndustryChainNode::getChainId, chain.getId())
                .eq(IndustryChainNode::getLevel, level));
    }

    @Override
    public List<IndustryChainNode> getTreesByNodeIds(List<String> chainNodeIds) {
        List<IndustryChainNode> nodeList = industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .in(IndustryChainNode::getId, chainNodeIds));
        return buildTrees(nodeList);
    }

    @Override
    public List<IndustryChainNode> buildTrees(List<IndustryChainNode> nodeList) {
        List<IndustryChainNode> rootList = new ArrayList<>();
        Map<String, IndustryChainNode> nodeIdMap = new HashMap<>();
        for (IndustryChainNode node : nodeList) {
            if (StringUtils.isBlank(node.getParentId())) {
                rootList.add(node);
            }
            nodeIdMap.put(node.getId(), node);
        }
        // 再一次循环，找父亲节点
        for (IndustryChainNode node : nodeIdMap.values()) {
            if (StringUtils.isBlank(node.getParentId())) {
                continue;
            }
            IndustryChainNode parent = nodeIdMap.get(node.getParentId());
            if (parent == null) {
                rootList.add(node);
                continue;
            }
            parent.getChildren().add(node);
        }
        return rootList;
    }

    @Override
    public List<IndustryChainNode> getNodesByChainIdAndStartLevel(String chainId, int startLevel) {
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .eq(IndustryChainNode::getChainId, chainId)
                .ge(IndustryChainNode::getLevel, startLevel)
                .orderByAsc(IndustryChainNode::getLevel)
                .orderByAsc(IndustryChainNode::getId));
    }

    @Override
    public List<IndustryChainNode> getNodeNameByNodeIds(Set<String> relateNodeIds) {
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .select(IndustryChainNode::getId, IndustryChainNode::getName, IndustryChainNode::getChainId)
                .in(IndustryChainNode::getId, relateNodeIds)
                .orderByAsc(IndustryChainNode::getLevel));
    }

    @Override
    public List<IndustryChainNode> getNodesByChainIdAndLevels(String chainId, int minLevel, int maxLevel) {
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .select(IndustryChainNode::getId, IndustryChainNode::getName, IndustryChainNode::getParentId, IndustryChainNode::getChainId)
                .eq(IndustryChainNode::getChainId, chainId)
                .le(IndustryChainNode::getLevel, maxLevel)
                .ge(IndustryChainNode::getLevel, minLevel));
    }

    @Override
    public List<IndustryChainNode> getSelectedTreeIncludesTwoLevel(String chainId) {
        // 查询所有可用的产业链
        List<IndustryChain> chainList = this.list(Wrappers.lambdaQuery(IndustryChain.class)
                .eq(StringUtils.isNotEmpty(chainId), IndustryChain::getId, chainId)
                .eq(IndustryChain::getIsValid, 1)
                .orderByAsc(IndustryChain::getSort));
        List<IndustryChainNode> rootList = new ArrayList<>(chainList.size());
        Map<String, IndustryChainNode> rootIdMap = new HashMap<>();
        for (IndustryChain chain : chainList) {
            IndustryChainNode root = new IndustryChainNode();
            root.setChainId(chain.getId());
            root.setName(chain.getName());
            root.setId(getParentNodeIdByChainId(chain.getId()));
            rootList.add(root);
            rootIdMap.put(root.getChainId(), root);
        }
        // 查询产业链的二级节点
        List<IndustryChainNode> children = industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .select(IndustryChainNode::getId, IndustryChainNode::getName, IndustryChainNode::getChainId)
                .in(IndustryChainNode::getChainId, rootIdMap.keySet())
                .eq(IndustryChainNode::getLevel, 2)
                .orderByAsc(IndustryChainNode::getId));
        for (IndustryChainNode child : children) {
            rootIdMap.get(child.getChainId()).getChildren().add(child);
        }
        return rootList;

    }

    @Override
    public List<IndustryChainNode> listNodeNameByNodeIds(Set<String> nodeIdSet) {
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .select(IndustryChainNode::getId, IndustryChainNode::getName)
                .in(IndustryChainNode::getId, nodeIdSet));
    }

    @Override
    public String getChainIdByName(String chainName) {
        return this.getOne(Wrappers.lambdaQuery(IndustryChain.class)
                        .eq(IndustryChain::getName, chainName))
                .getId();
    }

    @Override
    public String getChainNameById(String chainId) {
        return this.getOne(Wrappers.lambdaQuery(IndustryChain.class)
                        .eq(IndustryChain::getId, chainId))
                .getName();
    }

    @Override
    public IndustryChainNode buildChainTree(Map<String, Object> source) {
        final List<Map<String, Object>> chainNodeList =
                (List<Map<String, Object>>) source.get("chain_node");
        if (CollUtil.isEmpty(chainNodeList)) {
            return null;
        }
        IndustryChainNode root = new IndustryChainNode();
        root.setName((String) source.get("name"));
        List<String> chainNodeIds = new ArrayList<>();
        for (Map<String, Object> chainNode : chainNodeList) {
            chainNodeIds.add((String) chainNode.get("id"));
        }
        List<IndustryChainNode> childTrees = getTreesByNodeIds(chainNodeIds);
        root.setChildren(childTrees);
        return root;
    }

    @Override
    public IndustryChainNode getNodeById(String nodeId) {
        return industryChainNodeDAO.selectById(nodeId);
    }

    @Override
    public IndustryChainNode getParentNodeByChainId(String chainId) {
        return industryChainNodeDAO.selectOne(Wrappers.lambdaQuery(IndustryChainNode.class)
                .eq(IndustryChainNode::getChainId, chainId)
                .eq(IndustryChainNode::getLevel, 1));
    }

    @Override
    public List<IndustryChain> listChain(boolean isAll) {
        return this.list(Wrappers.lambdaQuery(IndustryChain.class)
                .eq(!isAll, IndustryChain::getIsValid, 1)
                .orderByAsc(IndustryChain::getSort));
    }

    @Override
    public List<ChainNodeTreeVO> getTreeNodesByChainId(String chainId) {
        List<IndustryChainNode> nodeList = getNodesByChainIdAndStartLevel(chainId, 0);
        List<ChainNodeTreeVO> treeNodeList = new ArrayList<>(nodeList.size());
        for (IndustryChainNode node : nodeList) {
            treeNodeList.add(ChainNodeTreeVO.transfromChainNode(node));
        }
        return treeNodeList;
    }

    @Override
    public Map<String, List<String>> getNodeTypeMapByChainId(String chainId) {
        List<IndustryChainNodeType> nodeTypeList = listChainNodeType(chainId, null);
        return nodeTypeList.stream().collect(Collectors.groupingBy(IndustryChainNodeType::getNodeId, Collectors.mapping(IndustryChainNodeType::getNodeType, Collectors.toList())));
    }

    @Override
    public List<CommonIndexBO> countNodeTypeMapByChainId(String chainId, String nodeId, boolean includeOrdinary) {
        List<IndustryChainNodeType> nodeTypeList = listChainNodeType(chainId, nodeId);
        Map<String, Long> nodeCountMap = nodeTypeList.stream().collect(Collectors.groupingBy(IndustryChainNodeType::getNodeType, Collectors.counting()));
        List<String> nodeTypeNames = Arrays.asList("强链", "补链", "固链", "拓链");
        List<CommonIndexBO> res = new ArrayList<>();
        for (int i = 0; i < nodeTypeNames.size(); i++) {
            String nodeTypeName = nodeTypeNames.get(i);
            res.add(new CommonIndexBO(String.valueOf(i + 1), nodeTypeName,
                    nodeCountMap.containsKey(nodeTypeName) ? nodeCountMap.get(nodeTypeName) : 0, "个"));
        }
        if (includeOrdinary) {
            // 计算普通节点数量
            Set<String> typeNodeIds = nodeTypeList.stream().map(IndustryChainNodeType::getNodeId).collect(Collectors.toSet());
            Long totalNum = industryChainNodeDAO.selectCount(Wrappers.lambdaQuery(IndustryChainNode.class)
                    .eq(IndustryChainNode::getChainId, chainId)
                    .and(StringUtils.isNotBlank(nodeId), w -> {
                        w.eq(IndustryChainNode::getId, nodeId)
                                .or().eq(IndustryChainNode::getParentId, nodeId);
                    })
                    .gt(IndustryChainNode::getLevel, 1));
            nodeCountMap.put(ORDINARY_NODE_TYPE, totalNum - typeNodeIds.size());
        }
        return res;
    }

    @Override
    public Long countNodes(String chainId) {
        return industryChainNodeDAO.selectCount(Wrappers.lambdaQuery(IndustryChainNode.class)
                .eq(IndustryChainNode::getChainId, chainId)
                .gt(IndustryChainNode::getLevel, 1));
    }

    @Override
    public Page<Map<String, Object>> page4NodeType(Integer pageNum, Integer pageSize, String chainId, String parentId, Integer nodeTypeId, String keyword) {
        Page<IndustryChainNodeType> objectPage = chainNodeTypeDAO.page(new Page(pageNum, pageSize), chainId, parentId, nodeTypeId, keyword,
                SpringUtils.getActiveProfile().startsWith("prod") ? DbType.MYSQL.getDb() : DbType.DM.getDb());
        List<IndustryChainNodeType> nodeTypeList = objectPage.getRecords();
        if (CollectionUtils.isEmpty(nodeTypeList)){
            return new Page<>();
        }
        List<Map<String, Object>> mapObjectList = new ArrayList<>();
        for (IndustryChainNodeType type: nodeTypeList){
            Map<String, Object> typeMap = new HashMap<>();
            typeMap.put("chain_id", type.getChainId());
            typeMap.put("node_id", type.getNodeId());
            typeMap.put("node_name", type.getNodeName());
            typeMap.put("type_id", type.getTypeId());
            typeMap.put("node_type", type.getNodeType());
            mapObjectList.add(typeMap);
        }
        Page<Map<String, Object>> resultPage = new Page<>();
        BeanUtils.copyProperties(objectPage, resultPage);
        resultPage.setRecords(mapObjectList);
        return resultPage;
    }

    @Override
    public List<IndustryChainNode> listRoots() {
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .eq(IndustryChainNode::getLevel, 1)
                .orderByAsc(IndustryChainNode::getChainId));
    }

    @Override
    public List<IndustryChainNodeType> listByNodeType(String chainId, String nodeTypeId) {
        return chainNodeTypeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNodeType.class)
                .eq(IndustryChainNodeType::getChainId, chainId)
                .eq(StringUtils.isNotEmpty(nodeTypeId), IndustryChainNodeType::getTypeId, nodeTypeId)
                .orderByAsc(IndustryChainNodeType::getNodeId));
    }

    @Override
    public List<IndustryChainNodeWeak> listWeakNodesByChainId(String chainId) {
        return weakDAO.selectList(Wrappers.lambdaQuery(IndustryChainNodeWeak.class)
                .eq(org.apache.commons.lang3.StringUtils.isNotEmpty(chainId), IndustryChainNodeWeak::getChainId, chainId)
                .orderByAsc(IndustryChainNodeWeak::getId));
    }

    @Override
    public List<WeakNodeAnalysisVO> listWeakNodesAnalysisByChainId(String chainId) {
        List<IndustryChainNodeWeak> weakNodeList = listWeakNodesByChainId(chainId);
        if (CollectionUtils.isEmpty(weakNodeList)) {
            return new ArrayList<>(0);
        }
        // 查询es实体
        EsPageResult pageResult = riskService.page(chainId, 1, 100, null);
        Map<String, String> descMap = new HashMap<>();
        for (Map<String, Object> techInfo : pageResult.getList()) {
            if (techInfo.containsKey("description")) {
                descMap.put((String) techInfo.get("id"), (String) techInfo.get("description"));
            }
        }
        Map<String, List<IndustryChainNodeWeak>> weakGroup = weakNodeList.stream().collect(Collectors.groupingBy(IndustryChainNodeWeak::getName));
        List<IndustryChainNodeWeakProject> weakProjects = weakProjectDAO.selectList(Wrappers.lambdaQuery(IndustryChainNodeWeakProject.class)
                .eq(IndustryChainNodeWeakProject::getChainId, chainId));
        Map<String, Long> weakProjectNumMap = weakProjects.stream().collect(Collectors.groupingBy(IndustryChainNodeWeakProject::getWeakDataId, Collectors.counting()));
        List<WeakNodeAnalysisVO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<IndustryChainNodeWeak>> group : weakGroup.entrySet()) {
            WeakNodeAnalysisVO vo = new WeakNodeAnalysisVO();
            vo.setName(group.getValue().get(0).getName());
            vo.setWeakReason(group.getValue().get(0).getDescription());
            vo.setDataId(group.getValue().get(0).getDataId());
//            if ("1003".equals(chainId)){
//                vo.setSort(weakProjectNumMap.containsKey(vo.getDataId()) ? 0 - weakProjectNumMap.get(vo.getDataId()).intValue() : 0);
//            }else{
                vo.setSort(group.getValue().get(0).getId());
//            }
            List<Map<String, Object>> targetCompanyList = new ArrayList<>();
            for (IndustryChainNodeWeak weak : group.getValue()) {
                Map<String, Object> company = new HashMap<>();
                company.put("id", weak.getCompanyId());
                company.put("name", weak.getCompanyName());
                targetCompanyList.add(company);
            }
            vo.setTargetCompanyList(targetCompanyList);
            vo.setTechDesc(descMap.get(vo.getDataId()));
            resultList.add(vo);
        }
        Collections.sort(resultList, Comparator.comparing(WeakNodeAnalysisVO::getSort));
        return resultList;
    }

    @Override
    public Page<IndustryChainNodeWeak> page4WeakNode(String chainId, int pageNum, int pageSize) {
        return weakDAO.selectPage(new Page<>(pageNum, pageSize), Wrappers.lambdaQuery(IndustryChainNodeWeak.class)
                .eq(StringUtils.isNotEmpty(chainId), IndustryChainNodeWeak::getChainId, chainId)
                .groupBy(IndustryChainNodeWeak::getName)
                .orderByAsc(IndustryChainNodeWeak::getId));
    }

    @Override
    public List<IndustryChainNode> getNodesByChainIds(Set<String> chainId) {
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .in(IndustryChainNode::getChainId, chainId)
                .orderByAsc(IndustryChainNode::getChainId)
                .orderByAsc(IndustryChainNode::getId));
    }

    @Override
    public List<IndustryChainNode> getPathNode(String chainNodeId, String chainId) {
        List<IndustryChainNode> chainNodeList = industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .eq(IndustryChainNode::getChainId, chainId)
                .orderByAsc(IndustryChainNode::getId));
        chainNodeList.removeIf(n -> !chainNodeId.contains(n.getId()));
        return chainNodeList;
    }

    @Override
    public List<IndustryChainNode> listAllTrees() {
        List<IndustryChain> chains = listChain(false);
        List<IndustryChainNode> chainNodeList = getNodesByChainIds(chains.stream().map(IndustryChain::getId).collect(Collectors.toSet()));
        return buildTrees(chainNodeList);
    }

    @Override
    public List<IndustryChainNode> findNodeByKey(String chainId, String key) {
        List<IndustryChainNode> nodeList = industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .eq(IndustryChainNode::getChainId, chainId)
                .like(IndustryChainNode::getName, key)
                .orderByAsc(IndustryChainNode::getId));
        List<IndustryChainNode> allNodeList = getNodesByChainIds(new HashSet<>(Arrays.asList(chainId)));
        for (IndustryChainNode node : nodeList) {
            List<String> path = new ArrayList<>();
            for (IndustryChainNode pathNode : allNodeList) {
                if (node.getId().contains(pathNode.getId()) && !node.getId().equals(pathNode.getId())) {
                    path.add(pathNode.getId());
                }
            }
            node.setPath(org.apache.commons.lang3.StringUtils.join(path, "|"));
        }
        return nodeList;
    }

    @Override
    public List<String> findAncestorsByNodeIds(List<String> nodeIds) {
        // 查询产业链
        List<IndustryChainNode> targetNodeList = industryChainNodeDAO.selectBatchIds(nodeIds);
        // 查询对应产业链节点
        List<IndustryChainNode> chainNodeList = industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .in(IndustryChainNode::getChainId, targetNodeList.stream().map(IndustryChainNode::getChainId).collect(Collectors.toSet()))
                .orderByAsc(IndustryChainNode::getId));
        Set<String> ancestorIds = new HashSet<>();
        for (IndustryChainNode ancestor : chainNodeList) {
            for (String nodeId : nodeIds) {
                if (nodeId.contains(ancestor.getId())) {
                    ancestorIds.add(ancestor.getId());
                }
            }
        }
        return new ArrayList<>(ancestorIds);
    }

    @Override
    public Page<Map<String, Object>> pageWeakNodeCompanyByChainId(Page<Map<String, Object>> page, String chainId, String techId) {
        return weakDAO.selectMapsPage(page, Wrappers.lambdaQuery(IndustryChainNodeWeak.class)
                .eq(StringUtils.isNotEmpty(chainId), IndustryChainNodeWeak::getChainId, chainId)
                .eq(StringUtils.isNotEmpty(techId), IndustryChainNodeWeak::getDataId, techId)
                .isNotNull(IndustryChainNodeWeak::getCompanyId)
                .groupBy(IndustryChainNodeWeak::getCompanyId)
                .orderByAsc(IndustryChainNodeWeak::getId));
    }

    @Override
    public List<IndustryChainNode> getNodeListByMaxLevel(int maxLevel) {
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .le(IndustryChainNode::getLevel, maxLevel)
                .orderByAsc(IndustryChainNode::getChainId)
                .orderByAsc(IndustryChainNode::getId));
    }

    @Override
    public Map<String, List<String>> getNodeTypeDefine(String chainId, Integer nodeTypeId) {
        List<IndustryChainNodeTypeDefine> defineList = defineMapper.selectList(Wrappers.lambdaQuery(IndustryChainNodeTypeDefine.class)
                .eq(IndustryChainNodeTypeDefine::getChainId, chainId)
                .eq(IndustryChainNodeTypeDefine::getTypeId, nodeTypeId)
                .orderByAsc(IndustryChainNodeTypeDefine::getSort));
        return defineList.stream().collect(Collectors.groupingBy(IndustryChainNodeTypeDefine::getType,
                Collectors.mapping(IndustryChainNodeTypeDefine::getDescription, Collectors.toList())));
    }

    @Override
    public List<IndustryChainNode> listByChainIdAndParentNodeId(String chainId, String parentNodeId) {
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .eq(IndustryChainNode::getChainId, chainId)
                .eq(IndustryChainNode::getParentId, parentNodeId)
                .orderByAsc(IndustryChainNode::getId));
    }

    @Override
    public IndustryChainNodeWeak getWeakNodesByDataId(String id) {
        return weakDAO.selectOne(Wrappers.lambdaQuery(IndustryChainNodeWeak.class)
                .eq(IndustryChainNodeWeak::getDataId, id).last("limit 1"));
    }

    @Override
    public List<IndustryChainNodeType> listNodeTypeByNodeId(String nodeId) {
        return chainNodeTypeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNodeType.class).eq(IndustryChainNodeType::getNodeId, nodeId));
    }

    @Override
    public IndustryChainNode getNodeByChainIdAndNodeName(String chainId, String chainNodeName) {
        return industryChainNodeDAO.selectOne(Wrappers.lambdaQuery(IndustryChainNode.class)
                .eq(IndustryChainNode::getChainId, chainId)
                .eq(IndustryChainNode::getName, chainNodeName));
    }

    @Override
    public List<String> getAllNodeNames() {
        List<IndustryChainNode> nodeList = industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .select(IndustryChainNode::getName));
        return nodeList.stream().map(IndustryChainNode::getName).collect(Collectors.toList());
    }

    @Override
    public List<IndustryChainNode> getNodesByNodeIds(Set<String> relateNodeIds) {
        return industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                .in(IndustryChainNode::getId, relateNodeIds)
                .orderByAsc(IndustryChainNode::getId));
    }

    private List<IndustryChainNodeType> listChainNodeType(String chainId, String parentId) {
        List<String> nodeIdRange = new ArrayList<>();
        if (StringUtils.isNotBlank(parentId)) {
            List<IndustryChainNode> nodeList = industryChainNodeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNode.class)
                    .eq(IndustryChainNode::getChainId, chainId)
                    .eq(IndustryChainNode::getParentId, parentId));
            if (CollectionUtils.isNotEmpty(nodeList)) {
                nodeIdRange.addAll(nodeList.stream().map(IndustryChainNode::getId).collect(Collectors.toList()));
            }
            nodeIdRange.add(parentId);
        }
        return chainNodeTypeDAO.selectList(Wrappers.lambdaQuery(IndustryChainNodeType.class)
                .eq(IndustryChainNodeType::getChainId, chainId)
                .in(CollectionUtils.isNotEmpty(nodeIdRange), IndustryChainNodeType::getNodeId, nodeIdRange)
                .orderByAsc(IndustryChainNodeType::getTypeId));
    }
}
