package com.quantchi.nanping.innovation.service.library.impl;

import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.RiskService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/1/10 5:53 下午
 * @description
 */
@Slf4j
@Service
public class RiskServiceImpl implements RiskService {

    @Resource
    private ElasticsearchHelper elasticsearchHelper;

    @Resource
    private IndustryChainService chainService;

    public static Map<String, Long> parseRiskTypeAggregationFromResponse(final SearchResponse searchResponse) {
        Map<String, Long> resultMap = new HashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "type");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            resultMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return resultMap;
    }

    @Override
    public Map<String, Long> getRiskTypeMap(String chainId) {
        final BoolQueryBuilder boolQuery = EsAlterUtil.buildAuthQuery(chainId, null, null, null, false);
        // 和薄弱环节保持一致
        if (!"1003".equals(chainId)){
            List<IndustryChainNodeWeak> weakList = chainService.listWeakNodesByChainId(chainId);
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(weakList)){
                Set<String> dataIds = weakList.stream().map(IndustryChainNodeWeak::getDataId).collect(Collectors.toSet());
                boolQuery.filter(QueryBuilders.idsQuery().addIds(dataIds.toArray(new String[0])));
            }
        }
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "type").field("research_status");
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.BOTTLENECK.getEsIndex(), boolQuery, aggregationBuilder);
        return parseRiskTypeAggregationFromResponse(pageResult.getSearchResponse());
    }

    @Override
    public List<String> getRiskNodeIds(String chainId) {
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
        String aggKey = TERMS_BUCKET_PREFIX + "product";
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(aggKey).field("product_node.id").size(200);
        AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.BOTTLENECK.getEsIndex(), boolQuery, termsAggregationBuilder);
        Terms terms = (Terms) pageResult.getSearchResponse().getAggregations().asMap().get(aggKey);
        List<String> nodeIds = new ArrayList<>();
        for (Terms.Bucket bucket: terms.getBuckets()){
            nodeIds.add(bucket.getKeyAsString());
        }
        return nodeIds;
    }

    @Override
    public Long count(String chainId, String nodeId) {
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
        if (StringUtils.isNotEmpty(nodeId)){
            boolQuery.filter(QueryBuilders.termQuery("chain_node.id", nodeId));
        }
        // 和薄弱环节保持一致
        if (!"1003".equals(chainId)){
            List<IndustryChainNodeWeak> weakList = chainService.listWeakNodesByChainId(chainId);
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(weakList)){
                Set<String> dataIds = weakList.stream().map(IndustryChainNodeWeak::getDataId).collect(Collectors.toSet());
                boolQuery.filter(QueryBuilders.idsQuery().addIds(dataIds.toArray(new String[0])));
            }
        }
        return elasticsearchHelper.countRequest(EsIndexEnum.BOTTLENECK.getEsIndex(), boolQuery);
    }

    @Override
    public Map<String, Object> getById(String id) {
        return elasticsearchHelper.getDataById(EsIndexEnum.BOTTLENECK.getEsIndex(), id, null, null);
    }

    @Override
    public Map<String, Long> getNodeNumMap(String chainId, List<String> nodeIds) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, false);
        if (CollectionUtils.isNotEmpty(nodeIds)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", nodeIds));
        }
        // 按节点分组
        return EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.BOTTLENECK, boolQueryBuilder, "chain_node.id");
    }

    @Override
    public List<String> getCityAdvantageCompanyIdsByChainId(String chainId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, false);
        Map<String, Long> cityAdvantageCompanyMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.BOTTLENECK, boolQueryBuilder, "city_advantage_enterprises.id");
        List<String> targetCompanyIds = new ArrayList<>(cityAdvantageCompanyMap.keySet());
        Collections.sort(targetCompanyIds);
        return targetCompanyIds;
    }

    @Override
    public EsPageResult page(String chainId, Integer pageNum, Integer pageSize, List<String> ids) {
        EsSimpleQuery simpleQuery = new EsSimpleQuery();
        simpleQuery.setChainId(chainId);
        simpleQuery.setPageNum(pageNum);
        simpleQuery.setPageSize(pageSize);
        BoolQueryBuilder alterQuery = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(ids)){
            alterQuery.filter(QueryBuilders.idsQuery().addIds(ids.toArray(new String[0])));
        }
        return EsAlterUtil.esPageSearch(elasticsearchHelper, simpleQuery, EsIndexEnum.BOTTLENECK, alterQuery, null);
    }
}
