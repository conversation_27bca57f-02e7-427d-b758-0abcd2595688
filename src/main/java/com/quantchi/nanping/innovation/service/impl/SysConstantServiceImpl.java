package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.admin.SysConstantMapper;
import com.quantchi.nanping.innovation.model.admin.SysConstant;
import com.quantchi.nanping.innovation.service.ISysConstantService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/1/4 14:52
 */
@Service
public class SysConstantServiceImpl extends ServiceImpl<SysConstantMapper, SysConstant> implements ISysConstantService {
    @Override
    public SysConstant getBySubjectIdAndConstantCode(String subjectId, String constantCode) {
        return this.getOne(Wrappers.lambdaQuery(SysConstant.class)
                .eq(SysConstant::getSubjectId, subjectId)
                .eq(SysConstant::getConstantCode, constantCode));
    }
}
