package com.quantchi.nanping.innovation.service.impl;

import cn.dev33.satoken.exception.NotLoginException;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.component.UnifyGateway;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.service.IMZTService;
import com.quantchi.nanping.innovation.service.ITechCommissionerService;
import com.quantchi.nanping.innovation.utils.AesEncryptUtils;
import com.quantchi.nanping.innovation.utils.HttpClientUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class MZTServiceImpl implements IMZTService {

    /**
     * 应用编号
     */
    @Value("${mzt.accessKey}")
    private String appKey;

    /**
     * 签名密钥
     */
    @Value("${mzt.secretKey}")
    private String secret;

    @Value("${mzt.appId}")
    private String appId;

    @Value("${mzt.appSecret}")
    private String appSecret;

    @Value("${mzt.host}")
    private String host;

    @Value("${mzt.getAppTokenUrl}")
    private String getAppTokenUrl;

    @Value("${mzt.getAccessTokenUrl}")
    private String getAccessTokenUrl;

    @Value("${mzt.getUserInfoUrl}")
    private String getUserInfoUrl;

    private static final String APP_ID = "appId";

    private static final String APP_SECRET = "appSecret";

    private static final String CODE = "code";

    private static final String GRANT_TYPE = "grant_type";

    private static final String GRANT_TYPE_VALUE = "authorization_code";

    private static final String DATA = "data";

    private static final String MSG = "msg";

    private static final String APP_TOKEN = "app_token";

    private static final String ACCESS_TOKEN = "access_token";

    private static final String AUTHORIZATION = "Authorization";

    private static final String APP_TOKEN_H = "App-Token";

    private static final String ACCEPT_CERTIFICATE = "Accept-Certificate";

    private static final String USER_INFO_STR = "userInfoStr";

    private static final String USER_INFO = "userInfo";

    private static final String COMPANY_INFO = "companyInfo";

    @Autowired
    private ITechCommissionerService commissionerService;


    @SneakyThrows
    private String getAppToken() {
        String url = host + getAppTokenUrl;

//        Map<String, String> paramMap = new HashMap<>();
//        paramMap.put(APP_ID, appId);
//        paramMap.put(APP_SECRET, appSecret);
//
//        String res = HttpClientUtils.post(url, paramMap);
//        log.info("url: {}, params: {}, 闵政通返回结果: {}", url, paramMap, res);
//
//        if (StringUtils.isBlank(res)) {
//            log.error("获取app_token异常，闵政通无返回结果");
//            throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
//        }

        // headers 可以为 null
        final Headers headers = new Headers.Builder()
                .build();

        final Map<String, Object> requestParams = new HashMap<>();
        requestParams.put(APP_ID, appId);
        requestParams.put(APP_SECRET, appSecret);

        final RequestBody requestBody = RequestBody.create(
                MediaType.get("application/json; charset=utf-8"),
                JSONUtil.toJsonStr(requestParams)
        );

        // POST 启用签名 禁用防篡改
        UnifyGateway unifyGateway = new UnifyGateway.Builder()
                .setAppKey(appKey)
                .setSecret(secret)
                .enableSignature() // 启用签名
                .disableAntiTamper() // 禁用防篡改
                .setSignType("sha256")
                .build();

        try (Response response = unifyGateway.doPost(url, headers, requestBody)) {
            log.info("response status: {}", response.code());
            log.info("response headers: \n{}", response.headers());
            if (response.body() == null) {
                log.error("获取app_token异常，闵政通无返回结果");
                throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);

            }

            String res = response.body().string();

            log.info("response body: {}", res);

            JSONObject resObj = JSONObject.parseObject(res);

            if (!"200".equals(resObj.getString(CODE))) {
                log.error("获取app_token异常，闵政通返回结果！response: {}", res);
                throw new NotLoginException(resObj.getString(MSG), null, resObj.getString(CODE));
            }

            if (!resObj.containsKey(DATA) || Objects.isNull(resObj.get(DATA))) {
                log.error("获取app_token异常，闵政通返回结果data为空！response: {}", res);
                throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
            }

            JSONObject dataObj = resObj.getJSONObject(DATA);

            if (dataObj.isEmpty() || !dataObj.containsKey(APP_TOKEN)
                    || Objects.isNull(dataObj.get(APP_TOKEN)) || StringUtils.isBlank(dataObj.getString(APP_TOKEN))) {
                log.error("获取app_token异常，闵政通未返回app_token！response: {}", res);
                throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
            }

            log.info("获取闵政通app_token成功，app_token: {}", dataObj.getString(APP_TOKEN));

            return dataObj.getString(APP_TOKEN);

        } catch (Exception e) {
            log.error("获取app_token异常", e);
            throw new RuntimeException(e);
        }
    }


    @Override
    public String getAccessToken(String code) {

        String url = host + getAccessTokenUrl;

        JSONObject param = new JSONObject();
        param.put(CODE, code);
        param.put(GRANT_TYPE, GRANT_TYPE_VALUE);

//        Map<String, String> headers = new HashMap<>();
//        headers.put(APP_TOKEN_H, getAppToken());
//        headers.put(ACCEPT_CERTIFICATE, appId);

//        String res = HttpClientUtils.postForJson(url, param, headers);

//        if (StringUtils.isBlank(res)) {
//            log.error("获取access_token异常，闵政通无返回结果");
//            throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
//        }

        // headers 可以为 null
        final Headers headers = new Headers.Builder()
                .add(APP_TOKEN_H, getAppToken())
                .add(ACCEPT_CERTIFICATE, appId)
                .build();

        final Map<String, Object> requestParams = new HashMap<>();
        requestParams.put(CODE, code);
        requestParams.put(GRANT_TYPE, GRANT_TYPE_VALUE);

        final RequestBody requestBody = RequestBody.create(
                MediaType.get("application/json; charset=utf-8"),
                JSONUtil.toJsonStr(requestParams));

        // POST 启用签名 禁用防篡改
        UnifyGateway unifyGateway = new UnifyGateway.Builder()
                .setAppKey(appKey)
                .setSecret(secret)
                .enableSignature() // 启用签名
                .disableAntiTamper() // 禁用防篡改
                .setSignType("sha256")
                .build();

        try (Response response = unifyGateway.doPost(url, headers, requestBody)) {
            log.info("response status: {}", response.code());
            log.info("response headers: \n{}", response.headers());
            if (response.body() == null) {
                log.error("获取access_token异常，闵政通无返回结果");
                throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
            }

            String res = response.body().string();

            log.info("response body: {}", res);

            JSONObject resObj = JSONObject.parseObject(res);

            if (!"200".equals(resObj.getString(CODE))) {
                log.error("获取access_token异常，闵政通返回结果！response: {}", res);
                throw new NotLoginException(resObj.getString(MSG), null, resObj.getString(CODE));
            }

            if (!resObj.containsKey(DATA) || Objects.isNull(resObj.get(DATA))) {
                log.error("获取access_token异常，闵政通返回结果data为空！response: {}", res);
                throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
            }

            JSONObject dataObj = resObj.getJSONObject(DATA);

            if (dataObj.isEmpty() || !dataObj.containsKey(ACCESS_TOKEN)
                    || Objects.isNull(dataObj.get(ACCESS_TOKEN)) || StringUtils.isBlank(dataObj.getString(ACCESS_TOKEN))) {
                log.error("获取access_token异常，闵政通未返回access_token！response: {}", res);
                throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
            }

            log.info("获取闵政通access_token成功，access_token: {}", dataObj.getString(ACCESS_TOKEN));

            return dataObj.getString(ACCESS_TOKEN);

            } catch (Exception e) {
                log.error("获取access_token异常！", e);
                throw new RuntimeException(e);
            }

    }

    @Override
    public UserInfoEntity getUserInfo(String accessToken) {

        String url = host + getUserInfoUrl;

//        Map<String, String> headers = new HashMap<>();
//        headers.put(AUTHORIZATION, accessToken);
//        headers.put(APP_TOKEN_H, getAppToken());
//        headers.put(ACCEPT_CERTIFICATE, appId);
//
//        String res = HttpClientUtils.get(url, headers);
//        log.info("url: {}, headers: {}, 闵政通返回结果: {}", url, headers, res);

        final Headers headers = new Headers.Builder()
                .add(AUTHORIZATION, accessToken)
                .add(APP_TOKEN_H, getAppToken())
                .add(ACCEPT_CERTIFICATE, appId)
                .build();
        // GET 启用签名 禁用防篡改
        UnifyGateway unifyGateway = new UnifyGateway.Builder()
                .setAppKey(appKey)
                .setSecret(secret)
                .enableSignature() // 启用签名
                .disableAntiTamper() // 禁用防篡改
                .setSignType("sha256")
                .build();

        try (Response response = unifyGateway.doGet(url, headers)) {
            log.info("response status: {}", response.code());
            log.info("response headers: \n{}", response.headers());
            if (response.body() == null) {
                log.error("获取user_info异常，闵政通无返回结果");
                throw new NotLoginException(NotLoginException.INVALID_TOKEN_MESSAGE, null, NotLoginException.INVALID_TOKEN);
            }

            String res = response.body().string();

            log.info("response body: {}", res);

            JSONObject dataObj = getDataFromRes(res);
            JSONObject userInfo = getUserInfoFromData(dataObj);

            UserInfoEntity userInfoEntity = new UserInfoEntity();
            userInfoEntity.setId(userInfo.getString("id"));
            userInfoEntity.setUsername(userInfo.getString("username"));
            userInfoEntity.setPhone(userInfo.getString("phone"));
            userInfoEntity.setPlatformType(UserInfoEntity.PLATFORM_USER);
            userInfoEntity.setStatus(userInfo.getString("status"));
            userInfoEntity.setAccount(userInfo.getString("name"));

            if (dataObj.containsKey(COMPANY_INFO) || Objects.nonNull(dataObj.get(COMPANY_INFO))) {
                JSONObject companyInfo = dataObj.getJSONObject(COMPANY_INFO);
                userInfoEntity.setPhone(companyInfo.getString("phone"));
                userInfoEntity.setAccount(companyInfo.getString("name"));
                userInfoEntity.setSocialCreditCode(companyInfo.getString("socialCreditCode"));
                userInfoEntity.setUserType(UserInfoEntity.USER_COMPANY);
                userInfoEntity.setCompanyId(companyInfo.getString("id"));
                log.info("企业用户登录成功");
            } else {
                // 区分科特派专家
                if (commissionerService.exist(userInfoEntity.getPhone())){
                    userInfoEntity.setUserType(UserInfoEntity.USER_EXPERT);
                    log.info("科特派专家登录成功");
                }else{
                    userInfoEntity.setUserType(UserInfoEntity.USER_PERSON);
                    log.info("个人用户登录成功");
                }
            }

            return userInfoEntity;

        } catch (Exception e) {
            log.error("获取user_info异常！", e);
            throw new RuntimeException(e);
        }

    }

    @SneakyThrows
    private JSONObject getDataFromRes(String res) {

        if (StringUtils.isBlank(res)) {
            log.error("获取user_info异常，闵政通无返回结果");
            throw new NotLoginException(NotLoginException.DEFAULT_MESSAGE, null, "500");
        }

        JSONObject resObj = JSONObject.parseObject(res);

        if (!"200".equals(resObj.getString(CODE))) {
            log.error("获取user_info异常，闵政通返回结果！response: {}", res);
            throw new NotLoginException(resObj.getString(MSG), null, resObj.getString(CODE));
        }

        if (!resObj.containsKey(DATA) || Objects.isNull(resObj.get(DATA)) || resObj.getJSONObject(DATA).isEmpty()) {
            log.error("获取user_info异常，闵政通返回结果data为空！response: {}", res);
            throw new NotLoginException(NotLoginException.DEFAULT_MESSAGE, null, "500");
        }

        JSONObject dataObj = resObj.getJSONObject(DATA);
        log.info("闵政通返回结果: user_info: {}", dataObj.toString());

        return dataObj;
    }

    /**
     * 从data中获取user_info解密结果
     */
    @SneakyThrows
    private JSONObject getUserInfoFromData(JSONObject dataObj) {

        if (!dataObj.containsKey(USER_INFO_STR) || StringUtils.isBlank(dataObj.getString(USER_INFO_STR))) {
            log.error("获取user_info异常，闵政通返回结果user_info_str为空！data: {}", dataObj);
            throw new NotLoginException(NotLoginException.DEFAULT_MESSAGE, null, "500");
        }

        String userInfoStr = dataObj.getString(USER_INFO_STR);

        //解密
        userInfoStr = AesEncryptUtils.decrypt(userInfoStr);

        JSONObject userInfoObj = JSONObject.parseObject(userInfoStr);

        if (!userInfoObj.containsKey(DATA) || Objects.isNull(userInfoObj.get(DATA))) {
            log.error("获取user_info异常，闵政通返回结果user_info为空！userInfoStr: {}", userInfoStr);
            throw new NotLoginException(NotLoginException.DEFAULT_MESSAGE, null, "500");
        }

        JSONObject userInfoData = userInfoObj.getJSONObject(DATA);

        if (!userInfoData.containsKey(USER_INFO) || Objects.isNull(userInfoData.get(USER_INFO))) {
            log.error("获取userInfo异常，闵政通返回结果userInfo为空！userInfoData: {}", userInfoData);
            throw new NotLoginException(NotLoginException.DEFAULT_MESSAGE, null, "500");
        }

        log.info("userInfoData: {}", userInfoData.getJSONObject(USER_INFO));

        return userInfoData.getJSONObject(USER_INFO);
    }


}
