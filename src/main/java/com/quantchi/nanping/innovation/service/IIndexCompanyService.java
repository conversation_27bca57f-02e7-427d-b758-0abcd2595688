package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.IndexCompany;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.TwoLevelIndex;
import com.quantchi.nanping.innovation.model.vo.InnovationDimensionVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/23 9:54
 */
public interface IIndexCompanyService extends IService<IndexCompany> {

    /**
     * 查询某家企业的科创系数指标雷达图
     *
     * @param companyId
     * @return
     */
    Map<String, InnovationDimensionVO> getIndexRadarMapByCompanyId(String companyId);

    /**
     * 获取某家企业的科创系数
     *
     * @param companyId
     * @return
     */
    BigDecimal getTechPointByCompanyId(String companyId);

    /**
     * 查询目标企业的科创指数
     *
     * @param companyIds
     * @return
     */
    Map<String, BigDecimal> listTechPointByCompanyIds(List<String> companyIds);

    /**
     * 统计科创指数各分数段企业数量
     *
     * @return
     */
    List<CommonIndexBO> getScoreDistribution();

    /**
     * 统计80分以上企业数量增长趋势
     *
     * @return
     */
    List<TwoLevelIndex> get80EntTend();

    /**
     * 统计80分以上企业区县分布
     *
     * @return
     */
    List<CommonIndexBO> get80RegionDistribution();

    /**
     * 统计TOP100企业产业分布
     *
     * @return
     */
    List<CommonIndexBO> getTop100IndustryDistribution();

    /**
     * 目标领域创新力TOP企业
     *
     * @param chainId
     * @param topNum
     * @return
     */
    List<CommonIndexBO> getTop50DomainDistribution(String chainId, int topNum);
}
