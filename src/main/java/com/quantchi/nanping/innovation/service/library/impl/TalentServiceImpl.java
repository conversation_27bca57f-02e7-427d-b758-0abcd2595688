package com.quantchi.nanping.innovation.service.library.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONArray;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.company.service.IModelChatService;
import com.quantchi.nanping.innovation.demand.model.DemandConfig;
import com.quantchi.nanping.innovation.demand.model.DemandExpertConfig;
import com.quantchi.nanping.innovation.demand.service.IDemandConfigService;
import com.quantchi.nanping.innovation.insight.model.bo.ExpertPageBo;
import com.quantchi.nanping.innovation.insight.model.bo.PersonStatsBo;
import com.quantchi.nanping.innovation.model.IndustryChain;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.IWordVectorService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.ResourceService;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import com.zhipu.oapi.service.v4.model.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.IdsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/1/15 5:01 下午
 * @description
 */
@Slf4j
@Service
public class TalentServiceImpl implements TalentService {

    /**
     * 科特派来源标识
     */
    private final int SOURCE_TECH_COMMISSIONER = 2;

    /**
     * 本科学历
     */
    private final List<String> DEGREE_UNDERGRADUATE = Arrays.asList("本科", "大学本科", "大学", "学士");

    /**
     * 硕士学历
     */
    private final List<String> DEGREE_MASTER = Arrays.asList("硕士", "研究生");

    /**
     * 博士学历
     */
    private final List<String> DEGREE_DOCTOR = Arrays.asList("博士");

    /**
     * 本科以上学历
     */
    private final List<String> DEGREE_UNDERGRADUATE_PLUS = Arrays.asList("博士", "硕士", "研究生", "本科", "大学本科", "大学", "学士");

    /**
     * 硕士以上学历
     */
    private final List<String> DEGREE_MASTER_PLUS = Arrays.asList("博士", "硕士", "研究生");

    /**
     * 人才等级
     */
    private final List<String> PERSON_LEVEL = Arrays.asList("A", "B", "C", "D", "E", "F");

    @Resource
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private ThreadPoolTaskExecutor expertExecutor;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private ICompanyNodeRelationService companyNodeRelationService;

    @Autowired
    private IWordVectorService wordVectorService;

    @Autowired
    private IModelChatService chatService;

    @Autowired
    private IDemandConfigService demandConfigService;

    @Override
    public EsPageResult page(ExpertPageBo expertPageBo) {
        boolean isLocal = expertPageBo.getType() != 1;
        BoolQueryBuilder boolQueryBuilder = buildBaseQuery(PersonStatsBo.build4Source(expertPageBo.getChainId(), null, null, null,
                isLocal, false), isLocal);
        if (CollectionUtils.isNotEmpty(expertPageBo.getChainIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.id", expertPageBo.getChainIds()));
        }
        if (CollectionUtils.isNotEmpty(expertPageBo.getNodeIds())) {
//            List<String> nodeIds = industryChainService.findAncestorsByNodeIds(expertPageBo.getNodeIds());
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", expertPageBo.getNodeIds()));
        }
        EsPageResult pageResult = pageQuery(boolQueryBuilder, expertPageBo.getPageNum(), expertPageBo.getPageSize());
        EsAlterUtil.filterChainByChainId(pageResult.getList(), expertPageBo.getChainId());
        return pageResult;
    }

    @Override
    public EsPageResult pageAll(ExpertPageBo expertPageBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(expertPageBo.getChainId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("chain.id", expertPageBo.getChainId()));
        }
        if (CollectionUtils.isNotEmpty(expertPageBo.getChainIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.id", expertPageBo.getChainIds()));
        }
        if (CollectionUtils.isNotEmpty(expertPageBo.getNodeIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", expertPageBo.getNodeIds()));
        }
        return pageQuery(boolQueryBuilder, expertPageBo.getPageNum(), expertPageBo.getPageSize());
    }

    @Override
    public EsPageResult pageQuery(BoolQueryBuilder boolQueryBuilder, int pageNum, int pageSize) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        EsAlterUtil.addSort(searchSourceBuilder, "field_rank:desc", EsAlterUtil.buildCustomSortRule(EsIndexEnum.EXPERT.getEsIndex(), null));
        searchSourceBuilder.from((pageNum - 1) * pageSize);
        searchSourceBuilder.size(pageSize);
        searchSourceBuilder.fetchSource(new String[]{"id", "name", "reference", "chain", "chain_node", "product_node", "organization.name", "prof_title", "final_edu_degree", "desc", "logo"}, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.EXPERT.getEsIndex());
        return ElasticsearchBuilder.buildPageResult(searchResponse);
    }

    @Override
    public PieVO getProfTitleDistribution(PersonStatsBo personStatsBo, boolean auth) {
        BoolQueryBuilder boolQueryBuilder = buildBaseQuery(personStatsBo, auth);
        boolQueryBuilder.filter(QueryBuilders.existsQuery("prof_title"));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("prof_title", StringUtils.EMPTY));
        //职称
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "prof_title").field("prof_title").order(BucketOrder.count(false));
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.EXPERT.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        Map<String, Long> countMap = parseTalentStatisticAggregationFromResponse(pageResult.getSearchResponse(), TERMS_BUCKET_PREFIX + "prof_title");
        // 统计总人数
        BoolQueryBuilder allQueryBuilder = buildBaseQuery(personStatsBo, auth);
        Long totalNum = elasticsearchHelper.countRequest(EsIndexEnum.EXPERT.getEsIndex(), allQueryBuilder);
        // 兼容无法解析的职称 和 职称为空的数据
        Long otherNum = countMap.containsKey("其他") ? countMap.get("其他") : 0L;
        Long existNum = 0L;
        for (Map.Entry<String, Long> entry : countMap.entrySet()) {
            existNum += entry.getValue();
        }
        otherNum += totalNum - existNum;
        if (otherNum > 0L) {
            countMap.put("其他", otherNum);
        }
        PieVO resultVO = new PieVO();
        resultVO.setTotal(totalNum);
        resultVO.setIndexList(EsAlterUtil.dealWithTopDistribution(countMap, null, "人"));
        return resultVO;
    }

    /**
     * 按照来源+产业链+地区构建查询条件
     *
     * @param personStatsBo
     * @param auth
     * @return
     */
    private BoolQueryBuilder buildBaseQuery(PersonStatsBo personStatsBo, boolean auth) {
        BoolQueryBuilder boolQueryBuilder = null;
        if (personStatsBo.isLocal()) {
            boolQueryBuilder = EsAlterUtil.buildAuthQuery(personStatsBo.getChainId(), personStatsBo.getNodeId(), personStatsBo.getCityId(), personStatsBo.getAreaId(), auth);
            if (personStatsBo.isTechCommissioner()) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("source", SOURCE_TECH_COMMISSIONER));
            }
        } else {
            boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
            // 所属产业
            if (StringUtils.isNotBlank(personStatsBo.getChainId())) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("chain.id", personStatsBo.getChainId()));
            }
            if (StringUtils.isNotBlank(personStatsBo.getNodeId())) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("chain_node.id", personStatsBo.getNodeId()));
            }
        }
        return boolQueryBuilder;
    }

    @Override
    public Map<String, Long> getRankDistribution(PersonStatsBo statsBo, boolean auth) {
        // 人才等级
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(statsBo.getChainId(), statsBo.getNodeId(), statsBo.getCityId(), statsBo.getAreaId(), auth);
        Map<String, Long> countMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.EXPERT, boolQueryBuilder, "level");
        Map<String, Long> resultMap = new LinkedHashMap<>();
        for (String level : PERSON_LEVEL) {
            resultMap.put(level, countMap.containsKey(level) ? countMap.get(level) : 0L);
        }
        return resultMap;
    }

    @Override
    public Long countByLevel(PersonStatsBo statsBo, boolean auth) {
        Set<String> levelRange = getTargetLevels(statsBo.getType(), statsBo.isAbove());
        // 人才等级
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(statsBo.getChainId(), statsBo.getNodeId(), statsBo.getCityId(), statsBo.getAreaId(), auth);
        if (CollectionUtils.isNotEmpty(levelRange)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("level", levelRange));
        }
        return elasticsearchHelper.countRequest(EsIndexEnum.EXPERT.getEsIndex(), boolQueryBuilder);
    }

    /**
     * 计算人才等级范围
     *
     * @param lowestLevel
     * @param isAbove
     * @return
     */
    private Set<String> getTargetLevels(String lowestLevel, boolean isAbove) {
        Set<String> levelRange = new HashSet<>();
        if (StringUtils.isEmpty(lowestLevel)) {
            levelRange.addAll(PERSON_LEVEL);
        } else {
            int fromIndex = 0;
            int lastIndex = PERSON_LEVEL.indexOf(lowestLevel);
            levelRange.addAll(PERSON_LEVEL.subList(fromIndex, lastIndex));
            if (fromIndex == lastIndex) {
                levelRange.add(PERSON_LEVEL.get(lastIndex));
            }
        }
        if (isAbove) {
            levelRange.add(lowestLevel);
        }
        return levelRange;
    }

    @Override
    public Map<String, Long> getNodeNumMap(String chainId, List<String> nodeIds, boolean local) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (local) {
            boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, true);
        } else {
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
            boolQueryBuilder.filter(QueryBuilders.termQuery("chain.id", chainId));
        }
        if (CollectionUtils.isNotEmpty(nodeIds)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", nodeIds));
        }
        // 按节点分组
        return EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.EXPERT, boolQueryBuilder, "chain_node.id");
    }

    @Override
    public List<CommonIndexBO> getExpertIndex4Insight(String chainId, String nodeId, String cityId, String areaId) {
        List<CommonIndexBO> resultList = new ArrayList<>();
        // 本地人才
        resultList.add(new CommonIndexBO("本地人才", countExpertNum(chainId, nodeId, cityId, areaId, false), "人"));
        // 市外高层次专家
        resultList.add(new CommonIndexBO("市外高层次专家", countExternalExpertNum(chainId, nodeId), "人"));
        // 单独计算硕士以上学历
        resultList.add(new CommonIndexBO("硕士以上人才", countMasterPlusNum(chainId, nodeId, cityId, areaId, false), "人"));
        return resultList;
    }

    private Long countExternalExpertNum(String chainId, String nodeId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, false);
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        return elasticsearchHelper.countRequest(EsIndexEnum.EXPERT.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public Long countExpertNum(String chainId, String nodeId, String cityId, String areaId, boolean onlyTechCommissioner) {
        BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, cityId, areaId, false);
        if (onlyTechCommissioner) {
            queryBuilder.filter(QueryBuilders.termQuery("source", SOURCE_TECH_COMMISSIONER));
        }
        return elasticsearchHelper.countRequest(EsIndexEnum.EXPERT.getEsIndex(), queryBuilder);
    }

    @Override
    public Long countMasterPlusNum(String chainId, String nodeId, String cityId, String areaId, boolean onlyTechCommissioner) {
        BoolQueryBuilder degreeQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, cityId, areaId, false);
        if (onlyTechCommissioner) {
            degreeQueryBuilder.filter(QueryBuilders.termQuery("source", SOURCE_TECH_COMMISSIONER));
        }
        return countNumByDegree(degreeQueryBuilder, DEGREE_MASTER_PLUS);
    }

    @Override
    public Long countSeniorNum(String chainId, String nodeId, String cityId, String areaId) {
        BoolQueryBuilder titleQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, cityId, areaId, false);
        titleQueryBuilder.filter(QueryBuilders.termQuery("professional_title.id", "高级"));
        return elasticsearchHelper.countRequest(EsIndexEnum.EXPERT.getEsIndex(), titleQueryBuilder);
    }

    @Override
    public PieVO getDegreeDistribution(PersonStatsBo statsBo, boolean auth) {
        BoolQueryBuilder boolQueryBuilder = buildBaseQuery(statsBo, auth);
        Map<String, Long> degreeMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.EXPERT, boolQueryBuilder, "final_edu_degree");
        Long otherNum = 0L, undergraduateNum = 0L, masterNum = 0L, doctorNum = 0L, totalNum = 0L;
        for (Map.Entry<String, Long> entry : degreeMap.entrySet()) {
            if (DEGREE_DOCTOR.contains(entry.getKey())) {
                doctorNum = entry.getValue();
            } else if (DEGREE_MASTER.contains(entry.getKey())) {
                masterNum += entry.getValue();
            } else if (DEGREE_UNDERGRADUATE.contains(entry.getKey())) {
                undergraduateNum += entry.getValue();
            } else {
                otherNum += entry.getValue();
            }
            totalNum += entry.getValue();
        }
        List<CommonIndexBO> resultList = new ArrayList<>();
        resultList.add(new CommonIndexBO("博士", doctorNum, "人"));
        resultList.add(new CommonIndexBO("硕士", masterNum, "人"));
        resultList.add(new CommonIndexBO("学士", undergraduateNum, "人"));
        resultList.add(new CommonIndexBO("其他", otherNum, "人"));
        PieVO vo = new PieVO();
        vo.setIndexList(resultList);
        vo.setTotal(totalNum);
        return vo;
    }

    @Override
    public Map<String, Map<String, Long>> getPortalIndustryDistribution(String regionId) {
        List<IndustryChain> chains = industryChainService.listChain(false);
        Map<String, Map<String, Long>> countMap = new HashMap<>();
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(5);
        for (IndustryChain chain : chains) {
            String chainId = chain.getId();
            CompletableFuture<Void> chainFuture = CompletableFuture.runAsync(() -> {
                Map<String, Long> subMap = new HashMap<>();
                subMap.put("人才数量", countExpertNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
                subMap.put("硕士以上", countMasterPlusNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
                subMap.put("C类以上", countByLevel(PersonStatsBo.build4Level(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, "C", true), false));
                countMap.put(chain.getName(), subMap);
            }, expertExecutor);
            futureList.add(chainFuture);
        }
        final CompletableFuture<Void> allOf =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[chains.size()]));
        try {
            allOf.get();
        } catch (final Exception e) {
            log.error("各产业链人才分布统计异常", e);
        }
        return countMap;
    }

    @Override
    public Long undergraduatePlusNum(String chainId, String nodeId, String cityId, String areaId) {
        BoolQueryBuilder degreeQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, cityId, areaId, false);
        return countNumByDegree(degreeQueryBuilder, DEGREE_UNDERGRADUATE_PLUS);
    }

    @Override
    public List<CommonIndexBO> getDistributionInChain(PersonStatsBo personStatsBo, boolean auth) {
        BoolQueryBuilder queryBuilder = buildBaseQuery(personStatsBo, auth);
        Map<String, Long> map = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.EXPERT, queryBuilder, "chain.name");
        List<CommonIndexBO> resultList = new ArrayList<>();
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            resultList.add(new CommonIndexBO(entry.getKey(), entry.getValue(), null));
        }
        return resultList;
    }

    @Override
    public EsPageResult pageOrderByTitle(int pageNum, int pageSize) {
        EsSimpleQuery esSimpleQuery = new EsSimpleQuery();
        esSimpleQuery.setResourceType(EsIndexEnum.EXPERT.getType());
        // 查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("chain.id", StringUtils.EMPTY));
        esSimpleQuery.setQueryBuilder(boolQueryBuilder);
        // 自定义排序
        List<FunctionScoreQueryBuilder.FilterFunctionBuilder> builders = new ArrayList<>();
        builders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(QueryBuilders.termQuery("professional_title.id", ""), ScoreFunctionBuilders.weightFactorFunction(20)));
        esSimpleQuery.setFilterFunctionBuilders(builders);
        return resourceService.listResource(esSimpleQuery);
    }

    @Override
    public List<CommonIndexBO> getProvinceDistribution(Integer type) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (type == 1) {
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        }
        Map<String, Long> countMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.EXPERT, boolQueryBuilder, "province.name");
        return EsAlterUtil.dealWithTopDistribution(countMap, null, "人");
    }

    @Override
    public List<Map<String, Object>> listExpertByIds(List<String> ids, String... includes) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query((new IdsQueryBuilder()).addIds(ids.toArray(new String[0])));
        if (!ArrayUtil.isEmpty(includes)) {
            FetchSourceContext fetchSourceContext = new FetchSourceContext(true, includes, null);
            searchSourceBuilder.fetchSource(fetchSourceContext);
        }
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.EXPERT.getEsIndex());
        EsPageResult esPageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        return esPageResult == null ? new ArrayList<>(0) : esPageResult.getList();
    }

    @Override
    public Map<String, Object> getInfoById(String expertId) {
        List<Map<String, Object>> expertList = listExpertByIds(Arrays.asList(expertId), null);
        final Map<String, Object> source = new HashMap<>();
        if (CollectionUtils.isNotEmpty(expertList)) {
            source.putAll(expertList.get(0));
        }
        // 所在产业链和链节点
        source.put("chainNodes", companyNodeRelationService.getChainNodeBoByEntityId(expertId, false, true));
        return source;
    }

    @Override
    public boolean isCommissionerInChain(String id) {
        List<Map<String, Object>> expertList = listExpertByIds(Arrays.asList(id), null);
        if (CollectionUtils.isEmpty(expertList)) {
            return false;
        }
        final Map<String, Object> source = expertList.get(0);
        List<Map<String, Object>> chainList = (List<Map<String, Object>>) source.get("chain");
        return CollectionUtils.isNotEmpty(chainList);
    }

    @Override
    public Map<String, Long> countLocalNumByRegion(String chainId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, CommonConstant.DIVISION_NANPING.getId(), null, false);
        return EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.EXPERT, boolQueryBuilder, "area.id");
    }

    @Override
    public Map<String, Object> countLocalNumByNode(String chainId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, true);
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms("node").field("product_node.name")
                .order(BucketOrder.count(false)).size(1000);
        TermsAggregationBuilder degreeAgg = AggregationBuilders.terms("degree").field("final_edu_degree")
                .includeExclude(new IncludeExclude(DEGREE_MASTER_PLUS.toArray(new String[0]), null));
        TermsAggregationBuilder titleAgg = AggregationBuilders.terms("title").field("professional_title.id")
                .includeExclude(new IncludeExclude(new String[]{"高级"}, null));
        TermsAggregationBuilder levelAgg = AggregationBuilders.terms("level").field("level")
                .includeExclude(new IncludeExclude(new String[]{"A", "B", "C", "D", "E"}, null));
        ;
        termsAggregationBuilder.subAggregation(degreeAgg);
        termsAggregationBuilder.subAggregation(titleAgg);
        termsAggregationBuilder.subAggregation(levelAgg);
        AggregationPageResult result = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.EXPERT.getEsIndex(), boolQueryBuilder, termsAggregationBuilder);
        Terms terms = (Terms) result.getSearchResponse().getAggregations().asMap().get("node");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        List<IndustryChainNode> nodeList = industryChainService.getNodesByChainIds(new HashSet<>(Arrays.asList(chainId)));
        Set<String> nodeNames = nodeList.stream().map(IndustryChainNode::getName).collect(Collectors.toSet());
        Map<String, Object> countMap = new LinkedHashMap<>();
        for (Terms.Bucket bucket : buckets) {
            if (!nodeNames.contains(bucket.getKeyAsString())) {
                continue;
            }
            Map<String, Long> subMap = new HashMap<>();
            Terms degreeTerm = (Terms) bucket.getAggregations().getAsMap().get("degree");
            Long degreeNum = 0L;
            for (Terms.Bucket degreeBucket : degreeTerm.getBuckets()) {
                degreeNum += degreeBucket.getDocCount();
            }
            subMap.put("degreeNum", degreeNum);
            Terms titleTerm = (Terms) bucket.getAggregations().getAsMap().get("title");
            Long titleNum = 0L;
            for (Terms.Bucket titleBucket : titleTerm.getBuckets()) {
                titleNum += titleBucket.getDocCount();
            }
            subMap.put("titleNum", titleNum);
            Terms levelTerm = (Terms) bucket.getAggregations().getAsMap().get("level");
            Long levelNum = 0L;
            for (Terms.Bucket levelBucket : levelTerm.getBuckets()) {
                levelNum += levelBucket.getDocCount();
            }
            subMap.put("levelNum", levelNum);
            countMap.put(bucket.getKeyAsString(), subMap);
        }
        return countMap;
    }

    @Override
    public Map<String, Long> countExternalNumByNode(String chainId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("chain.id", chainId));
        Set<String> targetChains = new HashSet<>();
        targetChains.add(chainId);
        List<IndustryChainNode> nodeList = industryChainService.getNodesByChainIds(targetChains);
        List<String> nodeNames = nodeList.stream().map(IndustryChainNode::getName).collect(Collectors.toList());
        String field = "product_node.name";
        String bucketName = TERMS_BUCKET_PREFIX + field;
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(bucketName).field(field).size(10000).order(BucketOrder.count(false))
                .includeExclude(new IncludeExclude(nodeNames.toArray(new String[0]), null));
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.EXPERT.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        Map<String, Long> resultMap = new LinkedHashMap<>();
        if (pageResult.getSearchResponse() == null) {
            return resultMap;
        }
        Terms terms = (Terms) pageResult.getSearchResponse().getAggregations().asMap().get(bucketName);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            resultMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return resultMap;
    }

    @Override
    public Long countExternalNumByChainId(String chainId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("chain.id", chainId));
        return elasticsearchHelper.countRequest(EsIndexEnum.EXPERT.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public List<Map<String, Object>> recommendByIds(String demandId, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return new ArrayList<>(0);
        }
       List<Map<String, Object>> expertList = listExpertByIds(ids, EsIndexEnum.EXPERT.getEsSearchFields());
        if (expertList == null) {
            return new ArrayList<>();
        }
        // 配置得分
        DemandConfig demandConfig = demandConfigService.getByDemandId(demandId);
        List<DemandExpertConfig> expertConfigs = demandConfigService.getExpertConfigByDemandId(demandId);
        // 判断是否使用新得分
        if (demandConfig.getUseOldScoreModel() == 1 && CollectionUtils.isEmpty(expertConfigs)){
            // 将老得分映射到新的分上
            expertConfigs = new ArrayList<>();
            for (Map<String, Object> expertInfo: expertList){
                DemandExpertConfig expertConfig = new DemandExpertConfig();
                expertConfig.setDemandId(demandId);
                expertConfig.setExpertId((String)expertInfo.get("id"));
                Map<String, Object> matchScoreMap = (Map<String, Object>) expertInfo.get("score_model");
                expertConfig.setAchievementScore(new BigDecimal(String.valueOf(matchScoreMap.get("unit"))));
                expertConfig.setMatchedScore(new BigDecimal(String.valueOf(matchScoreMap.get("personal"))));
                expertConfig.setInfluenceScore(new BigDecimal(String.valueOf(matchScoreMap.get("influence"))));
                expertConfig.setCooperationScore(new BigDecimal(String.valueOf(matchScoreMap.get("manage"))));
                expertConfig.setPerformanceScore(new BigDecimal(String.valueOf(matchScoreMap.get("performance"))));
                expertConfig.setFinalScore(new BigDecimal(String.valueOf(matchScoreMap.get("final_score"))));
                expertConfigs.add(expertConfig);
            }
            demandConfigService.saveBatchExpertConfig(demandId, expertConfigs);
        }
        // 组装专家分数
        Map<String, Map<String, Object>> expertMap = new HashMap<>();
        for (Map<String, Object> expertInfo: expertList){
            expertMap.put((String)expertInfo.get("id"), expertInfo);
        }
        Collections.sort(expertConfigs, new Comparator<DemandExpertConfig>() {
            @Override
            public int compare(DemandExpertConfig o1, DemandExpertConfig o2) {
                return o2.getFinalScore().compareTo(o1.getFinalScore());
            }
        });
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (DemandExpertConfig expertConfig: expertConfigs){
            Map<String, Object> expertInfo = expertMap.get(expertConfig.getExpertId());
            if (expertInfo == null){
                continue;
            }
            Map<String, Object> matchScore = new HashMap<>();
            matchScore.put("achievement", expertConfig.getAchievementScore());
            matchScore.put("cooperation", expertConfig.getCooperationScore());
            matchScore.put("final_score", expertConfig.getFinalScore());
            matchScore.put("influence", expertConfig.getInfluenceScore());
            matchScore.put("matched", expertConfig.getMatchedScore());
            matchScore.put("performance", expertConfig.getPerformanceScore());
            expertInfo.put("match_score", matchScore);
            resultList.add(expertInfo);
        }
        return resultList;
    }

    private Long countNumByDegree(BoolQueryBuilder degreeQueryBuilder, List<String> degreeRange) {
        degreeQueryBuilder.filter(QueryBuilders.termsQuery("final_edu_degree", degreeRange));
        return elasticsearchHelper.countRequest(EsIndexEnum.EXPERT.getEsIndex(), degreeQueryBuilder);
    }

    public static Map<String, Long> parseTalentStatisticAggregationFromResponse(final SearchResponse searchResponse, String bucketName) {
        Map<String, Long> talentMap = new LinkedHashMap<>();
        //职称
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(bucketName);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            talentMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return talentMap;
    }

}
