package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.IndexCompositeData;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/24 17:24
 */
public interface IIndexCompositeDataService extends IService<IndexCompositeData> {

    /**
     * 获取企业产业和绍兴市排名
     *
     * @param companyId
     * @return
     */
    Map<String, Object> getInnovationRankByCompanyId(String companyId);

    /**
     * 获取企业画像中的专利统计面板-市高新技术企业平均有效专利数
     *
     * @param companyId
     * @return
     */
    Integer getCityPatentAverageNum(String companyId);

    /**
     * 筛选指标数据
     *
     * @param indexIdList
     * @return
     */
    List<IndexCompositeData> listByIndexIds(List<String> indexIdList);

}
