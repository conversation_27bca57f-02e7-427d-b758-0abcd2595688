package com.quantchi.nanping.innovation.service.library;

import com.quantchi.nanping.innovation.demand.model.DemandConfig;
import com.quantchi.nanping.innovation.demand.model.bo.DemandBO;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.utils.BigDecimalUtil;
import com.quantchi.tianying.model.EsPageResult;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/20 13:44
 */
public interface DemandService {

    String STATUS_DOCKING = "对接中";
    String STATUS_NOT_DOCK = "未对接";

    /**
     * 计数
     *
     * @param chainId
     * @param nodeId
     * @return
     */
    Long count(String chainId, String nodeId);

    /**
     * 查找需求样例数据
     *
     * @param chainId
     * @return
     */
    EsPageResult listSample(String chainId);

    /**
     * 计算近几年的需求对接数量
     *
     * @param years
     * @param statusList 需求状态
     * @return
     */
    Map<Integer, Long> getDemandGrowth(int years, List<String> statusList);

    /**
     * 分页查询
     *
     * @param bo
     * @param matchExpert 是否需要补充推荐专家信息
     * @param onlyCompany 是否仅保留企业需求，不以合作社结尾
     * @return
     */
    EsPageResult page(DemandBO bo, boolean matchExpert, boolean onlyCompany);

    /**
     * 统计已匹配的需求总数
     *
     * @param chainId
     * @return
     */
    Pair<Long, BigDecimal> countMatchedDemand(String chainId);

    /**
     * 计算今日新增
     *
     * @return
     */
    Long countToday();

    /**
     * 按照需求状态进行统计
     *
     * @return
     */
    Pair<Long, Map<String, Long>> countByStatus();

    /**
     * 按产业链统计需求数
     *
     * @return
     */
    Map<String, Long> countByChain();

    /**
     * 查询需求对应的推荐专家
     *
     * @param id
     * @return
     */
    List<Map<String, Object>> getRecommendExpertById(String id);

    /**
     * 按产业链筛选对应的需求(仅保留企业信息)
     *
     * @param chainId
     * @param type 需求类型
     * @return
     */
    List<Map<String, Object>> listDemandByChainId(String chainId, String type);

    /**
     * 统计需求在一级节点上的分布
     *
     * @param chainId
     * @return
     */
    PieVO getDemandFirstNodeDistribution(String chainId);

    /**
     * 分页查询匹配需求的专家列表
     *
     * @param demandId
     * @return
     */
    List<Map<String, Object>> pageMatchingExpert(String demandId);
}
