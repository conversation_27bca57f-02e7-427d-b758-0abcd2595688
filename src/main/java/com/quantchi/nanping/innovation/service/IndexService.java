package com.quantchi.nanping.innovation.service;

import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.CommonIndexChainBO;
import com.quantchi.nanping.innovation.model.bo.IndexQuery;
import com.quantchi.nanping.innovation.model.bo.TwoLevelIndex;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/1/11 14:27
 * 指标service
 */
public interface IndexService {

    /**
     * 根据条件获取四链融合指标
     *
     * @param indexQuery
     * @return
     */
    TwoLevelIndex getFusionIndex(IndexQuery indexQuery);


    /**
     * 获取目标产业链四联融合指标列表
     *
     * @param chainId
     * @return
     */
    List<CommonIndexBO> getFusionPortalIndex(String chainId);

    Map<String, Object> totalIndex(String chainId);

    /**
     * 获得最新绿色创新指数及环比(=(本期数-上期数)/上期数×100%)
     *
     * @param chainId
     * @param regionId
     * @return
     */
    Pair<BigDecimal, BigDecimal> getFusionIndexByChainIdAndRegionId(String chainId, String regionId);
}
