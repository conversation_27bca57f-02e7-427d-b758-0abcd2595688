package com.quantchi.nanping.innovation.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.common.core.utils.StringUtils;
import com.quantchi.nanping.innovation.dao.mapper.admin.SysUserChainScopeMapper;
import com.quantchi.nanping.innovation.model.IndustryChain;
import com.quantchi.nanping.innovation.model.admin.SysUserChainScope;
import com.quantchi.nanping.innovation.service.ISysUserChainScopeService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/28 16:11
 */
@Service
public class SysUserChainScopeServiceImpl extends ServiceImpl<SysUserChainScopeMapper, SysUserChainScope> implements ISysUserChainScopeService {

    @Autowired
    private IndustryChainService chainService;

    @Override
    public Set<String> getCurrentUserPermittedChainIds() {
        if (!StpUtil.isLogin()){
            return new HashSet<>(0);
        }
        List<SysUserChainScope> scopes = this.list(Wrappers.lambdaQuery(SysUserChainScope.class).eq(SysUserChainScope::getUserId, StpUtil.getLoginIdAsString()));
        if (CollectionUtils.isNotEmpty(scopes)){
            return scopes.stream().map(SysUserChainScope::getChainId).collect(Collectors.toSet());
        }else{
            // 默认返回全部产业链
            return chainService.listChain(true).stream().map(IndustryChain::getId).collect(Collectors.toSet());
        }
    }
}
