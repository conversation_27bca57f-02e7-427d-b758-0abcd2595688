package com.quantchi.nanping.innovation.service.library;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.nanping.innovation.finance.model.FinanceLoanDetail;
import com.quantchi.nanping.innovation.finance.model.FinanceNeed;
import com.quantchi.nanping.innovation.model.bo.*;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.tianying.model.EsPageResult;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/12/27 10:23 上午
 * @description
 */
public interface FinanceService {

    /**
     * 获取投融资事件列表
     *
     * @return
     */
    EsPageResult getEventList(FinanceEventBo financeEventBo);

    EsPageResult getEventListTimeLimit(FinanceEventBo financeEventBo);

    /**
     * 获取投融资基金（国内/省内）
     *
     * @param chainId
     * @return
     */
    List<FinanceCountBo> getInvestmentFund(String chainId);

    /**
     * 获取省内投融资情况
     *
     * @return
     */
    Map<String, FinanceAreaCountBo> getAreaFund();

    /**
     * 获取投融资统计总览(科技型企业，非科技型企业)投融资/贷款
     *
     * @return
     */
    Map<String, Map<String, FinanceCompareBo>> getFundStatistic(String indexType);

    /**
     * 获取年度投融资概览情况
     *
     * @param year
     * @param cityId
     * @param areaId
     * @return
     */
    YearFinanceBo getYearFinanceInfo(String year, String cityId, String areaId);

    /**
     * 总览-投融资分析-投资事件和金额变化趋势
     *
     * @return 单位:亿
     */
    Map<String, FinanceAreaCountBo> getFinanceEvent(String chainId, String cityCode, String areaCode);


    /**
     * 总览-投融资分析-融资轮次分析、投融资细分赛道分析、投融资区域分析
     *
     * @param chainId
     * @param field   融资轮次分析:financing_round、投融资细分赛道分析:chain_node.name、投融资区域分析:financing_company.area_name
     * @return
     */
    Map<String, Long> getAnalyzeInfos(String chainId, String cityCode, String areaCode, String field);


    /**
     * 获取投融资金额统计数据(投融资，股权融资)
     *
     * @param cityCode
     * @param areaCode
     * @return
     */
    Map<String, BigDecimal> getStatisticByChain(String chainId, String cityCode, String areaCode);

    /**
     * 统计产业+城市的投融资总额
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param roundType
     * @return
     */
    BigDecimal sumByChainAndRegion(String chainId, String nodeId, String cityId, String areaId, String roundType);


    BigDecimal sumByChainAndRegionTimeLimit(String chainId, String nodeId, String cityId, String areaId, String roundType);

    /**
     * 总览-企业资金规模分析(投融资，股权融资)
     *
     * @param chainId
     * @param cityCode
     * @param areaCode
     * @return
     */
    Map<String, Object> getFinanceScaleTend(String chainId, String cityCode, String areaCode);

    /**
     * 统计融资企业数量
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param roundType
     * @return
     */
    Integer countCompanyByChainAndRegion(String chainId, String nodeId, String cityId, String areaId, String roundType);


    Integer countCompanyByChainAndRegionTimeLimit(String chainId, String nodeId, String cityId, String areaId, String roundType);

    /**
     * 构建投融资事件查询条件
     *
     * @param chainId
     * @param nodeId
     * @param provinceId
     * @param cityId
     * @param areaId
     * @return
     */
    BoolQueryBuilder buildBaseQuery(String chainId, String nodeId, String provinceId, String cityId, String areaId);


    BoolQueryBuilder buildBaseQueryTimeLimit(String chainId, String nodeId, String provinceId, String cityId, String areaId);

    /**
     * 按投资金额规模统计企业数量
     *
     * @param boolQueryBuilder
     * @return
     */
    List<CommonIndexBO> getCompanyCountByAmount(BoolQueryBuilder boolQueryBuilder);

    /**
     * 按融资轮次统计金额
     *
     * @param chainId
     * @param cityId
     * @param areaId
     * @return
     */
    Map<String, BigDecimal> countAmountByRound(String chainId, String cityId, String areaId);

    /**
     * 按产业链/一级节点统计资金情况
     *
     * @param indexType
     * @return
     */
    Map<String, BigDecimal> getFundDistributionInChain(String indexType);

    /**
     * 统计历年福建省及目标地区的投融资总额
     *
     * @param chainId
     * @param cityId
     * @param areaId
     * @return
     */
    Map<String, Object> getFinanceScaleTendIncludeProvince(String chainId, String cityId, String areaId);

    /**
     * 资金获取情况（融资，项目补助，放贷）
     *
     * @return
     */
    List<String> listFundAcquisition(String chainId, boolean fromCockpit);

    /**
     * 资金获取情况（融资，项目补助，放贷）,以对象返回
     *
     * @return
     */
    List<Map<String, Object>> listFundAcquisitionObject(String chainId);

    /**
     * 罗列金融产品和本地机构
     *
     * @param chainId
     * @return
     */
    List<Map<String, Object>> listProductAndOrg(String chainId);

    /**
     * 罗列企业融资需求
     *
     * @param chainId
     * @return
     */
    List<FinanceNeed> getFinancingNeeds(String chainId);

    /**
     * 查询投融资笔数
     *
     * @param chainId
     * @param cityId
     * @param areaId
     * @param roundType
     * @return
     */
    Long countFinanceNumByChain(String chainId, String cityId, String areaId, String roundType);

    Long countFinanceNumByChainTimeLimit(String chainId, String cityId, String areaId, String roundType);

    /**
     * 投融资指标
     *
     * @param chainId
     * @param cityId
     * @param areaId
     * @return
     */
    Map<String, Object> getFinanceInfo(String chainId, String cityId, String areaId);

    /**
     * 资金在节点上的分布情况
     *
     * @param indexType
     * @param chainId
     * @return
     */
    Map<String, Object> getFundDistributionInNode(String indexType, String chainId);

    /**
     * 分页查询贷款细节
     *
     * @param pageNum
     * @param pageSize
     * @param chainId
     * @return
     */
    Page<Map<String, Object>> page4Loan(Integer pageNum, Integer pageSize, String chainId);

    String desensitizeCompanyName(String companyName);

    EsPageResult pageByCompanyId(String id, int pageNum, int pageSize);


//    /**
//     * 分页获取项目补助
//     *
//     * @param chainId
//     * @return
//     */
//    EsPageResult listSubsidyFundAcquisition(String chainId, Integer pageNum, Integer pageSize);
}
