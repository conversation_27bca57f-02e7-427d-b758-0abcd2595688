package com.quantchi.nanping.innovation.service.library;

import com.quantchi.tianying.model.EsPageResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RiskService {


    /**
     * 按产业链+风险类型统计
     *
     * @param chainId
     * @return
     */
    Map<String, Long> getRiskTypeMap(String chainId);

    /**
     * 查询产业链上的关键技术节点（风险节点：有风险解析挂接的节点）
     *
     * @param chainId
     * @return
     */
    List<String> getRiskNodeIds(String chainId);

    /**
     * 计数
     *
     * @param chainId
     * @param nodeId
     * @return
     */
    Long count(String chainId, String nodeId);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    Map<String, Object> getById(String id);

    /**
     * 按产业链节点统计数量
     *
     * @param chainId
     * @param nodeIds
     * @return
     */
    Map<String, Long> getNodeNumMap(String chainId, List<String> nodeIds);

    /**
     * 查询产业链本地优势企业ids
     *
     * @param chainId
     * @return
     */
    List<String> getCityAdvantageCompanyIdsByChainId(String chainId);

    /**
     * 分页查询
     *
     * @param chainId
     * @param pageNum
     * @param pageSize
     * @param ids
     * @return
     */
    EsPageResult page(String chainId, Integer pageNum, Integer pageSize, List<String> ids);
}
