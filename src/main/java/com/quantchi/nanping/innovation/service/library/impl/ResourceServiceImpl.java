package com.quantchi.nanping.innovation.service.library.impl;

import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.library.ResourceService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/1/15 0:47
 */
@Service
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    /**
     * 查询资源
     *
     * @param query
     * @return
     */
    @Override
    public EsPageResult listResource(EsSimpleQuery query) {
        EsIndexEnum esIndexEnum = EsIndexEnum.getEsIndexEnumByType(query.getResourceType());
        return EsAlterUtil.esPageSearch(elasticsearchHelper, query, esIndexEnum, query.getQueryBuilder(), query.getFilterFunctionBuilders());
    }

    @Override
    public List<CommonIndexBO> countByType(EsSimpleQuery esSimpleQuery, String aggregationField, List<String> fieldValue) {
        EsIndexEnum esIndexEnum = EsIndexEnum.getEsIndexEnumByType(esSimpleQuery.getResourceType());
        Map<String, Long> typeMap = EsAlterUtil.getAggregation(elasticsearchHelper, esIndexEnum, esSimpleQuery.getQueryBuilder(), aggregationField);
        List<CommonIndexBO> boList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fieldValue)){
            Long otherNum = 0L;
            for (String type: fieldValue){
                boList.add(new CommonIndexBO(type, typeMap.get(type), null));
            }
            for (Map.Entry<String, Long> entry: typeMap.entrySet()){
                if (fieldValue.contains(entry.getKey())){
                    continue;
                }
                otherNum += entry.getValue();
            }
            if (otherNum > 0L){
                boList.add(new CommonIndexBO("其他", otherNum, null));
            }
        }else{
            boList = EsAlterUtil.dealWithTopDistribution(typeMap, null, null);
        }
        return boList;
    }
}
