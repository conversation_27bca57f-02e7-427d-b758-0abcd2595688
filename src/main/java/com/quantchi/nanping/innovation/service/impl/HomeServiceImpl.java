package com.quantchi.nanping.innovation.service.impl;


import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.insight.model.bo.PersonStatsBo;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.bo.CockpitGeoBO;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.entity.DmDivisionEntity;
import com.quantchi.nanping.innovation.model.enums.AreaAdvantageIndustryEnum;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.model.enums.index.RegionIndexEnum;
import com.quantchi.nanping.innovation.service.HomeService;
import com.quantchi.nanping.innovation.service.IDivisionService;
import com.quantchi.nanping.innovation.service.IRDRatioService;
import com.quantchi.nanping.innovation.service.library.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/26 14:28.
 */
@Service
public class HomeServiceImpl implements HomeService {

    @Autowired
    private IIndexFusionService indexFusionService;

    @Autowired
    private IDivisionService divisionService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private DemandService demandService;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private IRDRatioService rdRatioService;

    @Override
    public List<CockpitGeoBO> listRegion(String chainId) {
        List<CockpitGeoBO> res = new ArrayList<>();
        List<DmDivisionEntity> regionList = divisionService.listByParentId(CommonConstant.DIVISION_NANPING.getId());
        List<String> divisionIds = regionList.stream().map(DmDivisionEntity::getId).collect(Collectors.toList());
        List<IndexFusion> fusionList = indexFusionService.list(chainId, FusionIndexEnum.COMPOSITE.getIndexId(), divisionIds);
        Map<String, BigDecimal> fusionDataMap = fusionList.stream().collect(Collectors.toMap(IndexFusion::getRegionId, IndexFusion::getData));
        Map<String, Long> companyMap = companyService.getHighTechEntNumGroupByDivision(chainId, CommonConstant.DIVISION_NANPING.getId());
        Map<String, BigDecimal> rdMap = rdRatioService.getLatestAreaDataMap(CommonConstant.DIVISION_NANPING.getId());
        Map<String, String> advantageMap = AreaAdvantageIndustryEnum.getAdvantageMap();
        for (DmDivisionEntity division : regionList) {
            CockpitGeoBO temp = new CockpitGeoBO();
            temp.setRegionId(division.getId());
            temp.setRegionName(division.getName());
            temp.setCompanyCount(companyMap.get(division.getId()));
            temp.setFusion(fusionDataMap.get(division.getId()));
            temp.setRd(rdMap.get(division.getId()));
            temp.setAdvantage(advantageMap.get(division.getId()));
            res.add(temp);
        }
        res.sort((c1, c2) -> {
            if (c1.getFusion() == null || c2.getFusion() == null){
                return 0;
            }
            return c2.getFusion().compareTo(c1.getFusion());
        });
        return res;
    }

    @Override
    public List<CommonIndexBO> list4RegionDetail(String chainId, String regionId) {
        List<CommonIndexBO> result = new ArrayList<>();
        // PATENT_NUM
        result.add(new CommonIndexBO(RegionIndexEnum.PATENT_NUM.getIndexName(),
                patentService.countPatentNumByChainIdAndRegionId(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId), null));
        // E_PLUS_PERSON_NUM
        PersonStatsBo bo = new PersonStatsBo(chainId, null,  null, regionId, "E", true);
        result.add(new CommonIndexBO(RegionIndexEnum.E_PLUS_PERSON_NUM.getIndexName(), talentService.countByLevel(bo, false), null));
        // ENT_NUM
        result.add(new CommonIndexBO(RegionIndexEnum.ENT_NUM.getIndexName(),
                companyService.getTotalCountByRegionId(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, null), null));
        // DEMAND_NUM
        result.add(new CommonIndexBO(RegionIndexEnum.DEMAND_NUM.getIndexName(),
                demandService.count(chainId, null), null));
        // LOANS_TOTAL
        result.add(new CommonIndexBO(RegionIndexEnum.LOANS_TOTAL.getIndexName(),
                financeService.sumByChainAndRegion(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, null)));
        return result;
    }
}
