package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.LocalCompanyInvestedDAO;
import com.quantchi.nanping.innovation.model.LocalCompanyInvested;
import com.quantchi.nanping.innovation.service.ILocalInvestmentService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Wrapper;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/24 11:14
 */
@Service
public class LocalInvestmentServiceImpl extends ServiceImpl<LocalCompanyInvestedDAO, LocalCompanyInvested> implements ILocalInvestmentService {
    @Override
    public LocalCompanyInvested getTree() {
        // 初始化根节点
        LocalCompanyInvested root = new LocalCompanyInvested();
        root.setName("央企在南平投资企业");
        root.setLevel(0);
        root.setInvestId("0");
        root.setPath(root.getInvestId());
        // 组装叶子节点
        List<LocalCompanyInvested> leafNodeList = this.list(Wrappers.lambdaQuery(LocalCompanyInvested.class).orderByDesc(LocalCompanyInvested::getShareholdingRatio));
        Map<String, List<LocalCompanyInvested>> groups = leafNodeList.stream().collect(Collectors.groupingBy(LocalCompanyInvested::getInvestorName));
        List<LocalCompanyInvested> subRoots = new ArrayList<>();
        for (Map.Entry<String, List<LocalCompanyInvested>> group: groups.entrySet()){
            LocalCompanyInvested subRoot = new LocalCompanyInvested();
            subRoot.setChildren(group.getValue());
            subRoot.setName(group.getValue().get(0).getInvestorName());
            subRoot.setInvestId(UUID.randomUUID().toString());
            subRoot.setPath(root.getInvestId() + "|" + subRoot.getInvestId());
            subRoot.setLevel(1);
            subRoots.add(subRoot);
            for (LocalCompanyInvested leaf: subRoot.getChildren()){
                leaf.setLevel(2);
                leaf.setName(leaf.getInvesteeName());
                leaf.setPath(subRoot.getPath() + "|"+ leaf.getInvestId());
                leaf.setShareholdingRatio(leaf.getShareholdingRatio().multiply(new BigDecimal(100)).stripTrailingZeros());
            }
        }
        Collections.sort(subRoots, new Comparator<LocalCompanyInvested>() {
            @Override
            public int compare(LocalCompanyInvested o1, LocalCompanyInvested o2) {
                if (o1.getChildren().size() > o2.getChildren().size()){
                    return -1;
                }else{
                    return 1;
                }
            }
        });
        root.setChildren(subRoots);
        return root;
    }
}
