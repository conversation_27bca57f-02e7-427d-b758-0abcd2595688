package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.RDRatio;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/15 17:38
 */
public interface IRDRatioService extends IService<RDRatio> {
    /**
     * 获取目标城市最新的rd占比
     *
     * @param cityId
     * @return
     */
    BigDecimal getLatestByCityId(String cityId);

    /**
     * 获得目标城市下的区县rd占比
     *
     * @param cityId
     * @return
     */
    Map<String, BigDecimal> getLatestAreaDataMap(String cityId);
}
