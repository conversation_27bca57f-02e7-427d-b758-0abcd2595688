package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTargetDAO;
import com.quantchi.nanping.innovation.model.IndustryChainNodeTarget;
import com.quantchi.nanping.innovation.model.bo.CompanyPageBo;
import com.quantchi.nanping.innovation.service.IIndustryChainNodeTargetService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.tianying.model.EsPageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/11 14:46
 */
@Service
public class IndustryChainNodeTargetServiceImpl extends ServiceImpl<IndustryChainNodeTargetDAO, IndustryChainNodeTarget> implements IIndustryChainNodeTargetService {

    @Autowired
    private CompanyService companyService;

    @Override
    public List<IndustryChainNodeTarget> listByNodeIds(List<String> nodeIds) {
        return this.list(Wrappers.lambdaQuery(IndustryChainNodeTarget.class).in(IndustryChainNodeTarget::getNodeId, nodeIds));
    }

    @Override
    public EsPageResult pageByNodeIds(CompanyPageBo pageBo, List<String> nodeIds) {
        Page<Map<String, Object>> nodeTargetPage = this.pageMaps(new Page<>(pageBo.getPageNum(), pageBo.getPageSize()), Wrappers.lambdaQuery(IndustryChainNodeTarget.class)
                .in(IndustryChainNodeTarget::getNodeId, nodeIds)
                .groupBy(IndustryChainNodeTarget::getCompanyId)
                .orderByAsc(IndustryChainNodeTarget::getId));
        List<Map<String, Object>> nodeTargetList = nodeTargetPage.getRecords();
        if (CollectionUtils.isEmpty(nodeTargetList)){
            return null;
        }
        List<String> targetCompanyIds = new ArrayList<>();
        Map<String, Map<String, Object>> nodeTargetIdMap = new HashMap<>();
        for (Map<String, Object> nodeTarget: nodeTargetList){
            String companyId = (String) nodeTarget.get("company_id");
            targetCompanyIds.add(companyId);
            nodeTargetIdMap.put(companyId, nodeTarget);
        }
        pageBo.setCompanyIds(targetCompanyIds);
        pageBo.setPageNum(1);
        pageBo.setPageSize(pageBo.getPageSize());
        EsPageResult companyPage = companyService.getCompanyList(pageBo);
        for (Map<String, Object> companyInfo: companyPage.getList()){
            String companyId = (String) companyInfo.get("id");
            nodeTargetIdMap.get(companyId).putAll(companyInfo);
        }
        companyPage.setPageSize(pageBo.getPageSize());
        companyPage.setTotal(nodeTargetPage.getTotal());
        companyPage.setList(nodeTargetList);
        return companyPage;
    }
}
