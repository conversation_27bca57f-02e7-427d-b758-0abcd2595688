package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.IndexCompanyDAO;
import com.quantchi.nanping.innovation.model.IndexCompany;
import com.quantchi.nanping.innovation.model.IndexComposite;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.TwoLevelIndex;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.vo.InnovationDimensionVO;
import com.quantchi.nanping.innovation.service.IIndexCompanyService;
import com.quantchi.nanping.innovation.service.IIndexCompositeService;
import com.quantchi.nanping.innovation.utils.DateUtils;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/2/23 9:55
 */
@Service
public class IndexCompanyServiceImpl extends ServiceImpl<IndexCompanyDAO, IndexCompany> implements IIndexCompanyService {

    private static final String INDEX_PID = "50000000";

    private static final int SCORE_STANDARD = 60;

    @Autowired
    private IIndexCompositeService indexCompositeService;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Override
    public Map<String, InnovationDimensionVO> getIndexRadarMapByCompanyId(String companyId) {
        List<IndexComposite> indexList = indexCompositeService.listByPid(INDEX_PID);
        Map<String, IndexComposite> secondIndexNameMap = indexList.stream().collect(Collectors.toMap(IndexComposite::getId, Function.identity()));
        List<IndexCompany> indexDataList = this.list(Wrappers.lambdaQuery(IndexCompany.class)
                .in(IndexCompany::getIndexId, secondIndexNameMap.keySet())
                .and(w -> {
                    w.eq(IndexCompany::getCompanyId, companyId)
                            .or()
                            .eq(IndexCompany::getCompanyId, IndexCompany.INDUSTRY_STANDARDS);
                })
                .orderByAsc(IndexCompany::getIndexId)
                .orderByDesc(IndexCompany::getStatYear));
        if (CollectionUtils.isEmpty(indexDataList)) {
            return new HashMap<>(0);
        }
        Map<String, InnovationDimensionVO> dimensionMap = new HashMap<>();
        for (IndexCompany indexData : getLatest(indexDataList)) {
            String name = secondIndexNameMap.get(indexData.getIndexId()).getName();
            dimensionMap.computeIfAbsent(name, n -> new InnovationDimensionVO());
            dimensionMap.get(name).setData(IndexCompany.INDUSTRY_STANDARDS.equals(indexData.getCompanyId()), indexData.getData());
        }
        return dimensionMap;
    }

    @Override
    public BigDecimal getTechPointByCompanyId(String companyId) {
        List<IndexCompany> dataList = this.list(Wrappers.lambdaQuery(IndexCompany.class)
                .eq(IndexCompany::getIndexId, INDEX_PID)
                .eq(IndexCompany::getCompanyId, companyId)
                .orderByDesc(IndexCompany::getStatYear));
        return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0).getData();
    }

    @Override
    public Map<String, BigDecimal> listTechPointByCompanyIds(List<String> companyIds) {
        List<IndexCompany> indexDataList = this.list(Wrappers.lambdaQuery(IndexCompany.class)
                .eq(IndexCompany::getIndexId, INDEX_PID)
                .in(!CollectionUtils.isEmpty(companyIds), IndexCompany::getCompanyId, companyIds)
                .orderByDesc(IndexCompany::getStatYear));
        return getLatest(indexDataList).stream().collect(Collectors.toMap(IndexCompany::getCompanyId, IndexCompany::getData));
    }

    @Override
    public List<CommonIndexBO> getScoreDistribution() {
        // 查询各企业最新年份的科创系数分数
        Map<String, BigDecimal> scoreMap = listTechPointByCompanyIds(null);
        List<Pair<Integer, Integer>> rangeList = Arrays.asList(new ImmutablePair<>(0, 60), new ImmutablePair<>(60, 70),
                new ImmutablePair<>(70, 80), new ImmutablePair<>(80, 90), new ImmutablePair<>(90, 100));
        Map<String, Integer> countMap = new HashMap<>();
        for (BigDecimal score : scoreMap.values()) {
            for (Pair<Integer, Integer> range : rangeList) {
                String key = range.getLeft() + "-" + range.getRight();
                countMap.computeIfAbsent(key, k -> 0);
                if (score.compareTo(new BigDecimal(range.getLeft())) >= 0 && score.compareTo(new BigDecimal(range.getRight())) < 0) {
                    countMap.put(key, countMap.get(key) + 1);
                    continue;
                } else if (score.compareTo(new BigDecimal(range.getRight())) == 0 && score.compareTo(new BigDecimal(100)) == 0) {
                    // 正好等于100 且落在范围内
                    countMap.put(key, countMap.get(key) + 1);
                    continue;
                }
            }
        }
        // 拼装结果
        List<CommonIndexBO> resultList = new ArrayList<>(rangeList.size());
        for (int i = rangeList.size() - 1; i >= 0; i--) {
            String key = rangeList.get(i).getLeft() + "-" + rangeList.get(i).getRight();
            resultList.add(new CommonIndexBO(key, countMap.get(key), null));
        }
        return resultList;
    }

    @Override
    public List<TwoLevelIndex> get80EntTend() {
        List<String> years = DateUtils.getRecentYears(5);
        List<IndexCompany> indexDataList = this.list(Wrappers.lambdaQuery(IndexCompany.class)
                .eq(IndexCompany::getIndexId, INDEX_PID)
                .ge(IndexCompany::getData, SCORE_STANDARD)
                .ge(IndexCompany::getStatYear, years.get(years.size() - 1))
                .orderByDesc(IndexCompany::getStatYear));
        // 补上企业市县名称
        fillArea(indexDataList);
        List<TwoLevelIndex> resultList = new ArrayList<>();
        // 按年份计数
        Map<String, Long> yearGroup = indexDataList.stream().collect(Collectors.groupingBy(IndexCompany::getStatYear, Collectors.counting()));
        TwoLevelIndex total = new TwoLevelIndex();
        total.setName("总");
        for (int i = years.size() - 1; i >= 0; i--) {
            total.getChildList().add(new CommonIndexBO(years.get(i), yearGroup.get(years.get(i)), null));
        }
        resultList.add(total);
        // 按年份和市县统计企业数
        Map<String, Map<String, Long>> group = indexDataList.stream().collect(Collectors.groupingBy(IndexCompany::getAreaName,
                Collectors.groupingBy(IndexCompany::getStatYear, Collectors.counting())));
        for (Map.Entry<String, Map<String, Long>> subGroup : group.entrySet()) {
            TwoLevelIndex one = new TwoLevelIndex();
            one.setName(subGroup.getKey());
            for (int i = years.size() - 1; i >= 0; i--) {
                one.getChildList().add(new CommonIndexBO(years.get(i), subGroup.getValue().get(years.get(i)), null));
            }
            resultList.add(one);
        }
        return resultList;
    }

    @Override
    public List<CommonIndexBO> get80RegionDistribution() {
        List<IndexCompany> indexDataList = getLatest(this.list(Wrappers.lambdaQuery(IndexCompany.class)
                .eq(IndexCompany::getIndexId, INDEX_PID)
                .ge(IndexCompany::getData, SCORE_STANDARD)
                .orderByDesc(IndexCompany::getStatYear)));
        // 补上企业市县名称
        fillArea(indexDataList);
        Map<String, Long> regionDistributionMap = indexDataList.stream().collect(Collectors.groupingBy(IndexCompany::getAreaName, Collectors.counting()));
        List<CommonIndexBO> resultList = new ArrayList<>();
        for (Map.Entry<String, Long> part : regionDistributionMap.entrySet()) {
            resultList.add(new CommonIndexBO(part.getKey(), part.getValue(), null));
        }
        return resultList;
    }

    @Override
    public List<CommonIndexBO> getTop100IndustryDistribution() {
        List<IndexCompany> indexDataList = getLatest(this.list(Wrappers.lambdaQuery(IndexCompany.class)
                .eq(IndexCompany::getIndexId, INDEX_PID)
                .orderByDesc(IndexCompany::getStatYear)));
        indexDataList = indexDataList.subList(0, indexDataList.size() > 100 ? 100 : indexDataList.size());
        Set<String> companyIds = indexDataList.stream().map(IndexCompany::getCompanyId).collect(Collectors.toSet());
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termsQuery("id", companyIds));
        boolQuery.filter(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        String aggregationKey = TERMS_BUCKET_PREFIX + "chain_name";
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(aggregationKey).field("chain.name");
        AggregationPageResult aggregationPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.COMPANY.getEsIndex(), boolQuery, aggregationBuilder);
        Terms terms = (Terms) aggregationPageResult.getSearchResponse().getAggregations().asMap().get(aggregationKey);
        List<CommonIndexBO> resultList = new ArrayList<>();
        for (Terms.Bucket bucket : terms.getBuckets()) {
            resultList.add(new CommonIndexBO(bucket.getKeyAsString(), bucket.getDocCount(), null));
        }
        resultList.sort((b1, b2) -> (int) b1.getData() - (int) b2.getData());
        return resultList;
    }

    @Override
    public List<CommonIndexBO> getTop50DomainDistribution(String chainId, int topNum) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
        boolQuery.filter(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(10000);
        searchSourceBuilder.fetchSource(new String[]{"id","name"}, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.COMPANY.getEsIndex());
        Map<String, String> companyNameMap = new HashMap<>();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            companyNameMap.put((String) hit.getSourceAsMap().get("id"), (String) hit.getSourceAsMap().get("name"));
        }
        if (CollectionUtils.isEmpty(companyNameMap)) {
            return new ArrayList<>(0);
        }
        List<IndexCompany> indexDataList = getLatest(this.list(Wrappers.lambdaQuery(IndexCompany.class)
                .eq(IndexCompany::getIndexId, INDEX_PID)
                .in(IndexCompany::getCompanyId, companyNameMap.keySet())
                .orderByDesc(IndexCompany::getStatYear)));
        indexDataList.sort(Comparator.comparing(IndexCompany::getData).reversed());
        List<CommonIndexBO> boList = new ArrayList<>();
        for (int i = 0; i < (indexDataList.size() > topNum ? topNum : indexDataList.size()); i++) {
            IndexCompany current = indexDataList.get(i);
            boList.add(new CommonIndexBO(companyNameMap.get(current.getCompanyId()), current.getData(), null));
        }
        return boList;
    }

    private List<IndexCompany> getLatest(List<IndexCompany> originData) {
        if (CollectionUtils.isEmpty(originData)) {
            return new ArrayList<>(0);
        }
        List<IndexCompany> latestDataList = new ArrayList<>();
        Map<String, List<IndexCompany>> indexGroup = originData.stream().collect(Collectors.groupingBy(IndexCompany::getCompanyId));
        for (Map.Entry<String, List<IndexCompany>> companyGroup : indexGroup.entrySet()) {
            Map<String, List<IndexCompany>> subIndexGroup = companyGroup.getValue().stream().collect(Collectors.groupingBy(IndexCompany::getIndexId));
            for (Map.Entry<String, List<IndexCompany>> subGroup : subIndexGroup.entrySet()) {
                latestDataList.add(subGroup.getValue().get(0));
            }
        }
        return latestDataList;
    }

    private void fillArea(List<IndexCompany> dataList) {
        Set<String> companyIds = dataList.stream().map(IndexCompany::getCompanyId).collect(Collectors.toSet());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termsQuery("id", companyIds));
        boolQuery.filter(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.fetchSource(new String[]{"id", "area.name"}, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.COMPANY.getEsIndex());
        EsPageResult pageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        Map<String, String> areaMap = new HashMap<>();
        for (Map<String, Object> areaInfo : pageResult.getList()) {
            areaMap.put((String) areaInfo.get("id"), (String) ((Map<String, Object>) areaInfo.get("area")).get("name"));
        }
        // 补上企业市县名称
        for (IndexCompany indexData : dataList) {
            indexData.setAreaName(areaMap.get(indexData.getCompanyId()));
        }
    }

}
