package com.quantchi.nanping.innovation.service.library;

import com.quantchi.nanping.innovation.company.model.bo.PatentVectorMatchBO;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.TwoLevelIndex;
import com.quantchi.tianying.model.EsPageResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface PatentService {

    /**
     * 驾驶舱-获取专利变化趋势（发明专利，新增发明专利）
     *
     * @param chainId
     * @param areaId
     * @return
     */
    Map<String, Object> getPatentGrowthTend(String chainId, String areaId);

    /**
     * 总览-获取专利领域分布
     *
     * @param industry
     * @return
     */
    Map<String, Long> getPatentDomain(String industry);

    /**
     * 分页查询（按发布日期倒序）
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    Map<String, Object> page(Integer pageNum, Integer pageSize);

    /**
     * 查询近5年不同类型专利发布数量
     *
     * @param value
     * @param field
     * @return
     */
    List<TwoLevelIndex> getPatentPublishTrendRecentYears(final String value, final String field);

    /**
     * 统计有效/失效专利占比情况
     *
     * @param value
     * @param field
     * @return
     */
    Map<String, Long> getPatentIsOrNotValidRatio(final String value, final String field);

    /**
     * 获取专利词云
     *
     * @param chainId
     * @param value
     * @param field
     * @param cityId
     * @param areaId
     * @param auth
     * @param isGlobal
     * @return
     */
    List<CommonIndexBO> getWordCloud(String chainId, String value, String field, String cityId, String areaId, boolean auth, boolean isGlobal);

    /**
     * 查询专利近几年申请数量
     *
     * @param chainId
     * @param chainNodeId
     * @param cityId
     * @param areaId
     * @param auth
     * @param recentYears
     * @return
     */
    Map<String, Long> getPatentApplyTrendRecentYears(String chainId, String chainNodeId, String cityId, String areaId, boolean auth, int recentYears);

    /**
     * 专利旭日图
     *
     * @param chainId
     * @param auth
     * @return
     */
    List<IndustryChainNode> getPatentSunriseChart(String chainId, boolean auth);

    /**
     * 查询被引用次数前几的专利
     *
     * @param chainId
     * @param topNum
     * @param auth
     * @return
     */
    List<Map<String, Object>> getTopReferencedPatents(String chainId, int topNum, boolean auth);

    /**
     * 条件计算专利数量
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    Long countPatentNumByChainIdAndRegionId(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 总览-获取专利变化趋势（专利数量，高被引专利数量）
     *
     * @param chainId
     * @return
     */
    Map<String, Object> getPatentReferenceTend(String chainId);

    /**
     * 统计发明类型的专利数（排除实用新型）
     *
     * @param chainId
     * @param cityId
     * @param areaId
     * @return
     */
    Long countPatentTypeNumByChainIdAndRegion(String chainId, String cityId, String areaId);

    /**
     * 根据id查询
     *
     * @param ids
     * @param includes
     * @param excludes
     * @return
     */
    List<Map<String, Object>> getByIds(String[] ids, List<String> allKeywords, String[] includes, String[] excludes);

    /**
     * 向量匹配，搜索专利摘要
     *
     * @param matchBO
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<Map<String, Object>> vectorSearch(PatentVectorMatchBO matchBO, int pageNo, int pageSize);

    /**
     * 按企业id查询
     *
     * @param companyId
     * @param pageNum
     * @param pageSize
     * @return
     */
    Map<String, Object> pageByCompanyId(String companyId, Integer pageNum, Integer pageSize);
}
