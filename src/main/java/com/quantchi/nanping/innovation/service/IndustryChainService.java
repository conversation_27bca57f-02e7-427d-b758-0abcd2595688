package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.IndustryChain;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.IndustryChainNodeType;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.vo.ChainNodeTreeVO;
import com.quantchi.nanping.innovation.model.vo.WeakNodeAnalysisVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-04
 */
public interface IndustryChainService extends IService<IndustryChain> {

    /**
     * 获得根节点id
     *
     * @param chainId
     * @return
     */
    String getParentNodeIdByChainId(String chainId);

    /**
     * 根据产业链名称获得目标级别节点列表
     *
     * @param chainName
     * @param level
     * @return
     */
    List<IndustryChainNode> getNodesByChainNameAndLevel(String chainName, int level);

    /**
     * 根据节点id构建子树
     *
     * @param chainNodeIds
     * @return
     */
    List<IndustryChainNode> getTreesByNodeIds(List<String> chainNodeIds);

    /**
     * 按节点构建树
     *
     * @param nodeList
     * @return
     */
    List<IndustryChainNode> buildTrees(List<IndustryChainNode> nodeList);

    /**
     * 查找目标产业链从第X级开始的所有节点
     *
     * @param chainId
     * @param startLevel
     * @return
     */
    List<IndustryChainNode> getNodesByChainIdAndStartLevel(String chainId, int startLevel);

    /**
     * 获取指定产业链节点（id，名称, chainid）
     *
     * @param relateNodeIds
     * @return
     */
    List<IndustryChainNode> getNodeNameByNodeIds(Set<String> relateNodeIds);

    /**
     * 查询对应连续层级的产业链节点
     *
     * @param chainId
     * @param minLevel
     * @param maxLevel
     * @return
     */
    List<IndustryChainNode> getNodesByChainIdAndLevels(String chainId, int minLevel, int maxLevel);

    /**
     * 根节点和二级节点（非上，中，下游）
     *
     * @return
     */
    List<IndustryChainNode> getSelectedTreeIncludesTwoLevel(String chainId);

    /**
     * 查询属于强/补/延链的节点
     *
     * @param nodeIdSet
     * @return
     */
    List<IndustryChainNode> listNodeNameByNodeIds(Set<String> nodeIdSet);

    /**
     * 根据产业链名称获得产业链id
     *
     * @param chainName
     * @return
     */
    String getChainIdByName(String chainName);

    /**
     * 根据产业链id获得产业链名称
     *
     * @param chainId
     * @return
     */
    String getChainNameById(String chainId);

    /**
     * 根据自身的产业链节点构建产业节点树
     *
     * @param source
     */
    IndustryChainNode buildChainTree(final Map<String, Object> source);

    /**
     * 根据节点id查询节点
     *
     * @param nodeId
     * @return
     */
    IndustryChainNode getNodeById(String nodeId);

    /**
     * 获得产业链的根节点
     *
     * @param chainId
     * @return
     */
    IndustryChainNode getParentNodeByChainId(String chainId);

    /**
     * 查询产业链
     *
     * @param isAll true：返回全部产业链 false：仅返回已接入的产业链
     * @return
     */
    List<IndustryChain> listChain(boolean isAll);

    /**
     * 查询产业链节点
     *
     * @param chainId
     * @return
     */
    List<ChainNodeTreeVO> getTreeNodesByChainId(String chainId);

    /**
     * 查询节点链类型对应关系
     *
     * @param chainId
     * @return
     */
    Map<String, List<String>> getNodeTypeMapByChainId(String chainId);

    /**
     * 统计产业链各节点类型数量
     *
     * @param chainId
     * @param nodeId          不为空时，统计以该节点为根节点子树上的节点类型数量
     * @param includeOrdinary
     * @return
     */
    List<CommonIndexBO> countNodeTypeMapByChainId(String chainId, String nodeId, boolean includeOrdinary);

    /**
     * 统计链上节点数
     *
     * @param chainId
     * @return
     */
    Long countNodes(String chainId);

    /**
     * 分页查询目标产业链的强/补/固/拓
     *
     * @param pageNum
     * @param pageSize
     * @param chainId
     * @param parentId
     * @param nodeTypeId
     * @param keyword
     * @return
     */
    Page<Map<String, Object>> page4NodeType(Integer pageNum, Integer pageSize, String chainId, String parentId, Integer nodeTypeId, String keyword);

    /**
     * 查询所有根节点
     *
     * @return
     */
    List<IndustryChainNode> listRoots();

    /**
     * 按节点类型查询
     *
     * @param nodeTypeId
     * @return
     */
    List<IndustryChainNodeType> listByNodeType(String chainId, String nodeTypeId);

    /**
     * 查询薄弱节点列表
     *
     * @param chainId
     * @return
     */
    List<IndustryChainNodeWeak> listWeakNodesByChainId(String chainId);

    /**
     * 查询薄弱节点分析
     *
     * @param chainId
     * @return
     */
    List<WeakNodeAnalysisVO> listWeakNodesAnalysisByChainId(String chainId);

    /**
     * 查找薄弱节点
     *
     * @param chainId
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<IndustryChainNodeWeak> page4WeakNode(String chainId, int pageNum, int pageSize);

    /**
     * 按产业链id查询节点
     *
     * @param chainId
     * @return
     */
    List<IndustryChainNode> getNodesByChainIds(Set<String> chainId);

    /**
     * 获取到指定节点路径上的所有节点
     *
     * @param chainNodeId
     * @param chainId
     * @return
     */
    List<IndustryChainNode> getPathNode(String chainNodeId, String chainId);

    /**
     * 罗列所有的节点树
     *
     * @return
     */
    List<IndustryChainNode> listAllTrees();

    /**
     * 模糊搜索节点
     *
     * @param chainId
     * @param key
     * @return
     */
    List<IndustryChainNode> findNodeByKey(String chainId, String key);

    /**
     * 查询父辈节点
     *
     * @param nodeIds
     * @return
     */
    List<String> findAncestorsByNodeIds(List<String> nodeIds);

    /**
     * 分页查询薄弱环节对应的企业列表
     *
     * @param page
     * @param chainId
     * @param techId
     * @return
     */
    Page<Map<String, Object>> pageWeakNodeCompanyByChainId(Page<Map<String, Object>> page, String chainId, String techId);

    /**
     * 查询几级以内的节点
     *
     * @param maxLevel
     * @return
     */
    List<IndustryChainNode> getNodeListByMaxLevel(int maxLevel);

    /**
     * 获取强补固拓节点定义
     *
     * @param chainId
     * @param nodeTypeId
     * @return
     */
    Map<String, List<String>> getNodeTypeDefine(String chainId, Integer nodeTypeId);

    /**
     * 查询子节点
     *
     * @param chainId
     * @param parentNodeId
     * @return
     */
    List<IndustryChainNode> listByChainIdAndParentNodeId(String chainId, String parentNodeId);

    /**
     * 查询关键核心技术原因分析
     *
     * @param id
     * @return
     */
    IndustryChainNodeWeak getWeakNodesByDataId(String id);

    /**
     * 查询节点的强补固拓类型
     *
     * @param nodeId
     * @return
     */
    List<IndustryChainNodeType> listNodeTypeByNodeId(String nodeId);

    /**
     * 按产业链id和节点名称精确查询
     *
     * @param chainId
     * @param chainNodeName
     * @return
     */
    IndustryChainNode getNodeByChainIdAndNodeName(String chainId, String chainNodeName);

    /**
     * 罗列所有节点名称
     *
     * @return
     */
    List<String> getAllNodeNames();

    /**
     * 根据ids查找节点
     *
     * @param relateNodeIds
     * @return
     */
    List<IndustryChainNode> getNodesByNodeIds(Set<String> relateNodeIds);
}
