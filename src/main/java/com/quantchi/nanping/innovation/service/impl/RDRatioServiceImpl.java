package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.RDRatioDAO;
import com.quantchi.nanping.innovation.model.RDRatio;
import com.quantchi.nanping.innovation.service.IRDRatioService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/15 17:38
 */
@Service
public class RDRatioServiceImpl extends ServiceImpl<RDRatioDAO, RDRatio> implements IRDRatioService {
    @Override
    public BigDecimal getLatestByCityId(String cityId) {
        return this.getOne(Wrappers.lambdaQuery(RDRatio.class)
                .eq(RDRatio::getCityId, cityId)
                .isNull(RDRatio::getAreaId)
                .orderByDesc(RDRatio::getYear)
                .last("limit 1")).getRatio();
    }

    @Override
    public Map<String, BigDecimal> getLatestAreaDataMap(String cityId) {
        Integer latestYear = this.getOne(Wrappers.lambdaQuery(RDRatio.class)
                .eq(RDRatio::getCityId, cityId)
                .orderByDesc(RDRatio::getYear)
                .last("limit 1")).getYear();
        List<RDRatio> ratioList = this.list(Wrappers.lambdaQuery(RDRatio.class)
                .eq(RDRatio::getCityId, cityId)
                .eq(RDRatio::getYear, latestYear));
        return ratioList.stream().collect(Collectors.toMap(RDRatio::getAreaId, RDRatio::getRatio));
    }
}
