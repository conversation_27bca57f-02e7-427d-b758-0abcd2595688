package com.quantchi.nanping.innovation.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.IndexCompositeDataDAO;
import com.quantchi.nanping.innovation.model.IndexCompositeData;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.index.EntRelateIndexEnum;
import com.quantchi.nanping.innovation.model.vo.InnovationRankVO;
import com.quantchi.nanping.innovation.service.IIndexCompositeDataService;
import com.quantchi.nanping.innovation.utils.DecimalConvertUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/24 17:24
 */
@Service
public class IndexCompositeDataServiceImpl extends ServiceImpl<IndexCompositeDataDAO, IndexCompositeData> implements IIndexCompositeDataService {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Override
    public Map<String, Object> getInnovationRankByCompanyId(String companyId) {
        List<IndexCompositeData> chainRankDataList = this.list(Wrappers.lambdaQuery(IndexCompositeData.class)
                .eq(IndexCompositeData::getIndexId, EntRelateIndexEnum.INDUSTRY_CHAIN_RANK.getIndexId())
                .eq(IndexCompositeData::getDimension1, companyId)
                .orderByDesc(IndexCompositeData::getData));
        IndexCompositeData shaongxingRankData = this.getOne(Wrappers.lambdaQuery(IndexCompositeData.class)
                .eq(IndexCompositeData::getIndexId, EntRelateIndexEnum.SHAOXING_RANK.getIndexId())
                .eq(IndexCompositeData::getDimension1, companyId));
        InnovationRankVO rankVO = new InnovationRankVO();
        // 产业链排名
        if (CollectionUtils.isNotEmpty(chainRankDataList)) {
            // 若企业涉及多个产业链，取排名最高的产业链
            String chainId = chainRankDataList.get(0).getChainId();
            // 查询产业总数
            final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
            boolQuery.filter(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
            Long chainNum = elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(), boolQuery);
            rankVO.setIndustryRank(StrUtil.nullToEmpty(DecimalConvertUtil.bigDecimal2String(chainRankDataList.get(0).getData()))
                    + "/" + chainNum);
        }
        // 绍兴市排名
        if (shaongxingRankData != null) {
            // 绍兴市总数
            final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.filter(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
            Long shaoxingNum = elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(), boolQuery);
            Integer shaoxingRank = shaongxingRankData.getData().intValue();
            rankVO.setCityRank(shaoxingRank + "/" + shaoxingNum);
            rankVO.setRankPercent(DecimalConvertUtil.bigDecimal2Integer(new BigDecimal(100)
                    .subtract(new BigDecimal(shaoxingRank * 100).divide(new BigDecimal(shaoxingNum), 5, RoundingMode.UP))));
            rankVO.setUpdateTime(shaongxingRankData.getStatTime());
        }
        return JSON.parseObject(JSON.toJSONString(rankVO), new TypeReference<Map<String, Object>>() {
        });
    }

    @Override
    public Integer getCityPatentAverageNum(String companyId) {
        final String averageNumIndex = "51000003";
        IndexCompositeData data = this.getOne(Wrappers.lambdaQuery(IndexCompositeData.class)
                .eq(IndexCompositeData::getIndexId, averageNumIndex));
        if (data == null) {
            return null;
        }
        return DecimalConvertUtil.bigDecimal2Integer(data.getData());
    }

    @Override
    public List<IndexCompositeData> listByIndexIds(List<String> indexIdList) {
        return this.list(Wrappers.lambdaQuery(IndexCompositeData.class)
                .in(IndexCompositeData::getIndexId, indexIdList));
    }

}
