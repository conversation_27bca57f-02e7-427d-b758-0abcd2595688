package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeGroupDAO;
import com.quantchi.nanping.innovation.model.IndustryChainNodeGroup;
import com.quantchi.nanping.innovation.model.IndustryChainNodeType;
import com.quantchi.nanping.innovation.model.enums.CompanyTagEnum;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.vo.ChainNodeTreeVO;
import com.quantchi.nanping.innovation.service.IChainNodeGroupService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/11 16:36
 */
@Service
public class ChainNodeGroupServiceImpl extends ServiceImpl<IndustryChainNodeGroupDAO, IndustryChainNodeGroup> implements IChainNodeGroupService {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Override
    public IndustryChainNodeGroup getTreeByChainId(String chainId) {
        List<IndustryChainNodeGroup> nodeList = this.list(Wrappers.lambdaQuery(IndustryChainNodeGroup.class)
                .eq(IndustryChainNodeGroup::getChainId, chainId)
                .le(IndustryChainNodeGroup::getLevel, 4)
                .orderByAsc(IndustryChainNodeGroup::getId));
        List<String> nodeIds = nodeList.stream().filter(n -> StringUtils.isNotEmpty(n.getNodeId()))
                .map(IndustryChainNodeGroup::getNodeId).collect(Collectors.toList());
        Map<String, Long> LocalCompanyMap = companyService.countLocalNumByNodes(nodeIds);
        BoolQueryBuilder externalCompanyQuery = companyService.buildExternalCompanyQuery(chainId, null);
        Map<String, Long> externalCompanyMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.COMPANY, externalCompanyQuery, "chain_node.id");
        IndustryChainNodeGroup root = null;
        Map<String, IndustryChainNodeGroup> nodeMap = nodeList.stream().collect(Collectors.toMap(IndustryChainNodeGroup::getNodeId, Function.identity()));
        List<IndustryChainNodeType> nodeTypeList = industryChainService.listByNodeType(chainId, null);
        Map<String, List<String>> nodeTypeMap = nodeTypeList.stream().collect(Collectors.groupingBy(IndustryChainNodeType::getNodeId, Collectors.mapping(IndustryChainNodeType::getNodeType, Collectors.toList())));;
        for (IndustryChainNodeGroup node: nodeList){
            if (StringUtils.isEmpty(node.getParentId())){
                root = node;
            }else{
                IndustryChainNodeGroup parentNode = nodeMap.get(node.getParentId());
                if (parentNode.getChildList() == null){
                    parentNode.setChildList(new ArrayList<>());
                }
                parentNode.getChildList().add(node);
            }
            if (StringUtils.isNotEmpty(node.getNodeId())){
                node.setLocalCompanyNum(LocalCompanyMap.get(node.getNodeId()));
                node.setChainIndustryType(nodeTypeMap.get(node.getNodeId()));
                node.setExternalCompanyNum(externalCompanyMap.get(node.getNodeId()));
            }
        }
        // 将四级以下强补固拓节点放在三级节点的子节点列表
        for (IndustryChainNodeGroup node: nodeList){
            if (node.getLevel() != 4){
                continue;
            }
            IndustryChainNodeGroup parentNode = nodeMap.get(node.getParentId());
            for (IndustryChainNodeType nodeType: nodeTypeList){
                if (nodeType.getNodeId().startsWith(node.getNodeId())
                        && !nodeType.getNodeId().equals(node.getNodeId())){
                    IndustryChainNodeGroup newChildGroup = new IndustryChainNodeGroup();
                    newChildGroup.setLocalCompanyNum(LocalCompanyMap.get(nodeType.getNodeId()));
                    newChildGroup.setChainIndustryType(nodeTypeMap.get(nodeType.getNodeId()));
                    newChildGroup.setNodeId(nodeType.getNodeId());
                    newChildGroup.setName(nodeType.getNodeName());
                    parentNode.getChildList().add(newChildGroup);
                }
            }
        }
        return root;
    }
}
