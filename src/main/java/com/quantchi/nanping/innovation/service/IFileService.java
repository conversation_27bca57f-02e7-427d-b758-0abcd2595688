package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.FileInfo;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/1 11:22
 */
public interface IFileService extends IService<FileInfo> {

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    FileInfo upload(MultipartFile file);

    /**
     * 上传excel文件
     *
     * @param workbook
     * @param fileName
     * @return
     */
    FileInfo upload(Workbook workbook, String fileName);

    /**
     * 下载或预览文件
     * @param fileId
     * @param inline
     * @param response
     */
    void download(String fileId, boolean inline, HttpServletResponse response);

    /**
     * 删除文件
     *
     * @param fileId
     */
    boolean deleteByFileId(String fileId);

    /**
     * 根据关联对象的id和类型查找文件
     *
     * @param relatedId
     * @param relatedType
     * @return
     */
    List<FileInfo> listFileByRelatedIdAndType(String relatedId, String relatedType);

    /**
     * 根据关联对象的ids和类型查找文件
     *
     * @param relatedIds
     * @param relatedType
     * @return
     */
    Map<String, List<FileInfo>> listFileByRelatedIdsAndType(List<String> relatedIds, String relatedType);

    /**
     * 更新文件的归属
     *
     * @param relatedId
     * @param relatedType
     * @param fileIds
     * @return
     */
    boolean updateRelation(String relatedId, String relatedType, List<String> fileIds);

    /**
     * 批量删除目标id关联文件
     *
     * @param relatedIds
     * @return
     */
    boolean deleteByRelatedIds(List<String> relatedIds);
}
