package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.TechCommissionDemand;

/**
 * <AUTHOR>
 * @date 2023/5/19 14:33
 */
public interface ITechCommissionDemandService extends IService<TechCommissionDemand> {

    /**
     * 统计对接需求数量
     *
     * @return
     * @param cityId
     * @param areaId
     */
    Long countNumExcludeUndocking(String cityId, String areaId);
}
