package com.quantchi.nanping.innovation.service.library.impl;

import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.impl.CommonUseExtendsService;
import com.quantchi.nanping.innovation.service.library.AchievementService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/1/10 5:53 下午
 * @description
 */
@Slf4j
@Service
public class AchievementServiceImpl implements AchievementService {

    @Resource
    private ElasticsearchHelper elasticsearchHelper;

    @Override
    public List<CommonIndexBO> getAchievementTypeMap(String chainId, String nodeId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, true);
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("source", "全国科技奖励"));
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "type").field("type");
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.ACHIEVEMENT.getEsIndex(),
                boolQueryBuilder, aggregationBuilder);
        return parseAchievementTypeAggregationFromResponse(pageResult.getSearchResponse());
    }

    @Override
    public Long countByChain(String chainId, String nodeId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, null, null, true);
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("source", "全国科技奖励"));
        return elasticsearchHelper.countRequest(EsIndexEnum.ACHIEVEMENT.getEsIndex(), boolQueryBuilder);
    }

    private BoolQueryBuilder buildQueryBuilder(String chainId, String nodeId) {
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(chainId)) {
            boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
        }
        if (StringUtils.isNotEmpty(nodeId)) {
            boolQuery.filter(QueryBuilders.termQuery("chain_node.id", nodeId));
        }
        return boolQuery;
    }

    private List<CommonIndexBO> parseAchievementTypeAggregationFromResponse(final SearchResponse searchResponse) {
        List<CommonIndexBO> resultList = new ArrayList<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "type");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            resultList.add(new CommonIndexBO(bucket.getKeyAsString(), bucket.getDocCount(), null));
        }
        return resultList;
    }
}
