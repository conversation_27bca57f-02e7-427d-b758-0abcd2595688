package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.AreaNodeRelation;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/11 16:16
 */
public interface IAreaNodeRelationService extends IService<AreaNodeRelation> {
    /**
     * 获取目标产业链下面的地区节点分布
     *
     * @param chainId
     * @return
     */
    Map<String, List<AreaNodeRelation>> getMapByChainId(String chainId);
}
