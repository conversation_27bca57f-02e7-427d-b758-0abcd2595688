package com.quantchi.nanping.innovation.service.library.impl;

import cn.hutool.core.collection.CollUtil;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.PlatformService;
import com.quantchi.nanping.innovation.service.impl.CommonUseExtendsService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.model.MultidimensionalQuery;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;
import static com.quantchi.tianying.utils.ElasticsearchBuilder.getFieldTypeByMapping;

/**
 * <AUTHOR>
 * @date 2023/1/10 5:53 下午
 * @description
 */
@Slf4j
@Service
public class PlatformServiceImpl implements PlatformService {

    @Resource
    private CommonUseExtendsService commonUseExtendsService;

    @Resource
    private ElasticsearchHelper elasticsearchHelper;

    @Resource
    private IndustryChainService industryChainService;


    public static Map<String, Long> parsePlatformTypeAggregationFromResponse(final SearchResponse searchResponse) {

        Map<String, Long> resultMap = new HashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "type");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            resultMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return resultMap;
    }

    @Override
    public EsPageResult getPlatformList(String industry, Integer pageNum, Integer pageSize) {
        final MultidimensionalQuery query = new MultidimensionalQuery();
        final String esIndex = EsIndexEnum.PLATFORM.getEsIndex();
        query.setIndex(esIndex);
        final Map<String, List<String>> termQueries = new HashMap<>(8);
        // 所属产业
        if (StringUtils.isNotBlank(industry)) {
            String chainId = industryChainService.getChainIdByName(industry);
            termQueries.put("chain.id", Collections.singletonList(chainId));
        }
        termQueries.put("city.id", Collections.singletonList(CommonConstant.DIVISION_NANPING.getId()));

        query.setTermQueries(termQueries);
        query.setRequiredList(Arrays.asList("id","name", "type", "area.name"));
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        final EsPageResult libraryList = commonUseExtendsService.getLibraryList(query);
        libraryList.setPageSize(pageSize);
        // 列表为空时返回null，便于前端判断
        if (CollUtil.isEmpty(libraryList.getList())) {
            return null;
        }
        return libraryList;
    }

    @Override
    public Map<String, Long> getPlatformTypeMap(String chainId, String cityId, String areaId) {
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(cityId)){
            boolQuery.filter(QueryBuilders.termQuery("city.id", cityId));
        }
        if (StringUtils.isNotEmpty(areaId)){
            boolQuery.filter(QueryBuilders.termQuery("area.id", areaId));
        }
        if (StringUtils.isNotEmpty(chainId)){
            boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
        }
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "type").field("type");
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PLATFORM.getEsIndex(), boolQuery, aggregationBuilder);
        return parsePlatformTypeAggregationFromResponse(pageResult.getSearchResponse());
    }

    @Override
    public List<CommonIndexBO> countNationalGroupByProvince(String chainId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("chain.id", chainId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("type", "重点实验室"));
        boolQueryBuilder.filter(QueryBuilders.termQuery("level", "国家级"));
        Map<String, Long> countMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.PLATFORM, boolQueryBuilder, "province.name");
        return EsAlterUtil.dealWithTopDistribution(countMap, null, "个");
    }

    @Override
    public EsPageResult page4National(Integer pageNum, Integer pageSize, String chainId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("chain.id", chainId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("type", "重点实验室"));
        boolQueryBuilder.filter(QueryBuilders.termQuery("level", "国家级"));
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, pageNum, pageSize, null, null, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.PLATFORM.getEsIndex());
        return ElasticsearchBuilder.buildPageResult(searchResponse);
    }
}
