package com.quantchi.nanping.innovation.service.library.impl;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import com.quantchi.nanping.innovation.company.model.bo.StandardVectorMatchBO;
import com.quantchi.nanping.innovation.knowledge.center.utils.ElasticsearchBuilder;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.IWordVectorService;
import com.quantchi.nanping.innovation.service.library.StandardService;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/12 17:33
 */
@Service
public class StandardServiceImpl implements StandardService {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IWordVectorService wordVectorService;

    @Override
    public List<Map<String, Object>> vectorSearch(StandardVectorMatchBO matchBo, int pageNo, int pageSize) {
        BoolQueryBuilder preQueryBuilder = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(matchBo.getPublishDepartments())) {
            preQueryBuilder.filter(QueryBuilders.termsQuery("publish_department", matchBo.getPublishDepartments()));
        }
        QueryBuilder finalQuery = null;
        String sort = null;
        if (CollectionUtils.isNotEmpty(matchBo.getKeywords())) {
            matchBo.getKeywords().removeIf(k -> "null".equals(k) || k == null || "标准".equals(k));
            if (CollectionUtils.isNotEmpty(matchBo.getKeywords())) {
                finalQuery = EsAlterUtil.buildKeywordVectorSearch(wordVectorService.calculateVector(StringUtils.join(matchBo.getKeywords()), false), preQueryBuilder, "name_vector");
            } else {
                finalQuery = preQueryBuilder;
                sort = EsIndexEnum.STANDARD.getSort();
            }
        } else {
            finalQuery = preQueryBuilder;
            sort = EsIndexEnum.STANDARD.getSort();
        }
        final SearchSourceBuilder sourceBuilder = EsAlterUtil.buildSearchSource(finalQuery, pageNo, pageSize, null, new String[]{"name_vector"}, sort);
        final SearchResponse searchResponse = elasticsearchHelper.pageByFields(sourceBuilder, EsIndexEnum.STANDARD.getEsIndex());
        final EsPageResult libraryList = com.quantchi.tianying.utils.ElasticsearchBuilder.buildPageResult(searchResponse);
        // 手动高亮处理
        List<Map<String, Object>> list = libraryList.getList();
        // 手动放入最大分数，方便前端判断文案
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        list.get(0).put("score", searchResponse.getHits().getMaxScore());
        List<Term> terms = CollectionUtils.isEmpty(matchBo.getHighlightKeywords()) ? new ArrayList<>(0) :
                HanLP.segment(StringUtils.join(matchBo.getHighlightKeywords(), ""));
        if (CollectionUtils.isNotEmpty(terms)) {
            for (Map<String, Object> standard: list) {
                standard.put("standard_name",
                        ElasticsearchBuilder.replacementInfo((String) standard.get("standard_name"), terms, "<span style=\"color:rgb(30, 132, 246)\">", "</span>"));
            }
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getByIds(String[] ids, String[] includes, String[] excludes) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.idsQuery().addIds(ids));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(ids.length);
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.fetchSource(includes, excludes);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.STANDARD.getEsIndex());
        SearchHits hits = searchResponse.getHits();
        List<Map<String, Object>> list = new ArrayList<>();
        for (SearchHit hit : hits) {
            list.add(hit.getSourceAsMap());
        }
        return list;
    }
}
