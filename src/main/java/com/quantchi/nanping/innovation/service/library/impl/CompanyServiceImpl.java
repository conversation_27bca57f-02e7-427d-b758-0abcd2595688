package com.quantchi.nanping.innovation.service.library.impl;

import cn.hutool.core.collection.CollUtil;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.Tables;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.knowledge.center.utils.export.PatentExportUtil;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.*;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.constant.Constant;
import com.quantchi.nanping.innovation.model.enums.CompanyTagEnum;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.index.RegionIndexEnum;
import com.quantchi.nanping.innovation.service.IIndexCompanyService;
import com.quantchi.nanping.innovation.service.IndexService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.impl.CommonUseExtendsService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.utils.BigDecimalUtil;
import com.quantchi.nanping.innovation.utils.DateUtils;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.AggregationPageResult;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.model.MultidimensionalQuery;
import com.quantchi.tianying.model.SearchSourceQuery;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.range.ParsedRange;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.range.RangeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.SumAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.quantchi.tianying.utils.ElasticsearchBuilder.TERMS_BUCKET_PREFIX;

/**
 * <AUTHOR>
 * @date 2022/12/29 9:52 上午
 * @description
 */
@Slf4j
@Service
public class CompanyServiceImpl implements CompanyService {

    @Resource
    private ElasticsearchHelper elasticsearchHelper;

    @Resource
    private CommonUseExtendsService commonUseExtendsService;

    @Autowired
    private IndexService indexService;

    @Autowired
    private IIndexCompanyService indexCompanyService;

    @Autowired
    private ICompanyNodeRelationService companyNodeRelationService;

    @Autowired
    private IndustryChainService industryChainService;

    @Override
    public Map<String, CompanyTypeCountBo> getCompanyCountByType() {
        Map<String, CompanyTypeCountBo> resultMap = new HashMap<>();
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        Map<String, Long> valueMap = getCompanyCountMap(boolQueryBuilder, "tag.name");
        for (String tag : CompanyTagEnum.listCompanyTags()) {
            CompanyTypeCountBo countBo = new CompanyTypeCountBo();
            countBo.setAuthorizeCompanyNum(valueMap.get(tag));
            resultMap.put(tag, countBo);
        }
        return resultMap;
    }

    private Map<String, Long> getCompanyCountMap(BoolQueryBuilder boolQueryBuilder, String field) {
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + field).field(field).size(100).order(BucketOrder.count(false));
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        return parseCompanyTypeAggregationFromResponse(pageResult.getSearchResponse(), field);
    }

    @Override
    public EsPageResult getCompanyList(CompanyPageBo companyPageBo) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        List<String> includes = new ArrayList<>(Arrays.asList("id", "name", "chain", "city.name", "area.name", "tag.name", "desc", "product_node", "social_credit_code"));
        if (CollectionUtils.isNotEmpty(companyPageBo.getCompanyIds())) {
            boolQuery.filter(QueryBuilders.idsQuery().addIds(companyPageBo.getCompanyIds().toArray(new String[0])));
        }
        //公司类型
        if (StringUtils.isNotBlank(companyPageBo.getCompanyType())) {
            boolQuery.filter(QueryBuilders.termQuery("tag.name", companyPageBo.getCompanyType()));
        }
        //公司名称（模糊）
        if (StringUtils.isNotBlank(companyPageBo.getCompanyName())) {
            boolQuery.filter(QueryBuilders.matchPhraseQuery("name", companyPageBo.getCompanyName()));
        }
        // 所属产业
        if (StringUtils.isNotBlank(companyPageBo.getChainId())) {
            boolQuery.filter(QueryBuilders.termQuery("chain.id", companyPageBo.getChainId()));
        }
        // 企业所在地
        if (StringUtils.isNotBlank(companyPageBo.getAreaId())) {
            boolQuery.filter(QueryBuilders.termQuery("area.id", companyPageBo.getAreaId()));
        }
        // 企业挂接节点
        if (CollectionUtils.isNotEmpty(companyPageBo.getNodeIdList())) {
            boolQuery.filter(QueryBuilders.termsQuery("chain_node.id", companyPageBo.getNodeIdList()));
        }
        // 靶向企业
        if (companyPageBo.getTarget() != null && companyPageBo.getTarget()) {
            if (CollectionUtils.isEmpty(companyPageBo.getCompanyIds())) {
                boolQuery.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
                boolQuery.mustNot(QueryBuilders.termQuery("chain.id", ""));
                boolQuery.must(QueryBuilders.existsQuery("chain.id"));
                BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                shouldQuery.should(QueryBuilders.termsQuery("tag.name", new String[]{"创新领军", "科技小巨人", "独角兽企业", "强补固拓靶向企业"}));
                shouldQuery.should(QueryBuilders.termQuery("is_listed", 1));
                shouldQuery.minimumShouldMatch(1);
                boolQuery.filter(shouldQuery);
            }
            includes.add("chain_node");
        } else if (companyPageBo.getWeakTarget() == null || !companyPageBo.getWeakTarget()) {
            boolQuery.must(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        }
        SearchSourceQuery searchSourceQuery = new SearchSourceQuery();
        searchSourceQuery.setPageNum(companyPageBo.getPageNum());
        searchSourceQuery.setPageSize(companyPageBo.getPageSize());
        searchSourceQuery.setQueryBuilder(boolQuery);
        searchSourceQuery.setIncludes(includes.toArray(new String[0]));
        SearchSourceBuilder searchSourceBuilder = ElasticsearchBuilder.buildSearchSource(searchSourceQuery);
        EsAlterUtil.addSort(searchSourceBuilder, null, EsAlterUtil.buildCustomSortRule(EsIndexEnum.COMPANY.getEsIndex(), null));
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.COMPANY.getEsIndex());
        EsPageResult pageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        // 列表为空时返回null，便于前端判断
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new EsPageResult();
        }
        return pageResult;
    }

    @Override
    public EsPageResult getNewsList(NewsPageBo newsPageBo) {
        final MultidimensionalQuery query = new MultidimensionalQuery();
        final String esIndex = EsIndexEnum.NEWS.getEsIndex();
        query.setIndex(esIndex);
        final Map<String, List<String>> termQueries = new HashMap<>(2);
        // 链
        if (CollectionUtils.isNotEmpty(newsPageBo.getChainIds())) {
            termQueries.put("chain.id", newsPageBo.getChainIds());
        }
        // 节点
        if (CollectionUtils.isNotEmpty(newsPageBo.getNodeIds())) {
            List<String> nodeIds = industryChainService.findAncestorsByNodeIds(newsPageBo.getNodeIds());
            termQueries.put("chain_node.id", nodeIds);
        }
        query.setTermQueries(termQueries);
        query.setRequiredList(Arrays.asList("id", "title", "content", "abstract", "publish_time", "source"));
        query.setPageNum(newsPageBo.getPageNum());
        query.setPageSize(newsPageBo.getPageSize());
        query.setSort("publish_time:desc");
        final EsPageResult libraryList = commonUseExtendsService.getLibraryList(query);
        libraryList.setPageSize(newsPageBo.getPageSize());
        // 列表为空时返回null，便于前端判断
        if (CollUtil.isEmpty(libraryList.getList())) {
            return null;
        }
        return libraryList;
    }

    /**
     * 获取产业链项目统计
     *
     * @return
     */
    Map<String, Long> getChainProjectStatistic(String currentDay) {
        Map<String, Long> resultMap = new HashMap<>();
        final BoolQueryBuilder newQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(currentDay)) {
            newQueryBuilder.filter(QueryBuilders.rangeQuery("end_date").lte(currentDay));
        }
        newQueryBuilder.filter(QueryBuilders.existsQuery("chain.name"));
        newQueryBuilder.filter(QueryBuilders.termsQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "chain").field("chain.name").size(1);
        final AggregationPageResult newPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.PROJECT.getEsIndex(), newQueryBuilder, termsAggregationBuilder);
        resultMap = parseChainProjectAggregationFromResponse(newPageResult.getSearchResponse());
        return resultMap;
    }

    public static Map<String, Long> parseChainProjectAggregationFromResponse(final SearchResponse searchResponse) {
        Map<String, Long> compareMap = new LinkedHashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "chain");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            if (null != bucket) {
                compareMap.put(bucket.getKeyAsString(), bucket.getDocCount());
            }
//            FinanceCompareBo financeCompareBo = new FinanceCompareBo();
//            ParsedSum inventorNum = (ParsedSum) bucket.getAggregations().asMap().get("totalAmount");
//            double value = inventorNum.getValue();
//            BigDecimal result = new BigDecimal(String.valueOf(value)).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
//            financeCompareBo.setTotalAmount(new BigDecimal(String.valueOf(value)));
        }
        return compareMap;
    }

    @Override
    public Map<String, CompanyAnalyzeBo> getCompanyTendInfo(String chainId) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, null, null, true);
        RangeAggregationBuilder aggregationBuilder = AggregationBuilders.range(TERMS_BUCKET_PREFIX + "year").field("tag.approved_year");
        // 统计近五年
        List<String> years = DateUtils.getRecentYears(5);
        for (int i = 0; i < years.size(); i++) {
            aggregationBuilder.addUnboundedTo(Integer.parseInt(years.get(i)) + 1);
        }
        TermsAggregationBuilder typeAggregation = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "tag.name").field("tag.name").size(1000);
        aggregationBuilder.subAggregation(typeAggregation);
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        Map<String, CompanyAnalyzeBo> yearBo = parseCompanyTendAggregationFromResponse(pageResult.getSearchResponse());
        Map<String, CompanyAnalyzeBo> resultList = new LinkedHashMap<>();
        for (int i = years.size() - 1; i >= 0; i--) {
            String year = years.get(i);
            resultList.put(year, yearBo.get(year));
        }
        return resultList;
    }

    @Override
    public CompanyAnalyzeBo getCompanyTypeAnalyze(String chainId, String nodeId, String cityId, String areaId, boolean auth) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, cityId, areaId, auth);
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "tag.name").field("tag.name");
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        return parseCompanyAnalyzeAggregationFromResponse(pageResult.getSearchResponse());
    }

    @Override
    public Map<String, CompanyTypeStatisticBo> getAreaStatisticInfo(String industry) {
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("province.name", "浙江省"));
        // 所属产业
        if (StringUtils.isNotBlank(industry)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.name", industry));
        }

        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "city.name").field("city.name");
        TermsAggregationBuilder areaAggregation = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "tag.name").field("tag.name");
        aggregationBuilder.subAggregation(areaAggregation);
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder, aggregationBuilder);
        return parseAreaStatisticAggregationFromResponse(pageResult.getSearchResponse());
    }

//    @Override
//    public Map<String, Object> getCompanyInfo(String companyId) {
//        EsIndexEnum selectIndex = EsIndexEnum.getEsIndexByIndex(EsIndexEnum.COMPANY.getEsIndex());
//        String includes[] = new String[]{"name", "address", "tag.name", "logo"};
//        final Map<String, Object> source = elasticsearchHelper.getDataById(selectIndex.getEsIndex(), companyId, includes, null);
//        // 转换企业资质展示
//        for (Map<String, Object> tag : (List<Map<String, Object>>) source.get("tag")) {
//            String name = (String) tag.get("name");
//            CompanyTagEnum tagEnum = CompanyTagEnum.findByName(name);
//            if (tagEnum == null) {
//                continue;
//            }
//            tag.put("name", tagEnum.getAlias());
//        }
//        source.put("logo", source.get("logo"));
//        //知识产权数量
//        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//        boolQuery.filter(QueryBuilders.termQuery("applicants.id", companyId));
//        source.put("techNum", elasticsearchHelper.countRequest(EsIndexEnum.PATENT.getEsIndex(), boolQuery));
//        // 所在产业链和链节点
//        source.put("chainNodes", companyNodeRelationService.getChainNodeBoByCompanyId(companyId));
//        return source;
//    }

    @Override
    public Map<String, Object> getAnalyzeMap(String companyId) {
        //科创指数分析
        Map<String, Object> analyzeMap = new HashMap<>();
        analyzeMap.put("radarMap", indexCompanyService.getIndexRadarMapByCompanyId(companyId));
        return analyzeMap;
    }

    @Override
    public Long countNumInChain(String chainId, String chainNodeId, String cityId, String areaId, boolean auth) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, chainNodeId, cityId, areaId, auth);
        boolQueryBuilder.filter(QueryBuilders.existsQuery("chain.id"));
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("chain.id", StringUtils.EMPTY));
        return elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public List<CommonIndexBO> getCompanyCountByType(BoolQueryBuilder boolQueryBuilder, List<String> tagValues) {
        Map<String, Long> typeCountMap = getCompanyCountMap(boolQueryBuilder, "tag.name");
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("tag.name", tagValues));
        Long otherNum = elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder);
        List<CommonIndexBO> resultList = new ArrayList<>();
        for (String tag : tagValues) {
            resultList.add(new CommonIndexBO(tag, typeCountMap.containsKey(tag) ? typeCountMap.get(tag) : 0L, "家"));
        }
        resultList.add(new CommonIndexBO("其他", otherNum, "家"));
        return resultList;
    }

    @Override
    public List<CommonIndexBO> getCompanyCountByProvince(BoolQueryBuilder boolQueryBuilder) {
        Map<String, Long> provinceCountMap = getCompanyCountMap(boolQueryBuilder, "province.name");
        int top = 5, index = 0, otherNum = 0;
        List<CommonIndexBO> resultList = new ArrayList<>(top + 1);
        for (Map.Entry<String, Long> count : provinceCountMap.entrySet()) {
            if (index < top) {
                resultList.add(new CommonIndexBO(count.getKey(), count.getValue(), "个"));
                index++;
            } else if (index >= top && index < provinceCountMap.size()) {
                otherNum += count.getValue();
                index++;
            }
        }
        if (otherNum != 0) {
            resultList.add(new CommonIndexBO("其他", otherNum, "个"));
        }
        return resultList;
    }

    @Override
    public Long countNodeNumRelatedCompany(String chainId, String cityId, String areaId, boolean auth) {
        BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(chainId, null, cityId, areaId, auth);
        queryBuilder.filter(QueryBuilders.existsQuery("chain_node.id"));
        queryBuilder.mustNot(QueryBuilders.termQuery("chain_node.id", StringUtils.EMPTY));
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(TERMS_BUCKET_PREFIX + "node").field("chain_node.id").size(1000);
        AggregationPageResult aggregationPageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.COMPANY.getEsIndex(), queryBuilder, termsAggregationBuilder);
        Terms terms = (Terms) aggregationPageResult.getSearchResponse().getAggregations().getAsMap().get(TERMS_BUCKET_PREFIX + "node");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        Long nodeNum = 0L;
        String rootNode = industryChainService.getParentNodeIdByChainId(chainId);
        for (Terms.Bucket bucket : buckets) {
            if (bucket.getKeyAsString().startsWith(rootNode)){
                nodeNum++;
            }else{
                log.error("非本产业链节点：{}", bucket.getKeyAsString());
            }
        }
        return nodeNum;
    }

    @Override
    public BoolQueryBuilder buildTargetCompanyQuery(String chainId, String chainNodeId) {
        BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(chainId, chainNodeId, null, null, false);
        queryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        queryBuilder.mustNot(QueryBuilders.termQuery("chain.id", ""));
        queryBuilder.must(QueryBuilders.existsQuery("chain.id"));
        BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
        shouldQuery.should(QueryBuilders.termsQuery("tag.name", new String[]{"创新领军", "科技小巨人", "独角兽企业"}));
        shouldQuery.should(QueryBuilders.termQuery("is_listed", 1));
        shouldQuery.minimumShouldMatch(1);
        queryBuilder.filter(shouldQuery);
        return queryBuilder;
    }

    @Override
    public BoolQueryBuilder buildExternalCompanyQuery(String chainId, String chainNodeId) {
        BoolQueryBuilder queryBuilder = EsAlterUtil.buildAuthQuery(chainId, chainNodeId, null, null, false);
        queryBuilder.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        queryBuilder.mustNot(QueryBuilders.termQuery("chain.id", ""));
        queryBuilder.must(QueryBuilders.existsQuery("chain.id"));
        return queryBuilder;
    }

    @Override
    public Map<String, Object> getCompanyInfoByCreditCode(String creditCode) {
        final Map<String, Object> source = findCompanyByCreditCode(creditCode);
        if (!source.isEmpty()) {
            String companyId = (String) source.get("id");
            // 转换企业资质展示
            if (source.containsKey("tag")) {
                for (Map<String, Object> tag : (List<Map<String, Object>>) source.get("tag")) {
                    String name = (String) tag.get("name");
                    CompanyTagEnum tagEnum = CompanyTagEnum.findByName(name);
                    if (tagEnum == null) {
                        continue;
                    }
                    tag.put("name", tagEnum.getAlias());
                }
            }
            source.put("logo", source.get("logo"));
            //知识产权数量
            final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.filter(QueryBuilders.termQuery("applicants.name.term", (String) source.get("name")));
            source.put("techNum", elasticsearchHelper.countRequest(EsIndexEnum.PATENT.getEsIndex(), boolQuery));
            // 所在产业链和链节点
            source.put("chainNodes", companyNodeRelationService.getChainNodeBoByEntityId(companyId, true, false));
        } else {
            // 所在产业链和链节点
            source.put("chainNodes", companyNodeRelationService.getChainNodeBoByEntityId(creditCode, true, false));
        }
        return source;
    }

    @Override
    public String getCompanyIdByCreditCode(String socialCreditCode) {
        String includes[] = new String[]{"id"};
        // 查找社会统一信用代码对应的es id
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("social_credit_code", socialCreditCode));
        boolQueryBuilder.filter(QueryBuilders.existsQuery("chain.id"));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("chain.id", StringUtils.EMPTY));
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, 1, includes, null, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.COMPANY.getEsIndex());
        EsPageResult esPageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        if (CollectionUtils.isEmpty(esPageResult.getList())) {
            return null;
        }
        return (String) esPageResult.getList().get(0).get("id");
    }

    @Override
    public List<Map<String, Object>> getCompanyList4Demand(String chainNodeId) {
        BoolQueryBuilder boolQueryBuilder = buildExternalCompanyQuery(null, chainNodeId);
        boolQueryBuilder.filter(QueryBuilders.termQuery("province.id", CommonConstant.DIVISION_FUJIAN.getId()));
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, 2, null, null, EsIndexEnum.COMPANY.getSort());
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.COMPANY.getEsIndex());
        EsPageResult esPageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        if (esPageResult == null || CollectionUtils.isEmpty(esPageResult.getList())) {
            return new ArrayList<>(0);
        }
        return esPageResult.getList();
    }

    @Override
    public Long countExternalNumInChain(String chainId, String chainNodeId) {
        BoolQueryBuilder boolQueryBuilder = buildExternalCompanyQuery(chainId, chainNodeId);
        boolQueryBuilder.filter(QueryBuilders.existsQuery("chain.id"));
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("chain.id", StringUtils.EMPTY));
        return elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public Map<String, Long> countLocalNumByNodes(List<String> nodeIds) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(null, null, CommonConstant.DIVISION_NANPING.getId(), null, true);
        boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", nodeIds));
        return EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.COMPANY, boolQueryBuilder, "chain_node.id");
    }

    @Override
    public Long countCompanyNumByTag(String chainId, String chainNodeId, String cityId, String areaId, boolean auth, String companyTag) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, chainNodeId, cityId, areaId, auth);
        if (StringUtils.isNotEmpty(companyTag)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tag.name", companyTag));
        }
        return elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public Long countCompanyEmployeeNum(String chainId, String chainNodeId, String cityId, String areaId, boolean auth) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, chainNodeId, cityId, areaId, auth);
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum(TERMS_BUCKET_PREFIX + "employee").field("employee_size");
        AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder, sumAggregationBuilder);
        ParsedSum employeeNum = (ParsedSum) pageResult.getSearchResponse().getAggregations().getAsMap().get(TERMS_BUCKET_PREFIX + "employee");
        return Math.round(employeeNum.getValue());
    }

    @Override
    public List<IndustryChainNode> getFirstNodeListById(String id) {
        Map<String, Object> source = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY.getEsIndex(), id, null, null);
        List<Map<String, Object>> chainNodeList = (List<Map<String, Object>>) source.get("chain_node");
        List<IndustryChainNode> firstNodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(chainNodeList)) {
            for (Map<String, Object> chainNode : chainNodeList) {
                String nodeId = (String) chainNode.get("id");
                String[] nodePartArray = nodeId.split("-");
                if (nodePartArray.length <= 1) {
                    continue;
                }
                String nodePartNo = nodePartArray[1];

                if (nodePartNo.length() == 2) {
                    IndustryChainNode node = new IndustryChainNode();
                    node.setId(nodeId);
                    node.setName((String) chainNode.get("name"));
                    firstNodeList.add(node);
                }
            }
        }
        return firstNodeList;
    }

    @Override
    public List<Map<String, Object>> getAnnualReport(String id) {
        Map<String, Object> metaInfo = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY_META.getEsIndex(), id, null, null);
        if (metaInfo == null) {
            return new ArrayList<>();
        }
        final Map<String, Object> stockInfo = (Map<String, Object>) metaInfo.get("stock");
        if (stockInfo == null) {
            return new ArrayList<>();
        }
        final List<Map<String, Object>> operIncomeList = (List<Map<String, Object>>) stockInfo.get("oper_income");
        final int isListed = (int) metaInfo.get("is_listed");
        // 获取已有的年报年份
        List<Integer> yearList = new ArrayList<>();
        for (Map<String, Object> operIncome : operIncomeList) {
            if (yearList.contains(Integer.parseInt(String.valueOf(operIncome.get("year"))))) {
                continue;
            }
            yearList.add(Integer.parseInt(String.valueOf(operIncome.get("year"))));
        }
        // 组合年报
        Map<Integer, Map<String, Object>> reportMap = new HashMap<>();
        generateReportInfo(reportMap, yearList, "total_profit", (List<Map<String, Object>>) stockInfo.get("total_profit"), isListed);
        generateReportInfo(reportMap, yearList, "net_profit", (List<Map<String, Object>>) stockInfo.get("net_profit"), isListed);
        generateReportInfo(reportMap, yearList, "total_assets", (List<Map<String, Object>>) stockInfo.get("total_assets"), isListed);
        generateReportInfo(reportMap, yearList, "asset_liabilty", (List<Map<String, Object>>) stockInfo.get("asset_liabilty"), isListed);
        generateReportInfo(reportMap, yearList, "oper_income", (List<Map<String, Object>>) stockInfo.get("oper_income"), isListed);
        // 按照年份进行排序
        Collections.sort(yearList, new Comparator<Integer>() {
            @Override
            public int compare(Integer o1, Integer o2) {
                return o2 - o1;
            }
        });
        List<Map<String, Object>> reportList = new ArrayList<>();
        for (Integer year: yearList){
            reportMap.get(year).put("year", year);
            reportList.add(reportMap.get(year));
        }
        return reportList;
    }

    private void generateReportInfo(Map<Integer, Map<String, Object>> reportMap, List<Integer> yearList, String itemName, List<Map<String, Object>> itemList, int isListed) {
        for (Integer year : yearList) {
            reportMap.computeIfAbsent(year, y -> new HashMap<>());
            Map<String, Object> reportInfo = reportMap.get(year);
            for (Map<String, Object> item : itemList) {
                if (String.valueOf(year).equals(item.get("year")) && "4".equals(item.get("quarter")) && item.get("value") != null) {
                    if (0 == isListed && !"asset_liabilty".equals(itemName)) {
                        double value = Double.parseDouble(item.getOrDefault("value", "").toString());
                        reportInfo.put(itemName, convertToRange(value));
                    } else {
                        reportInfo.put(itemName, item.getOrDefault("value", "").toString());
                    }
                }
            }
        }
    }

    /**
     * 将一个具体的值转化为对应的区间值
     */
    private String convertToRange(double value) {
        int digitCount = (int) Math.log10(value) + 1;
        int step = (int) Math.pow(10, digitCount - 1);
        // 计算区间的下限
        int lowerBound = (int) (Math.floor(value / step) * step);
        // 计算区间的上限
        int upperBound = lowerBound + step - 1;
        // 组合成区间字符串
        return lowerBound + " - " + upperBound;
    }


    @Override
    public Map<String, Object> findCompanyByCreditCode(String creditCode) {
        String includes[] = new String[]{"id", "name", "address", "tag.name", "logo", "social_credit_code", "desc", "chain", "chain_node"};
        // 查找社会统一信用代码对应的es id
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("social_credit_code", creditCode));
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, 1, includes, null, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.COMPANY.getEsIndex());
        EsPageResult esPageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        if (esPageResult == null || CollectionUtils.isEmpty(esPageResult.getList())) {
            return new HashMap<>();
        }
        return esPageResult.getList().get(0);
    }

    @Override
    public Long getTotalCountByRegionId(String chainId, String nodeId, String cityId, String areaId, String companyTag) {
        BoolQueryBuilder boolQueryBuilder = EsAlterUtil.buildAuthQuery(chainId, nodeId, cityId, areaId, false);
        //公司类型
        if (StringUtils.isNotBlank(companyTag)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tag.name", companyTag));
        }
        return elasticsearchHelper.countRequest(EsIndexEnum.COMPANY.getEsIndex(), boolQueryBuilder);
    }

    @Override
    public Map<String, Long> getHighTechEntNumGroupByDivision(String chainId, String cityId) {
        final BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(chainId) && !Constant.INDEX_EMPTY_CONDITION.equals(chainId)) {
            boolQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
        }
        boolQuery.filter(QueryBuilders.termQuery("city.id", cityId));
        boolQuery.filter(QueryBuilders.termQuery("tag.name", CompanyTagEnum.HIGH_TECH.getName()));
        final TermsAggregationBuilder applicantBuilder = AggregationBuilders.terms("area").field("area.id");
        final AggregationPageResult pageResult = elasticsearchHelper.getBucketsAggregationPageResult(
                EsIndexEnum.COMPANY.getEsIndex(), boolQuery,
                applicantBuilder);
        Map<String, Long> dataMap = new HashMap<>();
        for (Aggregation aggregation : pageResult.getSearchResponse().getAggregations()) {
            Terms term = (Terms) aggregation;
            for (Terms.Bucket bucket : term.getBuckets()) {
                dataMap.put(bucket.getKeyAsString() + RegionIndexEnum.HIGH_TECH_ENT_NUM.getIndexId(), bucket.getDocCount());
            }
        }
        return dataMap;
    }

    public static CompanyAnalyzeBo parseCompanyAnalyzeAggregationFromResponse(final SearchResponse searchResponse) {
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "tag.name");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();

        CompanyAnalyzeBo typeBo = new CompanyAnalyzeBo();
        for (Terms.Bucket bucket : buckets) {
            if ("科技型中小企业".equals(bucket.getKeyAsString())) {
                typeBo.setMiddleAndSmallNum(bucket.getDocCount());
            }
            if ("高新技术企业".equals(bucket.getKeyAsString())) {
                typeBo.setHighTechNum(bucket.getDocCount());
            }
            if ("科技小巨人".equals(bucket.getKeyAsString())) {
                typeBo.setGiantNum(bucket.getDocCount());
            }
            if ("创新领军".equals(bucket.getKeyAsString())) {
                typeBo.setLeadNum(bucket.getDocCount());
            }
        }
        Long totalNum = searchResponse.getHits().getTotalHits().value;
        typeBo.setTotalNum(totalNum);
        if (null != typeBo.getTotalNum() && typeBo.getTotalNum() != 0L) {
            typeBo.setMiddleAndSmallRate(BigDecimalUtil.divide(new BigDecimal(typeBo.getMiddleAndSmallNum()), new BigDecimal(totalNum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            typeBo.setHighTechRate(BigDecimalUtil.divide(new BigDecimal(typeBo.getHighTechNum()), new BigDecimal(totalNum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            typeBo.setGiantNumRate(BigDecimalUtil.divide(new BigDecimal(typeBo.getGiantNum()), new BigDecimal(totalNum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            typeBo.setLeadNumRate(BigDecimalUtil.divide(new BigDecimal(typeBo.getLeadNum()), new BigDecimal(totalNum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        } else {
            typeBo.setMiddleAndSmallRate(BigDecimal.ZERO);
            typeBo.setHighTechRate(BigDecimal.ZERO);
            typeBo.setGiantNumRate(BigDecimal.ZERO);
            typeBo.setLeadNumRate(BigDecimal.ZERO);
        }
        return typeBo;
    }

    public static Map<String, CompanyAnalyzeBo> parseCompanyTendAggregationFromResponse(final SearchResponse searchResponse) {
        Map<String, CompanyAnalyzeBo> typeMap = new HashMap<>();
        ParsedRange parsedRange = (ParsedRange) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "year");
        List<? extends Range.Bucket> buckets = parsedRange.getBuckets();
        for (Range.Bucket bucket : buckets) {
            CompanyAnalyzeBo typeBo = new CompanyAnalyzeBo();
            Terms subTerms = (Terms) bucket.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "tag.name");
            List<? extends Terms.Bucket> subBuckets = subTerms.getBuckets();
            for (Terms.Bucket subBucket : subBuckets) {
                if ("科技型中小企业".equals(subBucket.getKeyAsString())) {
                    typeBo.setMiddleAndSmallNum(subBucket.getDocCount());
                }
                if ("高新技术企业".equals(subBucket.getKeyAsString())) {
                    typeBo.setHighTechNum(subBucket.getDocCount());
                }
                if ("科技小巨人".equals(subBucket.getKeyAsString())) {
                    typeBo.setGiantNum(subBucket.getDocCount());
                }
//                if ("创新领军".equals(subBucket.getKeyAsString())) {
//                    typeBo.setLeadNum(subBucket.getDocCount());
//                }
            }
            typeMap.put(String.valueOf(Math.round(Double.valueOf(bucket.getToAsString()) - 1)), typeBo);
        }
        return typeMap;
    }


    public static Map<String, CompanyTypeStatisticBo> parseAreaStatisticAggregationFromResponse(final SearchResponse searchResponse) {
        Map<String, CompanyTypeStatisticBo> areaMap = new HashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "city.name");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            CompanyTypeStatisticBo statisticBo = new CompanyTypeStatisticBo();
            statisticBo.setTotalNum(bucket.getDocCount());
            Terms subTerms = (Terms) bucket.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + "tag.name");
            List<? extends Terms.Bucket> subBuckets = subTerms.getBuckets();

            for (Terms.Bucket subBucket : subBuckets) {
                if ("高新技术企业".equals(subBucket.getKeyAsString())) {
                    statisticBo.setHighTechNum(subBucket.getDocCount());
                }
                if ("上市企业".equals(subBucket.getKeyAsString())) {
                    statisticBo.setIpoNum(subBucket.getDocCount());
                }
                if ("专精特新".equals(subBucket.getKeyAsString())) {
                    statisticBo.setSpecializedNum(subBucket.getDocCount());
                }
            }
            areaMap.put(bucket.getKeyAsString(), statisticBo);
        }
        return areaMap;
    }

    public static Map<String, Long> parseCompanyTypeAggregationFromResponse(final SearchResponse searchResponse, String field) {
        Map<String, Long> resultMap = new LinkedHashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + field);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            resultMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return resultMap;
    }

    public static Map<String, Long> parseTechTypeAggregationFromResponse(final SearchResponse searchResponse, String bucketName) {

        Map<String, Long> resultMap = new HashMap<>();
        Terms terms = (Terms) searchResponse.getAggregations().asMap().get(TERMS_BUCKET_PREFIX + bucketName);
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            resultMap.put(bucket.getKeyAsString(), bucket.getDocCount());
        }
        return resultMap;
    }
}
