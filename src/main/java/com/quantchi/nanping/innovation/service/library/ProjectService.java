package com.quantchi.nanping.innovation.service.library;

import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.tianying.model.EsPageResult;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/20 14:32
 */
public interface ProjectService {

    /**
     * 计数(不统计招商项目,工信局项目)
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    Long count(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 统计招商项目
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    Long countInvestmentAttract(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 按照项目进展分组统计招商项目
     *
     * @param chainId
     * @return
     */
    List<CommonIndexBO> countInvestmentAttractGroupByStatus(String chainId);

    /**
     * 分页查询招商项目
     *
     * @param pageNum
     * @param pageSize
     * @param chainId
     * @return
     */
    EsPageResult page4InvestmentAttract(Integer pageNum, Integer pageSize, String chainId);

    /**
     * 统计项目补助（排除国家自然科学基金项目，招商项目）
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param year
     * @return
     */
    BigDecimal countSubsidy(String chainId, String nodeId, String cityId, String areaId, String year);

    /**
     * 项目类型分布（不统计招商项目）
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    Map<String, Long> getProjectCountMap(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 统计获得项目补助的企业数量
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    Long countSubsidyCompanyNum(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 获得历年总投资金额和项目补助金额
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    Map<String, Object> getInvestmentAndSubsidyTend(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 构建项目补助查询的基本条件
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    BoolQueryBuilder buildSubsidyProjectQuery(String chainId, String nodeId, String cityId, String areaId);

    /**
     * 按企业类型统计历年项目补助金额
     *
     * @param cityId
     * @param areaId
     * @param isTechCompany 是否是科技型企业
     * @return
     */
    Map<String, BigDecimal> getSubsidyTendGroupByTag(String cityId, String areaId, boolean isTechCompany);

    /**
     * 查询项目补助列表
     *
     * @param pageNum
     * @param pageSize
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param companyName
     * @param projectType
     * @param projectName
     * @return
     */
    EsPageResult page4Subsidy(Integer pageNum, Integer pageSize, String chainId, String nodeId, String cityId, String areaId,
                              String companyName, String projectType, String projectName);

    /**
     * 统计项目补助笔数
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @return
     */
    Long countSubsidyNum(String chainId, String nodeId, String cityId, String areaId);
}
