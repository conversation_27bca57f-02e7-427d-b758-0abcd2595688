package com.quantchi.nanping.innovation.service.library;

import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.bo.*;
import com.quantchi.tianying.model.EsPageResult;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/27 10:23 上午
 * @description
 */
public interface CompanyService {

    /**
     * 获取各企业类型数量
     *
     * @return
     */
    Map<String, CompanyTypeCountBo> getCompanyCountByType();

    /**
     * 获取企业列表
     *
     * @return
     */
    EsPageResult getCompanyList(CompanyPageBo companyPageBo);

    /**
     * 获取资讯列表
     *
     * @param newsPageBo
     * @return
     */
    EsPageResult getNewsList(NewsPageBo newsPageBo);

    /**
     * 总览-获取各企业类变化趋势(近五年)
     *
     * @param chainId 产业链id
     * @return
     */
    Map<String, CompanyAnalyzeBo> getCompanyTendInfo(String chainId);

    /**
     * 总览-获取各企业类型分析
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param auth
     * @return
     */
    CompanyAnalyzeBo getCompanyTypeAnalyze(String chainId, String nodeId, String cityId, String areaId, boolean auth);

    /**
     * 总览-获取企业区域统计
     *
     * @param industry 产业链
     * @return
     */
    Map<String, CompanyTypeStatisticBo> getAreaStatisticInfo(String industry);

//    /**
//     * 服务侧企业详情
//     *
//     * @param companyId
//     * @return
//     */
//    Map<String, Object> getCompanyInfo(String companyId);

    Map<String, Object> findCompanyByCreditCode(String creditCode);

    /**
     * 按产业链+区域统计指定类型的企业
     *
     * @param chainId
     * @param nodeId
     * @param cityId
     * @param areaId
     * @param companyTag
     * @return
     */
    Long getTotalCountByRegionId(String chainId, String nodeId, String cityId, String areaId, String companyTag);

    /**
     * 查询某城市目标产业链下各区县的高新企业数
     *
     * @param chainId
     * @param cityId
     * @return
     */
    Map<String, Long> getHighTechEntNumGroupByDivision(String chainId, String cityId);

    /**
     * 科创指数分析
     *
     * @param companyId
     * @return
     */
    Map<String, Object> getAnalyzeMap(String companyId);

    /**
     * 统计链上企业
     *
     * @param chainId
     * @param chainNodeId
     * @param cityId
     * @param areaId
     * @param auth
     * @return
     */
    Long countNumInChain(String chainId, String chainNodeId, String cityId, String areaId, boolean auth);

    /**
     * 按企业类型统计，且返回不属于目标类型的统计组为”其他“
     *
     * @param boolQueryBuilder
     * @param tagValues
     * @return
     */
    List<CommonIndexBO> getCompanyCountByType(BoolQueryBuilder boolQueryBuilder, List<String> tagValues);

    /**
     * 按省份统计靶向企业数量，非前五省份企业数量计入”其他“
     *
     * @param boolQueryBuilder
     * @return
     */
    List<CommonIndexBO> getCompanyCountByProvince(BoolQueryBuilder boolQueryBuilder);

    /**
     * 统计关联企业的链节点数量
     *
     * @param chainId
     * @param cityId
     * @param areaId
     * @param auth
     * @return
     */
    Long countNodeNumRelatedCompany(String chainId, String cityId, String areaId, boolean auth);

    /**
     * 构建靶向企业的查询条件
     *
     * @param chainId
     * @param chainNodeId
     * @return
     */
    BoolQueryBuilder buildTargetCompanyQuery(String chainId, String chainNodeId);

    BoolQueryBuilder buildExternalCompanyQuery(String chainId, String chainNodeId);

    /**
     * 服务侧：根据社会信用代码获取企业详情
     *
     * @param creditCode
     * @return
     */
    Map<String, Object> getCompanyInfoByCreditCode(String creditCode);

    /**
     * 根据社会信用代码查找企业id
     *
     * @param socialCreditCode
     * @return
     */
    String getCompanyIdByCreditCode(String socialCreditCode);

    /**
     * 需求推荐企业列表
     * 省内非南平，filedrank高
     *
     * @param chainNodeId
     * @return
     */
    List<Map<String, Object>> getCompanyList4Demand(String chainNodeId);

    /**
     * 统计外部企业数量
     *
     * @param chainId
     * @param chainNodeId
     * @return
     */
    Long countExternalNumInChain(String chainId, String chainNodeId);

    /**
     * 按照目标节点统计本地企业数量
     *
     * @param nodeIds
     * @return
     */
    Map<String, Long> countLocalNumByNodes(List<String> nodeIds);

    /**
     * 统计指定标签的企业数量
     *
     * @param chainId
     * @param chainNodeId
     * @param cityId
     * @param areaId
     * @param auth
     * @param companyTag
     * @return
     */
    Long countCompanyNumByTag(String chainId, String chainNodeId, String cityId, String areaId, boolean auth, String companyTag);

    /**
     * 统计本地企业员工数量
     *
     * @param chainId
     * @param chainNodeId
     * @param cityId
     * @param areaId
     * @param auth
     * @return
     */
    Long countCompanyEmployeeNum(String chainId, String chainNodeId, String cityId, String areaId, boolean auth);

    /**
     * 获得企业所在的一级节点
     *
     * @param id
     * @return
     */
    List<IndustryChainNode> getFirstNodeListById(String id);

    /**
     * 获取企业年报
     * @param id
     * @return
     */
    List<Map<String, Object>> getAnnualReport(String id);
}
