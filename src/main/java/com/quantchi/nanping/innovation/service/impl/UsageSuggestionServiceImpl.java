package com.quantchi.nanping.innovation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.dao.mapper.admin.SysUsageSuggestionMapper;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.model.admin.SysUsageSuggestion;
import com.quantchi.nanping.innovation.service.IFileService;
import com.quantchi.nanping.innovation.service.IUsageSuggestionService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/4 14:09
 */
@Service
public class UsageSuggestionServiceImpl extends ServiceImpl<SysUsageSuggestionMapper, SysUsageSuggestion> implements IUsageSuggestionService {

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private IFileService fileService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(SysUsageSuggestion suggestion) {
        UserInfoEntity currentUser = sysLoginService.getUserInfo(false);
        suggestion.setUserId(currentUser.getId());
        suggestion.setUserName(currentUser.getUsername());
        suggestion.setId(UUID.randomUUID().toString());
        // 保存附件
        if (CollectionUtils.isNotEmpty(suggestion.getFileInfoList())){
            fileService.updateRelation(suggestion.getId(), FileInfo.RELATED_TYPE_SUGGESTION,
                    suggestion.getFileInfoList().stream().map(FileInfo::getFileId).collect(Collectors.toList()));
        }
        return this.save(suggestion);
    }
}
