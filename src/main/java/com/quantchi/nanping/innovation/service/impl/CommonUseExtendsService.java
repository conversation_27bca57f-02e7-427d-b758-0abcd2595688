package com.quantchi.nanping.innovation.service.impl;

import com.quantchi.tianying.config.property.KeywordSearchProperties;
import com.quantchi.tianying.config.property.NavigationSettings;
import com.quantchi.tianying.config.property.VisualAnalyzeSettings;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.service.CommonUseService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class CommonUseExtendsService extends CommonUseService {

    protected CommonUseExtendsService(ElasticsearchHelper elasticsearchHelper, NavigationSettings navigationSettings, KeywordSearchProperties keywordSearchProperties, VisualAnalyzeSettings visualAnalyzeSettings) {
        super(elasticsearchHelper, navigationSettings, keywordSearchProperties, visualAnalyzeSettings);
    }

    @Override
    protected void specialBuildBoolQuery(BoolQueryBuilder boolQuery, Map<String, List<String>> termQueries) {
        Map<String, List<String>> handledMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : termQueries.entrySet()) {
            if (entry.getKey().contains("special_handle")){
                String needHandleText = entry.getKey().split(":")[1];
                String index = needHandleText.split("-")[0];
                String field = needHandleText.split("-")[1];
                switch (index){
                    case "expert":{
                        if (field.equals("province.name")){
                            boolQuery.filter(QueryBuilders.existsQuery("province.name"));
                            boolQuery.mustNot(QueryBuilders.termsQuery("province.name", "浙江省"));
                        }
                    }
                }
                handledMap.put(entry.getKey(), entry.getValue());
            }
        }
        for (Map.Entry<String, List<String>> entry : handledMap.entrySet()) {
            termQueries.remove(entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected String specialSortForLibrarySearch(BoolQueryBuilder boolQuery, String sort, String keyword) {
        return sort;
    }

    @Override
    protected void specialSearchSourceForLibrarySearch(String esIndex, SearchSourceBuilder searchSource, Map<String, List<String>> termQueries, String keyword, BoolQueryBuilder boolQuery) {

    }

    @Override
    protected EsPageResult specialDealWithLibraryPageResult(EsPageResult pageResult) {
        return pageResult;
    }

}
