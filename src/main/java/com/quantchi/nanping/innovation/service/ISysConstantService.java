package com.quantchi.nanping.innovation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.model.admin.SysConstant;

/**
 * <AUTHOR>
 * @date 2024/1/4 14:49
 */
public interface ISysConstantService extends IService<SysConstant> {
    /**
     * 按关联主体id和常量编码查询
     *
     * @param subjectId
     * @param constantCode
     * @return
     */
    SysConstant getBySubjectIdAndConstantCode(String subjectId, String constantCode);
}
