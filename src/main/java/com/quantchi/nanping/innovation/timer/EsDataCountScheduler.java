package com.quantchi.nanping.innovation.timer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.service.IFileService;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/7/25 16:55
 */
@Configuration
@EnableScheduling
@Slf4j
public class EsDataCountScheduler {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private StringRedisCache redisCache;

    @Value("${timer.knowledge.center.count}")
    private boolean doCount;

    /**
     * 每天凌晨2点执行
     */
    //@Scheduled(cron = "0 0 2 * * ?")
    private void configureTasks() {
        if (!doCount){
            log.error("----------" + new Date() + "该环境不进行数据统计--------");
            return;
        }
        log.error("----------" + new Date() + "数据统计启动--------");
        List<String> esIndexList = Arrays.asList("nanping_innovation_company","nanping_innovation_company_meta","nanping_innovation_patent","nanping_innovation_company_financing",
                "nanping_innovation_expert","nanping_innovation_global_patent","nanping_innovation_policy","nanping_innovation_news","nanping_innovation_platform",
                "nanping_innovation_bottleneck","nanping_innovation_demand","nanping_innovation_achievement","nanping_innovation_project");
        Long count = 0L;
        for (String esIndex : esIndexList) {
            Long num = elasticsearchHelper.countRequest(esIndex, QueryBuilders.boolQuery());
            log.error("库：{}数据总量为：{}", esIndex, num);
            count += num;
        }
        // 暂时写死海关数据总量
        count += 281532892L;
        redisCache.put("es_count", String.valueOf(count), 1, TimeUnit.DAYS);
        log.error("----------" + new Date() + "数据统计结束--------");
    }
}
