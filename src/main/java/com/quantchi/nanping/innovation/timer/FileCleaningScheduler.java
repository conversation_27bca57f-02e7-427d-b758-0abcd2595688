package com.quantchi.nanping.innovation.timer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.service.IFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/25 16:55
 */
@Configuration
@EnableScheduling
@Slf4j
public class FileCleaningScheduler {

    @Autowired
    private IFileService fileService;

    /**
     * 每天凌晨一点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    private void configureTasks() {
        log.error("----------" + new Date() + "文件清理启动--------");
        List<FileInfo> files2Clean = fileService.list(Wrappers.lambdaQuery(FileInfo.class)
                .and(q -> q.notIn(FileInfo::getRelatedType, FileInfo.RELATED_TYPE_SYSTEM)
                        .or().isNull(FileInfo::getRelatedType))
                .isNull(FileInfo::getRelatedId));
        for (FileInfo fileInfo : files2Clean) {
            log.error("---------删除文件，名称：{},上传时间：{},关联目标类型：{}",
                    fileInfo.getOriginalFileName(), fileInfo.getCreateTime(), fileInfo.getRelatedType());
            try {
                fileService.deleteByFileId(fileInfo.getFileId());
            } catch (Exception e) {
                log.error("删除异常：", e);
                log.error("---------删除文件失败，ID:{},名称：{},上传时间：{},关联目标类型：{}", fileInfo.getFileId(),
                        fileInfo.getOriginalFileName(), fileInfo.getCreateTime(), fileInfo.getRelatedType());
            }
        }
        log.error("----------" + new Date() + "文件清理结束--------");
    }
}
