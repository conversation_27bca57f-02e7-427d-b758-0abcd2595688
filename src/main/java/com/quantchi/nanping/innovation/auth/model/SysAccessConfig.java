package com.quantchi.nanping.innovation.auth.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.nanping.innovation.model.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/7 10:32
 */
@Data
@ApiModel("第三方鉴权配置")
@TableName("sys_access_config")
public class SysAccessConfig extends BaseTime {

    @TableId(value = "access_id")
    private Long accessId;

    @ApiModelProperty("第三方id")
    private String appId;

    @ApiModelProperty("第三方名称")
    private String appName;

    @ApiModelProperty("密钥")
    private String appSecret;

}
