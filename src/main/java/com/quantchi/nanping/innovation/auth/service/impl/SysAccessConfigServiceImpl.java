package com.quantchi.nanping.innovation.auth.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.auth.dao.SysAccessConfigDAO;
import com.quantchi.nanping.innovation.auth.model.SysAccessConfig;
import com.quantchi.nanping.innovation.auth.service.ISysAccessConfigService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/6/7 10:40
 */
@Service
public class SysAccessConfigServiceImpl extends ServiceImpl<SysAccessConfigDAO, SysAccessConfig> implements ISysAccessConfigService {
    @Override
    public SysAccessConfig getByAppId(String appId) {
        return this.getOne(Wrappers.lambdaQuery(SysAccessConfig.class).eq(SysAccessConfig::getAppId, appId));
    }
}
