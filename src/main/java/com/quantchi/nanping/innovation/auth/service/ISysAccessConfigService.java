package com.quantchi.nanping.innovation.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.nanping.innovation.auth.model.SysAccessConfig;

/**
 * <AUTHOR>
 * @date 2024/6/7 10:39
 */
public interface ISysAccessConfigService extends IService<SysAccessConfig> {

    /**
     * 查询第三方密钥配置
     *
     * @param appId
     * @return
     */
    SysAccessConfig getByAppId(String appId);
}
