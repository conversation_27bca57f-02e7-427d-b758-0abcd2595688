package com.quantchi.nanping.innovation.auth.aop.aspect;

import com.alibaba.fastjson.JSONObject;
import com.quantchi.common.core.utils.security.Md5Utils;
import com.quantchi.nanping.innovation.auth.model.SysAccessConfig;
import com.quantchi.nanping.innovation.auth.service.ISysAccessConfigService;
import com.quantchi.nanping.innovation.auth.service.impl.SysAccessConfigServiceImpl;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.Method;

/**
 * Bean的优先级设置为最高
 */
@Aspect
@Component
@Slf4j
public class CheckThirdAccessAspect {

    /**
     * 请求头参数
     */
    private static final String HEADER_APP_ID = "appId";
    private static final String HEADER_SIGN = "sign";
    private static final String HEADER_TIMESTAMP = "timestamp";

    @Pointcut("@annotation(com.quantchi.nanping.innovation.auth.aop.CheckThirdAccess)")
    public void withAnnotationCheck() {
    }

    @Before("withAnnotationCheck()")
    public void beforeMethod(final JoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        //请求的类名以及方法名
        Class<?> targetClass = point.getTarget().getClass();
        String methodName = signature.getName();
        Method method = null;
        try {
            method = targetClass.getMethod(methodName, signature.getParameterTypes());
        } catch (NoSuchMethodException e) {
            log.error("权限校验，对应方法不存在", e);
            return;
        }
        // 获取请求头参数
        final HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        final String appId = request.getHeader(HEADER_APP_ID);
        final String sign = request.getHeader(HEADER_SIGN);
        final String timestamp = request.getHeader(HEADER_TIMESTAMP);
        if (StringUtils.isAnyEmpty(appId, sign, timestamp)){
            throw new BusinessException("接口参数异常");
        }
        // 查询APPID是否存在
        ISysAccessConfigService configService = SpringUtils.getBean(SysAccessConfigServiceImpl.class);
        SysAccessConfig accessConfig = configService.getByAppId(appId);
        if (accessConfig == null){
            throw new BusinessException("应用不存在");
        }
        // 时间校验
        if (System.currentTimeMillis() - Long.parseLong(timestamp) > 10 * 60 * 1000){
            throw new BusinessException("签名已超时");
        }
        // 校验Sign
        String signedContent = appId + accessConfig.getAppSecret() + getBody(request) + timestamp;
        log.error("未去除空格验签值，signedContent：{}", signedContent);
        signedContent = signedContent.replace(" ", "");
        if (!Md5Utils.hash(signedContent).equals(sign)){
            log.error("验签值，signedContent：{}", signedContent);
            throw new BusinessException("签名验证错误");
        }
    }

    public static String getBody(HttpServletRequest request) {
        StringBuilder body = new StringBuilder();
        if ("GET".equalsIgnoreCase(request.getMethod())){
            body.append(request.getQueryString());
        }else{
            try {
                BufferedReader br = request.getReader();
                String line = null;
                while((line = br.readLine()) != null){
                    body.append(line);
                }
            } catch (IOException e) {
                log.error("读取请求参数失败");
            }
        }
        return body.toString();
    }
}
