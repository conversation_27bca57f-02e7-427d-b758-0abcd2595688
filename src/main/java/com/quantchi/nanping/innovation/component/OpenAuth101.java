package com.quantchi.nanping.innovation.component;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.sirc.security.util.NationalSecretsUtils;
import com.sirc.security.util.RandomStringUtils;
import com.sirc.security.util.SM2Utils;
import com.sirc.security.util.SM4Utils;
import com.sirc.security.util.SignUtils;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 同步授权模式服务传参加密
 * <AUTHOR>
 *
 */
public class OpenAuth101 {

	private static final String APP_ID = "smgqvtdrof6fk5gtfl9ehl1vi7gd8tirlm4ylttdgqsi1cgr3xcmc1foxxcdythn";

	private static final String APP_KEY = "yD4cxqCRIr2DE1SP";

	public static void main(String args[]) throws Exception {
	    //主体授权应用场景同步
		//new OpenAuth101().syncAuth();
	    //主体授权文件上传
		new OpenAuth101().pushFile();
	}

	// 本地sm2私钥,本地生成，见上生成实现
	private String localPrivateKey = "18282996FF7538916B76B8222C04CBED440DB4EDE42AD61C86350E382CBA0399";

	// 服务器sm2公钥，可用获取公钥接口获取
	private String remotePublicKey = "044A29CDF3178B72946DFC991BA05EDB7AE5D76291BAEE9A827A06AAE9E49FB2FD47FECB115E8E92748C911FF76DF2984CFC7C66626B4A911C1E8FE4A66EDB0ED6";

	/**
	 * 主体授权文件上传
	 * @return
	 */
	public String pushFile() {
		// 客户端加密请求生成示例.
		JSONObject jsonObject = new JSONObject();
		String hashCode = "4428411C8E3DB9B3C38A3D6CFA0192C6E271D4E90845219C7293C19F5B612D81";
		jsonObject.put("mainCode", "");
		jsonObject.put("fileHash", hashCode);
		String requestData = jsonObject.toJSONString();
		String requestJson = null;
		try {
			Map<String, String> requestJsonObject = generateEncryptRequest(requestData);
			System.out.println("*****************************************请求参数如下:\n");
			System.out.println("appId:" + requestJsonObject.get("appId"));
			System.out.println("key:" + requestJsonObject.get("key"));
			System.out.println("signatureData:" + requestJsonObject.get("signatureData"));
			System.out.println("requestData:" + requestJsonObject.get("requestData"));
			System.out.println("checkCode:" + requestJsonObject.get("checkCode"));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return requestJson;
	}
	
	
	
	/**
	 * 主体授权应用场景同步
	 * @return
	 */
	public String syncAuth() {
		// 客户端加密请求生成示例.
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("authName", "张三的明星贷应用场景授权");
		jsonObject.put("certCode", "350102198007283715");
		jsonObject.put("certName", "张三");
		jsonObject.put("effectiveTime", "20240630");
		jsonObject.put("sceneId", "6dec5fc4a9aa7a2879183a5a5d7c28d9");
		jsonObject.put("fileId", "1774640971249115138");
		jsonObject.put("signTime", "2024-04-01 15:01:45");
		jsonObject.put("orderNo", "202403254614165151");
		String requestData = jsonObject.toJSONString();
		String requestJson = null;
		try {
			Map<String, String> requestJsonObject = generateEncryptRequest(requestData);
			requestJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(requestJsonObject);
			System.out.println("*****************************************请求json:\n" + requestJson);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return requestJson;
	}
	
	
	
	
	
	
	

	/**
	 * 申请协议信息
	 * 
	 * @return
	 */
	public String getProtocolTxId() {
		// 客户端加密请求生成示例.
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("certCode", "110106199006187970");
		jsonObject.put("certName", "张三");
		// jsonObject.put("companyName", "厦门信息技术有限公司");
		jsonObject.put("effectiveTime", "2023-08-30");
		jsonObject.put("sceneId", "3A21DCA48B3542BFB49078E94BE8EB52");
		// jsonObject.put("uscc", "9135018237W3C8E356");
		String requestData = jsonObject.toJSONString();
		String requestJson = null;
		try {
			Map<String, String> requestJsonObject = generateEncryptRequest(requestData);
			requestJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(requestJsonObject);
			System.out.println("*****************************************请求json:\n" + requestJson);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return requestJson;
	}

	/**
	 * 提交签署信息
	 * 
	 * @return
	 */
	public String getPushSignInfo() {
		// 客户端加密请求生成示例.
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("srcOrderNo", "202208111710205224331315181");
		jsonObject.put("txId", "249f0cb6170f23a9b6876e0243225f6e6d36992b0d89ef20400be4d8fb364674");
		String requestData = jsonObject.toJSONString();
		String requestJson = null;
		try {
			Map<String, String> requestJsonObject = generateEncryptRequest(requestData);
			requestJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(requestJsonObject);
			System.out.println("*****************************************请求json:\n" + requestJson);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return requestJson;
	}

	/**
	 * 回收授权
	 * 
	 * @return
	 */
	public String revokeAuth() {
		// 客户端加密请求生成示例.
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("txId", "ba6903b87d0eadc164a5211904dfa9fe55d58d50de09e194a76035e48448fb58");
		String requestData = jsonObject.toJSONString();
		String requestJson = null;
		try {
			Map<String, String> requestJsonObject = generateEncryptRequest(requestData);
			requestJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(requestJsonObject);
			System.out.println("*****************************************请求json:\n" + requestJson);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return requestJson;
	}

	/**
	 * 服务端返回值加密示例
	 **/
	private Map<String, String> generateEncryptRequest(String requestData) throws Exception {
		SignUtils signUtils = new SignUtils();
		String summary = signUtils.summary(requestData);
		System.out.println("摘要:" + summary);
		String sign = signUtils.sign(APP_ID, summary, localPrivateKey);
		System.out.println("请求signatureData:" + sign);
		String randomSm4Key = RandomStringUtils.getRandomString();
		System.out.println("随机sm4密码:" + randomSm4Key);
		SM4Utils sm4 = new SM4Utils(randomSm4Key, APP_KEY);
		String encryptData = sm4.encryptData_CBC(APP_KEY, requestData);
		System.out.println("请求中data:" + encryptData);
		String key = new SM2Utils().encrypt(NationalSecretsUtils.hexToByte(remotePublicKey),
				randomSm4Key.getBytes(StandardCharsets.UTF_8));
		System.out.println("请求中key:" + key);
		return ImmutableMap.of("appId", APP_ID, "key", key, "signatureData", sign, "requestData", encryptData,
				"checkCode", RandomStringUtils.getRandomString());
	}

}