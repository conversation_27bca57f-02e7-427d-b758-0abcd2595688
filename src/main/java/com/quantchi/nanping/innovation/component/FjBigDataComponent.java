package com.quantchi.nanping.innovation.component;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.quantchi.nanping.innovation.model.bo.FjBigDataAuthorizeBO;
import com.quantchi.nanping.innovation.utils.HttpClientUtils;
import com.sirc.security.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class FjBigDataComponent {

    /**
     *  服务器sm2公钥，可用获取公钥接口获取
     */
    @Value("${fj-big-data.appId}")
    private String appId;

    /**
     * 模型id
     */
    @Value("${fj-big-data.modelId}")
    private String modelId;

    /**
     * 场景id
     */
    @Value("${fj-big-data.sceneId}")
    private String sceneId;

    @Value("${fj-big-data.appKey}")
    private String appKey;

    /**
     * 客户端私钥
     */
    @Value("${fj-big-data.localPrivateKey}")
    private String localPrivateKey;

    /**
     * 客户端公钥
     */
    @Value("${fj-big-data.localPublickey}")
    private String localPublickey;

    /**
     * 授权服务端公钥
     */
    @Value("${fj-big-data.remotePublicKey}")
    private String remotePublicKey;

    /**
     * 模型服务端公钥
     */
    @Value("${fj-big-data.modelServerPublicKey}")
    private String modelServerPublicKey;


    @Value("${fj-big-data.baseUrl}")
    private String baseUrl;

    @Value("${fj-big-data.pushFile}")
    private String pushFile;

    @Value("${fj-big-data.syncAuth}")
    private String syncAuth;

    @Value("${fj-big-data.apiQuery}")
    private String apiQuery;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 4KB buffer
    private static final int BUFFER_SIZE = 4096;

    public JSONObject getCreditInfo(FjBigDataAuthorizeBO bo) {
        JSONObject jsonObject = new JSONObject();
        try {
            String fileId = pushFile(bo.getUscc(), bo.getFile());
            String token = syncAuth(bo.getAuthName(), bo.getCertCode(), bo.getCertName(), bo.getCompanyName(), bo.getUscc(),
                    bo.getEffectiveEndTime(), fileId, bo.getOrderNo(), bo.getSignTime());
            jsonObject = apiQuery(bo, token);
        } catch (Exception e) {
            log.error("error occurred when ivoke fjBigDataComponent: ", e);
            return null;
        }

        return jsonObject;
    }

    /**
     * 主体授权文件上传
     */
    public String pushFile(String mainCode, MultipartFile multipartFile) throws Exception {
        if (multipartFile == null || multipartFile.isEmpty()) {
            log.error("上传的文件为空");
            throw new IllegalArgumentException("上传的文件为空");
        }
        
        // 计算文件的哈希值
        String hashCode;
        try {
            // 首先读取全部内容到内存中，用于计算哈希值
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (InputStream inputStream = multipartFile.getInputStream()) {
                byte[] buffer = new byte[BUFFER_SIZE];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
            }
            byte[] fileBytes = baos.toByteArray();
            hashCode = new SignUtils().summary(new String(fileBytes));
            
            // 构建请求参数
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("mainCode", mainCode);
            jsonObject.put("fileHash", hashCode);
            String requestData = jsonObject.toJSONString();
            
            // 加密请求数据
            Map<String, String> requestJsonObject = generateEncryptRequest(requestData);
            log.info("*****************************************pushFile接口请求参数如下:\n");
            log.info("appId:" + requestJsonObject.get("appId"));
            log.info("key:" + requestJsonObject.get("key"));
            log.info("signatureData:" + requestJsonObject.get("signatureData"));
            log.info("requestData:" + requestJsonObject.get("requestData"));
            log.info("checkCode:" + requestJsonObject.get("checkCode"));
            
            // 准备HTTP请求
            Map<String, MultipartFile> fileParams = new HashMap<>();
            fileParams.put("file", multipartFile); // 直接使用原始multipartFile
            Map<String, String> headers = new HashMap<>();
            String url = baseUrl + pushFile;
            
            // 发送请求
            String responseString = HttpClientUtils.postMultipart(url, fileParams, requestJsonObject, headers);
            
            // 处理响应数据
            if (StringUtils.isNotBlank(responseString)) {
                JSONObject resJsonObj = JSONObject.parseObject(responseString);
                if (resJsonObj.get("code") != null && "0".equals(resJsonObj.get("code"))
                        && resJsonObj.get("data") != null && StringUtils.isNotBlank(resJsonObj.getString("data"))
                        && resJsonObj.get("key") != null && StringUtils.isNotBlank(resJsonObj.getString("key"))
                        && resJsonObj.get("signatureData") != null && StringUtils.isNotBlank(resJsonObj.getString("signatureData"))) {
                    String data = resJsonObj.getString("data");
                    String key = resJsonObj.getString("key");
                    String signatureData = resJsonObj.getString("signatureData");

                    String decryptData = responseDecrypt(key, data, signatureData);

                    if (StringUtils.isNotBlank(decryptData)) {
                        JSONObject dataObj = JSONObject.parseObject(decryptData);
                        if (dataObj != null && StringUtils.isNotBlank(dataObj.getString("fileId"))) {
                            return dataObj.getString("fileId");
                        }
                    }
                } else {
                    throw new RuntimeException("code: " + resJsonObj.get("code") + ", message: " + resJsonObj.get("message"));
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("上传文件处理失败: {}", e.getMessage(), e);
            throw e;
        }

    }

    /**
     * 主体授权应用场景同步
     */
    public String syncAuth(String authName, String certCode, String certName, String companyName, String uscc, String effectiveTime,
                           String fileId, String orderNo, String signName) throws Exception {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("authName", authName + "的南平市绿色创新企业信用评估与信贷服务场景授权");
        jsonObject.put("certCode", certCode);
        jsonObject.put("certName", certName);
        jsonObject.put("companyName", companyName);
        jsonObject.put("uscc", uscc);
        jsonObject.put("effectiveTime", effectiveTime);
        jsonObject.put("sceneId", sceneId);
        jsonObject.put("fileId", fileId);
        jsonObject.put("orderNo", orderNo);
        jsonObject.put("signTime", signName);
        String requestData = jsonObject.toJSONString();
        String responseString = null;
        Map<String, String> requestJsonObject = null;
        try {
            log.info("requestData: {}", requestData);
            requestJsonObject = generateEncryptRequest(requestData);
            log.info("*****************************************syncAuth接口请求参数如下:\n");
            log.info("appId:" + requestJsonObject.get("appId"));
            log.info("key:" + requestJsonObject.get("key"));
            log.info("signatureData:" + requestJsonObject.get("signatureData"));
            log.info("requestData:" + requestJsonObject.get("requestData"));
            log.info("checkCode:" + requestJsonObject.get("checkCode"));

        } catch (Exception e) {
            log.error("error occurred when generateEncryptRequest: ", e);
            throw e;
        }

        Map<String, String> headers = new HashMap<>();

        String url = baseUrl + syncAuth;

        responseString = HttpClientUtils.postForJson(url, JSONObject.parseObject(objectMapper.writeValueAsString(requestJsonObject)), headers);

        try {
            if (StringUtils.isNotBlank(responseString)) {
                JSONObject resJsonObj = JSONObject.parseObject(responseString);
                if (resJsonObj.get("code") != null && "0".equals(resJsonObj.get("code"))
                        && resJsonObj.get("data") != null && StringUtils.isNotBlank(resJsonObj.getString("data"))
                        && resJsonObj.get("key") != null && StringUtils.isNotBlank(resJsonObj.getString("key"))
                        && resJsonObj.get("signatureData") != null && StringUtils.isNotBlank(resJsonObj.getString("signatureData"))) {
                    String data = resJsonObj.getString("data");
                    String key = resJsonObj.getString("key");
                    String signatureData = resJsonObj.getString("signatureData");

                    String decryptData = responseDecrypt(key, data, signatureData);

                    if (StringUtils.isNotBlank(decryptData)) {
                        JSONObject dataObj = JSONObject.parseObject(decryptData);
                        if (dataObj != null && StringUtils.isNotBlank(dataObj.getString("token"))) {
                            return dataObj.getString("token");
                        }
                    }

                } else {
                    throw new RuntimeException("code: " + resJsonObj.get("code") + "message" + resJsonObj.get("message"));
                }

            }
        } catch (Exception e) {
            log.error("error occurred when handle response: ", e);
            throw e;
        }

        return null;
    }

    public JSONObject apiQuery(FjBigDataAuthorizeBO bo, String token) throws Exception {
        JSONObject param = new JSONObject();
        param.put("mainCode", bo.getUscc());
        param.put("company_name", bo.getCompanyName());
        param.put("year", bo.getYear());
        param.put("nsrmc", bo.getTaxpayer());
        param.put("sqsbh", bo.getOrderNo());
        param.put("sxsjq", bo.getEffectiveStartTime());
        param.put("sxsjz", bo.getEffectiveEndTime());
        param.put("fddbrxm", bo.getCertName());
        param.put("fddbrsfzjlxDm", bo.getCertType());
        param.put("fddbrsfzjhm", bo.getCertCode());
        param.put("fddbryddh", bo.getCertPhone());
        param.put("sqspdf", multipartFileToBase64UsingStream(bo.getFile()));

        String encryptStr = encrypt(JSONObject.toJSONString(param), token);
        JSONObject encrtptPram = JSONObject.parseObject(encryptStr);
        Map<String, String> headers = new HashMap<>();

        String responseString = HttpClientUtils.postForJson(apiQuery, encrtptPram, headers);

        try {
            if (StringUtils.isNotBlank(responseString)) {
                JSONObject resJsonObj = JSONObject.parseObject(responseString);
                if (resJsonObj.get("code") != null && "200".equals(resJsonObj.getString("code"))
                        && resJsonObj.get("data") != null && StringUtils.isNotBlank(resJsonObj.getString("data"))) {
                    String data = resJsonObj.getString("data");

                    JSONObject dataObj = JSONObject.parseObject(data);
                    if (dataObj.get("encryptData") != null && StringUtils.isNotBlank(dataObj.getString("encryptData"))
                        && dataObj.get("key") != null && StringUtils.isNotBlank(dataObj.getString("key"))
                            && dataObj.get("signatureData") != null && StringUtils.isNotBlank(dataObj.getString("signatureData"))) {

                        String encryptData= dataObj.getString("encryptData");
                        String key = dataObj.getString("key");
                        String signatureData = dataObj.getString("signatureData");

                        String decryptData = decrypt(key, encryptData, signatureData);

                        if (StringUtils.isNotBlank(decryptData)) {
                            JSONObject resObj = JSONObject.parseObject(decryptData);
                            if (resObj != null) {
                                return resObj;
                            }
                        }
                    }

                } else {
                    throw new RuntimeException("code: " + resJsonObj.get("code") + "message" + resJsonObj.get("message"));
                }

            }
        } catch (Exception e) {
            log.error("error occurred when handle response: ", e);
            throw e;
        }
        return null;
    }

    /**
     * 将 MultipartFile 转换为 Base64 编码的字符串 (使用流式读取)。
     * 此方法逐块读取文件，更适合大文件。
     *
     * @param file 要转换的 MultipartFile
     * @return Base64 编码的字符串，如果文件为空或转换失败则返回 null
     * @throws IOException 如果读取文件内容时发生 I/O 错误
     */
    public static String multipartFileToBase64UsingStream(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            return null;
        }

        try (InputStream inputStream = file.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            byte[] fileBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(fileBytes);
        }
    }

    /**
     *  获取授权协议信息
     */
    public String getProtocolInfo() throws Exception {
        // 客户端加密请求生成示例.
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sceneId", sceneId);
        String requestData = jsonObject.toJSONString();
        String requestJson = null;
        try {
            Map<String, String> requestJsonObject = generateEncryptRequest(requestData);
            requestJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(requestJsonObject);
            System.out.println("*****************************************请求json:\n" + requestJson);
        } catch (Exception e) {
            log.error("error occurred when handle request: ", e);
            throw e;
        }
        return requestJson;
    }

    /**
     * 服务端返回值加密
     **/
    private Map<String, String> generateEncryptRequest(String requestData) throws Exception {
        SignUtils signUtils = new SignUtils();
        String summary = signUtils.summary(requestData);
        String sign = signUtils.sign(appId, summary, localPrivateKey);
        String randomSm4Key = RandomStringUtils.getRandomString();
        SM4Utils sm4 = new SM4Utils(randomSm4Key, appKey);
        String encryptData = sm4.encryptData_CBC(appKey, requestData);
        String key = new SM2Utils().encrypt(NationalSecretsUtils.hexToByte(remotePublicKey),
                randomSm4Key.getBytes(StandardCharsets.UTF_8));
        return ImmutableMap.of("appId", appId, "key", key, "signatureData", sign, "requestData", encryptData,
                "checkCode", RandomStringUtils.getRandomString());

    }

    /**
     * 生成密钥
     */
    public static void generateKey() {
        SM2Utils sm2Utils=new SM2Utils();
        sm2Utils.generateKeyPair();

        System.out.println("调用密钥(APP_Key):"+ RandomStringUtils.getRandomString());

        System.out.println("客户端私钥(localPrivateKey):"+sm2Utils.privateKeyCode);
        System.out.println("客户端公钥(localPublickey):"+sm2Utils.publicKeyCode);
    }


    /**
     * 加密
     */
    public String encrypt(String params, String token) throws JsonProcessingException {
        //需要加密的参数
//        String params = "{\"sex\":\"false\",\"job\":\"programer\",\"age\":\"22\",\"names\":[]}";
        Map<String, String> map = new HashMap<>();
        String summary = SmUtil.sm3(params);
        log.info("摘要:" + summary);
        SM2 signUtil = SmUtil.sm2(localPrivateKey, null);
        String sign = signUtil.signHex(HexUtil.encodeHexStr(summary),modelId);
        log.info("请求signatureData:" + sign);
        String secretKey =  RandomUtil.randomString(16);
        log.info("随机sm4密钥:" + secretKey);
        SymmetricCrypto sm4 = SmUtil.sm4(secretKey.getBytes());
        String encryptData =  sm4.encryptHex(params);
        log.info("请求中data:" + encryptData);
        SM2 sm2 = SmUtil.sm2(null, modelServerPublicKey);
        String key = sm2.encryptHex(secretKey, KeyType.PublicKey);
        log.info("请求中key:" + key);
        map.put("modelId", modelId);
        map.put("publicKey", localPublickey);
        map.put("key", key);
        map.put("signatureData", sign);
        map.put("requestData", encryptData);
        map.put("token", token);
        String json = null;
        try {
            json = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(map);
        } catch (Exception e) {
            log.error("error occurred when handle request: ", e);
            throw e;
        }
        return json;
    }

    /**
     * 解密
     */
    public String decrypt(String key, String encryptData, String signatureData) throws Exception {
        SM2 sm2 = SmUtil.sm2(localPrivateKey, null);
        String secretKey = sm2.decryptStr(key, KeyType.PrivateKey);
        log.info("请求key解密结果: {}", secretKey);
        SymmetricCrypto sm4 = SmUtil.sm4(secretKey.getBytes());
        String decryptData = sm4.decryptStr(encryptData);
        log.info("请求encryptData解密结果: {}", decryptData);
        String summary = SmUtil.sm3(decryptData);
        SM2 signUtils = SmUtil.sm2(null, modelServerPublicKey);
        boolean verify = signUtils.verifyHex(HexUtil.encodeHexStr(summary), signatureData,modelId);
        log.info("请求验签结果: {}", verify);
        if (verify) {
            return decryptData;
        } else {
            throw new RuntimeException("验签失败");
        }
    }

    public String responseDecrypt(String key, String encryptData, String signatureData) throws Exception {

        // 服务端加密返回解密示例
        SM2Utils sm2Utils=new SM2Utils();
        String decryptSm4Key = sm2Utils.decrypt(localPrivateKey, key);
        log.info("请求key解密结果:" + decryptSm4Key);

        SM4Utils decryptSm4 = new SM4Utils(decryptSm4Key,appKey);
        String decryptData = decryptSm4.decryptData_CBC(appKey,encryptData);
        log.info("请求data解密结果:" + decryptData);

        SignUtils signUtils=new SignUtils();
        boolean verified = signUtils.verifiedSign(appId,decryptData, signatureData, remotePublicKey);

        log.info("请求验签结果:" + verified);
        if (verified) {
            return decryptData;
        } else {
            throw new RuntimeException("验签失败");
        }
    }

    public static void main(String[] args) {
        generateKey();
    }

}
