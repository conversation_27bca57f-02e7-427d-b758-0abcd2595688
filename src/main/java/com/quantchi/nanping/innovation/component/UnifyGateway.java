package com.quantchi.nanping.innovation.component;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.SM3;
import okhttp3.*;
import okhttp3.internal.http.HttpMethod;
import okio.Buffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public final class UnifyGateway {
    private static final Logger log = LoggerFactory.getLogger(UnifyGateway.class);

    private static final String HEADER_ACCESS_KEY = "X-Unify-App-Key";
    private static final String HEADER_SIGNATURE = "X-Unify-Signature";
    private static final String HEADER_TIMESTAMP = "X-Unify-Timestamp";
    private static final String HEADER_NONCE_STR = "X-Unify-Nonce-Str";

    /**
     * 是否签名 默认: 禁用
     */
    private boolean _signature;

    /**
     * 是否防篡改 默认: 禁用
     */
    private boolean _antiTamper;

    /**
     * 应用编号
     */
    private String _accessKey;

    /**
     * 签名密钥
     */
    private String _secretKey;

    /**
     * sm3 或者 sha256
     */
    private String _signType="sha256";

    /**
     * 忽略SSL证书校验
     */
    private static final X509TrustManager IGNORE_SSL_CERT = new X509TrustManager() {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    };

    /**
     * 创建OKHttpClient
     */
    private static OkHttpClient createHttpClient() {
        try {
            final TrustManager[] trustManagers = {IGNORE_SSL_CERT};
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustManagers, new SecureRandom());
            final SSLSocketFactory socketFactory = sslContext.getSocketFactory();

            final OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.sslSocketFactory(socketFactory, IGNORE_SSL_CERT);
            builder.hostnameVerifier((host, session) -> true);

            // 请求超时时间
            builder.callTimeout(5 * 60, TimeUnit.SECONDS);

            // 连接超时时间
            builder.connectTimeout(5 * 60, TimeUnit.SECONDS);

            // 读取超时时间
            builder.readTimeout(5 * 60, TimeUnit.SECONDS);

            // 写入超时时间
            builder.writeTimeout(5 * 60, TimeUnit.SECONDS);
            return builder.build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static byte[] readRequestBody(RequestBody requestBody) {
        if (requestBody == null) {
            return new byte[0];
        }
        try {
            Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            return buffer.readByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static String sm3(String content) {
        SM3 sm3 = SM3.create();
        return sm3.digestHex(content, StandardCharsets.UTF_8);
    }

    public static String sha256(String content) {
        byte[] bytes = DigestUtil.sha256(content);
        return HexUtil.encodeHexStr(bytes);
    }

    public Response request(String method, HttpUrl httpUrl, Headers headers, RequestBody requestBody) {
        Headers.Builder headersBuilder = new Headers.Builder();
        if (headers != null) {
            headersBuilder.addAll(headers);
        }

        // 无论是否要签名, 都要添加应用编号
        headersBuilder.set(HEADER_ACCESS_KEY, _accessKey);
        log.info("Header[{}]: {}", HEADER_ACCESS_KEY, _accessKey);

        // 判断是否启用了签名校验
        if (_signature) {
            String timestamp = System.currentTimeMillis() + "";
            String nonce_str = UUID.randomUUID().toString();
            List<String> signingItems = new ArrayList<>();

            if (_antiTamper) {
                String httpMethod = StrUtil.nullToDefault(method, "");
                String encodedPath = StrUtil.nullToDefault(httpUrl.encodedPath(), "");
                String encodedQuery = StrUtil.nullToDefault(httpUrl.encodedQuery(), "");

                signingItems.add(httpMethod);
                signingItems.add(encodedPath);
                signingItems.add(encodedQuery);
                signingItems.add(_accessKey);
                signingItems.add(_secretKey);
                signingItems.add(timestamp);
                signingItems.add(nonce_str);
                if (HttpMethod.requiresRequestBody(method)) {
                    // 读取计算哈希值
                    byte[] reqBody = readRequestBody(requestBody);
                    signingItems.add(SM3.create().digestHex(reqBody));

                    // 重新组装请求体
                    MediaType contentType = requestBody.contentType();
                    requestBody = RequestBody.create(contentType, reqBody);
                }
            } else {
                signingItems.add(_accessKey);
                signingItems.add(_secretKey);
                signingItems.add(timestamp);
                signingItems.add(nonce_str);
            }


            final String signing = String.join("\n", signingItems) + "\n";
            String signature = null;
            switch (_signType) {

                case "sha256":
                    signature = sha256(signing);
                    break;
                case "sm3":
                    signature = sm3(signing);
                    break;
                default:
                    throw new RuntimeException("未知的签名方式:" + _signType);

            }

            headersBuilder.set(HEADER_TIMESTAMP, timestamp);
            headersBuilder.set(HEADER_NONCE_STR, nonce_str);
            headersBuilder.set(HEADER_SIGNATURE, signature);

            log.info("Header[{}]: {}", HEADER_TIMESTAMP, timestamp);
            log.info("Header[{}]: {}", HEADER_NONCE_STR, nonce_str);
            log.info("Header[{}]: {}", HEADER_SIGNATURE, signature);
        }

        final OkHttpClient httpClient = createHttpClient();
        final Request request = new Request.Builder()
                .method(method, requestBody)
                .url(httpUrl)
                .headers(headersBuilder.build())
                .build();
        try {
            return httpClient.newCall(request).execute();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public Response request(String method, String httpUrl, Headers headers, RequestBody requestBody) {
        return request(method, HttpUrl.parse(httpUrl), headers, requestBody);
    }

    public Response doGet(HttpUrl httpUrl, Headers headers) {
        return request("GET", httpUrl, headers, null);
    }

    public Response doGet(String httpUrl, Headers headers) {
        log.info("doGet: url: {}, headers: {}", httpUrl, headers);
        return request("GET", httpUrl, headers, null);
    }

    public Response doDelete(HttpUrl httpUrl, Headers headers) {
        return request("DELETE", httpUrl, headers, null);
    }

    public Response doDelete(String httpUrl, Headers headers) {
        return request("DELETE", httpUrl, headers, null);
    }

    public Response doPost(HttpUrl httpUrl, Headers headers, RequestBody requestBody) {
        return request("POST", httpUrl, headers, requestBody);
    }

    public Response doPost(String httpUrl, Headers headers, RequestBody requestBody) {
        log.info("doPost: url: {}, headers: {}, requestBody: {}", httpUrl, headers, requestBody);
        return request("POST", httpUrl, headers, requestBody);
    }

    public Response doPut(HttpUrl httpUrl, Headers headers, RequestBody requestBody) {
        return request("PUT", httpUrl, headers, requestBody);
    }

    public Response doPut(String httpUrl, Headers headers, RequestBody requestBody) {
        return request("PUT", httpUrl, headers, requestBody);
    }

    public Response doPatch(HttpUrl httpUrl, Headers headers, RequestBody requestBody) {
        return request("PATCH", httpUrl, headers, requestBody);
    }

    public Response doPatch(String httpUrl, Headers headers, RequestBody requestBody) {
        return request("PATCH", httpUrl, headers, requestBody);
    }

    public static class Builder {
        /**
         * 应用编号
         */
        private String accessKey;

        /**
         * 签名密钥
         */
        private String secretKey;

        /**
         * 是否签名
         */
        private boolean signature = false;

        /**
         * 是否防篡改
         */
        private boolean antiTamper = false;

        /**
         * sm3 或者 sha256
         */
        private String signType="sha256";

        /**
         * 设置应用编号
         */
        public Builder setAppKey(String appKey) {
            this.accessKey = appKey;
            return this;
        }

        /**
         * 同 appKey
         */
        public Builder setAccessKey(String accessKey) {
            this.accessKey = accessKey;
            return this;
        }

        /**
         * 设置签名密钥
         */
        public Builder setSecret(String secret) {
            this.secretKey = secret;
            return this;
        }

        /**
         * 同 secret
         */
        public Builder setSecretKey(String secretKey) {
            this.secretKey = secretKey;
            return this;
        }

        /**
         * 启用签名
         */
        public Builder enableSignature() {
            this.signature = true;
            return this;
        }

        /**
         * 禁用签名
         */
        public Builder disableSignature() {
            this.signature = false;
            return this;
        }

        /**
         * 启用防篡改
         */
        public Builder enableAntiTamper() {
            this.antiTamper = true;
            return this;
        }

        /**
         * 禁用防篡改
         */
        public Builder disableAntiTamper() {
            this.antiTamper = false;
            return this;
        }

        /**
         * 设置签名密钥
         */
        public Builder setSignType(String signType) {
            this.signType = signType;
            return this;
        }


        /**
         * 构建
         */
        public UnifyGateway build() {
            UnifyGateway unifyGateway = new UnifyGateway();
            unifyGateway._accessKey = accessKey;
            unifyGateway._secretKey = secretKey;
            unifyGateway._signature = signature;
            unifyGateway._antiTamper = antiTamper;
            unifyGateway._signType = signType;
            return unifyGateway;
        }
    }
}
