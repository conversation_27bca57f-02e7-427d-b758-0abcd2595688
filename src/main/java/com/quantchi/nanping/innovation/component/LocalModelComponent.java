package com.quantchi.nanping.innovation.component;


import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.nanping.innovation.utils.HttpClientUtils;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class LocalModelComponent {

    @Autowired
    private StringRedisCache redisCache;

    private final static String CURRENT_REDIS_VALUE = "ON";

    public JSONObject buildRequestParam(Map<String, Object> valuePairs, String responseMode) {
        JSONObject paramBody = new JSONObject();
        JSONObject inputs = new JSONObject();
        for (Map.Entry<String, Object> valuePair: valuePairs.entrySet()){
            inputs.put(valuePair.getKey(), valuePair.getValue());
        }
        paramBody.put("inputs", inputs);
        paramBody.put("response_mode", responseMode);
        paramBody.put("user", StpUtil.getLoginIdAsString());
        return paramBody;
    }

    public JSONObject buildChatRequestParam(String query, String responseMode) {
        JSONObject paramBody = new JSONObject();
        paramBody.put("inputs", new JSONObject());
        paramBody.put("query", query);
        paramBody.put("response_mode", responseMode);
        paramBody.put("user", StpUtil.getLoginIdAsString());
        return paramBody;
    }

    public JSONObject buildDSRequestParam(String query) {
        JSONObject paramBody = new JSONObject();
        paramBody.put("model", "DeepSeek-R1-Distill-Qwen-7B");
        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("role", "user");
        message.put("content", query);
        messages.add(message);
        paramBody.put("messages", messages);
        return paramBody;
    }

    public JSONObject request(String api, String apiKey, JSONObject requestBody) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + apiKey);
        JSONObject result = JSONObject.parseObject(HttpClientUtils.postForJson(api, requestBody, headers));
        return result.getJSONObject("data").getJSONObject("outputs");
    }

    public String requestChat(String api, String apiKey, JSONObject requestBody, String chatKey) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + apiKey);
        JSONObject result = JSONObject.parseObject(HttpClientUtils.postForJson(api, requestBody, headers));
        if (StringUtils.isNotEmpty(chatKey)){
            redisCache.unLock(chatKey, CURRENT_REDIS_VALUE);
        }
        return result.getString("answer");
    }

    public String requestDS(String api, String token, JSONObject requestBody, String chatKey) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);
        JSONObject result = JSONObject.parseObject(HttpClientUtils.postForJson(api, requestBody, headers));
        if (StringUtils.isNotEmpty(chatKey)){
            redisCache.unLock(chatKey, CURRENT_REDIS_VALUE);
        }
        return result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");

    }

}
