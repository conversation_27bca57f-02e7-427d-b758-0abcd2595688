package com.quantchi.nanping.innovation.component;

import com.sirc.security.util.SM2Utils;
import com.sirc.security.util.SM4Utils;
import com.sirc.security.util.SignUtils;


/**
 * 同步授权模式服务结果解密
 * <AUTHOR>
 *
 */
public class OpenAuthClientDecrypt101 {

	private static final String APP_ID = "smgqvtdrof6fk5gtfl9ehl1vi7gd8tirlm4ylttdgqsi1cgr3xcmc1foxxcdythn";

	private static final String APP_KEY = "yD4cxqCRIr2DE1SP";
	
	// 本地sm2私钥,本地生成，见上生成实现
	private String localPrivateKey = "18282996FF7538916B76B8222C04CBED440DB4EDE42AD61C86350E382CBA0399";

	// 服务器sm2公钥，可用获取公钥接口获取
	private String remotePublicKey = "044A29CDF3178B72946DFC991BA05EDB7AE5D76291BAEE9A827A06AAE9E49FB2FD47FECB115E8E92748C911FF76DF2984CFC7C66626B4A911C1E8FE4A66EDB0ED6";

	private String key = "04D047CBAFCD26B62820ADD03523A5C8DE493BB1720297C8810EEA89F712BA53C81F05D0788A5CF2B258514E34DCF64B6A5386D4A4B49FA15E590AF974977C76C42FC00B901451B275C5512D9EC87265768FB781E30106BDC2B34BC3E4C948E63EE6167B5CC2118D3E5DD98D38DF7B4A9C";

	private String signatureData = "3045022100A2249742AE4876B241240D248BB7624F6635CC766CE0CA4AE7609BDACCE8D6F502207D4B0FA57155A0E41233364E70B45CB9C46CE0A2E48A9EF67FB703A28C7E6028";

	private String data = "WFiXYvdDY2TrffFx2UTDnGCERV8TrlnD5zPcW1ndm/6L4UpVHzOG0yLOA7Bb5VmvC+ByhxKD4z6sLI+EVjr4mB6L86BFZQanJ4sLHQ+dFM97Z8IsImVc1hKF21d/0DEC";

	
	public static void main(String args[]) throws Exception {
		new OpenAuthClientDecrypt101().test_response_decrypt();
	}
	
//	主体授权文件上传
//	{
//	    "code": "0",
//	    "data": "aU/WAo1/Gg133IGrO+14M6Sdy6nQ6S5tOk2itzIRJPlLbaW2I5I/f2MOpS+4DIy8",
//	    "key": "04826CA8835E46F0B51D80FE0B13045B9554F94F475AD2198D845ABFE52FA808A1E7970A9DFC9D1FA12E382D53F0A24FA208C474050C17BEEF247184E00E92967777A4E012942BBECDECC1E9EB44AB5829F33B121C6767ADD48849A47CB00ADC7F66C2A192CE368519CE68306BE7E9187A",
//	    "msg": "success",
//	    "signatureData": "3046022100AC53E035468955DD631AA02586B22E3E596FE6716D9777C5FEACC38F8280EFAC022100E72B9675226DB39D0544EF6D27D49014054E90EACA72854F6D51C65F1602AAAF"
//	}

//	主体授权应用场景同步
//	{
//	    "code": "0",
//	    "data": "Own9ZY0W/WZipPUVpZxlt4W7k6ZBtbINLBtiRc1Nyd6mWBnH7swt2wx/y+oSgZiCeErS4yl3/ETDK79vg25Y3OJ1H5Pl8FgBP3eRJKjnv4zdENvkuIg++BJ/9FLEn8Ey4t28+9ZRds8QXfBZrViyg9FF6RPnRKqsJi019u3sEqMxHnZ985YPi3I8JPJ/oLly/BOwM0DUrXbOO++cIluZabf/7M6RO1H86dcI5VJ9wOlgulSNW9wwSgvDVaAQNhiAG+isYJB9O9Bize4fHkkOcOQBdJGVyjUlL2OtYP/LMFrrUdIuGLilS0Kk1PgY/aQ+X77RTSZ1+kJnQSMVZN5G/Nvu+lCKAfWDuuJJVIcJ13+SV64xQGzOTCJj6jMmb+SVfuyDFyioeifBGPSrPUbUJwc4kTEG++2zjM7UsxhSdvyYlErhXJjCRZrOH8AJyI9qT8bn6uVdPehfO1FZTQvgRImvosxBt4xxTZjuM3U99hqL3NSwP+f7QtZZXlN5YaZ/ySYFUfIk2FgNtf6yoLz8xAKsreG32XYxI7lQTMUVfwUMrhPhSaMPStHGyY0ApKsD4ltjRik2qtXViUp2cVBV2D7+/nD3nQdMSltq/oa/OTpdYp/HpMHiJRmKcn9ta/cxF8R1b7/ZknooErec2R9aJ4LMcmow9zQalZQ4210e6+lf3FjJHUVmp4qBY31dXiOM",
//	    "key": "047426E6F01B95FCAE74ACEF4C7A758E0DDEE20CF58817AED7D9B0310125B3C756CE908D240FE8139A0CDB1D350FE01D503FFFD356240371133E10B1A0073B1759B175A694C74A4D257F6CD6A2BD4724BA4D63A64B5C5D90BCE8D4AEFC62AA6D6484412EA5368F103F64540994E177D324",
//	    "msg": "success",
//	    "signatureData": "304402201357AEAE3354083EA1013F4D020C6FB88CC484FA19035F5FC48F3D255DD929870220027307FF9947F11F5CE82FE0BA652E73CBFCBF76E11DB5381883E2C6979AAB40"
//	}
	


	/**
	 * 服务端加密返回解密示例
	 **/
	public void test_response_decrypt() throws Exception {
		// 服务端加密返回解密示例
		SM2Utils sm2Utils=new SM2Utils();
		String decryptSm4Key = sm2Utils.decrypt(localPrivateKey, key);
		System.out.println("请求key解密结果:" + decryptSm4Key);

		SM4Utils decryptSm4 = new SM4Utils(decryptSm4Key,APP_KEY);
		String decryptData = decryptSm4.decryptData_CBC(APP_KEY,data);
		System.out.println("请求data解密结果:" + decryptData);

		SignUtils signUtils=new SignUtils();
		boolean verified = signUtils.verifiedSign(APP_ID,decryptData, signatureData, remotePublicKey);

		System.out.println("请求验签结果:" + verified);
		if (verified) {
			System.out.println("正常请求:" + decryptData);
		} else {
			System.out.println("验签错误");
		}
	}

}
