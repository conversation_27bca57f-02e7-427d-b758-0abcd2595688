es:
  cluster:
    name: elasticsearch
    hosts: ************:8199
  username: elastic
  password: C8hLrU%L4yD4*C2K

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.kingbase8.Driver
    url: ***************************************************************************************,productName=PostgreSQL,SYS_CATALOG,PUBLIC
    username: nanping_innovation
    password: ^i$Qk986Z^5WGg#K
    # druid 配置
    druid:
      min-idle: 1
      initial-size: 1
      max-active: 1
  redis:
    database: 3
    host: ************
    port: 60379
    # Redis服务器连接密码
    password: qS$#ON1z6Ns1g&FJ
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
  # 反爬redission配置
  redisson:
    address: redis://************:60379
    password: qS$#ON1z6Ns1g&FJ
    database: 3

#攻关接口地址
overcome_evaluation_url: http://************:8404/

#服务地址
self.address: http://test.nanping-innovation.quant-chi.com:30800

# 掌上南平
zs-nanping:
  # 客户端标识
  client_id:
    pc: 691c5e9f7ae846d0909d2b8a9ed7375f
  # 客户端密钥
  client_secret:
    pc: 6930985e2df2475e99276bfc9b529f58
  # 服务地址
  login:
    redirect:
      pc: http://test.nanping-innovation.quant-chi.com:30800/service/company/index

# 接口返回加密
interface:
  encrypt: 0

# PDF文件存放位置
pdf:
  generate:
    dir:
      patent: C:\Users\<USER>\Desktop\file\

