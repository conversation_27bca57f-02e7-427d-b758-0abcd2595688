es:
    cluster:
        hosts: ***************:8199
        name: elasticsearch
    password: P$YfWy!YxvWHMaLZ
    username: elastic
inspur:
    oss:
        bucketName: oss-lscx202309180001
        endpoint: oss.nanping-hlw.inspurcloudoss.com
        keyId: OWI3NmU4ZDAtMTFkOC00NmFjLTkyZjQtMmM2Mjc0NWFhZTVk
        keySecret: ZGNlYzAxMDYtMTU5MC00NGMzLWJhYzAtNGYzZjAxNDhmYTEy
logging:
    config: classpath:logback.xml
mybatis-plus:
    config-location: classpath:mybatis/mybatis-config.xml
    mapper-locations: classpath:mybatis/mapper/**/*.xml
overcome_evaluation_url: http://***************:8404/
restTemplate:
    connectTimeout: 30000
    readTimeout: 30000
self:
    address: https://nplscx.np.gov.cn
server:
    port: 8804
    servlet:
        encoding:
            charset: UTF-8
            enabled: true
            force: true
    session:
        cookie:
            http-only: true
            secure: true
    tomcat:
        uri-encoding: UTF-8
spring:
    application:
        name: innovation-chain
    datasource:
        driver-class-name: com.kingbase8.Driver
        name: dev
        password: '12Cr]FZ8blOgR[o:'
        type: com.alibaba.druid.pool.DruidDataSource
        url: ******************************************************************************************,productName=PostgreSQL,SYS_CATALOG,PUBLIC,Timezone=PRC
        username: nanping_innovation
    messages:
        basename: message/messages
    redis:
        database: 3
        host: 127.0.0.1
        lettuce:
            pool:
                max-active: 200
                max-idle: 10
                max-wait: -1ms
                min-idle: 0
        password: dCHM&GEat3Jp3yDz
        port: 60379
        timeout: 10s
    redisson:
        address: redis://127.0.0.1:60379
        database: 3
        password: dCHM&GEat3Jp3yDz
springfox:
    documentation:
        auto-startup: false
        enabled: false
        swagger-ui:
            enabled: false
timer:
    knowledge:
        center:
            count: false
zs-nanping:
    login:
        url: http://**************:9001
    user:
        url: http://**************:9002

model:
    # 人才匹配接口
    expertMatch: http://***************:8404/model/team_ai_pool
    # 专利ES8搜索
    patentMatch: http://***************:8404/model/es_v8_search

# 易企办鉴权参数
ebus:
    passID: nps_lscxpt_pro
    token: s2hgp3xcNq823TbnYabMkJGWjSGPm0Gr


# 闵政通
mzt:
    appId: a367091c-8a35-455a-9d6b-6e266c4e6c4d
    appSecret: 4d05cdca281f4e3fa4b9d38f5d3628d7
    host: https://**********:50371
    getAppTokenUrl: /ebus/ability_tysfrz_gzcszsf/outside/oauth2/getAppToken
    getAccessTokenUrl: /ebus/ability_tysfrz_gzcszsf/outside/oauth2/token
    getUserInfoUrl: /ebus/ability_tysfrz_gzcszsf/outside/oauth2/getUserInfo