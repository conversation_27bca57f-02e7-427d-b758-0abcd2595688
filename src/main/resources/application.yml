spring:
  profiles:
    active: dev
  application:
    name: innovation-chain
  http:
    encoding: UTF-8
  datasource:
    # druid 配置
    druid:
      initial-size: 5
      max-active: 50
      min-idle: 5
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 1800000
      validationQuery: SELECT 'x'
      stat-view-servlet:
        enabled: false
      test-while-idle: true
mybatis-plus:
  config-location: classpath:mybatis/mybatis-config.xml
  mapper-locations: classpath:mybatis/mapper/*.xml

server:
  port: 8804
  servlet:
    context-path: /api
  tomcat:
    uri-encoding: UTF-8
logging:
  config: classpath:logback.xml

# Sa-Token配置
sa-token:
  # token 名称 (同时也是cookie名称)
  token-name: Authorization
  # token 有效期，单位s 默认1天, -1代表永不过期
  timeout: 86400
  # token 临时有效期 (指定时间内无操作就视为token过期) 单位: 秒 默认30分钟
  active-timeout: 7200
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: false
  # 是否支持动态设置token
  dynamic-active-timeout: true

# 文件上传目录
file:
  upload:
    root: /home/<USER>/nanping-innovation
    dir: upload
    # 临时文件存放目录
    temp: temp
    h5.report: h5-report

# PDF文件存放位置(专利报告)
pdf.generate.dir:
  patent: /home/<USER>/nanping-innovation/patent-report/
# 掌上南平
zs-nanping:
  # 客户端标识
  client_id:
    mobile: 65a81272c56540cda243e11a3add3369
    pc: 97fa24489c9d4604ba2641f53ddab5bc
  # 客户端密钥
  client_secret:
    mobile: bce89ea75f6d4dff82e964282cc32db7
    pc: 1aee341fcaf846a6a2e9cdf235024970
  # 登录
  login:
    url: https://auth-ser.zsnanping.com
    redirect:
      mobile: https://nplscx.np.gov.cn/h5/pages/index/index
      pc: https://nplscx.np.gov.cn/service/company/index
  # 用户
  user:
    url: https://auth-res.zsnanping.com
# 南平绿色金融token 推送接口
nanping-lsjr:
  appId: npsyjt_finance
  appSecret: HRTmQ3aPgSDQWCjy8BnFip7xuGkV5sLJ
  url: http://sznp.npbigdata.com.cn:18003/prod-api/nplscx
# 接口返回加密
interface:
  encrypt: 1

# 反爬配置
anti:
  reptile:
    manager:
      ip-rule:
        request-max-size: 100
        expiration-time: 2000
        lock-expire: 86400
      enabled: true

#浪潮云 OSS默认配置
inspur:
  oss:
    bucketName: oss-lscx202309180001

# 定时任务开关
timer:
  knowledge.center.count: true

# 密码修改定期时长(天)
user:
  password:
    valid_days: 90
  never_logout:
    id: -1
  admin_id: 1

# 智谱
chat:
  apiSecretKey: d7c79932ce0df80ca1e38ce258c56388.LYDLc6yvQO2YhFZo


model:
  # 人才匹配接口
  expertMatch: http://************:8404/model/team_ai_pool
  # 专利ES8搜索
  patentMatch: http://************:8404/model/es_v8_search
  # 向量计算
  vector:
    url: http://***************:8042/v1/embeddings
    authorization: sk-gH5EJWJdwnkokyhI1c387f8dC5Af4fC8870b386111Fc489b

# 敏感内容识别
oss.sensitive.check:
  accessKey: LTAI5tSoX59dYoTiCEc4hHE5
  accessSecretKey: ******************************

# 易企办鉴权参数
ebus:
  passID: nps_lscypt_test
  token: o3TOCsfwj5gP8LoeOPNseNCzLTrNtPGy

# 公司本地大模型接口调用参数
local.model:
  analysis.apiKey: app-AIIsiFAIxl700pp7faAUPMsi
  preInput.apiKey: app-Hcvrrmv4E1pndmzQN02sdtnH
  expert.apiKey: app-9QqQuV9xn1BuyzSBQUfWgHGA
  sensitive.apiKey: app-gVRv3mKjHBlG4Sb43psq3E7F
  patentExtract.apiKey: app-EHO8O4tNoL8rjbA9iilcYpEI
  standard.apiKey: app-y8hHd4sOjWpvYT1jJhpdZT1u
  keyTech.apiKey: app-tqCEXBmnHsBng8GxcPbQBAxK
  workflowApi: http://*************/v1/workflows/run
  chatApi: http://*************/v1/chat-messages
  dsApi: https://oneapi.supxmind.quant-chi.com/v1/chat/completions
  dsToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************.4TJgjW2Mp_Bk3miI-BCCfs8Apvh_Vxm9uKlgXu-TzBI

# 闵政通
mzt:
  accessKey: app_npslscycxfwpt
  secretKey: cl3WiPhPUmIZwFduFB8V7Sgt2IyicqFh
  appId: 037cde2b-882c-43a2-891c-fa1b17329de4
  appSecret: 1a430340154642dda058d73a6e05b3bd
  host: http://**************:8901
  getAppTokenUrl: /prod-api/outside/oauth2/getAppToken
  getAccessTokenUrl: /prod-api/outside/oauth2/token
  getUserInfoUrl: /prod-api/outside/oauth2/getUserInfo

fj-big-data:
  baseUrl: https://service.fjbigdata.com.cn/
  getProtocolInfo: openauth/api/getProtocolInfo
  pushFile: openauth/api/pushFile
  syncAuth: openauth/api/syncAuth
  apiQuery: https://service.fjbigdata.com.cn/datagateway/api/gateway/9640067fad018add1953d2ee173cbf4f/apiQuery
  sceneId: 879b9a3128d874dc10a3fea2e0a8bac9
  appId: smgqvtdrof6fk5gtfl9ehl1vi7gd8tirlm4ylttdgqsi1cgr3xcmc1foxxcdythn
  appKey: yD4cxqCRIr2DE1SP
  modelId: 9640067fad018add1953d2ee173cbf4f
  remotePublicKey: 04135E00085E8DB04CF246E0546519732FB920EC8F680BC4D95EB06B15B334EF126762542969B21E3D7D9F3CC1DC00F8A99632308CAA6D619F1D0DB519B662D7D2
  localPrivateKey: 18282996FF7538916B76B8222C04CBED440DB4EDE42AD61C86350E382CBA0399
  localPublickey: 044A29CDF3178B72946DFC991BA05EDB7AE5D76291BAEE9A827A06AAE9E49FB2FD47FECB115E8E92748C911FF76DF2984CFC7C66626B4A911C1E8FE4A66EDB0ED6
  modelServerPublicKey: 042e925b5ce6f76759af4e7cb117b4cc4462db5bdf6928d1706259c6d2b52caa27d9e310f06ad6302df8ac0871dee5bf20dcdcda5ade540eed427bf6cf66b9e0df
