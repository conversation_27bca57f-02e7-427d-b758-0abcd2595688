package ${package.Controller};


import com.quantchi.innovation.chain.common.JsonResultMessage;
import ${package.Entity}.${table.entityName};
import ${package.Service}.${table.serviceName};
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
<#if restControllerStyle>
import org.springframework.web.bind.annotation.RestController;
<#else>
import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
import ${superControllerClassPackage};
</#if>

import java.util.*;

/**
 * <p>
 * ${table.comment!}
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
<#if restControllerStyle>
@RestController
<#else>
@Controller
</#if>
@Slf4j
@Api(tags = "${table.comment}")
@RequestMapping("/api<#if package.ModuleName??>/${package.ModuleName}</#if>/<#if controllerMappingHyphenStyle??>${controllerMappingHyphen}<#else>${table.entityPath}</#if>")
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
<#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass} {
<#else>
public class ${table.controllerName} {
</#if>

    @Autowired
    private ${table.serviceName} ${table.name}Service;

    @ApiOperation(value = "获取${table.name}列表",notes="")
    @ApiImplicitParam(name = "map", value = "${table.name}实体中的参数", required = true, dataType = "Map")
    @GetMapping("/")
    public String ${table.name}List(@RequestParam Map<String, Object> map) throws Exception {
        Collection<${table.entityName}> ${table.name}List = ${table.name}Service.listByMap(map);
        return JsonResultMessage.success(${table.name}List);
    }

    @ApiOperation(value = "修改${table.name}", notes="根据id修改${table.name}")
    @ApiImplicitParam(name = "${table.name}", value = "${table.name}实体", required = true, dataType = "${table.name}")
    @PutMapping("/")
    public String ${table.name}Update(@RequestBody  ${table.entityName} ${table.name}) throws Exception {
        Boolean flag = ${table.name}Service.updateById(${table.name});
        return JsonResultMessage.success(flag);
    }
    @ApiOperation(value = "删除${table.name}", notes = "根据id删除${table.name}")
    @ApiImplicitParam(name = "id", value = "${table.name}id", required = true, dataType = "<#list table.fields as field><#if field.keyFlag == true>${field.columnType?lower_case?cap_first}</#if></#list> ")
    @DeleteMapping("/{id}")
    public String ${table.name}Delete(@PathVariable <#list table.fields as field><#if field.keyFlag == true>${field.columnType?lower_case?cap_first}</#if></#list> id) throws Exception {
        Boolean flag = ${table.name}Service.removeById(id);
        return JsonResultMessage.success(flag);
    }

    @ApiOperation(value = "添加${table.name}", notes = "新增一条${table.name}")
    @ApiImplicitParam(name = "${table.name}", value = "${table.name}实体", required = true, dataType = "${table.name}")
    @PostMapping("")
    public String ${table.name}Add(@RequestBody  ${table.entityName} ${table.name}) throws Exception {
        Boolean flag = ${table.name}Service.save(${table.name});
        return JsonResultMessage.success(flag);
    }
}
</#if>
