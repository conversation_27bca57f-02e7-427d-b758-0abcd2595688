{"keywordSearchList": [{"field": "nanping_innovation_company", "fieldName": "企业", "requiredList": ["id", "name", "labels", "logo", "logo_local", "legal_person", "establish_date", "regist_capi", "regist_capi_unit", "regist_capi_value", "tel", "website", "email", "address", "chain_node", "city", "plate", "share"], "keywordSearchList": [{"field": "name", "fieldName": "企业名", "boost": 10, "isHighlight": true}, {"field": "legal_person", "fieldName": "法人", "isHighlight": true}, {"field": "desc", "fieldName": "企业简介", "isHighlight": false}, {"field": "address", "fieldName": "注册地址", "isHighlight": true}, {"field": "chain_node.name", "fieldName": "所属节点", "isHighlight": false}]}, {"field": "nanping_innovation_expert", "fieldName": "专家", "requiredList": ["id", "name", "honors", "prof_title", "org", "city", "tel", "email", "address", "awards", "chain_node", "city"], "keywordSearchList": [{"field": "name", "fieldName": "姓名", "boost": 10, "isHighlight": true}, {"field": "org", "fieldName": "所在机构", "isHighlight": true}, {"field": "resume", "fieldName": "简介", "isHighlight": false}, {"field": "awards.name", "fieldName": "荣誉称号", "isHighlight": true}, {"field": "chain_node.name", "fieldName": "所属节点", "isHighlight": false}]}, {"field": "nanping_innovation_patent", "fieldName": "专利", "requiredList": ["id", "public_code", "name", "patent_type", "status", "domain", "applicants", "apply_date", "public_date", "chain_node", "city"], "keywordSearchList": [{"field": "public_code", "fieldName": "公开（公告）号", "isHighlight": true}, {"field": "apply_code", "fieldName": "申请号", "isHighlight": false}, {"field": "name", "fieldName": "专利名称", "boost": 10, "isHighlight": true}, {"field": "applicants.name", "fieldName": "申请（专利权）人", "isHighlight": true}, {"field": "inventors.name", "fieldName": "发明人", "isHighlight": false}, {"field": "abstract", "fieldName": "摘要", "isHighlight": false}, {"field": "chain_node.chain_name", "fieldName": "所属节点", "isHighlight": false}]}, {"field": "nanping_innovation_news", "fieldName": "资讯", "requiredList": ["id", "title", "publish_time", "source", "tags", "sentiment", "chain_node"], "keywordSearchList": [{"field": "title", "boost": 10, "fieldName": "标题", "isHighlight": true}, {"field": "source", "fieldName": "来源", "isHighlight": true}, {"field": "content", "fieldName": "内容", "boost": 0.5, "isHighlight": true}, {"field": "entities.name", "fieldName": "关联企业", "isHighlight": true}]}, {"field": "nanping_innovation_policy", "fieldName": "政策", "requiredList": [], "keywordSearchList": [{"field": "title", "fieldName": "标题", "boost": 10, "isHighlight": true}, {"field": "source", "fieldName": "来源", "isHighlight": true}, {"field": "content", "fieldName": "内容", "isHighlight": true}]}, {"field": "nanping_innovation_company_financing", "fieldName": "投融资", "requiredList": ["id", "financing_product", "financing_company", "business_desc", "industry", "financing_time", "logo", "chain_node", "financing_area", "financing_round", "financing_amount", "financing_scale", "financing_amount_cal", "investors", "url", "city", "source"], "keywordSearchList": [{"field": "financing_company.name", "fieldName": "公司名称", "boost": 10, "isHighlight": true}, {"field": "investors.name", "fieldName": "投资方", "isHighlight": true}]}, {"field": "nanping_innovation_platform", "fieldName": "平台", "requiredList": [], "keywordSearchList": [{"field": "name", "fieldName": "名称", "boost": 10, "isHighlight": true}]}, {"field": "nanping_innovation_achievement", "fieldName": "成果", "requiredList": [], "keywordSearchList": [{"field": "name", "fieldName": "名称", "boost": 10, "isHighlight": true}]}]}