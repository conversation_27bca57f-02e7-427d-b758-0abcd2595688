{"boostPropertyList": [{"field": "nanping_innovation_expert_dev", "fieldName": "专家", "weight": 20, "fieldBoostPropertyList": [{"field": "name", "fieldName": "姓名", "weight": 10}, {"field": "org", "fieldName": "工作单位", "weight": 1}, {"field": "experiences", "fieldName": "简介", "weight": 1}, {"field": "honors.name", "fieldName": "荣誉称号", "weight": 1}, {"field": "chain_node.chain_name", "fieldName": "所属节点", "weight": 1}]}, {"field": "nanping_innovation_company_dev", "fieldName": "企业", "weight": 18, "fieldBoostPropertyList": [{"field": "name", "fieldName": "企业名", "weight": 10}, {"field": "legal_person", "fieldName": "法人", "weight": 1}, {"field": "desc", "fieldName": "企业简介", "weight": 1}, {"field": "address", "fieldName": "注册地址", "weight": 1}, {"field": "chain_node.chain_name", "fieldName": "所属节点", "weight": 1}]}, {"field": "nanping_innovation_company_financing_dev", "fieldName": "融资", "weight": 16, "fieldBoostPropertyList": [{"field": "financing_company.name", "fieldName": "融资企业", "weight": 10}, {"field": "financing_round", "fieldName": "融资轮次", "weight": 1}, {"field": "chain_node.chain_name", "fieldName": "所属节点", "weight": 1}]}, {"field": "nanping_innovation_patent_dev", "fieldName": "专利", "weight": 12, "fieldBoostPropertyList": [{"field": "name", "fieldName": "标题", "weight": 10}, {"field": "applicants.name", "fieldName": "申请公司", "weight": 1}, {"field": "inventors", "fieldName": "发明人", "weight": 1}, {"field": "ipc", "fieldName": "IPC号", "weight": 1}, {"field": "abstract", "fieldName": "摘要", "weight": 1}, {"field": "chain_node.chain_name", "fieldName": "所属节点", "weight": 1}]}, {"field": "nanping_innovation_project_dev", "fieldName": "项目", "weight": 10, "fieldBoostPropertyList": [{"field": "name", "fieldName": "标题", "weight": 10}, {"field": "organization", "fieldName": "承担单位", "weight": 1}, {"field": "keyword", "fieldName": "关键词", "weight": 1}, {"field": "desc", "fieldName": "项目摘要", "weight": 1}, {"field": "chain_node.chain_name", "fieldName": "所属节点", "weight": 1}]}, {"field": "nanping_innovation_policy_dev", "fieldName": "政策", "weight": 4, "fieldBoostPropertyList": [{"field": "title", "fieldName": "标题", "weight": 10}, {"field": "content", "fieldName": "内容", "weight": 1}, {"field": "chain_node.chain_name", "fieldName": "所属节点", "weight": 1}]}, {"field": "nanping_innovation_report_dev", "fieldName": "研报", "weight": 3, "fieldBoostPropertyList": [{"field": "title", "fieldName": "标题", "weight": 10}, {"field": "content", "fieldName": "内容", "weight": 1}, {"field": "chain_node.chain_name", "fieldName": "所属节点", "weight": 1}]}, {"field": "nanping_innovation_news_dev", "fieldName": "资讯", "weight": 2, "fieldBoostPropertyList": [{"field": "title", "fieldName": "标题", "weight": 10}, {"field": "content", "fieldName": "内容", "weight": 1}, {"field": "chain_node.chain_name", "fieldName": "所属节点", "weight": 1}]}, {"field": "nanping_innovation_platform_dev", "fieldName": "载体", "weight": 1, "fieldBoostPropertyList": [{"field": "name", "fieldName": "名称", "weight": 10}]}, {"field": "nanping_innovation_achievement_dev", "fieldName": "成果", "weight": 1, "fieldBoostPropertyList": [{"field": "name", "fieldName": "名称", "weight": 10}]}]}