<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.nanping.innovation.knowledge.center.dao.IndustryIpcMapper">
    <select id="getIpcByCode" resultType="com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentPortraitIndustryIpcBO">
        SELECT id, code, level, name, parent_id parentId
        FROM dw_industry_ipc
        WHERE code = #{code}
    </select>

    <select id="getIpcByParentId" resultType="com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentPortraitIndustryIpcBO">
        SELECT id, code, level, name, parent_id parentId
        FROM dw_industry_ipc
        WHERE id = #{parentId}
    </select>

    <select id="getNameByNec" resultType="com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentPortraitIndustryClassBO">
        SELECT id, name
        FROM dw_industry_nec
        WHERE id IN (
            <foreach item="item" collection="list" separator="," >
                #{item}
            </foreach>
        )
<!--        ORDER BY FIELD(id,-->
<!--            <foreach item="item" collection="list" separator=",">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        )-->
    </select>

    <select id="getNameBySec" resultType="com.quantchi.nanping.innovation.knowledge.center.model.bo.PatentPortraitIndustryClassBO">
        SELECT id, name
        FROM dw_industry_sec
        WHERE id IN (
            <foreach item="item" collection="list" separator="," >
                #{item}
            </foreach>
        )
<!--        ORDER BY FIELD(id,-->
<!--            <foreach item="item" collection="list" separator=",">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        )-->
    </select>
</mapper>