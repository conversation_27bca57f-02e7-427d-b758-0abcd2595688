<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.nanping.innovation.dao.mapper.IndustryChainNodeTypeDAO">
    <select id="page" resultType="com.quantchi.nanping.innovation.model.IndustryChainNodeType">
        SELECT
        n.chain_id AS chainId,
        n.id AS nodeId,
        n.NAME AS nodeName,
        IFNULL (t.type_id, 100) AS typeId,
        t.node_type AS nodeType
        FROM
        industry_chain_node n
        LEFT JOIN (SELECT
        node_id,
        MIN( type_id ) AS type_id,
        GROUP_CONCAT( node_type ) AS node_type
        FROM
        industry_chain_node_type
        <if test="nodeTypeId != null">
        WHERE type_id = #{nodeTypeId}
        </if>
        GROUP BY node_id) t ON n.id = t.node_id
        WHERE n.chain_id = #{chainId} AND n.level > 1 AND n.is_valid=1
        <if test="nodeTypeId != null">
            AND t.type_id = #{nodeTypeId}
        </if>
        <if test="nodeId != null">
            AND (n.id = #{nodeId} OR n.parent_id = #{nodeId})
        </if>
        <if test="keyword != null and keyword != ''">
            AND n.name LIKE CONCAT('%', #{keyword}, '%')
        </if>
        ORDER BY typeId ASC, nodeId ASC
    </select>

</mapper>
