stages:
  - build
  - deploy
  - delete_branch
create_branch:
  stage: build
  script:
    - 'export BRANCH_NAME="test-$(date +%Y%m%d)"'
    - "curl --request POST --header \"PRIVATE-TOKEN: ${YOUR_PERSONAL_TOKEN}\" \"http://************:8099/api/v4/projects/${CI_PROJECT_ID}/repository/branches?branch=$BRANCH_NAME&ref=test\""
  only:
    - test #代码只提交到test分支触发该job生成test-yyyymmdd分支
create_tag:
  stage: deploy
  script:
    - 'export TAG_NAME="release-$(date +%Y%m%d)"'
    - "curl --request POST --header \"PRIVATE-TOKEN: ${YOUR_PERSONAL_TOKEN}\" \"http://************:8099/api/v4/projects/${CI_PROJECT_ID}/repository/tags?tag_name=${TAG_NAME}&ref=kingbase\""
  only:
    - kingbase #代码只提交到master分支触发该job生成一个release-yyyymmdd的tag
delete_branch:
  stage: delete_branch
  script:
    - |
      # 使用预定义变量或自定义变量根据你的情况设置
      CI_PROJECT_ID=${CI_PROJECT_ID}
      GITLAB_HOST="http://************:8099"
       
      # 列出所有分支并过滤出以 "test-" 开头的分支
      BRANCHES=$(curl --silent --header "PRIVATE-TOKEN: ${YOUR_PERSONAL_TOKEN}" "$GITLAB_HOST/api/v4/projects/$CI_PROJECT_ID/repository/branches" | jq -r '.[] | select(.name | startswith("test-")) | .name')
      # 遍历并删除提测分支
      for BRANCH_NAME in $BRANCHES; do
        curl --request DELETE --header "PRIVATE-TOKEN: $YOUR_PERSONAL_TOKEN" "$GITLAB_HOST/api/v4/projects/$CI_PROJECT_ID/repository/branches/$BRANCH_NAME"
        echo "Deleted branch: $BRANCH_NAME"
      done
  only:
    - kingbase